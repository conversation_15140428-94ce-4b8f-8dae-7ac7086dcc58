import { body, param, query, validationResult } from 'express-validator';
import DOMPurify from 'isomorphic-dompurify';
import validator from 'validator';

/**
 * Enterprise Input Validation Middleware
 * Comprehensive validation and sanitization for all user inputs
 */

/**
 * Handle validation errors and return standardized error response
 */
export const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.path,
      message: error.msg,
      value: error.value
    }));

    return res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: formattedErrors
    });
  }
  
  next();
};

/**
 * Sanitize HTML content to prevent XSS attacks
 */
export const sanitizeHTML = (value) => {
  if (typeof value !== 'string') return value;
  
  // Configure DOMPurify for safe HTML sanitization
  const config = {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'a'],
    ALLOWED_ATTR: ['href', 'target', 'rel'],
    ALLOW_DATA_ATTR: false,
    FORBID_TAGS: ['script', 'style', 'iframe', 'object', 'embed'],
    FORBID_ATTR: ['onclick', 'onload', 'onerror', 'onmouseover']
  };
  
  return DOMPurify.sanitize(value, config);
};

/**
 * Advanced keyword validation
 */
const validateKeyword = (value) => {
  if (!value || typeof value !== 'string') {
    throw new Error('Keyword must be a non-empty string');
  }
  
  // Reject demo/mock data patterns
  const demoPatterns = [
    /example/i,
    /test\s+keyword/i,
    /sample/i,
    /demo/i,
    /placeholder/i,
    /lorem\s+ipsum/i,
    /fake\s+keyword/i,
    /dummy/i
  ];
  
  for (const pattern of demoPatterns) {
    if (pattern.test(value)) {
      throw new Error('Demo or placeholder keywords are not allowed. Please provide a real keyword.');
    }
  }
  
  // Basic keyword validation
  if (value.length < 2 || value.length > 100) {
    throw new Error('Keyword must be between 2 and 100 characters');
  }
  
  return true;
};

/**
 * Website URL validation
 */
const validateWebsiteURL = (value) => {
  if (!value) return true; // Optional field
  
  if (!validator.isURL(value, {
    protocols: ['http', 'https'],
    require_protocol: true,
    require_valid_protocol: true,
    allow_underscores: false
  })) {
    throw new Error('Please provide a valid website URL');
  }
  
  // Reject demo/test URLs
  const demoPatterns = [
    /example\.com/i,
    /test\.com/i,
    /demo\.com/i,
    /localhost/i,
    /127\.0\.0\.1/i,
    /placeholder\.com/i
  ];
  
  for (const pattern of demoPatterns) {
    if (pattern.test(value)) {
      throw new Error('Demo or test URLs are not allowed. Please provide a real website URL.');
    }
  }
  
  return true;
};

/**
 * Location validation
 */
const validateLocation = (value) => {
  if (!value) return true; // Optional field
  
  if (typeof value !== 'string' || value.length < 2 || value.length > 100) {
    throw new Error('Location must be between 2 and 100 characters');
  }
  
  // Reject demo locations
  const demoPatterns = [
    /example\s+city/i,
    /test\s+location/i,
    /demo\s+place/i,
    /placeholder/i,
    /sample\s+city/i
  ];
  
  for (const pattern of demoPatterns) {
    if (pattern.test(value)) {
      throw new Error('Demo or placeholder locations are not allowed. Please provide a real location.');
    }
  }
  
  return true;
};

/**
 * Content validation for AI-generated text
 */
const validateContent = (value) => {
  if (!value || typeof value !== 'string') {
    throw new Error('Content must be a non-empty string');
  }
  
  // Check minimum content length
  if (value.length < 100) {
    throw new Error('Content must be at least 100 characters long');
  }
  
  // Check maximum content length (10MB)
  if (value.length > 10 * 1024 * 1024) {
    throw new Error('Content exceeds maximum size limit');
  }
  
  return true;
};

/**
 * Validation chains for different endpoints
 */

// User registration validation
export const validateUserRegistration = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  body('full_name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Full name must be between 2 and 100 characters')
    .matches(/^[a-zA-Z\s'-]+$/)
    .withMessage('Full name can only contain letters, spaces, hyphens, and apostrophes'),
  handleValidationErrors
];

// Project creation validation
export const validateProjectCreation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Project name must be between 2 and 100 characters')
    .customSanitizer(sanitizeHTML),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description must not exceed 500 characters')
    .customSanitizer(sanitizeHTML),
  body('website_url')
    .optional()
    .custom(validateWebsiteURL),
  body('target_keywords')
    .optional()
    .isArray()
    .withMessage('Target keywords must be an array'),
  body('target_keywords.*')
    .optional()
    .custom(validateKeyword),
  body('location')
    .optional()
    .custom(validateLocation),
  body('industry')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Industry must not exceed 100 characters')
    .customSanitizer(sanitizeHTML),
  handleValidationErrors
];

// Content generation validation
export const validateContentGeneration = [
  body('target_keyword')
    .custom(validateKeyword),
  body('location')
    .optional()
    .custom(validateLocation),
  body('content_type')
    .optional()
    .isIn(['blog_post', 'product_description', 'landing_page', 'meta_description'])
    .withMessage('Invalid content type'),
  body('word_count')
    .optional()
    .isInt({ min: 100, max: 5000 })
    .withMessage('Word count must be between 100 and 5000'),
  body('tone')
    .optional()
    .isIn(['professional', 'casual', 'technical', 'friendly', 'persuasive'])
    .withMessage('Invalid tone specified'),
  body('competitor_urls')
    .optional()
    .isArray()
    .withMessage('Competitor URLs must be an array'),
  body('competitor_urls.*')
    .optional()
    .custom(validateWebsiteURL),
  handleValidationErrors
];

// Content update validation
export const validateContentUpdate = [
  body('title')
    .optional()
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Title must be between 5 and 200 characters')
    .customSanitizer(sanitizeHTML),
  body('content')
    .optional()
    .custom(validateContent)
    .customSanitizer(sanitizeHTML),
  body('meta_description')
    .optional()
    .trim()
    .isLength({ max: 160 })
    .withMessage('Meta description must not exceed 160 characters')
    .customSanitizer(sanitizeHTML),
  body('status')
    .optional()
    .isIn(['draft', 'published', 'archived'])
    .withMessage('Invalid status'),
  handleValidationErrors
];

// Bulk operation validation
export const validateBulkOperation = [
  body('operation_type')
    .isIn(['bulk_content_generation', 'bulk_analysis'])
    .withMessage('Invalid operation type'),
  body('keywords')
    .isArray({ min: 1, max: 100 })
    .withMessage('Keywords array must contain 1-100 items'),
  body('keywords.*')
    .custom(validateKeyword),
  body('settings')
    .optional()
    .isObject()
    .withMessage('Settings must be an object'),
  body('settings.word_count')
    .optional()
    .isInt({ min: 100, max: 5000 })
    .withMessage('Word count must be between 100 and 5000'),
  body('settings.location')
    .optional()
    .custom(validateLocation),
  handleValidationErrors
];

// Search and filter validation
export const validateSearchAndFilter = [
  query('search')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Search term must not exceed 100 characters')
    .customSanitizer(sanitizeHTML),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('sort_by')
    .optional()
    .isIn(['created_at', 'updated_at', 'title', 'word_count', 'seo_score'])
    .withMessage('Invalid sort field'),
  query('sort_order')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),
  handleValidationErrors
];

// ID parameter validation
export const validateIdParam = [
  param('id')
    .isUUID()
    .withMessage('Invalid ID format'),
  handleValidationErrors
];

/**
 * Comprehensive input sanitization middleware
 */
export const sanitizeInputs = (req, res, next) => {
  try {
    // Sanitize request body
    if (req.body && typeof req.body === 'object') {
      req.body = sanitizeObject(req.body);
    }
    
    // Sanitize query parameters
    if (req.query && typeof req.query === 'object') {
      req.query = sanitizeObject(req.query);
    }
    
    next();
  } catch (error) {
    console.error('Input sanitization error:', error);
    return res.status(400).json({
      error: 'Input sanitization failed',
      code: 'SANITIZATION_ERROR'
    });
  }
};

/**
 * Recursively sanitize object properties
 */
const sanitizeObject = (obj) => {
  const sanitized = {};
  
  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'string') {
      sanitized[key] = sanitizeHTML(value);
    } else if (Array.isArray(value)) {
      sanitized[key] = value.map(item => 
        typeof item === 'string' ? sanitizeHTML(item) : item
      );
    } else if (value && typeof value === 'object') {
      sanitized[key] = sanitizeObject(value);
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized;
};

export default {
  handleValidationErrors,
  sanitizeHTML,
  validateUserRegistration,
  validateProjectCreation,
  validateContentGeneration,
  validateContentUpdate,
  validateBulkOperation,
  validateSearchAndFilter,
  validateIdParam,
  sanitizeInputs
};