/**
 * Real-time Provider
 * Context provider for real-time features and WebSocket connection management
 */

'use client'

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { useRealtimeConnection } from '@/lib/realtime/realtime-hooks'
import { wsClient, REALTIME_EVENTS } from '@/lib/realtime/websocket-client'
import { useNotifications } from '@/components/Notifications'
import LoadingSpinner from '@/components/UI/LoadingSpinner'
import Button from '@/components/UI/Button'
import Card from '@/components/UI/Card'
import Badge from '@/components/UI/Badge'
import {
  WifiIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  SignalIcon,
  BellIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline'

interface RealtimeContextType {
  isConnected: boolean
  reconnectAttempts: number
  error: Error | null
  connectionState: any
  onlineUsers: Map<string, any>
  notifications: any[]
  unreadNotifications: number
  reconnect: () => void
  sendNotification: (userId: string, message: string, type?: string) => void
  markNotificationAsRead: (notificationId: string) => void
}

const RealtimeContext = createContext<RealtimeContextType | undefined>(undefined)

export const useRealtime = () => {
  const context = useContext(RealtimeContext)
  if (!context) {
    throw new Error('useRealtime must be used within a RealtimeProvider')
  }
  return context
}

interface RealtimeProviderProps {
  children: ReactNode
  userId?: string
  enableConnectionStatus?: boolean
  enableNotifications?: boolean
  enablePresence?: boolean
  maxReconnectAttempts?: number
  reconnectInterval?: number
}

export default function RealtimeProvider({
  children,
  userId,
  enableConnectionStatus = true,
  enableNotifications = true,
  enablePresence = true,
  maxReconnectAttempts = 10,
  reconnectInterval = 5000
}: RealtimeProviderProps) {
  const [onlineUsers, setOnlineUsers] = useState<Map<string, any>>(new Map())
  const [notifications, setNotifications] = useState<any[]>([])
  const [unreadNotifications, setUnreadNotifications] = useState(0)
  const [showConnectionStatus, setShowConnectionStatus] = useState(false)
  
  const { success: notifySuccess, error: notifyError } = useNotifications()
  
  const {
    isConnected,
    reconnectAttempts,
    error,
    connectionState,
    reconnect
  } = useRealtimeConnection(userId)

  // Handle connection status changes
  useEffect(() => {
    if (enableConnectionStatus) {
      if (!isConnected && reconnectAttempts > 0) {
        setShowConnectionStatus(true)
      } else if (isConnected) {
        setShowConnectionStatus(false)
      }
    }
  }, [isConnected, reconnectAttempts, enableConnectionStatus])

  // Handle presence updates
  useEffect(() => {
    if (!enablePresence) return

    const handleUserOnline = (data: any) => {
      setOnlineUsers(prev => new Map(prev).set(data.userId, {
        ...data,
        status: 'online',
        lastSeen: Date.now()
      }))
    }

    const handleUserOffline = (data: any) => {
      setOnlineUsers(prev => {
        const updated = new Map(prev)
        updated.delete(data.userId)
        return updated
      })
    }

    const onlineSubscription = wsClient.subscribe(
      REALTIME_EVENTS.TEAM_MEMBER_ONLINE,
      handleUserOnline
    )
    
    const offlineSubscription = wsClient.subscribe(
      REALTIME_EVENTS.TEAM_MEMBER_OFFLINE,
      handleUserOffline
    )

    return () => {
      wsClient.unsubscribe(onlineSubscription)
      wsClient.unsubscribe(offlineSubscription)
    }
  }, [enablePresence])

  // Handle notifications
  useEffect(() => {
    if (!enableNotifications) return

    const handleNotificationCreated = (data: any) => {
      setNotifications(prev => [data, ...prev])
      setUnreadNotifications(prev => prev + 1)
      
      // Show system notification if supported
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(data.title || 'New Notification', {
          body: data.message,
          icon: '/favicon.ico',
          badge: '/favicon.ico'
        })
      }
    }

    const handleNotificationRead = (data: any) => {
      setNotifications(prev => 
        prev.map(notif => 
          notif.id === data.notificationId 
            ? { ...notif, read: true } 
            : notif
        )
      )
      setUnreadNotifications(prev => Math.max(0, prev - 1))
    }

    const notificationSubscription = wsClient.subscribe(
      REALTIME_EVENTS.NOTIFICATION_CREATED,
      handleNotificationCreated,
      userId ? { userId } : undefined
    )
    
    const readSubscription = wsClient.subscribe(
      REALTIME_EVENTS.NOTIFICATION_READ,
      handleNotificationRead,
      userId ? { userId } : undefined
    )

    return () => {
      wsClient.unsubscribe(notificationSubscription)
      wsClient.unsubscribe(readSubscription)
    }
  }, [enableNotifications, userId])

  // Request notification permission
  useEffect(() => {
    if (enableNotifications && 'Notification' in window) {
      if (Notification.permission === 'default') {
        Notification.requestPermission()
      }
    }
  }, [enableNotifications])

  const sendNotification = (userId: string, message: string, type: string = 'info') => {
    wsClient.send({
      type: REALTIME_EVENTS.NOTIFICATION_CREATED,
      payload: {
        userId,
        message,
        type,
        timestamp: Date.now()
      }
    })
  }

  const markNotificationAsRead = (notificationId: string) => {
    wsClient.send({
      type: REALTIME_EVENTS.NOTIFICATION_READ,
      payload: {
        notificationId,
        userId,
        timestamp: Date.now()
      }
    })
  }

  const contextValue: RealtimeContextType = {
    isConnected,
    reconnectAttempts,
    error,
    connectionState,
    onlineUsers,
    notifications,
    unreadNotifications,
    reconnect,
    sendNotification,
    markNotificationAsRead
  }

  return (
    <RealtimeContext.Provider value={contextValue}>
      {children}
      
      {/* Connection Status Overlay */}
      {showConnectionStatus && (
        <ConnectionStatusOverlay
          isConnected={isConnected}
          reconnectAttempts={reconnectAttempts}
          error={error}
          onReconnect={reconnect}
          onDismiss={() => setShowConnectionStatus(false)}
        />
      )}
    </RealtimeContext.Provider>
  )
}

// Connection Status Overlay Component
const ConnectionStatusOverlay = ({
  isConnected,
  reconnectAttempts,
  error,
  onReconnect,
  onDismiss
}: {
  isConnected: boolean
  reconnectAttempts: number
  error: Error | null
  onReconnect: () => void
  onDismiss: () => void
}) => {
  const getStatusIcon = () => {
    if (isConnected) {
      return <CheckCircleIcon className="h-5 w-5 text-green-500" />
    } else if (reconnectAttempts > 0) {
      return <ArrowPathIcon className="h-5 w-5 text-yellow-500 animate-spin" />
    } else {
      return <XCircleIcon className="h-5 w-5 text-red-500" />
    }
  }

  const getStatusMessage = () => {
    if (isConnected) {
      return 'Connected to real-time updates'
    } else if (reconnectAttempts > 0) {
      return `Reconnecting... (attempt ${reconnectAttempts})`
    } else {
      return 'Disconnected from real-time updates'
    }
  }

  const getStatusColor = () => {
    if (isConnected) {
      return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
    } else if (reconnectAttempts > 0) {
      return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20'
    } else {
      return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20'
    }
  }

  return (
    <div className="fixed top-4 right-4 z-50 max-w-sm">
      <Card className={`p-4 ${getStatusColor()} shadow-lg`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getStatusIcon()}
            <div>
              <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {getStatusMessage()}
              </div>
              {error && (
                <div className="text-xs text-red-600 dark:text-red-400 mt-1">
                  {error.message}
                </div>
              )}
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {!isConnected && (
              <Button
                variant="outline"
                size="sm"
                onClick={onReconnect}
                disabled={reconnectAttempts > 0}
              >
                <ArrowPathIcon className="h-4 w-4 mr-1" />
                Retry
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={onDismiss}
            >
              ×
            </Button>
          </div>
        </div>
      </Card>
    </div>
  )
}

// Connection Status Indicator Component
export const ConnectionStatusIndicator = ({ className = '' }: { className?: string }) => {
  const { isConnected, reconnectAttempts, connectionState } = useRealtime()

  const getStatusColor = () => {
    if (isConnected) return 'text-green-500'
    if (reconnectAttempts > 0) return 'text-yellow-500'
    return 'text-red-500'
  }

  const getStatusIcon = () => {
    if (isConnected) return <WifiIcon className="h-4 w-4" />
    if (reconnectAttempts > 0) return <ArrowPathIcon className="h-4 w-4 animate-spin" />
    return <ExclamationTriangleIcon className="h-4 w-4" />
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className={getStatusColor()}>
        {getStatusIcon()}
      </div>
      <div className="text-sm text-gray-600 dark:text-gray-400">
        {isConnected ? 'Connected' : reconnectAttempts > 0 ? 'Connecting...' : 'Disconnected'}
      </div>
    </div>
  )
}

// Online Users Indicator Component
export const OnlineUsersIndicator = ({ className = '' }: { className?: string }) => {
  const { onlineUsers } = useRealtime()
  const onlineCount = onlineUsers.size

  if (onlineCount === 0) return null

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <UserGroupIcon className="h-4 w-4 text-green-500" />
      <span className="text-sm text-gray-600 dark:text-gray-400">
        {onlineCount} online
      </span>
      <Badge variant="success" size="sm">
        {onlineCount}
      </Badge>
    </div>
  )
}

// Notifications Indicator Component
export const NotificationsIndicator = ({ 
  className = '',
  onClick
}: { 
  className?: string
  onClick?: () => void
}) => {
  const { notifications, unreadNotifications } = useRealtime()

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={onClick}
        className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
      >
        <BellIcon className="h-5 w-5 text-gray-600 dark:text-gray-400" />
        {unreadNotifications > 0 && (
          <Badge variant="error" size="sm">
            {unreadNotifications > 99 ? '99+' : unreadNotifications}
          </Badge>
        )}
      </button>
      
      {unreadNotifications > 0 && (
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse" />
      )}
    </div>
  )
}

// Real-time System Status Component
export const RealtimeSystemStatus = ({ className = '' }: { className?: string }) => {
  const { isConnected, connectionState, error } = useRealtime()

  return (
    <div className={`flex items-center space-x-4 ${className}`}>
      <div className="flex items-center space-x-2">
        <SignalIcon className={`h-4 w-4 ${isConnected ? 'text-green-500' : 'text-red-500'}`} />
        <span className="text-sm text-gray-600 dark:text-gray-400">
          Real-time: {isConnected ? 'Active' : 'Inactive'}
        </span>
      </div>
      
      <div className="flex items-center space-x-2">
        <ClockIcon className="h-4 w-4 text-gray-400" />
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {connectionState.subscriptions} subscriptions
        </span>
      </div>
      
      {connectionState.queuedMessages > 0 && (
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse" />
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {connectionState.queuedMessages} queued
          </span>
        </div>
      )}
    </div>
  )
}

// Typing Indicator Component
export const TypingIndicator = ({ 
  typingUsers,
  className = ''
}: { 
  typingUsers: any[]
  className?: string
}) => {
  if (typingUsers.length === 0) return null

  const formatTypingMessage = () => {
    if (typingUsers.length === 1) {
      return `${typingUsers[0].name} is typing...`
    } else if (typingUsers.length === 2) {
      return `${typingUsers[0].name} and ${typingUsers[1].name} are typing...`
    } else {
      return `${typingUsers[0].name} and ${typingUsers.length - 1} others are typing...`
    }
  }

  return (
    <div className={`flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 ${className}`}>
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" />
        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
      </div>
      <span>{formatTypingMessage()}</span>
    </div>
  )
}

// Collaboration Cursors Component
export const CollaborationCursors = ({ 
  cursors,
  className = ''
}: { 
  cursors: any[]
  className?: string
}) => {
  return (
    <div className={`relative ${className}`}>
      {cursors.map((cursor) => (
        <div
          key={cursor.userId}
          className="absolute pointer-events-none z-50"
          style={{
            left: cursor.position.x,
            top: cursor.position.y,
            transform: 'translate(-50%, -50%)'
          }}
        >
          <div className="flex items-center space-x-1">
            <div 
              className="w-3 h-3 rounded-full border-2 border-white shadow-md"
              style={{ backgroundColor: cursor.color || '#3B82F6' }}
            />
            <div className="bg-black text-white text-xs px-2 py-1 rounded shadow-lg">
              {cursor.name}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}