/**
 * Global API Test Setup
 * 
 * Global setup for API tests including database preparation,
 * external service mocking, and test environment initialization.
 */

const { createClient } = require('@supabase/supabase-js');
const { execSync } = require('child_process');

module.exports = async () => {
  console.log('🚀 Setting up API test environment...');
  
  try {
    // Set test environment
    process.env.NODE_ENV = 'test';
    
    // Initialize test database
    await initializeTestDatabase();
    
    // Setup external service mocks
    await setupExternalMocks();
    
    // Warm up test services
    await warmupServices();
    
    console.log('✅ API test environment setup complete');
    
  } catch (error) {
    console.error('❌ API test environment setup failed:', error);
    throw error;
  }
};

/**
 * Initialize test database
 */
async function initializeTestDatabase() {
  console.log('📊 Initializing test database...');
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL_TEST || process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_KEY_TEST || process.env.SUPABASE_SERVICE_KEY;
  
  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Missing Supabase configuration for API testing');
  }
  
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  try {
    // Test database connection
    const { data, error } = await supabase.from('users').select('count').limit(1);
    
    if (error && !error.message.includes('does not exist')) {
      console.error('Database connection test failed:', error);
      throw error;
    }
    
    // Create test tables if they don't exist
    await createTestTables(supabase);
    
    console.log('✅ Test database initialized');
    
  } catch (error) {
    console.error('❌ Test database initialization failed:', error);
    throw error;
  }
}

/**
 * Create test tables
 */
async function createTestTables(supabase) {
  console.log('📋 Creating test tables...');
  
  try {
    // Note: In a real application, you would run migrations here
    // For now, we'll assume tables exist from the main application
    
    // Verify essential tables exist
    const tables = ['users', 'projects', 'content', 'analytics_events', 'reports'];
    
    for (const table of tables) {
      const { data, error } = await supabase.from(table).select('*').limit(1);
      
      if (error && error.message.includes('does not exist')) {
        console.warn(`Table ${table} does not exist - this may cause test failures`);
      }
    }
    
    console.log('✅ Test tables verified');
    
  } catch (error) {
    console.error('❌ Test tables creation failed:', error);
    throw error;
  }
}

/**
 * Setup external service mocks
 */
async function setupExternalMocks() {
  console.log('🎭 Setting up external service mocks...');
  
  try {
    // Mock OpenAI API
    process.env.OPENAI_API_KEY = process.env.OPENAI_API_KEY_TEST || 'test-key';
    
    // Mock email service
    process.env.SENDGRID_API_KEY = process.env.SENDGRID_API_KEY_TEST || 'test-key';
    
    // Mock analytics service
    process.env.GOOGLE_ANALYTICS_ID = process.env.GOOGLE_ANALYTICS_ID_TEST || 'test-id';
    
    console.log('✅ External service mocks configured');
    
  } catch (error) {
    console.error('❌ External service mocks setup failed:', error);
    throw error;
  }
}

/**
 * Warm up test services
 */
async function warmupServices() {
  console.log('🔥 Warming up test services...');
  
  try {
    // Warm up Next.js API routes (if needed)
    // This could involve making a test request to ensure the server is ready
    
    console.log('✅ Test services warmed up');
    
  } catch (error) {
    console.error('❌ Test services warmup failed:', error);
    // Don't fail the entire setup for warmup failures
  }
}