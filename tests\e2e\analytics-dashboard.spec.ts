/**
 * Analytics Dashboard E2E Tests
 * 
 * Tests for analytics dashboard interactions, data visualization,
 * real-time updates, and user interface responsiveness.
 */

import { test, expect, Page } from '@playwright/test';
import { loginAsUser, existingUser } from './auth.spec';

test.describe('Analytics Dashboard', () => {
  
  test.beforeEach(async ({ page }) => {
    // Login as existing user
    await loginAsUser(page, existingUser.email, existingUser.password);
    
    // Navigate to analytics dashboard
    await page.click('[data-testid="analytics-nav"]');
    await expect(page).toHaveURL('**/dashboard/analytics');
  });

  test.describe('Dashboard Overview', () => {
    
    test('should load analytics dashboard successfully', async ({ page }) => {
      // Should show main dashboard elements
      await expect(page.locator('[data-testid="analytics-dashboard"]')).toBeVisible();
      await expect(page.locator('[data-testid="dashboard-header"]')).toBeVisible();
      await expect(page.locator('[data-testid="dashboard-tabs"]')).toBeVisible();
      await expect(page.locator('[data-testid="dashboard-content"]')).toBeVisible();
      
      // Should show loading states initially
      await expect(page.locator('[data-testid="dashboard-skeleton"]')).toBeVisible();
      
      // Should load actual content
      await expect(page.locator('[data-testid="metrics-grid"]')).toBeVisible({ timeout: 30000 });
    });

    test('should display key performance metrics', async ({ page }) => {
      // Wait for data to load
      await expect(page.locator('[data-testid="metrics-grid"]')).toBeVisible({ timeout: 30000 });
      
      // Should show main KPI cards
      await expect(page.locator('[data-testid="metric-card-total-users"]')).toBeVisible();
      await expect(page.locator('[data-testid="metric-card-page-views"]')).toBeVisible();
      await expect(page.locator('[data-testid="metric-card-conversion-rate"]')).toBeVisible();
      await expect(page.locator('[data-testid="metric-card-bounce-rate"]')).toBeVisible();
      
      // Each metric card should show value and change indicator
      const metricCards = page.locator('[data-testid^="metric-card-"]');
      const cardCount = await metricCards.count();
      
      for (let i = 0; i < cardCount; i++) {
        const card = metricCards.nth(i);
        await expect(card.locator('.metric-value')).toBeVisible();
        await expect(card.locator('.metric-title')).toBeVisible();
      }
    });

    test('should show charts and visualizations', async ({ page }) => {
      // Wait for charts to load
      await expect(page.locator('[data-testid="charts-grid"]')).toBeVisible({ timeout: 30000 });
      
      // Should show user growth chart
      await expect(page.locator('[data-testid="chart-user-growth"]')).toBeVisible();
      
      // Should show content performance chart
      await expect(page.locator('[data-testid="chart-content-performance"]')).toBeVisible();
      
      // Charts should be rendered
      const charts = page.locator('[data-testid^="chart-"]');
      const chartCount = await charts.count();
      
      for (let i = 0; i < chartCount; i++) {
        const chart = charts.nth(i);
        await expect(chart.locator('.chart-canvas')).toBeVisible();
      }
    });

    test('should display insights section', async ({ page }) => {
      // Wait for insights to load
      await expect(page.locator('[data-testid="insights-section"]')).toBeVisible({ timeout: 30000 });
      
      // Should show insights grid
      await expect(page.locator('[data-testid="insights-grid"]')).toBeVisible();
      
      // Should have at least one insight
      await expect(page.locator('[data-testid="insight-card"]')).toHaveCount({ min: 1 });
      
      // Each insight should have required elements
      const insights = page.locator('[data-testid="insight-card"]');
      const insightCount = await insights.count();
      
      for (let i = 0; i < insightCount; i++) {
        const insight = insights.nth(i);
        await expect(insight.locator('.insight-title')).toBeVisible();
        await expect(insight.locator('.insight-description')).toBeVisible();
        await expect(insight.locator('.insight-recommendation')).toBeVisible();
        await expect(insight.locator('.insight-priority')).toBeVisible();
      }
    });
  });

  test.describe('Dashboard Navigation', () => {
    
    test('should navigate between dashboard tabs', async ({ page }) => {
      // Should start on overview tab
      await expect(page.locator('[data-testid="tab-overview"]')).toHaveClass(/active/);
      
      // Navigate to users tab
      await page.click('[data-testid="tab-users"]');
      await expect(page.locator('[data-testid="tab-users"]')).toHaveClass(/active/);
      await expect(page.locator('[data-testid="users-tab"]')).toBeVisible();
      
      // Navigate to content tab
      await page.click('[data-testid="tab-content"]');
      await expect(page.locator('[data-testid="tab-content"]')).toHaveClass(/active/);
      await expect(page.locator('[data-testid="content-tab"]')).toBeVisible();
      
      // Navigate to business tab
      await page.click('[data-testid="tab-business"]');
      await expect(page.locator('[data-testid="tab-business"]')).toHaveClass(/active/);
      await expect(page.locator('[data-testid="business-tab"]')).toBeVisible();
      
      // Navigate back to overview
      await page.click('[data-testid="tab-overview"]');
      await expect(page.locator('[data-testid="overview-tab"]')).toBeVisible();
    });

    test('should maintain tab state on page refresh', async ({ page }) => {
      // Navigate to content tab
      await page.click('[data-testid="tab-content"]');
      await expect(page.locator('[data-testid="content-tab"]')).toBeVisible();
      
      // Refresh page
      await page.reload();
      
      // Should still be on content tab
      await expect(page.locator('[data-testid="tab-content"]')).toHaveClass(/active/);
      await expect(page.locator('[data-testid="content-tab"]')).toBeVisible();
    });
  });

  test.describe('Time Range Controls', () => {
    
    test('should change time range and update data', async ({ page }) => {
      // Wait for initial data load
      await expect(page.locator('[data-testid="metrics-grid"]')).toBeVisible({ timeout: 30000 });
      
      // Get initial metric value
      const initialValue = await page.locator('[data-testid="metric-card-total-users"] .metric-value').textContent();
      
      // Change time range to last 7 days
      await page.selectOption('[data-testid="time-range-selector"]', 'week');
      
      // Should show loading state
      await expect(page.locator('[data-testid="dashboard-skeleton"]')).toBeVisible();
      
      // Data should update
      await expect(page.locator('[data-testid="metrics-grid"]')).toBeVisible({ timeout: 30000 });
      
      // Metric value might be different
      const newValue = await page.locator('[data-testid="metric-card-total-users"] .metric-value').textContent();
      // Note: Values might be the same in test data, just verify the request was made
    });

    test('should support all time range options', async ({ page }) => {
      const timeRanges = ['day', 'week', 'month', 'quarter', 'year'];
      
      for (const range of timeRanges) {
        // Select time range
        await page.selectOption('[data-testid="time-range-selector"]', range);
        
        // Should update data
        await expect(page.locator('[data-testid="dashboard-skeleton"]')).toBeVisible();
        await expect(page.locator('[data-testid="metrics-grid"]')).toBeVisible({ timeout: 30000 });
        
        // Should show appropriate time range in header
        await expect(page.locator('[data-testid="dashboard-header"]')).toContainText(range === 'day' ? '24 Hours' : 
                                                                                    range === 'week' ? '7 Days' :
                                                                                    range === 'month' ? '30 Days' :
                                                                                    range === 'quarter' ? '90 Days' : '365 Days');
      }
    });
  });

  test.describe('Real-time Updates', () => {
    
    test('should refresh data manually', async ({ page }) => {
      // Wait for initial load
      await expect(page.locator('[data-testid="metrics-grid"]')).toBeVisible({ timeout: 30000 });
      
      // Click refresh button
      await page.click('[data-testid="refresh-button"]');
      
      // Should show loading state
      await expect(page.locator('[data-testid="refresh-button"] [data-testid="loading-spinner"]')).toBeVisible();
      
      // Should complete refresh
      await expect(page.locator('[data-testid="refresh-button"]')).not.toHaveClass(/loading/);
    });

    test('should disable refresh button during loading', async ({ page }) => {
      // Click refresh
      await page.click('[data-testid="refresh-button"]');
      
      // Button should be disabled during loading
      await expect(page.locator('[data-testid="refresh-button"]')).toBeDisabled();
      
      // Should re-enable after loading
      await expect(page.locator('[data-testid="refresh-button"]')).toBeEnabled({ timeout: 30000 });
    });
  });

  test.describe('Users Tab', () => {
    
    test('should display user behavior metrics', async ({ page }) => {
      // Navigate to users tab
      await page.click('[data-testid="tab-users"]');
      await expect(page.locator('[data-testid="users-tab"]')).toBeVisible();
      
      // Should show user metrics grid
      await expect(page.locator('[data-testid="user-metrics-grid"]')).toBeVisible();
      
      // Should show individual user metric cards
      await expect(page.locator('[data-testid="user-metric-card"]')).toHaveCount({ min: 1 });
      
      // Each user card should show key metrics
      const userCards = page.locator('[data-testid="user-metric-card"]');
      const cardCount = await userCards.count();
      
      for (let i = 0; i < cardCount; i++) {
        const card = userCards.nth(i);
        await expect(card.locator('.user-id')).toBeVisible();
        await expect(card.locator('.total-visits')).toBeVisible();
        await expect(card.locator('.total-time')).toBeVisible();
        await expect(card.locator('.conversion-rate')).toBeVisible();
      }
    });

    test('should show device type distribution', async ({ page }) => {
      await page.click('[data-testid="tab-users"]');
      
      // Should show device types chart
      await expect(page.locator('[data-testid="chart-device-types"]')).toBeVisible();
      
      // Chart should be rendered
      await expect(page.locator('[data-testid="chart-device-types"] .chart-canvas')).toBeVisible();
    });

    test('should show traffic sources chart', async ({ page }) => {
      await page.click('[data-testid="tab-users"]');
      
      // Should show traffic sources chart
      await expect(page.locator('[data-testid="chart-traffic-sources"]')).toBeVisible();
      
      // Chart should be rendered
      await expect(page.locator('[data-testid="chart-traffic-sources"] .chart-canvas')).toBeVisible();
    });
  });

  test.describe('Content Tab', () => {
    
    test('should display content performance metrics', async ({ page }) => {
      // Navigate to content tab
      await page.click('[data-testid="tab-content"]');
      await expect(page.locator('[data-testid="content-tab"]')).toBeVisible();
      
      // Should show content metrics grid
      await expect(page.locator('[data-testid="content-metrics-grid"]')).toBeVisible();
      
      // Should show content metric cards
      await expect(page.locator('[data-testid="content-metric-card"]')).toHaveCount({ min: 1 });
      
      // Each content card should show key metrics
      const contentCards = page.locator('[data-testid="content-metric-card"]');
      const cardCount = await contentCards.count();
      
      for (let i = 0; i < cardCount; i++) {
        const card = contentCards.nth(i);
        await expect(card.locator('.content-title')).toBeVisible();
        await expect(card.locator('.views')).toBeVisible();
        await expect(card.locator('.average-time')).toBeVisible();
        await expect(card.locator('.conversions')).toBeVisible();
      }
    });

    test('should show SEO performance chart', async ({ page }) => {
      await page.click('[data-testid="tab-content"]');
      
      // Should show SEO performance chart
      await expect(page.locator('[data-testid="chart-seo-performance"]')).toBeVisible();
      
      // Chart should be rendered
      await expect(page.locator('[data-testid="chart-seo-performance"] .chart-canvas')).toBeVisible();
    });
  });

  test.describe('Business Tab', () => {
    
    test('should display business metrics', async ({ page }) => {
      // Navigate to business tab
      await page.click('[data-testid="tab-business"]');
      await expect(page.locator('[data-testid="business-tab"]')).toBeVisible();
      
      // Should show business metrics grid
      await expect(page.locator('[data-testid="business-metrics-grid"]')).toBeVisible();
      
      // Should show key business metrics
      await expect(page.locator('[data-testid="metric-card-total-revenue"]')).toBeVisible();
      await expect(page.locator('[data-testid="metric-card-active-users"]')).toBeVisible();
      await expect(page.locator('[data-testid="metric-card-conversion-rate"]')).toBeVisible();
      await expect(page.locator('[data-testid="metric-card-customer-churn"]')).toBeVisible();
    });

    test('should show revenue breakdown chart', async ({ page }) => {
      await page.click('[data-testid="tab-business"]');
      
      // Should show revenue breakdown chart
      await expect(page.locator('[data-testid="chart-revenue-breakdown"]')).toBeVisible();
      
      // Chart should be rendered
      await expect(page.locator('[data-testid="chart-revenue-breakdown"] .chart-canvas')).toBeVisible();
    });

    test('should show conversion funnel', async ({ page }) => {
      await page.click('[data-testid="tab-business"]');
      
      // Should show conversion funnel chart
      await expect(page.locator('[data-testid="chart-conversion-funnel"]')).toBeVisible();
      
      // Should show funnel stages
      await expect(page.locator('[data-testid="funnel-stage"]')).toHaveCount({ min: 3 });
      
      // Each stage should show stage name and value
      const stages = page.locator('[data-testid="funnel-stage"]');
      const stageCount = await stages.count();
      
      for (let i = 0; i < stageCount; i++) {
        const stage = stages.nth(i);
        await expect(stage.locator('.stage-label')).toBeVisible();
        await expect(stage.locator('.stage-value')).toBeVisible();
      }
    });
  });

  test.describe('Chart Interactions', () => {
    
    test('should show chart tooltips on hover', async ({ page }) => {
      // Wait for chart to load
      await expect(page.locator('[data-testid="chart-user-growth"]')).toBeVisible({ timeout: 30000 });
      
      // Hover over chart
      const chart = page.locator('[data-testid="chart-user-growth"] .chart-canvas');
      await chart.hover();
      
      // Should show tooltip (implementation depends on chart library)
      // This test would need to be adapted based on actual chart implementation
    });

    test('should support chart legend interactions', async ({ page }) => {
      // Navigate to content tab for multi-series chart
      await page.click('[data-testid="tab-content"]');
      
      // Wait for SEO chart to load
      await expect(page.locator('[data-testid="chart-seo-performance"]')).toBeVisible({ timeout: 30000 });
      
      // Should show chart legend
      await expect(page.locator('[data-testid="chart-seo-performance"] .chart-legend')).toBeVisible();
    });
  });

  test.describe('Error Handling', () => {
    
    test('should handle API errors gracefully', async ({ page }) => {
      // Simulate network error by intercepting requests
      await page.route('**/api/analytics/**', route => {
        route.abort('failed');
      });
      
      // Refresh page to trigger error
      await page.reload();
      
      // Should show error message
      await expect(page.locator('[data-testid="analytics-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="analytics-error"]')).toContainText('Failed to load analytics data');
      
      // Should show retry button
      await expect(page.locator('[data-testid="retry-button"]')).toBeVisible();
    });

    test('should retry loading on error', async ({ page }) => {
      // Simulate initial error then success
      let requestCount = 0;
      await page.route('**/api/analytics/**', route => {
        requestCount++;
        if (requestCount === 1) {
          route.abort('failed');
        } else {
          route.continue();
        }
      });
      
      // Refresh page to trigger error
      await page.reload();
      
      // Should show error
      await expect(page.locator('[data-testid="analytics-error"]')).toBeVisible();
      
      // Click retry
      await page.click('[data-testid="retry-button"]');
      
      // Should load successfully
      await expect(page.locator('[data-testid="metrics-grid"]')).toBeVisible({ timeout: 30000 });
    });
  });

  test.describe('Performance and Responsiveness', () => {
    
    test('should load dashboard within acceptable time', async ({ page }) => {
      const startTime = Date.now();
      
      // Navigate to analytics
      await page.goto('/dashboard/analytics');
      
      // Wait for core content to load
      await expect(page.locator('[data-testid="metrics-grid"]')).toBeVisible({ timeout: 30000 });
      
      const loadTime = Date.now() - startTime;
      
      // Should load within 30 seconds
      expect(loadTime).toBeLessThan(30000);
    });

    test('should be responsive on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Dashboard should be usable on mobile
      await expect(page.locator('[data-testid="analytics-dashboard"]')).toBeVisible();
      
      // Metrics should stack vertically
      const metricsGrid = page.locator('[data-testid="metrics-grid"]');
      const gridBox = await metricsGrid.boundingBox();
      expect(gridBox?.width).toBeLessThanOrEqual(375);
      
      // Tabs should be scrollable or stacked
      await expect(page.locator('[data-testid="dashboard-tabs"]')).toBeVisible();
    });

    test('should handle large datasets efficiently', async ({ page }) => {
      // Change to yearly view (largest dataset)
      await page.selectOption('[data-testid="time-range-selector"]', 'year');
      
      // Should still load within reasonable time
      await expect(page.locator('[data-testid="metrics-grid"]')).toBeVisible({ timeout: 45000 });
      
      // Charts should render properly
      await expect(page.locator('[data-testid="chart-user-growth"] .chart-canvas')).toBeVisible();
    });
  });

  test.describe('Accessibility', () => {
    
    test('should be keyboard navigable', async ({ page }) => {
      // Use keyboard to navigate tabs
      await page.keyboard.press('Tab'); // Focus on first tab
      await page.keyboard.press('ArrowRight'); // Navigate to next tab
      await page.keyboard.press('Enter'); // Activate tab
      
      // Should navigate to users tab
      await expect(page.locator('[data-testid="tab-users"]')).toHaveClass(/active/);
    });

    test('should have proper ARIA labels', async ({ page }) => {
      // Check metric cards have proper labels
      const metricCards = page.locator('[data-testid^="metric-card-"]');
      const cardCount = await metricCards.count();
      
      for (let i = 0; i < cardCount; i++) {
        const card = metricCards.nth(i);
        const ariaLabel = await card.getAttribute('aria-label');
        expect(ariaLabel).toBeTruthy();
      }
    });

    test('should support screen readers', async ({ page }) => {
      // Check that important elements have proper roles
      await expect(page.locator('[data-testid="dashboard-tabs"]')).toHaveAttribute('role', 'tablist');
      await expect(page.locator('[data-testid="tab-overview"]')).toHaveAttribute('role', 'tab');
      
      // Check that content areas have proper labels
      await expect(page.locator('[data-testid="overview-tab"]')).toHaveAttribute('role', 'tabpanel');
    });
  });
});

// Helper function to wait for charts to load
async function waitForChartsToLoad(page: Page) {
  await expect(page.locator('[data-testid="chart-user-growth"] .chart-canvas')).toBeVisible({ timeout: 30000 });
  await expect(page.locator('[data-testid="chart-content-performance"] .chart-canvas')).toBeVisible({ timeout: 30000 });
}

// Export helper for use in other test files
export { waitForChartsToLoad };