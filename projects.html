<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Manage all your SEO content generation projects in one place with advanced filtering and analytics.">
    <title>Projects - SEO SAAS</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/data-visualization.css">
    <link rel="stylesheet" href="css/animations.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
</head>
<body class="dashboard-layout">
    <!-- Dashboard App Container -->
    <div class="dashboard-app">
        <!-- Global Top Navigation -->
        <header class="top-navigation">
            <div class="nav-left">
                <button class="sidebar-toggle" aria-label="Toggle sidebar" onclick="toggleSidebar()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
                <div class="brand-section">
                    <svg class="brand-logo w-8 h-8 text-primary-600" viewBox="0 0 40 40" fill="currentColor">
                        <path d="M20 5L5 15v10l15 10 15-10V15L20 5z"/>
                        <path d="M20 15l-10 6v8l10 6 10-6v-8l-10-6z" fill="white" opacity="0.5"/>
                    </svg>
                    <span class="brand-name">SEO SAAS</span>
                    <span class="environment-badge">Pro</span>
                </div>
            </div>
            
            <div class="nav-center">
                <div class="global-search">
                    <svg class="search-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <input type="search" placeholder="Search projects, keywords, content..." class="search-input" onkeyup="searchProjects(this.value)">
                </div>
            </div>
            
            <div class="nav-right">
                <button class="quick-action-btn" title="New Project" onclick="createNewProject()">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    <span>New</span>
                </button>
                
                <div class="notifications-dropdown">
                    <button class="notification-btn" title="Notifications">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5-5V9.5a6.5 6.5 0 10-13 0V12l-5 5h5"></path>
                        </svg>
                        <span class="notification-badge">3</span>
                    </button>
                </div>
                
                <div class="help-dropdown">
                    <button class="help-btn" title="Help & Support">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </button>
                </div>
                
                <div class="user-menu-dropdown">
                    <button class="user-menu-btn">
                        <div class="user-avatar">
                            <span>JD</span>
                        </div>
                        <div class="user-info">
                            <span class="user-name">John Doe</span>
                            <span class="user-plan">Professional</span>
                        </div>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <aside class="sidebar-navigation" id="sidebarNav">
            <nav class="nav-menu">
                <!-- Dashboard Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Overview</h3>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="dashboard.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z"></path>
                                </svg>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="analytics.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                <span>Analytics</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Content Creation Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Content Creation</h3>
                        <button class="section-toggle" aria-label="Toggle section" onclick="toggleSection(this)">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="content-creator.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                <span>Create Content</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="content-optimizer.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                <span>Optimize Content</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="bulk-generator.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                <span>Bulk Generator</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="content-editor.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                                </svg>
                                <span>Content Editor</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Research & Analysis Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Research & Analysis</h3>
                        <button class="section-toggle" aria-label="Toggle section" onclick="toggleSection(this)">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="keyword-research.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-3.586l4.293-4.293A6 6 0 0119 9z"></path>
                                </svg>
                                <span>Keyword Research</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="competitor-analysis.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                <span>Competitor Analysis</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="serp-analyzer.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                <span>SERP Analyzer</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="content-gaps.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                                </svg>
                                <span>Content Gaps</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Projects Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Projects</h3>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item active">
                            <a href="projects.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                <span>All Projects</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="templates.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <span>Templates</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Settings Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Settings</h3>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="account-settings.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <span>Account</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="billing.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                </svg>
                                <span>Billing</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="api-settings.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <span>API Settings</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Sidebar Footer -->
            <div class="sidebar-footer">
                <div class="usage-indicator">
                    <div class="usage-label">API Usage</div>
                    <div class="usage-bar">
                        <div class="usage-fill" style="width: 65%"></div>
                    </div>
                    <div class="usage-text">325 / 500 requests</div>
                </div>
                
                <button class="upgrade-btn" onclick="window.location.href='pricing.html'">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    <span>Upgrade Plan</span>
                </button>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="page-title">Projects</h1>
                        <p class="page-subtitle">Manage all your SEO content generation projects</p>
                    </div>
                    <div class="header-right">
                        <button class="btn btn-secondary" onclick="exportProjects()">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                            </svg>
                            Export
                        </button>
                        <button class="btn btn-primary" onclick="createNewProject()">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            New Project
                        </button>
                    </div>
                </div>
            </div>

            <!-- Projects Overview Cards -->
            <div class="projects-overview">
                <div class="overview-grid">
                    <div class="overview-card">
                        <div class="card-content">
                            <div class="card-number" data-count="24">24</div>
                            <div class="card-label">Total Projects</div>
                        </div>
                        <div class="card-icon">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </div>
                    </div>
                    
                    <div class="overview-card">
                        <div class="card-content">
                            <div class="card-number" data-count="18">18</div>
                            <div class="card-label">Active Projects</div>
                        </div>
                        <div class="card-icon">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                    </div>
                    
                    <div class="overview-card">
                        <div class="card-content">
                            <div class="card-number" data-count="92.4">92.4</div>
                            <div class="card-label">Avg. SEO Score</div>
                        </div>
                        <div class="card-icon">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                    
                    <div class="overview-card">
                        <div class="card-content">
                            <div class="card-number" data-count="6">6</div>
                            <div class="card-label">Completed Today</div>
                        </div>
                        <div class="card-icon">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters and Controls -->
            <div class="projects-controls">
                <div class="filters-section">
                    <div class="filter-group">
                        <label class="filter-label">Status</label>
                        <select class="form-select" onchange="filterProjects('status', this.value)">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="completed">Completed</option>
                            <option value="draft">Draft</option>
                            <option value="archived">Archived</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label">Industry</label>
                        <select class="form-select" onchange="filterProjects('industry', this.value)">
                            <option value="">All Industries</option>
                            <option value="marketing">Marketing</option>
                            <option value="ecommerce">E-commerce</option>
                            <option value="legal">Legal</option>
                            <option value="healthcare">Healthcare</option>
                            <option value="finance">Finance</option>
                            <option value="technology">Technology</option>
                            <option value="real-estate">Real Estate</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label">SEO Score</label>
                        <select class="form-select" onchange="filterProjects('score', this.value)">
                            <option value="">All Scores</option>
                            <option value="excellent">95-100</option>
                            <option value="very-good">90-94</option>
                            <option value="good">80-89</option>
                            <option value="needs-work">Below 80</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label">Sort By</label>
                        <select class="form-select" onchange="sortProjects(this.value)">
                            <option value="updated">Last Updated</option>
                            <option value="created">Date Created</option>
                            <option value="name">Project Name</option>
                            <option value="score">SEO Score</option>
                        </select>
                    </div>
                    
                    <button class="btn btn-ghost" onclick="clearFilters()">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Clear Filters
                    </button>
                </div>
                
                <div class="view-controls">
                    <div class="view-toggle">
                        <button class="view-btn active" onclick="setView('table')" data-view="table">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 6h18M3 14h18M3 18h18"></path>
                            </svg>
                        </button>
                        <button class="view-btn" onclick="setView('grid')" data-view="grid">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Projects Table View -->
            <div class="projects-table-container" id="tableView">
                <div class="table-wrapper">
                    <table class="projects-table">
                        <thead>
                            <tr>
                                <th class="checkbox-col">
                                    <label class="table-checkbox">
                                        <input type="checkbox" onchange="toggleSelectAll(this)">
                                        <span class="checkbox-indicator"></span>
                                    </label>
                                </th>
                                <th class="sortable" onclick="sortTable('name')">
                                    Project Name
                                    <svg class="sort-icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                    </svg>
                                </th>
                                <th class="sortable" onclick="sortTable('keyword')">
                                    Target Keyword
                                    <svg class="sort-icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                    </svg>
                                </th>
                                <th>Industry</th>
                                <th class="sortable" onclick="sortTable('score')">
                                    SEO Score
                                    <svg class="sort-icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                    </svg>
                                </th>
                                <th>Status</th>
                                <th class="sortable" onclick="sortTable('updated')">
                                    Last Updated
                                    <svg class="sort-icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                    </svg>
                                </th>
                                <th class="actions-col">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="projectsTableBody">
                            <!-- Projects will be dynamically inserted here -->
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="table-pagination">
                    <div class="pagination-info">
                        Showing <span id="showingFrom">1</span> to <span id="showingTo">10</span> of <span id="totalResults">24</span> projects
                    </div>
                    <div class="pagination-controls">
                        <button class="btn btn-ghost btn-sm" onclick="changePage(-1)" id="prevBtn">Previous</button>
                        <div class="page-numbers" id="pageNumbers">
                            <!-- Page numbers will be dynamically inserted -->
                        </div>
                        <button class="btn btn-ghost btn-sm" onclick="changePage(1)" id="nextBtn">Next</button>
                    </div>
                </div>
            </div>

            <!-- Projects Grid View (Hidden by default) -->
            <div class="projects-grid-container hidden" id="gridView">
                <div class="projects-grid" id="projectsGrid">
                    <!-- Project cards will be dynamically inserted here -->
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script>
        // Sample project data
        const projectsData = [
            {
                id: 1,
                name: "Digital Marketing Agency",
                keyword: "digital marketing services",
                industry: "Marketing",
                score: 96,
                status: "active",
                updated: "2025-01-10T10:30:00Z",
                created: "2025-01-05T14:22:00Z",
                website: "digitalagency.com"
            },
            {
                id: 2,
                name: "E-commerce Store",
                keyword: "buy running shoes online",
                industry: "E-commerce",
                score: 88,
                status: "active",
                updated: "2025-01-09T16:45:00Z",
                created: "2025-01-03T09:15:00Z",
                website: "runningstore.com"
            },
            {
                id: 3,
                name: "Law Firm Website",
                keyword: "personal injury lawyer",
                industry: "Legal",
                score: 92,
                status: "completed",
                updated: "2025-01-08T11:20:00Z",
                created: "2025-01-02T16:30:00Z",
                website: "lawfirm.com"
            },
            {
                id: 4,
                name: "Healthcare Clinic",
                keyword: "family doctor near me",
                industry: "Healthcare",
                score: 94,
                status: "active",
                updated: "2025-01-10T14:15:00Z",
                created: "2025-01-07T12:45:00Z",
                website: "healthclinic.com"
            },
            {
                id: 5,
                name: "Financial Advisory",
                keyword: "retirement planning services",
                industry: "Finance",
                score: 89,
                status: "draft",
                updated: "2025-01-09T09:30:00Z",
                created: "2025-01-06T13:20:00Z",
                website: "financialadvisor.com"
            },
            {
                id: 6,
                name: "Tech Startup",
                keyword: "cloud computing solutions",
                industry: "Technology",
                score: 91,
                status: "active",
                updated: "2025-01-10T08:45:00Z",
                created: "2025-01-04T10:10:00Z",
                website: "techstartup.com"
            }
        ];

        let currentPage = 1;
        let itemsPerPage = 10;
        let filteredProjects = [...projectsData];
        let currentSort = { field: 'updated', order: 'desc' };
        let currentView = 'table';

        document.addEventListener('DOMContentLoaded', function() {
            initializeProjects();
            animateOverviewCards();
        });

        function initializeProjects() {
            renderProjects();
            updatePagination();
        }

        function renderProjects() {
            if (currentView === 'table') {
                renderTableView();
            } else {
                renderGridView();
            }
        }

        function renderTableView() {
            const tbody = document.getElementById('projectsTableBody');
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageProjects = filteredProjects.slice(startIndex, endIndex);

            tbody.innerHTML = pageProjects.map(project => `
                <tr class="project-row" data-project-id="${project.id}">
                    <td>
                        <label class="table-checkbox">
                            <input type="checkbox" value="${project.id}">
                            <span class="checkbox-indicator"></span>
                        </label>
                    </td>
                    <td>
                        <div class="project-cell">
                            <div class="project-name">${project.name}</div>
                            <div class="project-url">${project.website}</div>
                        </div>
                    </td>
                    <td class="keyword-cell">${project.keyword}</td>
                    <td>
                        <span class="industry-badge">${project.industry}</span>
                    </td>
                    <td>
                        <div class="score-badge ${getScoreClass(project.score)}">${project.score}</div>
                    </td>
                    <td>
                        <span class="status-badge status-${project.status}">${formatStatus(project.status)}</span>
                    </td>
                    <td class="date-cell">${formatDate(project.updated)}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-icon" title="Edit" onclick="editProject(${project.id})">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </button>
                            <button class="btn-icon" title="Analyze" onclick="analyzeProject(${project.id})">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </button>
                            <button class="btn-icon" title="More options" onclick="showProjectMenu(${project.id})">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                                </svg>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        function renderGridView() {
            const grid = document.getElementById('projectsGrid');
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageProjects = filteredProjects.slice(startIndex, endIndex);

            grid.innerHTML = pageProjects.map(project => `
                <div class="project-card" data-project-id="${project.id}">
                    <div class="project-card-header">
                        <div class="project-card-title">
                            <h3>${project.name}</h3>
                            <span class="project-card-url">${project.website}</span>
                        </div>
                        <div class="score-badge ${getScoreClass(project.score)}">${project.score}</div>
                    </div>
                    
                    <div class="project-card-content">
                        <div class="project-card-meta">
                            <div class="meta-item">
                                <span class="meta-label">Keyword:</span>
                                <span class="meta-value">${project.keyword}</span>
                            </div>
                            <div class="meta-item">
                                <span class="meta-label">Industry:</span>
                                <span class="industry-badge">${project.industry}</span>
                            </div>
                            <div class="meta-item">
                                <span class="meta-label">Status:</span>
                                <span class="status-badge status-${project.status}">${formatStatus(project.status)}</span>
                            </div>
                            <div class="meta-item">
                                <span class="meta-label">Updated:</span>
                                <span class="meta-value">${formatDate(project.updated)}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="project-card-actions">
                        <button class="btn btn-secondary btn-sm" onclick="editProject(${project.id})">Edit</button>
                        <button class="btn btn-primary btn-sm" onclick="analyzeProject(${project.id})">Analyze</button>
                    </div>
                </div>
            `).join('');
        }

        function getScoreClass(score) {
            if (score >= 95) return 'score-excellent';
            if (score >= 90) return 'score-very-good';
            if (score >= 80) return 'score-good';
            return 'score-needs-work';
        }

        function formatStatus(status) {
            return status.charAt(0).toUpperCase() + status.slice(1);
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffTime = Math.abs(now - date);
            const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
            
            if (diffHours < 24) {
                return `${diffHours} hours ago`;
            } else {
                const diffDays = Math.floor(diffHours / 24);
                return `${diffDays} days ago`;
            }
        }

        function animateOverviewCards() {
            const cards = document.querySelectorAll('.card-number[data-count]');
            
            cards.forEach(card => {
                const target = parseFloat(card.getAttribute('data-count'));
                const duration = 2000;
                const start = performance.now();
                
                function updateValue(currentTime) {
                    const elapsed = currentTime - start;
                    const progress = Math.min(elapsed / duration, 1);
                    
                    const current = target * easeOutCubic(progress);
                    
                    if (target % 1 === 0) {
                        card.textContent = Math.floor(current);
                    } else {
                        card.textContent = current.toFixed(1);
                    }
                    
                    if (progress < 1) {
                        requestAnimationFrame(updateValue);
                    }
                }
                
                requestAnimationFrame(updateValue);
            });
        }

        function easeOutCubic(t) {
            return 1 - Math.pow(1 - t, 3);
        }

        function searchProjects(query) {
            if (!query.trim()) {
                filteredProjects = [...projectsData];
            } else {
                const searchTerm = query.toLowerCase();
                filteredProjects = projectsData.filter(project => 
                    project.name.toLowerCase().includes(searchTerm) ||
                    project.keyword.toLowerCase().includes(searchTerm) ||
                    project.industry.toLowerCase().includes(searchTerm) ||
                    project.website.toLowerCase().includes(searchTerm)
                );
            }
            
            currentPage = 1;
            renderProjects();
            updatePagination();
        }

        function filterProjects(filterType, value) {
            // Apply all active filters
            const filters = {
                status: document.querySelector('select[onchange*="status"]').value,
                industry: document.querySelector('select[onchange*="industry"]').value,
                score: document.querySelector('select[onchange*="score"]').value
            };
            
            // Update the current filter
            filters[filterType] = value;
            
            filteredProjects = projectsData.filter(project => {
                let matches = true;
                
                if (filters.status && project.status !== filters.status) {
                    matches = false;
                }
                
                if (filters.industry && project.industry.toLowerCase() !== filters.industry.toLowerCase()) {
                    matches = false;
                }
                
                if (filters.score) {
                    const score = project.score;
                    switch (filters.score) {
                        case 'excellent':
                            if (score < 95) matches = false;
                            break;
                        case 'very-good':
                            if (score < 90 || score >= 95) matches = false;
                            break;
                        case 'good':
                            if (score < 80 || score >= 90) matches = false;
                            break;
                        case 'needs-work':
                            if (score >= 80) matches = false;
                            break;
                    }
                }
                
                return matches;
            });
            
            currentPage = 1;
            renderProjects();
            updatePagination();
        }

        function sortProjects(field) {
            currentSort.field = field;
            currentSort.order = currentSort.order === 'asc' ? 'desc' : 'asc';
            
            filteredProjects.sort((a, b) => {
                let aVal = a[field];
                let bVal = b[field];
                
                if (field === 'updated' || field === 'created') {
                    aVal = new Date(aVal);
                    bVal = new Date(bVal);
                }
                
                if (typeof aVal === 'string') {
                    aVal = aVal.toLowerCase();
                    bVal = bVal.toLowerCase();
                }
                
                if (currentSort.order === 'asc') {
                    return aVal > bVal ? 1 : -1;
                } else {
                    return aVal < bVal ? 1 : -1;
                }
            });
            
            renderProjects();
        }

        function clearFilters() {
            document.querySelectorAll('.filters-section select').forEach(select => {
                select.value = '';
            });
            
            filteredProjects = [...projectsData];
            currentPage = 1;
            renderProjects();
            updatePagination();
        }

        function setView(view) {
            currentView = view;
            
            // Update view buttons
            document.querySelectorAll('.view-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-view="${view}"]`).classList.add('active');
            
            // Show/hide views
            if (view === 'table') {
                document.getElementById('tableView').classList.remove('hidden');
                document.getElementById('gridView').classList.add('hidden');
            } else {
                document.getElementById('tableView').classList.add('hidden');
                document.getElementById('gridView').classList.remove('hidden');
            }
            
            renderProjects();
        }

        function updatePagination() {
            const totalItems = filteredProjects.length;
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            const startItem = (currentPage - 1) * itemsPerPage + 1;
            const endItem = Math.min(currentPage * itemsPerPage, totalItems);
            
            document.getElementById('showingFrom').textContent = totalItems > 0 ? startItem : 0;
            document.getElementById('showingTo').textContent = endItem;
            document.getElementById('totalResults').textContent = totalItems;
            
            // Update pagination buttons
            document.getElementById('prevBtn').disabled = currentPage <= 1;
            document.getElementById('nextBtn').disabled = currentPage >= totalPages;
            
            // Generate page numbers
            const pageNumbers = document.getElementById('pageNumbers');
            pageNumbers.innerHTML = '';
            
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    const pageBtn = document.createElement('button');
                    pageBtn.className = `page-btn ${i === currentPage ? 'active' : ''}`;
                    pageBtn.textContent = i;
                    pageBtn.onclick = () => goToPage(i);
                    pageNumbers.appendChild(pageBtn);
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    const ellipsis = document.createElement('span');
                    ellipsis.textContent = '...';
                    ellipsis.className = 'page-ellipsis';
                    pageNumbers.appendChild(ellipsis);
                }
            }
        }

        function changePage(delta) {
            const totalPages = Math.ceil(filteredProjects.length / itemsPerPage);
            const newPage = currentPage + delta;
            
            if (newPage >= 1 && newPage <= totalPages) {
                currentPage = newPage;
                renderProjects();
                updatePagination();
            }
        }

        function goToPage(page) {
            currentPage = page;
            renderProjects();
            updatePagination();
        }

        function toggleSelectAll(checkbox) {
            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
            checkboxes.forEach(cb => {
                cb.checked = checkbox.checked;
            });
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebarNav');
            sidebar.classList.toggle('collapsed');
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }

        function toggleSection(button) {
            const section = button.closest('.nav-section');
            const items = section.querySelector('.nav-items');
            const icon = button.querySelector('svg');
            
            section.classList.toggle('collapsed');
            
            if (section.classList.contains('collapsed')) {
                items.style.display = 'none';
                icon.style.transform = 'rotate(-90deg)';
            } else {
                items.style.display = 'block';
                icon.style.transform = 'rotate(0deg)';
            }
        }

        function createNewProject() {
            window.location.href = 'content-creator.html';
        }

        function editProject(id) {
            window.location.href = `project-details.html?id=${id}`;
        }

        function analyzeProject(id) {
            window.location.href = `project-details.html?id=${id}&tab=analysis`;
        }

        function showProjectMenu(id) {
            // Placeholder for project context menu
            console.log('Show menu for project', id);
        }

        function exportProjects() {
            // Placeholder for export functionality
            console.log('Export projects');
        }
    </script>
</body>
</html>