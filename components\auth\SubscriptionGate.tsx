'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>ontent, <PERSON>ton, Badge } from '@/components/UI';
import { useUnifiedAuth } from '@/hooks/useUnifiedAuth';
import {
  LockClosedIcon,
  BoltIcon,
  TrophyIcon,
  CheckIcon,
  ArrowUpIcon
} from '@heroicons/react/24/outline';

interface SubscriptionGateProps {
  requiredTier: 'pro' | 'enterprise';
  feature: string;
  description?: string;
  benefits?: string[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

const tierFeatures = {
  pro: [
    'Unlimited content generation',
    'Advanced SEO analytics',
    'Competitor analysis',
    'Bulk content processing',
    'Priority support'
  ],
  enterprise: [
    'Everything in Pro',
    'Team collaboration',
    'White-label options',
    'API access',
    'Custom integrations',
    'Dedicated account manager'
  ]
};

const tierPricing = {
  pro: { monthly: 29, yearly: 290 },
  enterprise: { monthly: 99, yearly: 990 }
};

export default function SubscriptionGate({
  requiredTier,
  feature,
  description,
  benefits,
  children,
  fallback
}: SubscriptionGateProps) {
  const { userTier, isSubscribed, isDemoMode } = useUnifiedAuth();

  // Check if user has access
  if (isSubscribed(requiredTier)) {
    return <>{children}</>;
  }

  // Custom fallback if provided
  if (fallback) {
    return <>{fallback}</>;
  }

  // Default upgrade prompt
  const TierIcon = requiredTier === 'enterprise' ? TrophyIcon : BoltIcon;
  const currentTierNum = userTier === 'free' ? 0 : userTier === 'pro' ? 1 : 2;
  const requiredTierNum = requiredTier === 'pro' ? 1 : 2;
  const isUpgrade = requiredTierNum > currentTierNum;

  return (
    <div className="min-h-96 flex items-center justify-center p-4">
      <Card className="max-w-lg w-full">
        <CardHeader
          title={
            <div className="flex items-center space-x-2">
              <LockClosedIcon className="h-6 w-6 text-gray-400" />
              <span>{feature} - {requiredTier} Feature</span>
            </div>
          }
          subtitle={description || `This feature requires a ${requiredTier} subscription to access.`}
        />

        <CardContent>
          {/* Current vs Required Tier */}
          <div className="flex items-center justify-center space-x-4 mb-6">
            <div className="text-center">
              <Badge variant="default" className="mb-2">
                Current: {userTier}
              </Badge>
              <p className="text-xs text-gray-500 dark:text-gray-400">Your plan</p>
            </div>
            
            <ArrowUpIcon className="h-5 w-5 text-gray-400" />
            
            <div className="text-center">
              <Badge 
                variant={requiredTier === 'enterprise' ? 'secondary' : 'primary'}
                icon={<TierIcon className="h-3 w-3" />}
                className="mb-2"
              >
                {requiredTier}
              </Badge>
              <p className="text-xs text-gray-500 dark:text-gray-400">Required</p>
            </div>
          </div>

          {/* Feature Benefits */}
          <div className="mb-6">
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
              {requiredTier} Plan includes:
            </h4>
            <ul className="space-y-2">
              {(benefits || tierFeatures[requiredTier]).map((benefit, index) => (
                <li key={index} className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                  <CheckIcon className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  {benefit}
                </li>
              ))}
            </ul>
          </div>

          {/* Pricing */}
          {!isDemoMode && (
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    ${tierPricing[requiredTier].monthly}/month
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    or ${tierPricing[requiredTier].yearly}/year (save 17%)
                  </p>
                </div>
                {isUpgrade && (
                  <Badge variant="success">
                    Upgrade
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* Demo Mode Notice */}
          {isDemoMode && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
              <p className="text-sm text-blue-800 dark:text-blue-200">
                <strong>Demo Mode:</strong> In a real application, you would upgrade your subscription to access this feature.
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              variant="primary"
              fullWidth
              onClick={() => window.location.href = isDemoMode ? '#' : '/pricing'}
              leftIcon={<TierIcon className="h-4 w-4" />}
            >
              {isDemoMode ? 'View Pricing' : `Upgrade to ${requiredTier}`}
            </Button>
            
            <Button
              variant="ghost"
              fullWidth
              onClick={() => window.history.back()}
            >
              Go Back
            </Button>
          </div>

          {/* Contact Sales for Enterprise */}
          {requiredTier === 'enterprise' && !isDemoMode && (
            <div className="mt-4 text-center">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Need a custom plan?{' '}
                <a href="/contact" className="text-blue-600 dark:text-blue-400 hover:underline">
                  Contact sales
                </a>
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Convenience wrapper for specific features
export function ProFeatureGate({ children, feature, description }: {
  children: React.ReactNode;
  feature: string;
  description?: string;
}) {
  return (
    <SubscriptionGate
      requiredTier="pro"
      feature={feature}
      description={description}
    >
      {children}
    </SubscriptionGate>
  );
}

export function EnterpriseFeatureGate({ children, feature, description }: {
  children: React.ReactNode;
  feature: string;
  description?: string;
}) {
  return (
    <SubscriptionGate
      requiredTier="enterprise"
      feature={feature}
      description={description}
    >
      {children}
    </SubscriptionGate>
  );
}