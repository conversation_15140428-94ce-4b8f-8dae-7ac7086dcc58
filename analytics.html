<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Comprehensive analytics dashboard with real-time performance metrics, competitor insights, and advanced data visualization for SEO optimization.">
    <title>Analytics & Reports - SEO SAAS</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/data-visualization.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/global-search.css">
    
    <!-- Chart.js for Data Visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
</head>
<body class="dashboard-layout analytics-layout">
    <!-- Dashboard App Container -->
    <div class="dashboard-app">
        <!-- Global Top Navigation -->
        <header class="top-navigation">
            <div class="nav-left">
                <button class="sidebar-toggle" aria-label="Toggle sidebar" onclick="toggleSidebar()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
                <div class="brand-section">
                    <svg class="brand-logo w-8 h-8 text-primary-600" viewBox="0 0 40 40" fill="currentColor">
                        <path d="M20 5L5 15v10l15 10 15-10V15L20 5z"/>
                        <path d="M20 15l-10 6v8l10 6 10-6v-8l-10-6z" fill="white" opacity="0.5"/>
                    </svg>
                    <span class="brand-name">SEO SAAS</span>
                    <span class="environment-badge">Pro</span>
                </div>
            </div>
            
            <div class="nav-center">
                <div class="global-search">
                    <svg class="search-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <input type="search" placeholder="Search projects, content, keywords..." class="search-input" onclick="globalSearch.openSearch()">
                    <div class="search-shortcut">
                        <span class="shortcut-hint">⌘K</span>
                    </div>
                </div>
                <div class="analytics-controls">
                    <div class="date-range-selector">
                        <select id="dateRange" onchange="updateDateRange()">
                            <option value="7">Last 7 days</option>
                            <option value="30" selected>Last 30 days</option>
                            <option value="90">Last 90 days</option>
                            <option value="365">Last 12 months</option>
                        </select>
                    </div>
                    <button class="analytics-btn" onclick="exportReport()" title="Export Report">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                        </svg>
                        Export
                    </button>
                    <button class="analytics-btn analytics-btn-primary" onclick="refreshData()">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Refresh
                    </button>
                </div>
            </div>
            
            <div class="nav-right">
                <div class="performance-indicator" id="performanceIndicator">
                    <div class="indicator-dot indicator-excellent"></div>
                    <span class="indicator-text">Excellent Performance</span>
                </div>
                
                <div class="notifications-dropdown">
                    <button class="notification-btn" title="Notifications">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5-5V9.5a6.5 6.5 0 10-13 0V12l-5 5h5"></path>
                        </svg>
                        <span class="notification-badge">3</span>
                    </button>
                </div>
                
                <div class="user-menu-dropdown">
                    <button class="user-menu-btn">
                        <div class="user-avatar">
                            <span>JD</span>
                        </div>
                        <div class="user-info">
                            <span class="user-name">John Doe</span>
                            <span class="user-plan">Professional</span>
                        </div>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <aside class="sidebar-navigation" id="sidebarNav">
            <nav class="nav-menu">
                <!-- Dashboard Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Overview</h3>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="dashboard.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z"></path>
                                </svg>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li class="nav-item active">
                            <a href="analytics.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                <span>Analytics</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Content Creation Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Content Creation</h3>
                        <button class="section-toggle" aria-label="Toggle section" onclick="toggleSection(this)">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="content-creator.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                <span>Create Content</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="content-optimizer.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                <span>Optimize Content</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="bulk-generator.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                <span>Bulk Generator</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="content-editor.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                                </svg>
                                <span>Content Editor</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Projects Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Projects</h3>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="projects.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                <span>All Projects</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="templates.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <span>Templates</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Settings Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Settings</h3>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="account-settings.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <span>Account</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="billing.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                </svg>
                                <span>Billing</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Sidebar Footer -->
            <div class="sidebar-footer">
                <div class="usage-indicator">
                    <div class="usage-label">API Usage</div>
                    <div class="usage-bar">
                        <div class="usage-fill" style="width: 65%"></div>
                    </div>
                    <div class="usage-text">325 / 500 requests</div>
                </div>
                
                <button class="upgrade-btn" onclick="window.location.href='pricing.html'">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    <span>Upgrade Plan</span>
                </button>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Analytics Header -->
            <div class="analytics-header">
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="analytics-title">Analytics & Performance Reports</h1>
                        <p class="analytics-subtitle">Comprehensive insights into your SEO content performance and competitor analysis</p>
                    </div>
                    <div class="header-right">
                        <div class="header-stats">
                            <div class="stat-item">
                                <div class="stat-value">96.2%</div>
                                <div class="stat-label">Avg. Quality Score</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">2,847</div>
                                <div class="stat-label">Content Pieces</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">15</div>
                                <div class="stat-label">Active Projects</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Key Metrics Overview -->
            <div class="metrics-overview">
                <div class="metrics-grid">
                    <div class="metric-card metric-primary">
                        <div class="metric-icon">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">+347%</div>
                            <div class="metric-label">SEO Performance</div>
                            <div class="metric-change positive">+23% vs last month</div>
                        </div>
                    </div>
                    
                    <div class="metric-card metric-success">
                        <div class="metric-icon">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                            </svg>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">98.5%</div>
                            <div class="metric-label">Quality Score</div>
                            <div class="metric-change positive">+2.1% vs last month</div>
                        </div>
                    </div>
                    
                    <div class="metric-card metric-warning">
                        <div class="metric-icon">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">87%</div>
                            <div class="metric-label">Competitor Edge</div>
                            <div class="metric-change positive">+12% vs industry avg</div>
                        </div>
                    </div>
                    
                    <div class="metric-card metric-info">
                        <div class="metric-icon">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">2.47s</div>
                            <div class="metric-label">Avg. Generation Time</div>
                            <div class="metric-change positive">-18% vs last month</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts and Visualizations -->
            <div class="analytics-charts">
                <div class="charts-grid">
                    <!-- Performance Trends Chart -->
                    <div class="chart-card chart-large">
                        <div class="chart-header">
                            <h3>Performance Trends</h3>
                            <div class="chart-controls">
                                <select id="performanceMetric" onchange="updatePerformanceChart()">
                                    <option value="quality">Quality Score</option>
                                    <option value="seo">SEO Performance</option>
                                    <option value="readability">Readability</option>
                                    <option value="keyword-density">Keyword Density</option>
                                </select>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="performanceChart"></canvas>
                        </div>
                    </div>

                    <!-- Project Distribution -->
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>Project Distribution</h3>
                            <span class="chart-subtitle">By industry type</span>
                        </div>
                        <div class="chart-container">
                            <canvas id="distributionChart"></canvas>
                        </div>
                    </div>

                    <!-- Content Volume -->
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>Content Volume</h3>
                            <span class="chart-subtitle">Daily generation stats</span>
                        </div>
                        <div class="chart-container">
                            <canvas id="volumeChart"></canvas>
                        </div>
                    </div>

                    <!-- Competitor Analysis -->
                    <div class="chart-card chart-wide">
                        <div class="chart-header">
                            <h3>Competitor Performance Comparison</h3>
                            <span class="chart-subtitle">Your content vs. top competitors</span>
                        </div>
                        <div class="chart-container">
                            <canvas id="competitorChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Analytics Tables -->
            <div class="analytics-tables">
                <!-- Top Performing Content -->
                <div class="table-card">
                    <div class="table-header">
                        <h3>Top Performing Content</h3>
                        <span class="table-subtitle">Highest quality scores this month</span>
                        <button class="btn btn-ghost btn-sm" onclick="exportTopContent()">Export</button>
                    </div>
                    <div class="table-container">
                        <table class="analytics-table">
                            <thead>
                                <tr>
                                    <th>Content Title</th>
                                    <th>Project</th>
                                    <th>Quality Score</th>
                                    <th>SEO Score</th>
                                    <th>Word Count</th>
                                    <th>Generated</th>
                                </tr>
                            </thead>
                            <tbody id="topContentTable">
                                <tr>
                                    <td>
                                        <div class="content-title">
                                            <strong>Ultimate Guide to Digital Marketing in 2025</strong>
                                            <span class="content-type">Blog Post</span>
                                        </div>
                                    </td>
                                    <td>Digital Marketing Agency</td>
                                    <td><span class="score-badge score-excellent">98</span></td>
                                    <td><span class="score-badge score-excellent">96</span></td>
                                    <td>3,247</td>
                                    <td>2 hours ago</td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="content-title">
                                            <strong>Real Estate Investment Strategies for Beginners</strong>
                                            <span class="content-type">Guide</span>
                                        </div>
                                    </td>
                                    <td>Property Investment Hub</td>
                                    <td><span class="score-badge score-excellent">97</span></td>
                                    <td><span class="score-badge score-excellent">94</span></td>
                                    <td>2,891</td>
                                    <td>4 hours ago</td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="content-title">
                                            <strong>Healthcare Technology Trends and Innovations</strong>
                                            <span class="content-type">Article</span>
                                        </div>
                                    </td>
                                    <td>MedTech Solutions</td>
                                    <td><span class="score-badge score-excellent">96</span></td>
                                    <td><span class="score-badge score-excellent">95</span></td>
                                    <td>2,654</td>
                                    <td>6 hours ago</td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="content-title">
                                            <strong>Sustainable Business Practices for Modern Companies</strong>
                                            <span class="content-type">White Paper</span>
                                        </div>
                                    </td>
                                    <td>Green Business Solutions</td>
                                    <td><span class="score-badge score-excellent">95</span></td>
                                    <td><span class="score-badge score-good">92</span></td>
                                    <td>4,123</td>
                                    <td>1 day ago</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Project Performance Summary -->
                <div class="table-card">
                    <div class="table-header">
                        <h3>Project Performance Summary</h3>
                        <span class="table-subtitle">All active projects with key metrics</span>
                        <button class="btn btn-ghost btn-sm" onclick="exportProjectSummary()">Export</button>
                    </div>
                    <div class="table-container">
                        <table class="analytics-table">
                            <thead>
                                <tr>
                                    <th>Project Name</th>
                                    <th>Industry</th>
                                    <th>Content Count</th>
                                    <th>Avg. Quality</th>
                                    <th>Avg. SEO</th>
                                    <th>Status</th>
                                    <th>Last Updated</th>
                                </tr>
                            </thead>
                            <tbody id="projectSummaryTable">
                                <tr>
                                    <td>
                                        <div class="project-name">
                                            <strong>Digital Marketing Agency</strong>
                                            <span class="project-keyword">digital marketing services</span>
                                        </div>
                                    </td>
                                    <td>Marketing</td>
                                    <td>47</td>
                                    <td><span class="score-badge score-excellent">96</span></td>
                                    <td><span class="score-badge score-excellent">94</span></td>
                                    <td><span class="status-badge status-active">Active</span></td>
                                    <td>2 hours ago</td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="project-name">
                                            <strong>Property Investment Hub</strong>
                                            <span class="project-keyword">real estate investment</span>
                                        </div>
                                    </td>
                                    <td>Real Estate</td>
                                    <td>32</td>
                                    <td><span class="score-badge score-excellent">95</span></td>
                                    <td><span class="score-badge score-excellent">93</span></td>
                                    <td><span class="status-badge status-active">Active</span></td>
                                    <td>4 hours ago</td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="project-name">
                                            <strong>MedTech Solutions</strong>
                                            <span class="project-keyword">healthcare technology</span>
                                        </div>
                                    </td>
                                    <td>Healthcare</td>
                                    <td>28</td>
                                    <td><span class="score-badge score-excellent">97</span></td>
                                    <td><span class="score-badge score-excellent">96</span></td>
                                    <td><span class="status-badge status-active">Active</span></td>
                                    <td>6 hours ago</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <!-- Enhanced Navigation Scripts -->
    <script src="js/sidebar-manager.js"></script>
    <script src="js/breadcrumb-navigation.js"></script>
    <script src="js/search-autocomplete.js"></script>
    <script src="js/global-search.js"></script>
    
    <script>
        // Analytics Dashboard JavaScript
        let performanceChart, distributionChart, volumeChart, competitorChart;
        
        document.addEventListener('DOMContentLoaded', function() {
            initializeAnalytics();
            loadAnalyticsData();
            createCharts();
        });

        function initializeAnalytics() {
            // Initialize sidebar state
            const sidebar = document.getElementById('sidebarNav');
            const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (isCollapsed) {
                sidebar.classList.add('collapsed');
            }
            
            // Set initial date range
            updateDateRange();
        }

        function loadAnalyticsData() {
            // Sample analytics data - would come from API in real implementation
            const analyticsData = {
                overview: {
                    avgQualityScore: 96.2,
                    totalContent: 2847,
                    activeProjects: 15,
                    seoPerformance: 347,
                    competitorEdge: 87,
                    avgGenerationTime: 2.47
                },
                trends: {
                    qualityScores: [92, 94, 95, 96, 97, 96, 98],
                    seoPerformance: [85, 87, 89, 91, 93, 95, 97],
                    contentVolume: [45, 52, 48, 61, 55, 67, 59]
                },
                projects: [
                    { name: 'Digital Marketing Agency', industry: 'Marketing', count: 47, quality: 96, seo: 94 },
                    { name: 'Property Investment Hub', industry: 'Real Estate', count: 32, quality: 95, seo: 93 },
                    { name: 'MedTech Solutions', industry: 'Healthcare', count: 28, quality: 97, seo: 96 }
                ]
            };
            
            updateAnalyticsDisplay(analyticsData);
        }

        function updateAnalyticsDisplay(data) {
            // Update key metrics
            document.querySelector('.metric-primary .metric-value').textContent = `+${data.overview.seoPerformance}%`;
            document.querySelector('.metric-success .metric-value').textContent = `${data.overview.avgQualityScore}%`;
            document.querySelector('.metric-warning .metric-value').textContent = `${data.overview.competitorEdge}%`;
            document.querySelector('.metric-info .metric-value').textContent = `${data.overview.avgGenerationTime}s`;
            
            // Update header stats
            const statItems = document.querySelectorAll('.stat-item');
            statItems[0].querySelector('.stat-value').textContent = `${data.overview.avgQualityScore}%`;
            statItems[1].querySelector('.stat-value').textContent = data.overview.totalContent.toLocaleString();
            statItems[2].querySelector('.stat-value').textContent = data.overview.activeProjects;
        }

        function createCharts() {
            // Performance Trends Chart
            const performanceCtx = document.getElementById('performanceChart').getContext('2d');
            performanceChart = new Chart(performanceCtx, {
                type: 'line',
                data: {
                    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6', 'Week 7'],
                    datasets: [{
                        label: 'Quality Score',
                        data: [92, 94, 95, 96, 97, 96, 98],
                        borderColor: '#3B82F6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'SEO Performance',
                        data: [85, 87, 89, 91, 93, 95, 97],
                        borderColor: '#10B981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 80,
                            max: 100
                        }
                    }
                }
            });

            // Project Distribution Chart
            const distributionCtx = document.getElementById('distributionChart').getContext('2d');
            distributionChart = new Chart(distributionCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Marketing', 'Real Estate', 'Healthcare', 'Technology', 'Finance'],
                    datasets: [{
                        data: [35, 25, 20, 12, 8],
                        backgroundColor: [
                            '#3B82F6',
                            '#10B981',
                            '#F59E0B',
                            '#EF4444',
                            '#8B5CF6'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Content Volume Chart
            const volumeCtx = document.getElementById('volumeChart').getContext('2d');
            volumeChart = new Chart(volumeCtx, {
                type: 'bar',
                data: {
                    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    datasets: [{
                        label: 'Content Generated',
                        data: [45, 52, 48, 61, 55, 67, 59],
                        backgroundColor: '#3B82F6'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Competitor Comparison Chart
            const competitorCtx = document.getElementById('competitorChart').getContext('2d');
            competitorChart = new Chart(competitorCtx, {
                type: 'radar',
                data: {
                    labels: ['Quality Score', 'SEO Performance', 'Readability', 'E-E-A-T', 'Keyword Density', 'Content Length'],
                    datasets: [{
                        label: 'Your Content',
                        data: [96, 94, 92, 95, 88, 90],
                        borderColor: '#3B82F6',
                        backgroundColor: 'rgba(59, 130, 246, 0.2)'
                    }, {
                        label: 'Competitor Average',
                        data: [78, 82, 75, 70, 85, 88],
                        borderColor: '#EF4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.2)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }

        function updateDateRange() {
            const dateRange = document.getElementById('dateRange').value;
            console.log(`Updating data for last ${dateRange} days`);
            // Implementation would refresh all charts and data based on selected range
            refreshData();
        }

        function updatePerformanceChart() {
            const metric = document.getElementById('performanceMetric').value;
            console.log(`Updating performance chart for: ${metric}`);
            // Implementation would update the performance chart based on selected metric
        }

        function refreshData() {
            console.log('Refreshing analytics data...');
            // Implementation would reload all data from API
            loadAnalyticsData();
        }

        function exportReport() {
            console.log('Exporting comprehensive analytics report...');
            // Implementation would generate and download a PDF/Excel report
            alert('Report export functionality would be implemented here');
        }

        function exportTopContent() {
            console.log('Exporting top performing content data...');
            // Implementation would export the top content table as CSV/Excel
            alert('Top content export functionality would be implemented here');
        }

        function exportProjectSummary() {
            console.log('Exporting project summary data...');
            // Implementation would export the project summary table as CSV/Excel
            alert('Project summary export functionality would be implemented here');
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebarNav');
            sidebar.classList.toggle('collapsed');
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
            
            // Resize charts after sidebar toggle
            setTimeout(() => {
                if (performanceChart) performanceChart.resize();
                if (distributionChart) distributionChart.resize();
                if (volumeChart) volumeChart.resize();
                if (competitorChart) competitorChart.resize();
            }, 300);
        }

        function toggleSection(button) {
            const section = button.closest('.nav-section');
            const items = section.querySelector('.nav-items');
            const icon = button.querySelector('svg');
            
            section.classList.toggle('collapsed');
            
            if (section.classList.contains('collapsed')) {
                items.style.display = 'none';
                icon.style.transform = 'rotate(-90deg)';
            } else {
                items.style.display = 'block';
                icon.style.transform = 'rotate(0deg)';
            }
        }
    </script>
</body>
</html>