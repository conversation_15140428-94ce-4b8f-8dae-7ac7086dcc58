/**
 * Competitor Insights Component
 * Displays competitor analysis and content gaps
 */

'use client'

import React, { useState } from 'react'
import Card from '@/components/UI/Card'
import Button from '@/components/UI/Button'
import Badge from '@/components/UI/Badge'
import { 
  MagnifyingGlassIcon,
  ChartBarIcon,
  ExternalLinkIcon,
  TrophyIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline'

interface CompetitorInsightsProps {
  competitorData: {
    top_competitors: Array<{
      url: string
      title: string
      word_count: number
      seo_score: number
      key_insights: string[]
    }>
  }
  serpAnalysis: {
    total_results: number
    featured_snippet: boolean
    average_word_count: number
    common_topics: string[]
  }
  keywordAnalysis: {
    difficulty_score: number
    search_volume: number
    related_keywords: string[]
    lsi_keywords: string[]
  }
}

type TabType = 'competitors' | 'serp' | 'keywords'

export default function CompetitorInsights({ 
  competitorData, 
  serpAnalysis, 
  keywordAnalysis 
}: CompetitorInsightsProps) {
  const [activeTab, setActiveTab] = useState<TabType>('competitors')

  const getDifficultyColor = (score: number) => {
    if (score >= 80) return 'text-red-600 dark:text-red-400'
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400'
    return 'text-green-600 dark:text-green-400'
  }

  const getDifficultyLabel = (score: number) => {
    if (score >= 80) return 'Very Hard'
    if (score >= 60) return 'Medium'
    if (score >= 40) return 'Easy'
    return 'Very Easy'
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
    return num.toString()
  }

  const tabs = [
    { id: 'competitors', label: 'Top Competitors', icon: TrophyIcon },
    { id: 'serp', label: 'SERP Analysis', icon: MagnifyingGlassIcon },
    { id: 'keywords', label: 'Keyword Data', icon: ChartBarIcon }
  ]

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
          <MagnifyingGlassIcon className="h-6 w-6 mr-2" />
          Competitor Intelligence
        </h3>
        
        <div className="flex items-center space-x-2">
          <Badge variant="secondary">
            {competitorData.top_competitors.length} competitors analyzed
          </Badge>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 mb-6 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
        {tabs.map((tab) => {
          const IconComponent = tab.icon
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as TabType)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
              }`}
            >
              <IconComponent className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          )
        })}
      </div>

      {/* Tab Content */}
      {activeTab === 'competitors' && (
        <div className="space-y-4">
          {competitorData.top_competitors.map((competitor, index) => (
            <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <Badge variant="outline" size="sm">
                      #{index + 1}
                    </Badge>
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                      {competitor.title}
                    </h4>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                    <span>{competitor.url}</span>
                    <ExternalLinkIcon className="h-4 w-4" />
                  </div>
                </div>
                
                <div className="text-right ml-4">
                  <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {competitor.seo_score}%
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    SEO Score
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-3">
                <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
                  <div className="font-medium text-gray-900 dark:text-gray-100">
                    {competitor.word_count.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Words
                  </div>
                </div>
                
                <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
                  <div className="font-medium text-gray-900 dark:text-gray-100">
                    Position {index + 1}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Ranking
                  </div>
                </div>
              </div>

              {competitor.key_insights.length > 0 && (
                <div>
                  <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Key Insights:
                  </div>
                  <div className="space-y-1">
                    {competitor.key_insights.map((insight, insightIndex) => (
                      <div key={insightIndex} className="flex items-start space-x-2">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {insight}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {activeTab === 'serp' && (
        <div className="space-y-6">
          {/* SERP Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {serpAnalysis.total_results.toLocaleString()}
              </div>
              <div className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                Total Results
              </div>
            </div>

            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {serpAnalysis.average_word_count}
              </div>
              <div className="text-sm text-green-700 dark:text-green-300 mt-1">
                Avg Word Count
              </div>
            </div>

            <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {serpAnalysis.featured_snippet ? 'Yes' : 'No'}
              </div>
              <div className="text-sm text-purple-700 dark:text-purple-300 mt-1">
                Featured Snippet
              </div>
            </div>
          </div>

          {/* Common Topics */}
          <div>
            <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100 mb-3">
              Common Topics in Top Results
            </h4>
            <div className="flex flex-wrap gap-2">
              {serpAnalysis.common_topics.map((topic, index) => (
                <Badge key={index} variant="outline">
                  {topic}
                </Badge>
              ))}
            </div>
          </div>

          {/* SERP Features */}
          <div>
            <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100 mb-3">
              SERP Features
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <div className={`p-3 rounded-lg border ${
                serpAnalysis.featured_snippet 
                  ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800' 
                  : 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700'
              }`}>
                <div className="flex items-center space-x-2">
                  {serpAnalysis.featured_snippet ? (
                    <CheckCircleIcon className="h-4 w-4 text-green-500" />
                  ) : (
                    <ExclamationTriangleIcon className="h-4 w-4 text-gray-400" />
                  )}
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    Featured Snippet
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'keywords' && (
        <div className="space-y-6">
          {/* Keyword Difficulty */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="text-center p-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg">
              <div className={`text-3xl font-bold ${getDifficultyColor(keywordAnalysis.difficulty_score)}`}>
                {keywordAnalysis.difficulty_score}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Keyword Difficulty
              </div>
              <Badge 
                variant={keywordAnalysis.difficulty_score >= 80 ? 'error' : 
                        keywordAnalysis.difficulty_score >= 60 ? 'warning' : 'success'}
                className="mt-2"
              >
                {getDifficultyLabel(keywordAnalysis.difficulty_score)}
              </Badge>
            </div>

            <div className="text-center p-6 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg">
              <div className="text-3xl font-bold text-green-600 dark:text-green-400">
                {formatNumber(keywordAnalysis.search_volume)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Monthly Searches
              </div>
              <div className="flex items-center justify-center mt-2">
                <ArrowUpIcon className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600 dark:text-green-400">
                  High Volume
                </span>
              </div>
            </div>
          </div>

          {/* Related Keywords */}
          <div>
            <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100 mb-3">
              Related Keywords
            </h4>
            <div className="flex flex-wrap gap-2">
              {keywordAnalysis.related_keywords.map((keyword, index) => (
                <Badge key={index} variant="secondary">
                  {keyword}
                </Badge>
              ))}
            </div>
          </div>

          {/* LSI Keywords */}
          <div>
            <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100 mb-3">
              LSI Keywords (Semantic)
            </h4>
            <div className="flex flex-wrap gap-2">
              {keywordAnalysis.lsi_keywords.map((keyword, index) => (
                <Badge key={index} variant="outline">
                  {keyword}
                </Badge>
              ))}
            </div>
          </div>

          {/* Opportunity Analysis */}
          <div className="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg p-4">
            <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100 mb-2 flex items-center">
              <TrophyIcon className="h-5 w-5 text-yellow-500 mr-2" />
              Ranking Opportunity
            </h4>
            <p className="text-sm text-gray-700 dark:text-gray-300">
              {keywordAnalysis.difficulty_score < 40 
                ? "Great opportunity! This keyword has low competition and good search volume. Focus on comprehensive content to rank well."
                : keywordAnalysis.difficulty_score < 70
                ? "Moderate opportunity. You'll need high-quality, comprehensive content and some authority to compete effectively."
                : "Challenging keyword. Consider targeting long-tail variations first to build topical authority before tackling this main keyword."
              }
            </p>
          </div>
        </div>
      )}
    </Card>
  )
}