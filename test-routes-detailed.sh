#!/bin/bash

echo "Testing SEO SAAS Application Routes - Detailed Analysis"
echo "======================================================="
echo ""

# Test function
test_route() {
    local path=$1
    local name=$2
    echo "Testing: $name ($path)"
    
    # Get full response with headers
    response=$(curl -s -w "\n\nHTTP_CODE:%{http_code}\nREDIRECT_URL:%{redirect_url}\n" -L --max-time 5 "http://localhost:3000$path" 2>&1)
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d':' -f2)
    redirect_url=$(echo "$response" | grep "REDIRECT_URL:" | cut -d':' -f2)
    
    # Extract title if HTML
    title=$(echo "$response" | grep -o '<title>[^<]*</title>' | sed 's/<[^>]*>//g' | head -1)
    
    # Check for errors in response
    error_text=$(echo "$response" | grep -i "error" | head -1)
    
    echo "  Status: $http_code"
    [ ! -z "$title" ] && echo "  Title: $title"
    [ ! -z "$redirect_url" ] && echo "  Redirected to: $redirect_url"
    [ ! -z "$error_text" ] && echo "  Error found: $error_text"
    echo ""
}

# Test all routes
test_route "/" "Homepage"
test_route "/auth/login" "Login Page"
test_route "/auth/register" "Register Page"
test_route "/dashboard" "Dashboard"
test_route "/content-generator" "Content Generator"
test_route "/projects" "Projects"
test_route "/analytics" "Analytics"
test_route "/profile" "Profile"
test_route "/settings" "Settings"
test_route "/demo" "Demo"

echo "======================================================="
echo "Testing API Routes"
echo "======================================================="
echo ""

# Test API routes
echo "Testing API Health Check:"
curl -s "http://localhost:3000/api/health" | jq . 2>/dev/null || echo "API not responding with JSON"
echo ""

echo "Testing Backend API (if running on port 5001):"
curl -s "http://localhost:5001/api/health" | jq . 2>/dev/null || echo "Backend API not responding"
echo ""