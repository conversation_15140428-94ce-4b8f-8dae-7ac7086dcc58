/* 
 * SEO SAAS Form Components
 * Advanced form styling system with professional validation
 * Enterprise-grade user experience
 */

/* ===========================
   FORM FOUNDATION
   =========================== */

.form {
  width: 100%;
}

.form-container {
  background: white;
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.form-header {
  margin-bottom: var(--space-6);
  text-align: center;
}

.form-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.form-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

/* ===========================
   FORM GROUPS & LAYOUT
   =========================== */

.form-group {
  margin-bottom: var(--space-6);
  position: relative;
}

.form-group.required .form-label::after {
  content: ' *';
  color: var(--error-500);
}

.form-row {
  display: flex;
  gap: var(--space-4);
  align-items: flex-start;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

.form-section {
  margin-bottom: var(--space-8);
  padding-bottom: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.form-section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.form-section-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: var(--space-6);
}

/* ===========================
   LABELS & HELP TEXT
   =========================== */

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: var(--space-2);
  line-height: 1.25;
}

.form-label-inline {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.form-help {
  margin-top: var(--space-1);
  font-size: 0.75rem;
  color: var(--gray-500);
  line-height: 1.4;
}

.form-help.important {
  color: var(--warning-600);
  font-weight: 500;
}

/* ===========================
   INPUT FIELDS
   =========================== */

.form-input,
.form-select,
.form-textarea {
  display: block;
  width: 100%;
  padding: var(--space-3);
  font-size: 0.875rem;
  font-family: var(--font-primary);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-lg);
  background-color: white;
  color: var(--text-primary);
  transition: all 0.2s ease-in-out;
  line-height: 1.5;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:disabled,
.form-select:disabled,
.form-textarea:disabled {
  background-color: var(--gray-100);
  color: var(--gray-500);
  cursor: not-allowed;
  opacity: 0.7;
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--gray-400);
}

/* Input sizes */
.form-input.sm,
.form-select.sm {
  padding: var(--space-2) var(--space-3);
  font-size: 0.75rem;
}

.form-input.lg,
.form-select.lg {
  padding: var(--space-4) var(--space-4);
  font-size: 1rem;
}

/* Textarea specific */
.form-textarea {
  min-height: 120px;
  resize: vertical;
  font-family: var(--font-primary);
}

.form-textarea.sm {
  min-height: 80px;
  padding: var(--space-2) var(--space-3);
  font-size: 0.75rem;
}

.form-textarea.lg {
  min-height: 160px;
  padding: var(--space-4);
  font-size: 1rem;
}

/* ===========================
   INPUT GROUPS & ADDONS
   =========================== */

.input-group {
  position: relative;
  display: flex;
  align-items: stretch;
}

.input-group .form-input {
  border-radius: 0;
  border-right: none;
}

.input-group .form-input:first-child {
  border-top-left-radius: var(--radius-lg);
  border-bottom-left-radius: var(--radius-lg);
}

.input-group .form-input:last-child {
  border-top-right-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
  border-right: 1px solid var(--gray-300);
}

.input-addon {
  display: flex;
  align-items: center;
  padding: var(--space-3);
  background-color: var(--gray-50);
  border: 1px solid var(--gray-300);
  font-size: 0.875rem;
  color: var(--gray-600);
  white-space: nowrap;
}

.input-addon.prefix {
  border-right: none;
  border-top-left-radius: var(--radius-lg);
  border-bottom-left-radius: var(--radius-lg);
}

.input-addon.suffix {
  border-left: none;
  border-top-right-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
}

.input-group .form-input:focus + .input-addon,
.input-addon + .form-input:focus {
  border-color: var(--primary-500);
}

/* Input with icons */
.input-with-icon {
  position: relative;
}

.input-with-icon .form-input {
  padding-left: var(--space-10);
}

.input-with-icon.icon-right .form-input {
  padding-left: var(--space-3);
  padding-right: var(--space-10);
}

.input-icon {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
  width: 16px;
  height: 16px;
  pointer-events: none;
}

.input-with-icon.icon-right .input-icon {
  left: auto;
  right: var(--space-3);
}

/* ===========================
   SELECT COMPONENTS
   =========================== */

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--space-3) center;
  background-repeat: no-repeat;
  background-size: 16px 16px;
  padding-right: var(--space-10);
  appearance: none;
}

.form-select option {
  padding: var(--space-2);
}

/* Custom select */
.select-custom {
  position: relative;
}

.select-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: var(--space-3);
  font-size: 0.875rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-lg);
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.select-trigger:hover {
  border-color: var(--gray-400);
}

.select-trigger.open {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.select-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 50;
  background-color: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  margin-top: var(--space-1);
  max-height: 200px;
  overflow-y: auto;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease-in-out;
}

.select-options.open {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.select-option {
  padding: var(--space-3);
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
}

.select-option:hover {
  background-color: var(--gray-100);
}

.select-option.selected {
  background-color: var(--primary-100);
  color: var(--primary-700);
  font-weight: 500;
}

/* ===========================
   CHECKBOX & RADIO COMPONENTS
   =========================== */

.form-checkbox,
.form-radio {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  cursor: pointer;
}

.form-checkbox input,
.form-radio input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.checkbox-indicator,
.radio-indicator {
  width: 20px;
  height: 20px;
  border: 2px solid var(--gray-300);
  background-color: white;
  transition: all 0.2s ease-in-out;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1px;
}

.checkbox-indicator {
  border-radius: var(--radius-sm);
}

.radio-indicator {
  border-radius: 50%;
}

.form-checkbox:hover .checkbox-indicator,
.form-radio:hover .radio-indicator {
  border-color: var(--primary-500);
}

.form-checkbox input:checked + .checkbox-indicator,
.form-radio input:checked + .radio-indicator {
  background-color: var(--primary-600);
  border-color: var(--primary-600);
}

.form-checkbox input:checked + .checkbox-indicator::after {
  content: '';
  width: 8px;
  height: 8px;
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3-3a.5.5 0 1 1 .708-.708L6 9.793l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
  background-size: 12px 12px;
  background-repeat: no-repeat;
  background-position: center;
}

.form-radio input:checked + .radio-indicator::after {
  content: '';
  width: 8px;
  height: 8px;
  background-color: white;
  border-radius: 50%;
}

.checkbox-label,
.radio-label {
  font-size: 0.875rem;
  color: var(--text-primary);
  line-height: 1.5;
}

/* Checkbox and radio groups */
.checkbox-group,
.radio-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.checkbox-group.horizontal,
.radio-group.horizontal {
  flex-direction: row;
  flex-wrap: wrap;
  gap: var(--space-6);
}

/* ===========================
   FILE UPLOAD COMPONENTS
   =========================== */

.file-upload {
  position: relative;
}

.file-upload-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.file-upload-area {
  border: 2px dashed var(--gray-300);
  border-radius: var(--radius-lg);
  padding: var(--space-8);
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  background-color: var(--gray-50);
}

.file-upload-area:hover {
  border-color: var(--primary-500);
  background-color: var(--primary-50);
}

.file-upload-area.dragover {
  border-color: var(--primary-600);
  background-color: var(--primary-100);
}

.file-upload-icon {
  width: 48px;
  height: 48px;
  color: var(--gray-400);
  margin: 0 auto var(--space-4);
}

.file-upload-text {
  font-size: 0.875rem;
  color: var(--gray-600);
  margin-bottom: var(--space-2);
}

.file-upload-hint {
  font-size: 0.75rem;
  color: var(--gray-500);
}

.file-upload-list {
  margin-top: var(--space-4);
}

.file-upload-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-3);
  background-color: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-2);
}

.file-upload-item:last-child {
  margin-bottom: 0;
}

.file-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.file-icon {
  width: 24px;
  height: 24px;
  color: var(--gray-400);
}

.file-details {
  display: flex;
  flex-direction: column;
}

.file-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

.file-size {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.file-remove {
  background: none;
  border: none;
  color: var(--gray-400);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-sm);
  transition: all 0.2s ease-in-out;
}

.file-remove:hover {
  color: var(--error-600);
  background-color: var(--error-50);
}

/* ===========================
   FORM VALIDATION STATES
   =========================== */

.form-group.error .form-input,
.form-group.error .form-select,
.form-group.error .form-textarea {
  border-color: var(--error-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-group.error .form-label {
  color: var(--error-600);
}

.form-group.success .form-input,
.form-group.success .form-select,
.form-group.success .form-textarea {
  border-color: var(--success-500);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-group.warning .form-input,
.form-group.warning .form-select,
.form-group.warning .form-textarea {
  border-color: var(--warning-500);
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

.form-error {
  margin-top: var(--space-1);
  font-size: 0.75rem;
  color: var(--error-600);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.form-success {
  margin-top: var(--space-1);
  font-size: 0.75rem;
  color: var(--success-600);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.form-warning {
  margin-top: var(--space-1);
  font-size: 0.75rem;
  color: var(--warning-600);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.validation-icon {
  width: 12px;
  height: 12px;
  flex-shrink: 0;
}

/* ===========================
   MULTI-STEP FORM WIZARD
   =========================== */

.form-wizard {
  width: 100%;
}

.wizard-header {
  margin-bottom: var(--space-8);
}

.wizard-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-6);
}

.wizard-step {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: 0.875rem;
  color: var(--gray-500);
}

.wizard-step:not(:last-child)::after {
  content: '';
  width: 40px;
  height: 1px;
  background-color: var(--gray-300);
  margin: 0 var(--space-4);
}

.wizard-step.active {
  color: var(--primary-600);
  font-weight: 600;
}

.wizard-step.completed {
  color: var(--success-600);
}

.wizard-step.completed::after {
  background-color: var(--success-600);
}

.step-indicator {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid var(--gray-300);
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.wizard-step.active .step-indicator {
  border-color: var(--primary-600);
  background-color: var(--primary-600);
  color: white;
}

.wizard-step.completed .step-indicator {
  border-color: var(--success-600);
  background-color: var(--success-600);
  color: white;
}

.wizard-content {
  margin-bottom: var(--space-8);
}

.wizard-step-content {
  display: none;
}

.wizard-step-content.active {
  display: block;
  animation: fadeIn 0.3s ease-in-out;
}

/* ===========================
   FORM ACTIONS
   =========================== */

.form-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
  padding-top: var(--space-6);
  border-top: 1px solid var(--gray-200);
  margin-top: var(--space-6);
}

.form-actions.centered {
  justify-content: center;
}

.form-actions.space-between {
  justify-content: space-between;
}

.form-actions.stacked {
  flex-direction: column;
  align-items: stretch;
}

/* ===========================
   RESPONSIVE FORM DESIGN
   =========================== */

@media (max-width: 768px) {
  .form-container {
    padding: var(--space-4);
  }
  
  .form-row {
    flex-direction: column;
  }
  
  .form-row .form-group {
    margin-bottom: var(--space-4);
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .checkbox-group.horizontal,
  .radio-group.horizontal {
    flex-direction: column;
    gap: var(--space-3);
  }
  
  .wizard-steps {
    overflow-x: auto;
    padding-bottom: var(--space-2);
  }
  
  .wizard-step {
    flex-shrink: 0;
  }
  
  .input-group {
    flex-direction: column;
  }
  
  .input-group .form-input {
    border-radius: var(--radius-lg);
    border-right: 1px solid var(--gray-300);
  }
  
  .input-addon {
    border-radius: var(--radius-lg);
    justify-content: center;
  }
}