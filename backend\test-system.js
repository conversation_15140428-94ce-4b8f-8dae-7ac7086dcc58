#!/usr/bin/env node

/**
 * SEO SAAS Backend System Test
 * Comprehensive validation of content generation backend with demo data protection
 */

import { DemoDataValidator } from '../utils/demoDataDetection.js';
import { AuthorityLinkValidator } from '../utils/authorityLinkValidator.js';
import fetch from 'node-fetch';
import chalk from 'chalk';

const BASE_URL = 'http://localhost:5000';

class SystemTester {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      tests: []
    };
  }

  log(message, type = 'info') {
    const colors = {
      info: chalk.blue,
      success: chalk.green,
      error: chalk.red,
      warning: chalk.yellow
    };
    console.log(colors[type](`[${type.toUpperCase()}] ${message}`));
  }

  async test(name, testFn) {
    try {
      this.log(`Testing: ${name}`);
      await testFn();
      this.results.passed++;
      this.results.tests.push({ name, status: 'PASSED' });
      this.log(`✅ ${name}`, 'success');
    } catch (error) {
      this.results.failed++;
      this.results.tests.push({ name, status: 'FAILED', error: error.message });
      this.log(`❌ ${name}: ${error.message}`, 'error');
    }
  }

  async makeRequest(endpoint, data = null) {
    const options = {
      method: data ? 'POST' : 'GET',
      headers: { 'Content-Type': 'application/json' }
    };
    
    if (data) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    const result = await response.json();
    
    return { status: response.status, data: result };
  }

  async runTests() {
    this.log('🚀 Starting SEO SAAS Backend System Tests', 'info');
    
    // Test 1: Server Health Check
    await this.test('Server Health Check', async () => {
      const { status, data } = await this.makeRequest('/api/health');
      if (status !== 200) throw new Error('Server not responding');
      if (data.status !== 'OK') throw new Error('Server unhealthy');
    });

    // Test 2: Content System Health
    await this.test('Content System Health', async () => {
      const { status, data } = await this.makeRequest('/api/content/health');
      if (status !== 200) throw new Error('Content system not responding');
      if (!data.health.services.demoValidator) throw new Error('Demo validator not active');
    });

    // Test 3: Authority Links System Health
    await this.test('Authority Links System Health', async () => {
      const { status, data } = await this.makeRequest('/api/authority-links/health');
      if (status !== 200) throw new Error('Authority links system not responding');
      if (!data.health.capabilities.demoDataRejection) throw new Error('Demo data rejection not active');
    });

    // Test 4: Demo Data Validator Unit Tests
    await this.test('Demo Data Validator - Basic Patterns', async () => {
      const demoInputs = ['example keyword', 'test data', 'placeholder content'];
      demoInputs.forEach(input => {
        const result = DemoDataValidator.validate(input);
        if (result.isValid) throw new Error(`Should reject: ${input}`);
        if (!result.isDemo) throw new Error(`Should detect demo data: ${input}`);
      });
    });

    await this.test('Demo Data Validator - Real Data Acceptance', async () => {
      const realInputs = ['artificial intelligence', 'sustainable energy', 'cloud computing'];
      realInputs.forEach(input => {
        const result = DemoDataValidator.validate(input);
        if (!result.isValid) throw new Error(`Should accept: ${input}`);
        if (result.isDemo) throw new Error(`Should not detect as demo: ${input}`);
      });
    });

    // Test 5: Input Validation API
    await this.test('Content Input Validation - Demo Data Rejection', async () => {
      const demoData = {
        target_keyword: 'example keyword',
        location: 'Test City',
        content_type: 'blog_post'
      };
      
      const { status, data } = await this.makeRequest('/api/content/validate-input', demoData);
      if (status !== 200) throw new Error('Validation endpoint not responding');
      if (data.validation.isValid) throw new Error('Should reject demo data');
    });

    await this.test('Content Input Validation - Real Data Acceptance', async () => {
      const realData = {
        target_keyword: 'machine learning applications',
        location: 'San Francisco',
        content_type: 'blog_post'
      };
      
      const { status, data } = await this.makeRequest('/api/content/validate-input', realData);
      if (status !== 200) throw new Error('Validation endpoint not responding');
      if (!data.validation.isValid) throw new Error('Should accept real data');
    });

    // Test 6: Authority Links Demo Data Detection
    await this.test('Authority Links - Demo Data Detection', async () => {
      const demoLinks = [{
        url: 'https://example.com/article',
        title: 'Example Article',
        description: 'Sample description',
        domain: 'example.com',
        authorityScore: 80,
        relevanceScore: 0.9
      }];
      
      const { status, data } = await this.makeRequest('/api/authority-links/check-demo-data', { links: demoLinks });
      if (status !== 200) throw new Error('Demo data check not responding');
      if (data.data.summary.demoLinksFound !== 1) throw new Error('Should detect 1 demo link');
    });

    await this.test('Authority Links - Real Link Acceptance', async () => {
      const realLinks = [{
        url: 'https://wikipedia.org/wiki/Machine_learning',
        title: 'Machine Learning - Wikipedia',
        description: 'Comprehensive article about machine learning',
        domain: 'wikipedia.org',
        authorityScore: 95,
        relevanceScore: 0.95
      }];
      
      const { status, data } = await this.makeRequest('/api/authority-links/check-demo-data', { links: realLinks });
      if (status !== 200) throw new Error('Demo data check not responding');
      if (data.data.summary.demoLinksFound !== 0) throw new Error('Should not detect demo links');
    });

    // Test 7: Rate Limiting
    await this.test('Rate Limiting Protection', async () => {
      // Make multiple rapid requests to trigger rate limiting
      const requests = Array(6).fill().map(() => 
        this.makeRequest('/api/content/validate-input', { target_keyword: 'test' })
      );
      
      const responses = await Promise.all(requests.map(p => p.catch(e => ({ status: 500, error: e.message }))));
      const rateLimited = responses.some(res => res.status === 429);
      
      if (!rateLimited) {
        this.log('Rate limiting may not be active (could be normal in testing)', 'warning');
      }
    });

    // Test 8: Cache System
    await this.test('Cache System Statistics', async () => {
      const { status, data } = await this.makeRequest('/api/cache/stats');
      if (status !== 200) throw new Error('Cache stats not available');
      if (!data.cache) throw new Error('Cache data not present');
    });

    // Test 9: Sequential Content Generation (if OpenAI key available)
    if (process.env.OPENAI_API_KEY) {
      await this.test('Sequential Content Generation - Demo Rejection', async () => {
        const demoRequest = {
          target_keyword: 'example keyword',
          content_type: 'blog_post',
          word_count: 500
        };
        
        const { status } = await this.makeRequest('/api/content/generate', demoRequest);
        if (status !== 400) throw new Error('Should reject demo data in content generation');
      });

      await this.test('Sequential Content Generation - Real Data', async () => {
        const realRequest = {
          target_keyword: 'renewable energy benefits',
          location: 'Austin',
          content_type: 'blog_post',
          word_count: 500,
          tone: 'professional'
        };
        
        try {
          const { status, data } = await this.makeRequest('/api/content/generate', realRequest);
          if (status === 200 && data.success) {
            this.log('✨ Content generation working with real data', 'success');
          } else {
            this.log('Content generation endpoint responding but may need API key configuration', 'warning');
          }
        } catch (error) {
          this.log('Content generation may need OpenAI API key configuration', 'warning');
        }
      });
    } else {
      this.log('⚠️  OpenAI API key not found - skipping content generation tests', 'warning');
    }

    // Summary
    this.log('\n🏁 Test Results Summary', 'info');
    this.log(`✅ Passed: ${this.results.passed}`, 'success');
    this.log(`❌ Failed: ${this.results.failed}`, this.results.failed > 0 ? 'error' : 'info');
    
    if (this.results.failed > 0) {
      this.log('\nFailed Tests:', 'error');
      this.results.tests
        .filter(test => test.status === 'FAILED')
        .forEach(test => this.log(`  - ${test.name}: ${test.error}`, 'error'));
    }

    const overallStatus = this.results.failed === 0 ? 'ALL SYSTEMS OPERATIONAL' : 'ISSUES DETECTED';
    this.log(`\n🎯 Overall Status: ${overallStatus}`, this.results.failed === 0 ? 'success' : 'error');
    
    return this.results.failed === 0;
  }
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new SystemTester();
  
  console.log(chalk.cyan('\n🔬 SEO SAAS Backend System Validation'));
  console.log(chalk.cyan('=====================================\n'));
  
  tester.runTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error(chalk.red('❌ Test runner failed:'), error);
      process.exit(1);
    });
}

export default SystemTester;
