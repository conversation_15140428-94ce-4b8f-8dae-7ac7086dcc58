'use client';

import React from 'react';
import { ProtectedRoute } from '@/hooks/useAuth';
import { AuthenticatedLayout, PageHeader } from '@/components/Layout/DashboardLayout';
import { ActivityFeed } from '@/components/UI/ActivityFeed';
import { ArrowPathIcon } from '@heroicons/react/24/outline';

export default function ActivityPage() {
  return (
    <ProtectedRoute>
      <AuthenticatedLayout>
        <PageHeader
          title="Activity Feed"
          description="Live updates on all your SEO activities and content generation"
          actions={
            <div className="flex items-center space-x-4">
              <button
                onClick={() => window.location.reload()}
                className="btn btn-secondary"
              >
                <ArrowPathIcon className="w-4 h-4 mr-2" />
                Refresh
              </button>
            </div>
          }
        />
        
        <div className="max-w-4xl mx-auto">
          <ActivityFeed 
            className="space-y-6"
            maxItems={50}
            showRealTimeIndicator={true}
          />
        </div>
      </AuthenticatedLayout>
    </ProtectedRoute>
  );
}