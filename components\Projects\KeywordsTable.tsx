/**
 * KeywordsTable Component
 * Enterprise SEO SAAS - Keywords tracking and management interface
 */

import { useState } from 'react'
import { Project, ProjectKeyword } from '@/types/project'
import { validateKeyword } from '@/utils/projectHelpers'
import {
  MagnifyingGlassIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  MinusIcon,
  ChartBarIcon,
  TrendingUpIcon,
  TrendingDownIcon
} from '@heroicons/react/24/outline'

interface KeywordsTableProps {
  project: Project
  limit?: number
  showActions?: boolean
  onProjectUpdate?: (project: Project) => void
}

export default function KeywordsTable({ project, limit, showActions = true, onProjectUpdate }: KeywordsTableProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState<'keyword' | 'priority' | 'searchVolume' | 'difficulty' | 'currentRanking'>('keyword')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [showAddKeyword, setShowAddKeyword] = useState(false)
  const [newKeyword, setNewKeyword] = useState('')
  const [newPriority, setNewPriority] = useState<'high' | 'medium' | 'low'>('medium')
  const [editingKeyword, setEditingKeyword] = useState<string | null>(null)

  const filteredKeywords = project.keywords.filter(keyword =>
    keyword.keyword.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const sortedKeywords = [...filteredKeywords].sort((a, b) => {
    let aVal: any, bVal: any

    switch (sortBy) {
      case 'keyword':
        aVal = a.keyword.toLowerCase()
        bVal = b.keyword.toLowerCase()
        break
      case 'priority':
        const priorityOrder = { high: 3, medium: 2, low: 1 }
        aVal = priorityOrder[a.priority]
        bVal = priorityOrder[b.priority]
        break
      case 'searchVolume':
        aVal = a.searchVolume || 0
        bVal = b.searchVolume || 0
        break
      case 'difficulty':
        aVal = a.difficulty || 0
        bVal = b.difficulty || 0
        break
      case 'currentRanking':
        aVal = a.currentRanking || 999
        bVal = b.currentRanking || 999
        break
      default:
        aVal = a.keyword
        bVal = b.keyword
    }

    if (aVal < bVal) return sortOrder === 'asc' ? -1 : 1
    if (aVal > bVal) return sortOrder === 'asc' ? 1 : -1
    return 0
  })

  const displayedKeywords = limit ? sortedKeywords.slice(0, limit) : sortedKeywords

  const handleSort = (column: typeof sortBy) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(column)
      setSortOrder('asc')
    }
  }

  const handleAddKeyword = async () => {
    if (!newKeyword.trim()) return

    // Validate keyword using existing validation
    const validation = validateKeyword(newKeyword.trim())
    if (!validation.isValid) {
      alert(`Invalid keyword: ${validation.errors[0]}`)
      return
    }

    const keyword: ProjectKeyword = {
      id: `kw_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      keyword: newKeyword.trim(),
      priority: newPriority,
      createdAt: new Date().toISOString(),
      // Mock data for demonstration (in real app, would come from keyword research API)
      searchVolume: Math.floor(Math.random() * 10000) + 100,
      difficulty: Math.floor(Math.random() * 100) + 1,
      cpc: Math.random() * 5 + 0.1,
      intent: ['informational', 'commercial', 'navigational', 'transactional'][Math.floor(Math.random() * 4)] as any
    }

    const updatedProject = {
      ...project,
      keywords: [...project.keywords, keyword],
      updatedAt: new Date().toISOString()
    }

    onProjectUpdate?.(updatedProject)
    setNewKeyword('')
    setShowAddKeyword(false)
  }

  const handleDeleteKeyword = (keywordId: string) => {
    if (confirm('Are you sure you want to remove this keyword?')) {
      const updatedProject = {
        ...project,
        keywords: project.keywords.filter(k => k.id !== keywordId),
        updatedAt: new Date().toISOString()
      }
      onProjectUpdate?.(updatedProject)
    }
  }

  const handleUpdatePriority = (keywordId: string, priority: 'high' | 'medium' | 'low') => {
    const updatedProject = {
      ...project,
      keywords: project.keywords.map(k => 
        k.id === keywordId ? { ...k, priority } : k
      ),
      updatedAt: new Date().toISOString()
    }
    onProjectUpdate?.(updatedProject)
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getRankingTrend = (current?: number, target?: number) => {
    if (!current || !target) return null
    
    const diff = target - current
    if (diff > 0) {
      return <ArrowUpIcon className="h-4 w-4 text-green-600" />
    } else if (diff < 0) {
      return <ArrowDownIcon className="h-4 w-4 text-red-600" />
    }
    return <MinusIcon className="h-4 w-4 text-gray-400" />
  }

  const SortHeader = ({ column, children }: { column: typeof sortBy, children: React.ReactNode }) => (
    <th 
      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-50"
      onClick={() => handleSort(column)}
    >
      <div className="flex items-center gap-1">
        {children}
        {sortBy === column && (
          sortOrder === 'asc' ? 
            <ArrowUpIcon className="h-3 w-3" /> : 
            <ArrowDownIcon className="h-3 w-3" />
        )}
      </div>
    </th>
  )

  if (!showActions && displayedKeywords.length === 0) {
    return (
      <div className="text-center py-8">
        <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
        <p className="text-gray-500">No keywords tracked</p>
        <p className="text-sm text-gray-400">Add keywords to start tracking rankings</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Search and Add Button */}
      {showActions && (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="relative flex-1 max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search keywords..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-white text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <button
            onClick={() => setShowAddKeyword(true)}
            className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Keyword
          </button>
        </div>
      )}

      {/* Add Keyword Form */}
      {showAddKeyword && showActions && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Add New Keyword</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div className="md:col-span-2">
              <input
                type="text"
                placeholder="Enter real target keyword (no demo data)"
                value={newKeyword}
                onChange={(e) => setNewKeyword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                onKeyPress={(e) => e.key === 'Enter' && handleAddKeyword()}
              />
            </div>
            <div>
              <select
                value={newPriority}
                onChange={(e) => setNewPriority(e.target.value as any)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="high">High Priority</option>
                <option value="medium">Medium Priority</option>
                <option value="low">Low Priority</option>
              </select>
            </div>
          </div>
          <div className="flex justify-end gap-2 mt-3">
            <button
              onClick={() => setShowAddKeyword(false)}
              className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
            >
              Cancel
            </button>
            <button
              onClick={handleAddKeyword}
              className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded"
            >
              Add Keyword
            </button>
          </div>
        </div>
      )}

      {/* Keywords Table */}
      {displayedKeywords.length > 0 ? (
        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <SortHeader column="keyword">Keyword</SortHeader>
                  <SortHeader column="priority">Priority</SortHeader>
                  <SortHeader column="searchVolume">Search Volume</SortHeader>
                  <SortHeader column="difficulty">Difficulty</SortHeader>
                  <SortHeader column="currentRanking">Current Rank</SortHeader>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Intent
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    CPC
                  </th>
                  {showActions && (
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  )}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {displayedKeywords.map((keyword) => (
                  <tr key={keyword.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="font-medium text-gray-900">{keyword.keyword}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {showActions ? (
                        <select
                          value={keyword.priority}
                          onChange={(e) => handleUpdatePriority(keyword.id, e.target.value as any)}
                          className={`
                            text-xs font-medium px-2 py-1 rounded-full border-0 focus:ring-2 focus:ring-blue-500
                            ${getPriorityColor(keyword.priority)}
                          `}
                        >
                          <option value="high">High</option>
                          <option value="medium">Medium</option>
                          <option value="low">Low</option>
                        </select>
                      ) : (
                        <span className={`
                          inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                          ${getPriorityColor(keyword.priority)}
                        `}>
                          {keyword.priority}
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {keyword.searchVolume?.toLocaleString() || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-900">{keyword.difficulty || '-'}</span>
                        {keyword.difficulty && (
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div 
                              className={`h-2 rounded-full ${
                                keyword.difficulty < 30 ? 'bg-green-500' :
                                keyword.difficulty < 70 ? 'bg-yellow-500' : 'bg-red-500'
                              }`}
                              style={{ width: `${Math.min(keyword.difficulty, 100)}%` }}
                            />
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-900">
                          {keyword.currentRanking ? `#${keyword.currentRanking}` : '-'}
                        </span>
                        {getRankingTrend(keyword.currentRanking, keyword.targetRanking)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm text-gray-600 capitalize">
                        {keyword.intent || '-'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {keyword.cpc ? `$${keyword.cpc.toFixed(2)}` : '-'}
                    </td>
                    {showActions && (
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => setEditingKeyword(keyword.id)}
                            className="text-gray-400 hover:text-gray-600"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteKeyword(keyword.id)}
                            className="text-gray-400 hover:text-red-600"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        // Empty state
        <div className="text-center py-12">
          <ChartBarIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No keywords found</h3>
          <p className="text-gray-600 mb-6">
            {searchTerm
              ? 'Try adjusting your search term'
              : 'Add keywords to start tracking your SEO performance'
            }
          </p>
          {showActions && (
            <button
              onClick={() => setShowAddKeyword(true)}
              className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Your First Keyword
            </button>
          )}
        </div>
      )}

      {/* Load More Button (if limited) */}
      {limit && filteredKeywords.length > limit && (
        <div className="text-center">
          <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
            View All Keywords ({filteredKeywords.length})
          </button>
        </div>
      )}
    </div>
  )
}