'use client';

import React, { useState, useEffect } from 'react';
import { EnhancedProtectedRoute, useUnifiedAuth } from '@/hooks/useUnifiedAuth';
import { AuthenticatedLayout } from '@/components/Layout/DashboardLayout';
import { <PERSON><PERSON>, <PERSON>, CardHeader, CardContent, Badge } from '@/components/UI';
import { useNotifications } from '@/components/Notifications';
import {
  CogIcon,
  BellIcon,
  KeyIcon,
  ShieldCheckIcon,
  GlobeAltIcon,
  EyeIcon,
  EyeSlashIcon,
  ClipboardDocumentIcon,
  TrashIcon,
  PlusIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

interface APIKey {
  id: string;
  name: string;
  key: string;
  created_at: string;
  last_used: string | null;
  is_active: boolean;
}

interface UserSettings {
  notifications: {
    email_reports: boolean;
    content_completion: boolean;
    usage_alerts: boolean;
    security_alerts: boolean;
  };
  preferences: {
    default_content_type: string;
    default_word_count: number;
    default_tone: string;
    auto_save: boolean;
  };
  privacy: {
    analytics_tracking: boolean;
    data_sharing: boolean;
    public_profile: boolean;
  };
}

export default function SettingsPage() {
  const { user, userProfile, isDemoMode, userTier } = useUnifiedAuth();
  const { success: notifySuccess, error: notifyError } = useNotifications();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  
  // API Keys state
  const [apiKeys, setApiKeys] = useState<APIKey[]>([]);
  const [showCreateApiKey, setShowCreateApiKey] = useState(false);
  const [newApiKeyName, setNewApiKeyName] = useState('');
  const [visibleKeys, setVisibleKeys] = useState<Set<string>>(new Set());

  // Settings state
  const [settings, setSettings] = useState<UserSettings>({
    notifications: {
      email_reports: true,
      content_completion: true,
      usage_alerts: true,
      security_alerts: true
    },
    preferences: {
      default_content_type: 'blog_post',
      default_word_count: 1500,
      default_tone: 'professional',
      auto_save: true
    },
    privacy: {
      analytics_tracking: true,
      data_sharing: false,
      public_profile: false
    }
  });

  useEffect(() => {
    loadUserSettings();
    loadApiKeys();
  }, []);

  const loadUserSettings = async () => {
    try {
      // In a real app, this would fetch from the backend
      // For now, we'll use localStorage as a fallback
      const savedSettings = localStorage.getItem('userSettings');
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const loadApiKeys = async () => {
    try {
      // Mock API keys for now
      setApiKeys([
        {
          id: '1',
          name: 'Production API',
          key: 'sk_live_1234567890abcdef',
          created_at: '2024-01-15T10:30:00Z',
          last_used: '2024-01-20T14:20:00Z',
          is_active: true
        },
        {
          id: '2',
          name: 'Development API',
          key: 'sk_test_abcdef1234567890',
          created_at: '2024-01-10T09:15:00Z',
          last_used: null,
          is_active: true
        }
      ]);
    } catch (error) {
      console.error('Error loading API keys:', error);
    }
  };

  const saveSettings = async () => {
    setLoading(true);

    try {
      // In a real app, this would save to the backend
      localStorage.setItem('userSettings', JSON.stringify(settings));
      notifySuccess('Settings Saved', 'Your preferences have been updated successfully!');
    } catch (error) {
      notifyError('Save Failed', 'Failed to save settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSettingChange = (category: keyof UserSettings, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
  };

  const createApiKey = async () => {
    if (!newApiKeyName.trim()) {
      notifyError('Validation Error', 'Please enter a name for the API key');
      return;
    }

    try {
      const newKey: APIKey = {
        id: Date.now().toString(),
        name: newApiKeyName,
        key: `sk_live_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`,
        created_at: new Date().toISOString(),
        last_used: null,
        is_active: true
      };

      setApiKeys(prev => [...prev, newKey]);
      setNewApiKeyName('');
      setShowCreateApiKey(false);
      notifySuccess('API Key Created', 'Your new API key has been generated successfully!');
    } catch (error) {
      notifyError('Creation Failed', 'Failed to create API key');
    }
  };

  const toggleKeyVisibility = (keyId: string) => {
    setVisibleKeys(prev => {
      const newSet = new Set(prev);
      if (newSet.has(keyId)) {
        newSet.delete(keyId);
      } else {
        newSet.add(keyId);
      }
      return newSet;
    });
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    notifySuccess('Copied!', 'API key copied to clipboard');
  };

  const deleteApiKey = (keyId: string) => {
    setApiKeys(prev => prev.filter(key => key.id !== keyId));
    notifySuccess('API Key Deleted', 'The API key has been removed successfully');
  };

  const maskApiKey = (key: string) => {
    return key.slice(0, 8) + '••••••••••••••••' + key.slice(-4);
  };

  const tabs = [
    { id: 'general', name: 'General', icon: CogIcon },
    { id: 'notifications', name: 'Notifications', icon: BellIcon },
    { id: 'api-keys', name: 'API Keys', icon: KeyIcon },
    { id: 'privacy', name: 'Privacy & Security', icon: ShieldCheckIcon }
  ];

  return (
    <EnhancedProtectedRoute>
      <AuthenticatedLayout>
        <div className="max-w-6xl mx-auto space-y-6">
          {/* Page Header */}
          <div className="border-b border-gray-200 dark:border-gray-700 pb-5">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Settings</h1>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Manage your account preferences and application settings
                </p>
              </div>
              <div className="flex items-center space-x-2">
                {isDemoMode && (
                  <Badge variant="info">Demo Mode</Badge>
                )}
                <Badge variant={userTier === 'enterprise' ? 'secondary' : userTier === 'pro' ? 'primary' : 'default'}>
                  {userTier} Plan
                </Badge>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Settings Navigation */}
            <div className="lg:col-span-1">
              <Card>
                <CardContent className="p-4">
                  <nav className="space-y-1">
                    {tabs.map((tab) => {
                      const Icon = tab.icon;
                      return (
                        <button
                          key={tab.id}
                          onClick={() => setActiveTab(tab.id)}
                          className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                            activeTab === tab.id
                              ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300'
                              : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700'
                          }`}
                        >
                          <Icon className="h-5 w-5 mr-3" />
                          {tab.name}
                        </button>
                      );
                    })}
                  </nav>
                </CardContent>
              </Card>
            </div>

            {/* Settings Content */}
            <div className="lg:col-span-3">
              <Card>
                <CardContent>
                  {/* General Settings */}
                  {activeTab === 'general' && (
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">
                          Content Preferences
                        </h3>
                        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                          Set your default content generation preferences
                        </p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700">
                            Default Content Type
                          </label>
                          <select
                            value={settings.preferences.default_content_type}
                            onChange={(e) => handleSettingChange('preferences', 'default_content_type', e.target.value)}
                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                          >
                            <option value="blog_post">Blog Post</option>
                            <option value="article">Article</option>
                            <option value="product_description">Product Description</option>
                            <option value="landing_page">Landing Page</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700">
                            Default Word Count
                          </label>
                          <input
                            type="number"
                            value={settings.preferences.default_word_count}
                            onChange={(e) => handleSettingChange('preferences', 'default_word_count', parseInt(e.target.value))}
                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            min="100"
                            max="10000"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700">
                            Default Tone
                          </label>
                          <select
                            value={settings.preferences.default_tone}
                            onChange={(e) => handleSettingChange('preferences', 'default_tone', e.target.value)}
                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                          >
                            <option value="professional">Professional</option>
                            <option value="casual">Casual</option>
                            <option value="friendly">Friendly</option>
                            <option value="authoritative">Authoritative</option>
                          </select>
                        </div>

                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            checked={settings.preferences.auto_save}
                            onChange={(e) => handleSettingChange('preferences', 'auto_save', e.target.checked)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <label className="ml-2 block text-sm text-gray-900">
                            Auto-save drafts
                          </label>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Notifications Settings */}
                  {activeTab === 'notifications' && (
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-lg leading-6 font-medium text-gray-900">
                          Notification Preferences
                        </h3>
                        <p className="mt-1 text-sm text-gray-500">
                          Choose which notifications you'd like to receive
                        </p>
                      </div>

                      <div className="space-y-4">
                        {Object.entries(settings.notifications).map(([key, value]) => (
                          <div key={key} className="flex items-center justify-between">
                            <div>
                              <h4 className="text-sm font-medium text-gray-900 capitalize">
                                {key.replace('_', ' ')}
                              </h4>
                              <p className="text-sm text-gray-500">
                                {key === 'email_reports' && 'Receive weekly analytics reports via email'}
                                {key === 'content_completion' && 'Get notified when content generation is complete'}
                                {key === 'usage_alerts' && 'Alerts when approaching usage limits'}
                                {key === 'security_alerts' && 'Important security and account notifications'}
                              </p>
                            </div>
                            <input
                              type="checkbox"
                              checked={value}
                              onChange={(e) => handleSettingChange('notifications', key, e.target.checked)}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* API Keys */}
                  {activeTab === 'api-keys' && (
                    <div className="space-y-6">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="text-lg leading-6 font-medium text-gray-900">
                            API Keys
                          </h3>
                          <p className="mt-1 text-sm text-gray-500">
                            Manage your API keys for programmatic access
                          </p>
                        </div>
                        <button
                          onClick={() => setShowCreateApiKey(true)}
                          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                        >
                          <PlusIcon className="h-4 w-4 mr-1" />
                          Create Key
                        </button>
                      </div>

                      {/* Create API Key Form */}
                      {showCreateApiKey && (
                        <div className="border border-gray-200 rounded-lg p-4">
                          <h4 className="text-sm font-medium text-gray-900 mb-3">Create New API Key</h4>
                          <div className="flex space-x-3">
                            <input
                              type="text"
                              value={newApiKeyName}
                              onChange={(e) => setNewApiKeyName(e.target.value)}
                              placeholder="Enter key name..."
                              className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            />
                            <button
                              onClick={createApiKey}
                              className="px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                            >
                              Create
                            </button>
                            <button
                              onClick={() => {
                                setShowCreateApiKey(false);
                                setNewApiKeyName('');
                              }}
                              className="px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
                            >
                              Cancel
                            </button>
                          </div>
                        </div>
                      )}

                      {/* API Keys List */}
                      <div className="space-y-3">
                        {apiKeys.map((apiKey) => (
                          <div key={apiKey.id} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <h4 className="text-sm font-medium text-gray-900">{apiKey.name}</h4>
                                <div className="flex items-center space-x-4 mt-1">
                                  <div className="flex items-center space-x-2">
                                    <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                                      {visibleKeys.has(apiKey.id) ? apiKey.key : maskApiKey(apiKey.key)}
                                    </code>
                                    <button
                                      onClick={() => toggleKeyVisibility(apiKey.id)}
                                      className="text-gray-400 hover:text-gray-600"
                                    >
                                      {visibleKeys.has(apiKey.id) ? (
                                        <EyeSlashIcon className="h-4 w-4" />
                                      ) : (
                                        <EyeIcon className="h-4 w-4" />
                                      )}
                                    </button>
                                    <button
                                      onClick={() => copyToClipboard(apiKey.key)}
                                      className="text-gray-400 hover:text-gray-600"
                                    >
                                      <ClipboardDocumentIcon className="h-4 w-4" />
                                    </button>
                                  </div>
                                </div>
                                <p className="text-xs text-gray-500 mt-1">
                                  Created: {new Date(apiKey.created_at).toLocaleDateString()} | 
                                  Last used: {apiKey.last_used ? new Date(apiKey.last_used).toLocaleDateString() : 'Never'}
                                </p>
                              </div>
                              <button
                                onClick={() => deleteApiKey(apiKey.id)}
                                className="text-red-400 hover:text-red-600"
                              >
                                <TrashIcon className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Privacy Settings */}
                  {activeTab === 'privacy' && (
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-lg leading-6 font-medium text-gray-900">
                          Privacy & Security
                        </h3>
                        <p className="mt-1 text-sm text-gray-500">
                          Control your privacy and data sharing preferences
                        </p>
                      </div>

                      <div className="space-y-4">
                        {Object.entries(settings.privacy).map(([key, value]) => (
                          <div key={key} className="flex items-center justify-between">
                            <div>
                              <h4 className="text-sm font-medium text-gray-900 capitalize">
                                {key.replace('_', ' ')}
                              </h4>
                              <p className="text-sm text-gray-500">
                                {key === 'analytics_tracking' && 'Allow anonymous usage analytics to improve the service'}
                                {key === 'data_sharing' && 'Share anonymized data for research and development'}
                                {key === 'public_profile' && 'Make your profile visible to other users'}
                              </p>
                            </div>
                            <input
                              type="checkbox"
                              checked={value}
                              onChange={(e) => handleSettingChange('privacy', key, e.target.checked)}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Save Button */}
                  {(activeTab === 'general' || activeTab === 'notifications' || activeTab === 'privacy') && (
                    <div className="pt-6 border-t border-gray-200">
                      <button
                        onClick={saveSettings}
                        disabled={loading}
                        className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                      >
                        {loading ? 'Saving...' : 'Save Settings'}
                      </button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </AuthenticatedLayout>
    </EnhancedProtectedRoute>
  );
}