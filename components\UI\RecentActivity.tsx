'use client';

import React from 'react';
import Link from 'next/link';

interface ActivityItem {
  id: string;
  type: string;
  title: string;
  description: string;
  timestamp: string;
  link?: string;
  metadata?: any;
}

interface RecentActivityProps {
  activities: ActivityItem[];
  loading?: boolean;
  className?: string;
}

export default function RecentActivity({
  activities,
  loading = false,
  className = ''
}: RecentActivityProps) {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'content_generated':
        return '📝';
      case 'analysis_completed':
        return '📊';
      case 'project_created':
        return '📁';
      case 'research_done':
        return '🔍';
      default:
        return '📄';
    }
  };

  const formatTime = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMs / 3600000);
      const diffDays = Math.floor(diffMs / 86400000);

      if (diffMins < 1) return 'Just now';
      if (diffMins < 60) return `${diffMins} minutes ago`;
      if (diffHours < 24) return `${diffHours} hours ago`;
      if (diffDays < 7) return `${diffDays} days ago`;
      
      return date.toLocaleDateString();
    } catch {
      return 'Recently';
    }
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="h-6 bg-gray-200 rounded w-48 mb-4 animate-pulse"></div>
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="flex items-start space-x-3 animate-pulse">
              <div className="h-10 w-10 bg-gray-200 rounded-full"></div>
              <div className="flex-1 space-y-1">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/4"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-6">Recent Activity</h3>
      
      {activities.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-gray-400 mb-2">📋</div>
          <p className="text-gray-500">No recent activity</p>
          <p className="text-sm text-gray-400 mt-1">
            Your content generation and analysis activities will appear here
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                <span className="text-lg">{getActivityIcon(activity.type)}</span>
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900">
                    {activity.title}
                  </p>
                  <time className="text-xs text-gray-500">
                    {formatTime(activity.timestamp)}
                  </time>
                </div>
                
                <p className="text-sm text-gray-600 mt-1">
                  {activity.description}
                </p>
                
                {activity.metadata && (
                  <div className="flex items-center space-x-4 mt-2">
                    {activity.metadata.keyword && (
                      <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                        {activity.metadata.keyword}
                      </span>
                    )}
                    {activity.metadata.word_count && (
                      <span className="text-xs text-gray-500">
                        {activity.metadata.word_count} words
                      </span>
                    )}
                    {activity.metadata.seo_score && (
                      <span className="text-xs text-green-600 font-medium">
                        SEO: {activity.metadata.seo_score}%
                      </span>
                    )}
                  </div>
                )}
                
                {activity.link && (
                  <Link
                    href={activity.link}
                    className="text-xs text-blue-600 hover:text-blue-800 font-medium mt-2 inline-block"
                  >
                    View details →
                  </Link>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
      
      <div className="mt-6 pt-4 border-t border-gray-200">
        <Link
          href="/activity"
          className="text-sm text-blue-600 hover:text-blue-800 font-medium"
        >
          View all activity →
        </Link>
      </div>
    </div>
  );
}