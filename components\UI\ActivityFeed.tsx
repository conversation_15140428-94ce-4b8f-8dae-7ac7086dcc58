'use client';

import React, { useState, useEffect } from 'react';
import { useActivityFeed } from '@/hooks/useRealTimeUpdates';
import RecentActivity from './RecentActivity';
import { RealTimeIndicator } from '@/hooks/useRealTimeUpdates';

interface ActivityFeedProps {
  userId?: string;
  className?: string;
  maxItems?: number;
  showRealTimeIndicator?: boolean;
}

export default function ActivityFeed({ 
  userId, 
  className = '', 
  maxItems = 10,
  showRealTimeIndicator = true 
}: ActivityFeedProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const {
    activities,
    isLoading,
    isConnected,
    error,
    lastUpdate,
    refresh,
    addActivity
  } = useActivityFeed(userId);

  // Simulate receiving new activities in real-time
  useEffect(() => {
    if (isConnected && activities.length > 0) {
      const interval = setInterval(() => {
        // Simulate random activity updates
        const activityTypes = ['content_generated', 'analysis_completed', 'project_created', 'research_done'];
        const randomType = activityTypes[Math.floor(Math.random() * activityTypes.length)];
        
        const newActivity = {
          id: `sim-${Date.now()}`,
          type: randomType,
          title: getRandomTitle(randomType),
          description: getRandomDescription(randomType),
          timestamp: new Date().toISOString(),
          metadata: getRandomMetadata(randomType)
        };
        
        // Only add new activity occasionally (10% chance every 30 seconds)
        if (Math.random() < 0.1) {
          addActivity(newActivity);
        }
      }, 30000); // Check every 30 seconds
      
      return () => clearInterval(interval);
    }
  }, [isConnected, activities.length, addActivity]);

  const getRandomTitle = (type: string) => {
    const titles = {
      content_generated: [
        'Blog Post Generated',
        'Product Description Created',
        'Landing Page Content Generated',
        'Email Campaign Written'
      ],
      analysis_completed: [
        'Competitor Analysis Complete',
        'Keyword Research Finished',
        'SERP Analysis Done',
        'Content Gap Analysis Complete'
      ],
      project_created: [
        'New Project Created',
        'Campaign Launched',
        'Client Project Started',
        'SEO Strategy Initialized'
      ],
      research_done: [
        'Market Research Complete',
        'Keyword Discovery Finished',
        'Trend Analysis Done',
        'Competitor Insights Gathered'
      ]
    };
    
    const typeTitle = titles[type] || titles.content_generated;
    return typeTitle[Math.floor(Math.random() * typeTitle.length)];
  };

  const getRandomDescription = (type: string) => {
    const descriptions = {
      content_generated: [
        'Created high-quality SEO content with target keywords',
        'Generated optimized content for better search rankings',
        'Produced engaging content with strong SEO foundation',
        'Crafted content optimized for user intent and search engines'
      ],
      analysis_completed: [
        'Analyzed competitor strategies and identified opportunities',
        'Completed comprehensive keyword research with long-tail variations',
        'Finished SERP analysis with ranking insights',
        'Identified content gaps and optimization opportunities'
      ],
      project_created: [
        'Started new SEO project with defined goals and metrics',
        'Launched comprehensive content marketing campaign',
        'Initialized new client project with strategic planning',
        'Created project roadmap with milestone tracking'
      ],
      research_done: [
        'Completed market research with actionable insights',
        'Finished keyword discovery with search volume data',
        'Analyzed industry trends and competitive landscape',
        'Gathered competitor insights for strategic planning'
      ]
    };
    
    const typeDesc = descriptions[type] || descriptions.content_generated;
    return typeDesc[Math.floor(Math.random() * typeDesc.length)];
  };

  const getRandomMetadata = (type: string) => {
    const keywords = ['SEO optimization', 'content marketing', 'digital strategy', 'keyword research', 'competitive analysis'];
    const randomKeyword = keywords[Math.floor(Math.random() * keywords.length)];
    
    switch (type) {
      case 'content_generated':
        return {
          keyword: randomKeyword,
          word_count: Math.floor(Math.random() * 2000) + 500,
          seo_score: Math.floor(Math.random() * 20) + 80
        };
      case 'analysis_completed':
        return {
          keyword: randomKeyword
        };
      default:
        return {};
    }
  };

  const displayedActivities = isExpanded ? activities : activities.slice(0, maxItems);

  return (
    <div className={`space-y-4 ${className}`}>
      {showRealTimeIndicator && (
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">Live Activity Feed</h2>
          <RealTimeIndicator 
            isConnected={isConnected} 
            lastUpdate={lastUpdate}
            className="text-sm"
          />
        </div>
      )}
      
      <RecentActivity 
        activities={displayedActivities}
        loading={isLoading}
        realTimeIndicator={showRealTimeIndicator ? (
          <RealTimeIndicator 
            isConnected={isConnected} 
            lastUpdate={lastUpdate}
          />
        ) : undefined}
      />
      
      {activities.length > maxItems && (
        <div className="text-center">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
          >
            {isExpanded ? 'Show Less' : `Show ${activities.length - maxItems} More`}
          </button>
        </div>
      )}
      
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="text-red-600 text-sm">
              <strong>Connection Error:</strong> {error.message}
            </div>
            <button
              onClick={refresh}
              className="ml-auto text-red-600 hover:text-red-800 text-sm font-medium"
            >
              Retry
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

// Export for use in other components
export { ActivityFeed };