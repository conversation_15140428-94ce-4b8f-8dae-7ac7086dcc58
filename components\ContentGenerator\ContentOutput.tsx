/**
 * ContentOutput Component
 * Enterprise SEO SAAS - Content review, editing, and export functionality
 */

import { useState, useEffect, useRef } from 'react'
import {
  DocumentTextIcon,
  PencilIcon,
  EyeIcon,
  ArrowDownTrayIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClipboardDocumentIcon,
  ShareIcon,
  BookmarkIcon,
  SparklesIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  ChartBarIcon,
  LinkIcon,
  TagIcon,
  ClockIcon,
  DocumentDuplicateIcon
} from '@heroicons/react/24/outline'

interface ContentOutputProps {
  content: string
  formData: {
    primaryKeyword: string
    secondaryKeywords: string[]
    contentType: string
    wordCount: number
    tone: string
    targetLocation: string
  }
  generationResult?: any // Full generation result with metrics
  onSave: (content: string, metadata: any) => void
  onExport: (format: string) => void
}

interface ContentMetrics {
  wordCount: number
  characterCount: number
  paragraphCount: number
  headingCount: number
  linkCount: number
  keywordDensity: number
  readabilityScore: number
  seoScore: number
}

interface ExportFormat {
  id: string
  name: string
  description: string
  extension: string
  icon: any
}

export default function ContentOutput({ content, formData, generationResult, onSave, onExport }: ContentOutputProps) {
  const [editableContent, setEditableContent] = useState(content)
  const [viewMode, setViewMode] = useState<'preview' | 'edit'>('preview')
  const [metrics, setMetrics] = useState<ContentMetrics | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle')
  const [selectedExportFormat, setSelectedExportFormat] = useState('markdown')
  const [showExportModal, setShowExportModal] = useState(false)
  const [activeTab, setActiveTab] = useState<'content' | 'seo' | 'export'>('content')
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Get authority link metrics from generation result
  const authorityLinkMetrics = generationResult?.authorityLinkMetrics
  const hasAuthorityLinks = authorityLinkMetrics && authorityLinkMetrics.linksEmbedded > 0

  const exportFormats: ExportFormat[] = [
    {
      id: 'markdown',
      name: 'Markdown',
      description: 'Standard markdown format for CMS platforms',
      extension: '.md',
      icon: DocumentTextIcon
    },
    {
      id: 'html',
      name: 'HTML',
      description: 'Clean HTML for web publishing',
      extension: '.html',
      icon: DocumentTextIcon
    },
    {
      id: 'docx',
      name: 'Word Document',
      description: 'Microsoft Word document format',
      extension: '.docx',
      icon: DocumentDuplicateIcon
    },
    {
      id: 'pdf',
      name: 'PDF',
      description: 'Portable document format',
      extension: '.pdf',
      icon: DocumentTextIcon
    },
    {
      id: 'wordpress',
      name: 'WordPress',
      description: 'WordPress-ready HTML with meta fields',
      extension: '.html',
      icon: DocumentTextIcon
    },
    {
      id: 'json',
      name: 'JSON',
      description: 'Structured data format with metadata',
      extension: '.json',
      icon: DocumentTextIcon
    }
  ]

  useEffect(() => {
    calculateMetrics(editableContent)
  }, [editableContent])

  useEffect(() => {
    if (textareaRef.current && viewMode === 'edit') {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px'
    }
  }, [viewMode, editableContent])

  const calculateMetrics = (text: string) => {
    const words = text.trim().split(/\s+/).filter(word => word.length > 0)
    const characters = text.length
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0)
    const headings = (text.match(/^#+\s/gm) || []).length
    const links = (text.match(/\[([^\]]*)\]\([^)]*\)/g) || []).length
    
    // Calculate keyword density
    const primaryKeywordOccurrences = (text.toLowerCase().match(new RegExp(formData.primaryKeyword.toLowerCase(), 'g')) || []).length
    const keywordDensity = words.length > 0 ? (primaryKeywordOccurrences / words.length) * 100 : 0
    
    // Simple readability score (Flesch-like approximation)
    const avgWordsPerSentence = words.length / Math.max(1, (text.match(/[.!?]+/g) || []).length)
    const avgSyllablesPerWord = 1.5 // Simplified estimate
    const readabilityScore = Math.max(0, Math.min(100, 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord)))
    
    // SEO score calculation
    let seoScore = 0
    if (words.length >= formData.wordCount * 0.8) seoScore += 20
    if (keywordDensity >= 0.5 && keywordDensity <= 2.5) seoScore += 25
    if (headings >= 3) seoScore += 20
    if (links >= 3) seoScore += 15
    if (text.includes(formData.primaryKeyword)) seoScore += 20
    
    // Boost SEO score based on authority link metrics
    if (hasAuthorityLinks) {
      if (authorityLinkMetrics.averageAuthorityScore >= 80) seoScore += 10
      if (authorityLinkMetrics.embeddingQuality >= 70) seoScore += 5
      if (authorityLinkMetrics.validationWarnings.length === 0) seoScore += 5
    }

    setMetrics({
      wordCount: words.length,
      characterCount: characters,
      paragraphCount: paragraphs.length,
      headingCount: headings,
      linkCount: links,
      keywordDensity: Math.round(keywordDensity * 100) / 100,
      readabilityScore: Math.round(readabilityScore),
      seoScore: Math.round(seoScore)
    })
  }

  const handleSave = async () => {
    setIsSaving(true)
    setSaveStatus('saving')

    try {
      const metadata = {
        ...formData,
        metrics,
        lastUpdated: new Date().toISOString(),
        version: '1.0'
      }

      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate save
      
      onSave(editableContent, metadata)
      setSaveStatus('saved')
      
      setTimeout(() => setSaveStatus('idle'), 3000)
    } catch (error) {
      console.error('Error saving content:', error)
      setSaveStatus('error')
    } finally {
      setIsSaving(false)
    }
  }

  const handleExport = (format: string) => {
    onExport(format)
    setShowExportModal(false)
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(editableContent)
      // Show temporary success indicator
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
    }
  }

  const getSeoScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-100'
    if (score >= 60) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  const getReadabilityLevel = (score: number) => {
    if (score >= 90) return 'Very Easy'
    if (score >= 80) return 'Easy'
    if (score >= 70) return 'Fairly Easy'
    if (score >= 60) return 'Standard'
    if (score >= 50) return 'Fairly Difficult'
    if (score >= 30) return 'Difficult'
    return 'Very Difficult'
  }

  const tabs = [
    { id: 'content', label: 'Content', icon: DocumentTextIcon },
    { id: 'seo', label: 'SEO Analysis', icon: ChartBarIcon },
    { id: 'export', label: 'Export & Share', icon: ArrowDownTrayIcon }
  ]

  return (
    <div className="p-8 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Generated Content</h2>
          <p className="text-gray-600">
            Review, edit, and export your AI-generated SEO content
          </p>
        </div>

        <div className="flex items-center gap-3">
          <button
            onClick={() => setViewMode(viewMode === 'preview' ? 'edit' : 'preview')}
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            {viewMode === 'preview' ? (
              <>
                <PencilIcon className="h-4 w-4 mr-2" />
                Edit Content
              </>
            ) : (
              <>
                <EyeIcon className="h-4 w-4 mr-2" />
                Preview
              </>
            )}
          </button>

          <button
            onClick={handleSave}
            disabled={isSaving}
            className={`
              inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg
              ${saveStatus === 'saved'
                ? 'text-green-600 bg-green-100 border border-green-200'
                : 'text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300'
              }
            `}
          >
            {isSaving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Saving...
              </>
            ) : saveStatus === 'saved' ? (
              <>
                <CheckCircleIcon className="h-4 w-4 mr-2" />
                Saved
              </>
            ) : (
              <>
                <BookmarkIcon className="h-4 w-4 mr-2" />
                Save Content
              </>
            )}
          </button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`
                  py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center gap-2
                  ${activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <Icon className="h-4 w-4" />
                {tab.label}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'content' && (
        <div className="space-y-6">
          {/* Content Metrics Bar */}
          {metrics && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">{metrics.wordCount.toLocaleString()}</div>
                  <div className="text-gray-500">Words</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">{metrics.headingCount}</div>
                  <div className="text-gray-500">Headers</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">{metrics.linkCount}</div>
                  <div className="text-gray-500">Links</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">{metrics.keywordDensity}%</div>
                  <div className="text-gray-500">Keyword Density</div>
                </div>
              </div>
            </div>
          )}

          {/* Content Editor/Preview */}
          <div className="bg-white border border-gray-200 rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">
                {viewMode === 'edit' ? 'Edit Content' : 'Content Preview'}
              </h3>
              
              <div className="flex items-center gap-2">
                <button
                  onClick={copyToClipboard}
                  className="text-gray-500 hover:text-gray-700"
                  title="Copy to clipboard"
                >
                  <ClipboardDocumentIcon className="h-4 w-4" />
                </button>
                
                {metrics && (
                  <span className={`
                    inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                    ${getSeoScoreColor(metrics.seoScore)}
                  `}>
                    SEO: {metrics.seoScore}/100
                  </span>
                )}
              </div>
            </div>

            <div className="p-6">
              {viewMode === 'edit' ? (
                <textarea
                  ref={textareaRef}
                  value={editableContent}
                  onChange={(e) => setEditableContent(e.target.value)}
                  className="w-full min-h-96 p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none font-mono text-sm"
                  placeholder="Edit your content here..."
                />
              ) : (
                <div className="prose max-w-none">
                  <div 
                    className="whitespace-pre-wrap"
                    dangerouslySetInnerHTML={{ 
                      __html: editableContent
                        .replace(/^# (.+)$/gm, '<h1 class="text-3xl font-bold text-gray-900 mb-4">$1</h1>')
                        .replace(/^## (.+)$/gm, '<h2 class="text-2xl font-semibold text-gray-900 mb-3 mt-6">$1</h2>')
                        .replace(/^### (.+)$/gm, '<h3 class="text-xl font-medium text-gray-900 mb-2 mt-4">$1</h3>')
                        .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
                        .replace(/^\d+\.\s(.+)$/gm, '<li class="mb-1">$1</li>')
                        .replace(/^-\s(.+)$/gm, '<li class="mb-1">$1</li>')
                        .replace(/\n\n/g, '</p><p class="mb-4">')
                        .replace(/^(.+)$/gm, '<p class="mb-4">$1</p>')
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'seo' && metrics && (
        <div className="space-y-6">
          {/* SEO Score Overview */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">SEO Analysis</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="text-center">
                <div className={`
                  text-4xl font-bold mb-2
                  ${metrics.seoScore >= 80 ? 'text-green-600' : 
                    metrics.seoScore >= 60 ? 'text-yellow-600' : 'text-red-600'}
                `}>
                  {metrics.seoScore}/100
                </div>
                <div className="text-sm text-gray-500">Overall SEO Score</div>
              </div>
              
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-600 mb-2">{metrics.readabilityScore}</div>
                <div className="text-sm text-gray-500">Readability Score</div>
                <div className="text-xs text-gray-400">{getReadabilityLevel(metrics.readabilityScore)}</div>
              </div>
              
              <div className="text-center">
                <div className="text-4xl font-bold text-purple-600 mb-2">{metrics.keywordDensity}%</div>
                <div className="text-sm text-gray-500">Keyword Density</div>
                <div className={`text-xs ${
                  metrics.keywordDensity >= 0.5 && metrics.keywordDensity <= 2.5 
                    ? 'text-green-600' : 'text-yellow-600'
                }`}>
                  {metrics.keywordDensity >= 0.5 && metrics.keywordDensity <= 2.5 ? 'Optimal' : 'Needs adjustment'}
                </div>
              </div>
            </div>
          </div>

          {/* Authority Link Metrics */}
          {hasAuthorityLinks && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <div className="flex items-center gap-2 mb-4">
                <LinkIcon className="h-5 w-5 text-blue-600" />
                <h3 className="text-lg font-medium text-blue-900">Authority Link Performance</h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-1">
                    {authorityLinkMetrics.linksEmbedded}
                  </div>
                  <div className="text-sm text-blue-800">Links Embedded</div>
                </div>
                
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 mb-1">
                    {authorityLinkMetrics.averageAuthorityScore}
                  </div>
                  <div className="text-sm text-blue-800">Avg Authority</div>
                </div>
                
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600 mb-1">
                    {authorityLinkMetrics.linkDensity}%
                  </div>
                  <div className="text-sm text-blue-800">Link Density</div>
                </div>
                
                <div className="text-center">
                  <div className="text-3xl font-bold text-orange-600 mb-1">
                    {authorityLinkMetrics.embeddingQuality}
                  </div>
                  <div className="text-sm text-blue-800">Quality Score</div>
                </div>
              </div>

              {authorityLinkMetrics.validationWarnings && authorityLinkMetrics.validationWarnings.length > 0 && (
                <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                  <div className="flex items-start">
                    <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mt-0.5 mr-2" />
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-yellow-800">Authority Link Warnings</h4>
                      <ul className="mt-1 text-sm text-yellow-700 space-y-1">
                        {authorityLinkMetrics.validationWarnings.map((warning: string, index: number) => (
                          <li key={index}>• {warning}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Detailed Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h4 className="font-medium text-gray-900 mb-4">Content Structure</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Word Count</span>
                  <span className="text-sm font-medium">{metrics.wordCount.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Paragraphs</span>
                  <span className="text-sm font-medium">{metrics.paragraphCount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Headings</span>
                  <span className="text-sm font-medium">{metrics.headingCount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Links</span>
                  <span className="text-sm font-medium">{metrics.linkCount}</span>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h4 className="font-medium text-gray-900 mb-4">SEO Recommendations</h4>
              <div className="space-y-2">
                {metrics.wordCount < formData.wordCount * 0.8 && (
                  <div className="flex items-start gap-2 text-sm text-yellow-700">
                    <ExclamationTriangleIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    Consider adding more content to reach target word count
                  </div>
                )}
                {metrics.keywordDensity < 0.5 && (
                  <div className="flex items-start gap-2 text-sm text-yellow-700">
                    <ExclamationTriangleIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    Increase keyword density for better SEO
                  </div>
                )}
                {metrics.keywordDensity > 2.5 && (
                  <div className="flex items-start gap-2 text-sm text-yellow-700">
                    <ExclamationTriangleIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    Reduce keyword density to avoid over-optimization
                  </div>
                )}
                {metrics.headingCount < 3 && (
                  <div className="flex items-start gap-2 text-sm text-yellow-700">
                    <ExclamationTriangleIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    Add more headings for better content structure
                  </div>
                )}
                {metrics.linkCount < 3 && (
                  <div className="flex items-start gap-2 text-sm text-yellow-700">
                    <ExclamationTriangleIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    Add more internal and external links
                  </div>
                )}
                {metrics.seoScore >= 80 && (
                  <div className="flex items-start gap-2 text-sm text-green-700">
                    <CheckCircleIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    Content is well-optimized for SEO
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'export' && (
        <div className="space-y-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Export Options</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {exportFormats.map((format) => {
                const Icon = format.icon
                return (
                  <button
                    key={format.id}
                    onClick={() => handleExport(format.id)}
                    className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors text-left"
                  >
                    <div className="flex items-center gap-3 mb-2">
                      <Icon className="h-5 w-5 text-gray-600" />
                      <span className="font-medium text-gray-900">{format.name}</span>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{format.description}</p>
                    <span className="text-xs text-gray-500">{format.extension}</span>
                  </button>
                )
              })}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <button
                onClick={copyToClipboard}
                className="inline-flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <ClipboardDocumentIcon className="h-5 w-5 mr-2 text-gray-600" />
                Copy to Clipboard
              </button>
              
              <button
                onClick={() => {/* Implement share functionality */}}
                className="inline-flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <ShareIcon className="h-5 w-5 mr-2 text-gray-600" />
                Share Content
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}