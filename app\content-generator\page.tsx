/**
 * Content Generator Page
 * Enterprise SEO SAAS - AI-powered content generation with competitor analysis
 */

'use client'

import { useState } from 'react'
import { AuthenticatedLayout, PageHeader, ContentContainer } from '@/components/Layout/DashboardLayout'
import Button from '@/components/UI/Button'
import Input from '@/components/UI/Input'
import Card from '@/components/UI/Card'
import LoadingSpinner from '@/components/UI/LoadingSpinner'
import { useNotifications } from '@/components/Notifications'
import { DocumentTextIcon, ClipboardDocumentIcon, ArrowDownTrayIcon, CheckCircleIcon, ExclamationCircleIcon, ClockIcon } from '@heroicons/react/24/outline'
import { contentService } from '@/lib/api'
import { ContentGenerationRequest } from '@/lib/api/types'
import ContentPreview from '@/components/ContentResults/ContentPreview'
import SEOAnalysis from '@/components/ContentResults/SEOAnalysis'
import CompetitorInsights from '@/components/ContentResults/CompetitorInsights'
import ResultsActions from '@/components/ContentResults/ResultsActions'

interface ContentFormData {
  keyword: string
  target_country: string
  content_type: string
}

interface ProcessingStep {
  id: string
  title: string
  description: string
  status: 'pending' | 'processing' | 'completed' | 'error'
  progress: number
  duration?: number
}

interface ProcessingState {
  isProcessing: boolean
  currentStep: string | null
  steps: ProcessingStep[]
  startTime: number | null
  estimatedTimeRemaining: number | null
}

export default function ContentGeneratorPage() {
  const [formData, setFormData] = useState<ContentFormData>({
    keyword: '',
    target_country: 'United States',
    content_type: 'blog-post'
  })
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedContent, setGeneratedContent] = useState<string>('')
  const [contentResults, setContentResults] = useState<any>(null)
  const [processingState, setProcessingState] = useState<ProcessingState>({
    isProcessing: false,
    currentStep: null,
    steps: [],
    startTime: null,
    estimatedTimeRemaining: null
  })
  const { error: notifyError, success: notifySuccess } = useNotifications()

  const defaultSteps: ProcessingStep[] = [
    {
      id: 'serp_analysis',
      title: 'SERP Analysis',
      description: 'Analyzing search results for your keyword',
      status: 'pending',
      progress: 0
    },
    {
      id: 'competitor_research',
      title: 'Competitor Research',
      description: 'Studying top-ranking competitor content',
      status: 'pending',
      progress: 0
    },
    {
      id: 'content_outline',
      title: 'Content Planning',
      description: 'Creating optimized content structure',
      status: 'pending',
      progress: 0
    },
    {
      id: 'content_generation',
      title: 'Content Generation',
      description: 'Generating SEO-optimized content',
      status: 'pending',
      progress: 0
    },
    {
      id: 'quality_analysis',
      title: 'Quality Analysis',
      description: 'Analyzing content quality and SEO metrics',
      status: 'pending',
      progress: 0
    }
  ]

  const simulateProgressStep = (stepId: string, duration: number = 2000): Promise<void> => {
    return new Promise((resolve) => {
      const startTime = Date.now()
      
      setProcessingState(prev => ({
        ...prev,
        currentStep: stepId,
        steps: prev.steps.map(step => 
          step.id === stepId 
            ? { ...step, status: 'processing', progress: 0 }
            : step
        )
      }))

      const interval = setInterval(() => {
        const elapsed = Date.now() - startTime
        const progress = Math.min((elapsed / duration) * 100, 100)
        
        setProcessingState(prev => ({
          ...prev,
          steps: prev.steps.map(step =>
            step.id === stepId
              ? { ...step, progress }
              : step
          ),
          estimatedTimeRemaining: Math.max(duration - elapsed, 0)
        }))

        if (progress >= 100) {
          clearInterval(interval)
          setProcessingState(prev => ({
            ...prev,
            steps: prev.steps.map(step =>
              step.id === stepId
                ? { ...step, status: 'completed', progress: 100, duration: elapsed }
                : step
            )
          }))
          resolve()
        }
      }, 50)
    })
  }

  const handleGenerate = async () => {
    if (!formData.keyword.trim()) {
      notifyError('Please enter a keyword')
      return
    }

    setIsGenerating(true)
    setGeneratedContent('')
    
    // Initialize processing state
    setProcessingState({
      isProcessing: true,
      currentStep: null,
      steps: defaultSteps,
      startTime: Date.now(),
      estimatedTimeRemaining: null
    })

    try {
      // Step 1: SERP Analysis
      await simulateProgressStep('serp_analysis', 1500)
      
      // Step 2: Competitor Research  
      await simulateProgressStep('competitor_research', 2000)
      
      // Step 3: Content Planning
      await simulateProgressStep('content_outline', 1000)
      
      // Step 4: Content Generation (actual API call)
      setProcessingState(prev => ({
        ...prev,
        currentStep: 'content_generation',
        steps: prev.steps.map(step => 
          step.id === 'content_generation'
            ? { ...step, status: 'processing', progress: 0 }
            : step
        )
      }))

      // Simulate progress during API call
      const progressInterval = setInterval(() => {
        setProcessingState(prev => ({
          ...prev,
          steps: prev.steps.map(step =>
            step.id === 'content_generation' && step.progress < 90
              ? { ...step, progress: step.progress + 2 }
              : step
          )
        }))
      }, 100)

      try {
        const request: ContentGenerationRequest = {
          keyword: formData.keyword,
          target_country: formData.target_country,
          content_type: formData.content_type
        }

        const response = await contentService.generateContent(request, (step, progress) => {
          setProcessingState(prev => ({
            ...prev,
            steps: prev.steps.map(s =>
              s.id === step
                ? { ...s, progress }
                : s
            )
          }))
        })

        clearInterval(progressInterval)

        if (!response.success) {
          setProcessingState(prev => ({
            ...prev,
            steps: prev.steps.map(step =>
              step.id === 'content_generation'
                ? { ...step, status: 'error', progress: 100 }
                : step
            )
          }))

          throw new Error(response.error || 'Failed to generate content')
        }

        // Complete content generation step
        setProcessingState(prev => ({
          ...prev,
          steps: prev.steps.map(step =>
            step.id === 'content_generation'
              ? { ...step, status: 'completed', progress: 100 }
              : step
          )
        }))

        // Step 5: Quality Analysis
        await simulateProgressStep('quality_analysis', 1000)

        if (response.data?.content) {
          setGeneratedContent(response.data.content.body || '')
          setContentResults(response.data)
          notifySuccess('Content generated successfully!')
          
          if (response.data.content.metadata) {
            console.log('Content metrics:', response.data.content.metadata)
          }
        } else {
          throw new Error('Invalid response format from API - no content received')
        }
      } catch (apiError) {
        clearInterval(progressInterval)
        setProcessingState(prev => ({
          ...prev,
          steps: prev.steps.map(step =>
            step.id === 'content_generation'
              ? { ...step, status: 'error', progress: 100 }
              : step
          )
        }))
        throw apiError
      }
    } catch (err) {
      console.error('Content generation error:', err)
      
      // Mark all remaining steps as error
      setProcessingState(prev => ({
        ...prev,
        isProcessing: false,
        steps: prev.steps.map(step =>
          step.status === 'pending' || step.status === 'processing'
            ? { ...step, status: 'error', progress: 100 }
            : step
        )
      }))
      
      notifyError(err instanceof Error ? err.message : 'An unexpected error occurred')
    } finally {
      setIsGenerating(false)
      setProcessingState(prev => ({
        ...prev,
        isProcessing: false,
        currentStep: null
      }))
    }
  }


  return (
    <AuthenticatedLayout>
      <PageHeader 
        title="AI Content Generator"
        description="Generate SEO-optimized content for any keyword in any industry"
      />

      <div className="max-w-4xl mx-auto space-y-6">
        {/* Content Generation Form */}
        <Card className="p-8">
          <div className="flex items-center mb-6">
            <DocumentTextIcon className="h-6 w-6 text-blue-600 dark:text-blue-400 mr-3" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Content Generation Settings
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Keyword Input */}
            <div className="md:col-span-3">
              <Input
                label="Target Keyword"
                type="text"
                value={formData.keyword}
                onChange={(e) => setFormData({...formData, keyword: e.target.value})}
                placeholder="e.g., digital marketing strategies"
                required
                disabled={isGenerating || processingState.isProcessing}
                helperText="The main keyword you want to target for SEO optimization"
              />
            </div>

            {/* Target Country */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Target Country
              </label>
              <select
                value={formData.target_country}
                onChange={(e) => setFormData({...formData, target_country: e.target.value})}
                disabled={isGenerating || processingState.isProcessing}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <option value="United States">United States</option>
                <option value="United Kingdom">United Kingdom</option>
                <option value="Canada">Canada</option>
                <option value="Australia">Australia</option>
                <option value="Germany">Germany</option>
                <option value="France">France</option>
                <option value="Spain">Spain</option>
                <option value="Italy">Italy</option>
                <option value="Netherlands">Netherlands</option>
                <option value="Brazil">Brazil</option>
              </select>
            </div>

            {/* Content Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Content Type
              </label>
              <select
                value={formData.content_type}
                onChange={(e) => setFormData({...formData, content_type: e.target.value})}
                disabled={isGenerating || processingState.isProcessing}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <option value="blog-post">Blog Post</option>
                <option value="article">Article</option>
                <option value="guide">Guide</option>
                <option value="tutorial">Tutorial</option>
                <option value="review">Review</option>
                <option value="landing-page">Landing Page</option>
                <option value="product-description">Product Description</option>
                <option value="social-media">Social Media Post</option>
              </select>
            </div>

            {/* Generate Button */}
            <div className="flex items-end">
              <Button
                onClick={handleGenerate}
                disabled={isGenerating || processingState.isProcessing || !formData.keyword.trim()}
                className="w-full"
                size="lg"
              >
                {isGenerating || processingState.isProcessing ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    {processingState.currentStep ? 
                      processingState.steps.find(s => s.id === processingState.currentStep)?.title || 'Processing...' 
                      : 'Generating...'
                    }
                  </>
                ) : (
                  'Generate Content'
                )}
              </Button>
            </div>
          </div>
        </Card>

        {/* Progress Tracker */}
        {processingState.isProcessing && (
          <Card className="p-8">
            <div className="flex items-center mb-6">
              <ClockIcon className="h-6 w-6 text-blue-600 dark:text-blue-400 mr-3" />
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Processing Your Content
              </h2>
              {processingState.estimatedTimeRemaining && (
                <span className="ml-auto text-sm text-gray-500 dark:text-gray-400">
                  ~{Math.ceil(processingState.estimatedTimeRemaining / 1000)}s remaining
                </span>
              )}
            </div>

            <div className="space-y-4">
              {processingState.steps.map((step, index) => {
                const isActive = processingState.currentStep === step.id
                const isCompleted = step.status === 'completed'
                const isError = step.status === 'error'
                const isProcessing = step.status === 'processing'

                return (
                  <div key={step.id} className="relative">
                    {/* Connection Line */}
                    {index < processingState.steps.length - 1 && (
                      <div className={`absolute left-5 top-10 w-0.5 h-8 ${
                        isCompleted ? 'bg-green-500' : 
                        isError ? 'bg-red-500' : 
                        'bg-gray-300 dark:bg-gray-600'
                      }`} />
                    )}

                    <div className="flex items-start space-x-4">
                      {/* Step Icon */}
                      <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
                        isCompleted ? 'bg-green-100 dark:bg-green-900/20' :
                        isError ? 'bg-red-100 dark:bg-red-900/20' :
                        isProcessing ? 'bg-blue-100 dark:bg-blue-900/20' :
                        'bg-gray-100 dark:bg-gray-800'
                      }`}>
                        {isCompleted ? (
                          <CheckCircleIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
                        ) : isError ? (
                          <ExclamationCircleIcon className="h-6 w-6 text-red-600 dark:text-red-400" />
                        ) : isProcessing ? (
                          <LoadingSpinner size="sm" className="text-blue-600 dark:text-blue-400" />
                        ) : (
                          <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            {index + 1}
                          </span>
                        )}
                      </div>

                      {/* Step Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h3 className={`text-sm font-medium ${
                            isActive ? 'text-blue-600 dark:text-blue-400' :
                            isCompleted ? 'text-green-600 dark:text-green-400' :
                            isError ? 'text-red-600 dark:text-red-400' :
                            'text-gray-900 dark:text-gray-100'
                          }`}>
                            {step.title}
                          </h3>
                          {step.duration && (
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {(step.duration / 1000).toFixed(1)}s
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          {step.description}
                        </p>

                        {/* Progress Bar */}
                        {(isProcessing || isActive) && (
                          <div className="mt-2">
                            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                              <div 
                                className={`h-2 rounded-full transition-all duration-300 ${
                                  isError ? 'bg-red-500' : 'bg-blue-500'
                                }`}
                                style={{ width: `${step.progress}%` }}
                              />
                            </div>
                            <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                              <span>Progress</span>
                              <span>{Math.round(step.progress)}%</span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>

            {/* Overall Progress Summary */}
            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">
                  Overall Progress
                </span>
                <span className="font-medium text-gray-900 dark:text-gray-100">
                  {processingState.steps.filter(s => s.status === 'completed').length} of {processingState.steps.length} steps completed
                </span>
              </div>
              <div className="mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="h-2 bg-gradient-to-r from-blue-500 to-green-500 rounded-full transition-all duration-500"
                  style={{ 
                    width: `${(processingState.steps.filter(s => s.status === 'completed').length / processingState.steps.length) * 100}%` 
                  }}
                />
              </div>
            </div>
          </Card>
        )}

        {/* Generated Content Results */}
        {contentResults && (
          <div className="space-y-6">
            {/* Content Preview */}
            <ContentPreview
              content={{
                title: contentResults.content?.title || '',
                body: contentResults.content?.body || '',
                meta_description: contentResults.content?.meta_description,
                meta_title: contentResults.content?.meta_title,
                sections: contentResults.content?.sections
              }}
              metadata={{
                word_count: contentResults.content?.metadata?.word_count || 0,
                keyword_density: contentResults.content?.metadata?.keyword_density || 0,
                readability_score: contentResults.content?.metadata?.readability_score || 0,
                seo_score: contentResults.seo_analysis?.overall_score || 0
              }}
              keyword={formData.keyword}
            />

            {/* SEO Analysis */}
            {contentResults.seo_analysis && (
              <SEOAnalysis
                analysis={contentResults.seo_analysis}
                recommendations={contentResults.seo_analysis.recommendations || []}
                keyword={formData.keyword}
                metadata={{
                  word_count: contentResults.content?.metadata?.word_count || 0,
                  keyword_density: contentResults.content?.metadata?.keyword_density || 0,
                  readability_score: contentResults.content?.metadata?.readability_score || 0
                }}
              />
            )}

            {/* Competitor Insights */}
            {(contentResults.competitor_data || contentResults.serp_analysis || contentResults.keyword_analysis) && (
              <CompetitorInsights
                competitorData={contentResults.competitor_data || { top_competitors: [] }}
                serpAnalysis={contentResults.serp_analysis || {
                  total_results: 0,
                  featured_snippet: false,
                  average_word_count: 0,
                  common_topics: []
                }}
                keywordAnalysis={contentResults.keyword_analysis || {
                  difficulty_score: 0,
                  search_volume: 0,
                  related_keywords: [],
                  lsi_keywords: []
                }}
              />
            )}

            {/* Results Actions */}
            <ResultsActions
              content={{
                title: contentResults.content?.title || '',
                body: contentResults.content?.body || '',
                meta_description: contentResults.content?.meta_description,
                meta_title: contentResults.content?.meta_title
              }}
              keyword={formData.keyword}
              contentType={formData.content_type}
              onSave={(savedContent) => {
                console.log('Content saved:', savedContent)
              }}
              onEdit={() => {
                notifySuccess('Opening content editor...')
                // TODO: Navigate to content editor
              }}
              onOptimize={() => {
                notifySuccess('Opening SEO optimizer...')
                // TODO: Open SEO optimizer
              }}
            />
          </div>
        )}
      </div>
    </AuthenticatedLayout>
  )
}