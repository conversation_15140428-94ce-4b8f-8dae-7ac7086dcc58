/**
 * Content Generator Page
 * Enterprise SEO SAAS - AI-powered content generation with competitor analysis
 */

'use client'

import { useState } from 'react'
import Link from 'next/link'

interface ContentFormData {
  keyword: string
  target_country: string
  content_type: string
  tone: string
  length: string
}

export default function ContentGeneratorPage() {
  const [formData, setFormData] = useState<ContentFormData>({
    keyword: '',
    target_country: 'United States',
    content_type: 'blog-post',
    tone: 'professional',
    length: 'medium'
  })
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedContent, setGeneratedContent] = useState<string>('')
  const [error, setError] = useState<string>('')

  const handleGenerate = async () => {
    if (!formData.keyword.trim()) {
      setError('Please enter a keyword')
      return
    }

    setIsGenerating(true)
    setError('')
    setGeneratedContent('')

    try {
      console.log('Sending request with data:', formData) // Debug log

      const response = await fetch('http://localhost:5000/api/seo/generate-content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      })

      console.log('Response status:', response.status) // Debug log

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Error response:', errorText) // Debug log
        let errorData: any
        try {
          errorData = JSON.parse(errorText)
        } catch {
          errorData = { message: errorText }
        }
        
        // Provide specific error messages based on status codes
        if (response.status === 400) {
          throw new Error(errorData.message || 'Please check your input - keyword and target country are required')
        } else if (response.status === 500) {
          if (errorData.message?.includes('OpenAI API key')) {
            throw new Error('Server configuration issue: OpenAI API key not properly configured. Please contact support.')
          } else {
            throw new Error(errorData.message || 'Server error occurred while generating content')
          }
        } else if (response.status === 429) {
          throw new Error('Too many requests. Please wait a moment and try again.')
        } else {
          throw new Error(errorData.message || `HTTP ${response.status}: Failed to generate content`)
        }
      }

      const result = await response.json()
      console.log('API Response:', result) // Debug log

      // Handle the API response structure
      if (result.success && result.content && result.content.body) {
        setGeneratedContent(result.content.body)
        
        // Show success message with metrics if available
        if (result.content.metadata) {
          console.log('Content generated successfully:', {
            wordCount: result.content.metadata.word_count,
            keywordDensity: result.content.metadata.keyword_density,
            model: result.content.metadata.model
          })
        }
      } else if (result.content && typeof result.content === 'string') {
        setGeneratedContent(result.content)
      } else {
        throw new Error('Invalid response format from API - no content received')
      }
    } catch (err) {
      console.error('Content generation error:', err)
      
      // Handle network errors
      if (err instanceof TypeError && err.message.includes('fetch')) {
        setError('Unable to connect to the server. Please ensure the backend is running on http://localhost:5000')
      } else {
        setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      }
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900">
      {/* Header */}
      <header className="bg-white/10 backdrop-blur-md border-b border-white/20">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="text-2xl font-bold text-white">
              SEO SAAS
            </Link>
            <nav className="flex space-x-6">
              <Link href="/dashboard" className="text-white hover:text-blue-300 transition-colors">
                Dashboard
              </Link>
              <Link href="/" className="text-white hover:text-blue-300 transition-colors">
                Home
              </Link>
            </nav>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-12">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-white mb-4">
              AI Content Generator
            </h1>
            <p className="text-xl text-gray-300">
              Generate SEO-optimized content for any keyword in any industry
            </p>
          </div>

          <div className="bg-white/10 backdrop-blur-md rounded-xl p-8 border border-white/20">
            <div className="space-y-6">
              {/* Keyword Input */}
              <div>
                <label className="block text-white font-medium mb-2">
                  Target Keyword *
                </label>
                <input
                  type="text"
                  value={formData.keyword}
                  onChange={(e) => setFormData({...formData, keyword: e.target.value})}
                  placeholder="e.g., digital marketing strategies"
                  className="w-full px-4 py-3 rounded-lg bg-white/20 border border-white/30 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Target Country */}
              <div>
                <label className="block text-white font-medium mb-2">
                  Target Country
                </label>
                <select
                  value={formData.target_country}
                  onChange={(e) => setFormData({...formData, target_country: e.target.value})}
                  className="w-full px-4 py-3 rounded-lg bg-white/20 border border-white/30 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="United States">United States</option>
                  <option value="United Kingdom">United Kingdom</option>
                  <option value="Canada">Canada</option>
                  <option value="Australia">Australia</option>
                  <option value="Germany">Germany</option>
                  <option value="France">France</option>
                </select>
              </div>

              {/* Content Type */}
              <div>
                <label className="block text-white font-medium mb-2">
                  Content Type
                </label>
                <select
                  value={formData.content_type}
                  onChange={(e) => setFormData({...formData, content_type: e.target.value})}
                  className="w-full px-4 py-3 rounded-lg bg-white/20 border border-white/30 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="blog-post">Blog Post</option>
                  <option value="article">Article</option>
                  <option value="guide">Guide</option>
                  <option value="tutorial">Tutorial</option>
                  <option value="review">Review</option>
                </select>
              </div>

              {/* Tone */}
              <div>
                <label className="block text-white font-medium mb-2">
                  Tone
                </label>
                <select
                  value={formData.tone}
                  onChange={(e) => setFormData({...formData, tone: e.target.value})}
                  className="w-full px-4 py-3 rounded-lg bg-white/20 border border-white/30 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="professional">Professional</option>
                  <option value="casual">Casual</option>
                  <option value="friendly">Friendly</option>
                  <option value="authoritative">Authoritative</option>
                  <option value="conversational">Conversational</option>
                </select>
              </div>

              {/* Length */}
              <div>
                <label className="block text-white font-medium mb-2">
                  Content Length
                </label>
                <select
                  value={formData.length}
                  onChange={(e) => setFormData({...formData, length: e.target.value})}
                  className="w-full px-4 py-3 rounded-lg bg-white/20 border border-white/30 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="short">Short (800-1200 words)</option>
                  <option value="medium">Medium (1500-2500 words)</option>
                  <option value="long">Long (2500-4000 words)</option>
                </select>
              </div>

              {/* Error Display */}
              {error && (
                <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-4">
                  <p className="text-red-200">{error}</p>
                </div>
              )}

              {/* Generate Button */}
              <button
                onClick={handleGenerate}
                disabled={isGenerating || !formData.keyword.trim()}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-600 disabled:to-gray-600 text-white font-semibold py-4 px-6 rounded-lg transition-all transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed"
              >
                {isGenerating ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                    Generating Content...
                  </div>
                ) : (
                  'Generate SEO Content'
                )}
              </button>
            </div>
          </div>

          {/* Generated Content */}
          {generatedContent && (
            <div className="mt-8 bg-white/10 backdrop-blur-md rounded-xl p-8 border border-white/20">
              <h2 className="text-2xl font-bold text-white mb-6">Generated Content</h2>
              <div className="bg-white rounded-lg p-6 max-h-96 overflow-y-auto">
                <pre className="whitespace-pre-wrap text-gray-800 text-sm leading-relaxed">
                  {generatedContent}
                </pre>
              </div>
              <div className="mt-4 flex space-x-4">
                <button
                  onClick={() => navigator.clipboard.writeText(generatedContent)}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  Copy Content
                </button>
                <button
                  onClick={() => {
                    const blob = new Blob([generatedContent], { type: 'text/plain' })
                    const url = URL.createObjectURL(blob)
                    const a = document.createElement('a')
                    a.href = url
                    a.download = `${formData.keyword.replace(/\s+/g, '-')}-content.txt`
                    a.click()
                    URL.revokeObjectURL(url)
                  }}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                >
                  Download
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}