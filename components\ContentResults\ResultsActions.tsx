/**
 * Results Actions Component
 * Action buttons for saving, editing, and managing generated content
 */

'use client'

import React, { useState } from 'react'
import Button from '@/components/UI/Button'
import Card from '@/components/UI/Card'
import { 
  BookmarkIcon,
  PencilIcon,
  ShareIcon,
  ClipboardDocumentIcon,
  ArrowDownTrayIcon,
  HeartIcon,
  EyeIcon,
  SparklesIcon
} from '@heroicons/react/24/outline'
import { useNotifications } from '@/components/Notifications'
import { contentService } from '@/lib/api'

interface ResultsActionsProps {
  content: {
    title: string
    body: string
    meta_description?: string
    meta_title?: string
  }
  keyword: string
  contentType: string
  onSave?: (savedContent: any) => void
  onEdit?: () => void
  onOptimize?: () => void
}

export default function ResultsActions({ 
  content, 
  keyword, 
  contentType, 
  onSave, 
  onEdit, 
  onOptimize 
}: ResultsActionsProps) {
  const [isSaving, setIsSaving] = useState(false)
  const [isCopying, setIsCopying] = useState(false)
  const [isDownloading, setIsDownloading] = useState(false)
  const [isSharing, setIsSharing] = useState(false)
  const [isFavorited, setIsFavorited] = useState(false)
  
  const { success: notifySuccess, error: notifyError } = useNotifications()

  const handleSaveContent = async () => {
    setIsSaving(true)
    try {
      const response = await contentService.saveContent({
        title: content.title,
        content: content.body,
        keyword,
        content_type: contentType,
        status: 'draft',
        metadata: {
          meta_title: content.meta_title,
          meta_description: content.meta_description,
          keyword_density: 0, // This would be calculated
          readability_score: 0 // This would be calculated
        }
      })

      if (response.success && response.data) {
        notifySuccess('Content saved to library!')
        onSave?.(response.data)
      } else {
        throw new Error(response.error || 'Failed to save content')
      }
    } catch (error) {
      notifyError(error instanceof Error ? error.message : 'Failed to save content')
    } finally {
      setIsSaving(false)
    }
  }

  const handleCopyContent = async () => {
    setIsCopying(true)
    try {
      const fullContent = `${content.title}\n\n${content.body}`
      await navigator.clipboard.writeText(fullContent)
      notifySuccess('Content copied to clipboard!')
    } catch (error) {
      notifyError('Failed to copy content')
    } finally {
      setIsCopying(false)
    }
  }

  const handleDownloadContent = () => {
    setIsDownloading(true)
    try {
      const fullContent = `${content.title}\n\n${content.body}`
      const blob = new Blob([fullContent], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${keyword.replace(/\s+/g, '-')}-content.txt`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      notifySuccess('Content downloaded successfully!')
    } catch (error) {
      notifyError('Failed to download content')
    } finally {
      setIsDownloading(false)
    }
  }

  const handleShareContent = async () => {
    setIsSharing(true)
    try {
      if (navigator.share) {
        await navigator.share({
          title: content.title,
          text: content.body.substring(0, 200) + '...',
          url: window.location.href
        })
        notifySuccess('Content shared successfully!')
      } else {
        // Fallback to copying share URL
        await navigator.clipboard.writeText(window.location.href)
        notifySuccess('Share link copied to clipboard!')
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        notifyError('Failed to share content')
      }
    } finally {
      setIsSharing(false)
    }
  }

  const handleToggleFavorite = () => {
    setIsFavorited(!isFavorited)
    notifySuccess(isFavorited ? 'Removed from favorites' : 'Added to favorites!')
  }

  const handlePreviewContent = () => {
    const previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes')
    if (previewWindow) {
      previewWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>${content.title}</title>
          <meta name="description" content="${content.meta_description || ''}">
          <style>
            body { 
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
              line-height: 1.6;
              color: #333;
            }
            h1 { color: #2563eb; margin-bottom: 20px; }
            h2 { color: #1e40af; margin-top: 30px; margin-bottom: 15px; }
            h3 { color: #1e3a8a; margin-top: 25px; margin-bottom: 10px; }
            p { margin-bottom: 15px; }
            .meta-info {
              background: #f3f4f6;
              padding: 15px;
              border-radius: 8px;
              margin-bottom: 20px;
              border-left: 4px solid #2563eb;
            }
          </style>
        </head>
        <body>
          <div class="meta-info">
            <strong>Meta Title:</strong> ${content.meta_title || content.title}<br>
            <strong>Meta Description:</strong> ${content.meta_description || 'No meta description'}
          </div>
          <h1>${content.title}</h1>
          ${content.body.replace(/\n\n/g, '</p><p>').replace(/^(.+)$/gm, '<p>$1</p>')}
        </body>
        </html>
      `)
      previewWindow.document.close()
    }
  }

  return (
    <div className="space-y-4">
      {/* Primary Actions */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Content Actions
          </h3>
          <Button
            onClick={handleToggleFavorite}
            variant="outline"
            size="sm"
            className={isFavorited ? 'text-red-500 border-red-500' : ''}
          >
            <HeartIcon className={`h-4 w-4 ${isFavorited ? 'fill-current' : ''}`} />
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
          <Button
            onClick={handleSaveContent}
            disabled={isSaving}
            className="w-full"
          >
            <BookmarkIcon className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save to Library'}
          </Button>

          <Button
            onClick={onEdit}
            variant="outline"
            className="w-full"
          >
            <PencilIcon className="h-4 w-4 mr-2" />
            Edit Content
          </Button>

          <Button
            onClick={handleCopyContent}
            disabled={isCopying}
            variant="outline"
            className="w-full"
          >
            <ClipboardDocumentIcon className="h-4 w-4 mr-2" />
            {isCopying ? 'Copying...' : 'Copy'}
          </Button>

          <Button
            onClick={handleDownloadContent}
            disabled={isDownloading}
            variant="outline"
            className="w-full"
          >
            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
            {isDownloading ? 'Downloading...' : 'Download'}
          </Button>
        </div>
      </Card>

      {/* Secondary Actions */}
      <Card className="p-6">
        <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Additional Options
        </h4>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <Button
            onClick={handlePreviewContent}
            variant="outline"
            className="w-full"
          >
            <EyeIcon className="h-4 w-4 mr-2" />
            Preview Page
          </Button>

          <Button
            onClick={onOptimize}
            variant="outline"
            className="w-full"
          >
            <SparklesIcon className="h-4 w-4 mr-2" />
            Optimize SEO
          </Button>

          <Button
            onClick={handleShareContent}
            disabled={isSharing}
            variant="outline"
            className="w-full"
          >
            <ShareIcon className="h-4 w-4 mr-2" />
            {isSharing ? 'Sharing...' : 'Share'}
          </Button>
        </div>
      </Card>

      {/* Export Options */}
      <Card className="p-6">
        <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Export Formats
        </h4>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <Button
            onClick={() => {
              const fullContent = `# ${content.title}\n\n${content.body}`
              const blob = new Blob([fullContent], { type: 'text/markdown' })
              const url = URL.createObjectURL(blob)
              const a = document.createElement('a')
              a.href = url
              a.download = `${keyword.replace(/\s+/g, '-')}-content.md`
              a.click()
              URL.revokeObjectURL(url)
              notifySuccess('Markdown file downloaded!')
            }}
            variant="outline"
            size="sm"
            className="w-full"
          >
            Markdown
          </Button>

          <Button
            onClick={() => {
              const html = `
                <!DOCTYPE html>
                <html>
                <head>
                  <title>${content.title}</title>
                  <meta name="description" content="${content.meta_description || ''}">
                </head>
                <body>
                  <h1>${content.title}</h1>
                  ${content.body.replace(/\n\n/g, '</p><p>').replace(/^(.+)$/gm, '<p>$1</p>')}
                </body>
                </html>
              `
              const blob = new Blob([html], { type: 'text/html' })
              const url = URL.createObjectURL(blob)
              const a = document.createElement('a')
              a.href = url
              a.download = `${keyword.replace(/\s+/g, '-')}-content.html`
              a.click()
              URL.revokeObjectURL(url)
              notifySuccess('HTML file downloaded!')
            }}
            variant="outline"
            size="sm"
            className="w-full"
          >
            HTML
          </Button>

          <Button
            onClick={() => {
              const json = JSON.stringify({
                title: content.title,
                body: content.body,
                meta_title: content.meta_title,
                meta_description: content.meta_description,
                keyword,
                content_type: contentType,
                generated_at: new Date().toISOString()
              }, null, 2)
              const blob = new Blob([json], { type: 'application/json' })
              const url = URL.createObjectURL(blob)
              const a = document.createElement('a')
              a.href = url
              a.download = `${keyword.replace(/\s+/g, '-')}-content.json`
              a.click()
              URL.revokeObjectURL(url)
              notifySuccess('JSON file downloaded!')
            }}
            variant="outline"
            size="sm"
            className="w-full"
          >
            JSON
          </Button>

          <Button
            onClick={async () => {
              try {
                const response = await contentService.exportContent([keyword], 'docx')
                if (response.success && response.data) {
                  window.open(response.data.download_url, '_blank')
                  notifySuccess('DOCX export started!')
                } else {
                  throw new Error('Export failed')
                }
              } catch (error) {
                notifyError('DOCX export failed')
              }
            }}
            variant="outline"
            size="sm"
            className="w-full"
          >
            DOCX
          </Button>
        </div>
      </Card>

      {/* Usage Tips */}
      <Card className="p-4 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
        <div className="flex items-start space-x-3">
          <SparklesIcon className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
          <div>
            <h5 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
              Pro Tips
            </h5>
            <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
              <li>• Save to library to build your content collection</li>
              <li>• Use the preview to see how it looks to readers</li>
              <li>• Export as HTML for direct website publishing</li>
              <li>• Optimize SEO to improve search rankings</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  )
}