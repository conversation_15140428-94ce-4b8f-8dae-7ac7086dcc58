import request from 'supertest';
import app from '../server.js';
import { DemoDataValidator } from '../../utils/demoDataDetection.js';

/**
 * Comprehensive Demo Data Validation Tests
 * Ensures all input points reject demo/placeholder data
 */

describe('Demo Data Validation System', () => {
  
  describe('DemoDataValidator Unit Tests', () => {
    
    test('should reject basic demo patterns', () => {
      const testCases = [
        'example keyword',
        'test content',
        'demo data',
        'sample text',
        'placeholder content',
        'lorem ipsum',
        'dummy content',
        'mock data',
        'fake content',
        'your keyword here'
      ];
      
      testCases.forEach(testCase => {
        const result = DemoDataValidator.validate(testCase);
        expect(result.isValid).toBe(false);
        expect(result.isDemo).toBe(true);
        expect(result.errors.length).toBeGreaterThan(0);
      });
    });
    
    test('should accept real data', () => {
      const testCases = [
        'digital marketing strategies',
        'sustainable energy solutions',
        'artificial intelligence applications',
        'cloud computing benefits',
        'cybersecurity best practices'
      ];
      
      testCases.forEach(testCase => {
        const result = DemoDataValidator.validate(testCase);
        expect(result.isValid).toBe(true);
        expect(result.isDemo).toBe(false);
        expect(result.errors.length).toBe(0);
      });
    });
    
    test('should reject demo URLs', () => {
      const demoUrls = [
        'https://example.com',
        'http://test.com',
        'https://demo.com/page',
        'http://localhost:3000',
        'https://placeholder.com'
      ];
      
      demoUrls.forEach(url => {
        const result = DemoDataValidator.validateURL(url);
        expect(result.isValid).toBe(false);
        expect(result.isDemo).toBe(true);
      });
    });
    
    test('should accept real URLs', () => {
      const realUrls = [
        'https://wikipedia.org',
        'https://github.com',
        'https://stackoverflow.com',
        'https://google.com',
        'https://microsoft.com'
      ];
      
      realUrls.forEach(url => {
        const result = DemoDataValidator.validateURL(url);
        expect(result.isValid).toBe(true);
        expect(result.isDemo).toBe(false);
      });
    });
    
    test('should reject Lorem Ipsum content', () => {
      const loremContent = `
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, 
        sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
      `;
      
      const result = DemoDataValidator.validateContent(loremContent);
      expect(result.isValid).toBe(false);
      expect(result.isDemo).toBe(true);
    });
    
    test('should validate batch data correctly', () => {
      const mixedData = [
        'real keyword here',
        'example keyword',
        'legitimate content',
        'demo content',
        'authentic data'
      ];
      
      const result = DemoDataValidator.validateBatch(mixedData);
      expect(result.valid.length).toBe(3);
      expect(result.invalid.length).toBe(2);
    });
  });
  
  describe('Content Generation API Demo Data Rejection', () => {
    
    test('should reject content generation with demo keyword', async () => {
      const demoRequest = {
        target_keyword: 'example keyword',
        content_type: 'blog_post',
        word_count: 1500
      };
      
      const response = await request(app)
        .post('/api/content/generate')
        .send(demoRequest)
        .expect(400);
      
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Demo data detected');
    });
    
    test('should reject content generation with demo location', async () => {
      const demoRequest = {
        target_keyword: 'digital marketing',
        location: 'Example City',
        content_type: 'blog_post'
      };
      
      const response = await request(app)
        .post('/api/content/generate')
        .send(demoRequest)
        .expect(400);
      
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Demo data detected');
    });
    
    test('should reject content generation with demo URLs', async () => {
      const demoRequest = {
        target_keyword: 'seo services',
        competitor_urls: ['https://example.com', 'https://test.com'],
        content_type: 'blog_post'
      };
      
      const response = await request(app)
        .post('/api/content/generate')
        .send(demoRequest)
        .expect(400);
      
      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Demo data detected');
    });
    
    test('should accept valid content generation request', async () => {
      const validRequest = {
        target_keyword: 'artificial intelligence benefits',
        location: 'San Francisco',
        content_type: 'blog_post',
        word_count: 1500,
        tone: 'professional'
      };
      
      // Note: This test requires OpenAI API key to work fully
      // In testing environment, we might mock the OpenAI response
      const response = await request(app)
        .post('/api/content/generate')
        .send(validRequest);
        
      if (process.env.OPENAI_API_KEY) {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      } else {
        // If no API key, should fail gracefully
        expect(response.status).toBe(400);
      }
    });
    
    test('should validate input before processing', async () => {
      const testInput = {
        target_keyword: 'sustainable energy',
        location: 'Austin',
        content_type: 'blog_post'
      };
      
      const response = await request(app)
        .post('/api/content/validate-input')
        .send(testInput)
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.validation.isValid).toBe(true);
    });
    
    test('should reject demo data in validation endpoint', async () => {
      const demoInput = {
        target_keyword: 'example keyword',
        location: 'Demo City',
        content_type: 'blog_post'
      };
      
      const response = await request(app)
        .post('/api/content/validate-input')
        .send(demoInput)
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.validation.isValid).toBe(false);
      expect(response.body.validation.errors.length).toBeGreaterThan(0);
    });
  });
  
  describe('Authority Links API Demo Data Rejection', () => {
    
    test('should reject authority link discovery with demo keyword', async () => {
      const demoRequest = {
        keyword: 'example keyword',
        industry: 'technology'
      };
      
      const response = await request(app)
        .post('/api/authority-links/discover')
        .send(demoRequest)
        .expect(400);
      
      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('DEMO_DATA_DETECTED');
    });
    
    test('should reject authority link discovery with demo context', async () => {
      const demoRequest = {
        keyword: 'artificial intelligence',
        context: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit',
        industry: 'technology'
      };
      
      const response = await request(app)
        .post('/api/authority-links/discover')
        .send(demoRequest)
        .expect(400);
      
      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('DEMO_DATA_DETECTED');
    });
    
    test('should detect demo links in validation', async () => {
      const demoLinks = [{
        url: 'https://example.com/article',
        title: 'Example Article',
        description: 'Sample description',
        domain: 'example.com',
        authorityScore: 80,
        relevanceScore: 0.9,
        sourceType: 'reference'
      }];
      
      const response = await request(app)
        .post('/api/authority-links/check-demo-data')
        .send({ links: demoLinks })
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.data.summary.demoLinksFound).toBe(1);
    });
    
    test('should accept real authority links', async () => {
      const realLinks = [{
        url: 'https://wikipedia.org/wiki/Artificial_intelligence',
        title: 'Artificial Intelligence - Wikipedia',
        description: 'Comprehensive article about AI',
        domain: 'wikipedia.org',
        authorityScore: 95,
        relevanceScore: 0.95,
        sourceType: 'wikipedia'
      }];
      
      const response = await request(app)
        .post('/api/authority-links/check-demo-data')
        .send({ links: realLinks })
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.data.summary.demoLinksFound).toBe(0);
      expect(response.body.data.summary.cleanLinks).toBe(1);
    });
    
    test('should validate single authority link', async () => {
      const realLink = {
        url: 'https://github.com/microsoft/TypeScript',
        title: 'TypeScript Official Repository',
        description: 'Official TypeScript repository',
        domain: 'github.com',
        authorityScore: 90,
        relevanceScore: 0.8,
        sourceType: 'reference'
      };
      
      const response = await request(app)
        .post('/api/authority-links/validate-single')
        .send({ link: realLink })
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.data.isDemoData).toBe(false);
    });
    
    test('should reject single demo authority link', async () => {
      const demoLink = {
        url: 'https://example.com/demo-article',
        title: 'Demo Article Title',
        description: 'Sample placeholder description',
        domain: 'example.com',
        authorityScore: 70,
        relevanceScore: 0.5,
        sourceType: 'reference'
      };
      
      const response = await request(app)
        .post('/api/authority-links/validate-single')
        .send({ link: demoLink })
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.data.isDemoData).toBe(true);
      expect(response.body.data.isValid).toBe(false);
    });
  });
  
  describe('Rate Limiting for Demo Data Attempts', () => {
    
    test('should apply rate limiting to demo data attempts', async () => {
      const demoRequest = {
        target_keyword: 'example keyword'
      };
      
      // Make multiple requests to trigger rate limiting
      const requests = Array(6).fill().map(() => 
        request(app)
          .post('/api/content/validate-input')
          .send(demoRequest)
      );
      
      const responses = await Promise.all(requests);
      
      // At least one should be rate limited
      const rateLimited = responses.some(res => res.status === 429);
      expect(rateLimited).toBe(true);
    });
  });
  
  describe('System Health Checks', () => {
    
    test('should report content system health', async () => {
      const response = await request(app)
        .get('/api/content/health')
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.health.services.demoValidator).toBe(true);
      expect(response.body.health.services.sequentialThinking).toBe(true);
    });
    
    test('should report authority links system health', async () => {
      const response = await request(app)
        .get('/api/authority-links/health')
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.health.capabilities.demoDataRejection).toBe(true);
    });
    
    test('should provide cache statistics', async () => {
      const response = await request(app)
        .get('/api/cache/stats')
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.cache).toBeDefined();
    });
  });
});

/**
 * Integration Tests for Complete Workflow
 */
describe('Complete Content Generation Workflow with Demo Data Protection', () => {
  
  test('should complete full content generation with real data', async () => {
    // Skip if no OpenAI API key
    if (!process.env.OPENAI_API_KEY) {
      console.log('Skipping full workflow test - no OpenAI API key');
      return;
    }
    
    const realRequest = {
      target_keyword: 'quantum computing applications',
      location: 'Boston',
      content_type: 'blog_post',
      word_count: 1000,
      tone: 'professional',
      industry: 'technology'
    };
    
    // Step 1: Validate input
    const validationResponse = await request(app)
      .post('/api/content/validate-input')
      .send(realRequest)
      .expect(200);
    
    expect(validationResponse.body.validation.isValid).toBe(true);
    
    // Step 2: Generate content
    const generationResponse = await request(app)
      .post('/api/content/generate')
      .send(realRequest)
      .timeout(30000); // 30 second timeout for AI generation
    
    if (generationResponse.status === 200) {
      expect(generationResponse.body.success).toBe(true);
      expect(generationResponse.body.data.content).toBeDefined();
      expect(generationResponse.body.data.qualityScore).toBeDefined();
    }
  });
  
  test('should block complete workflow with demo data', async () => {
    const demoRequest = {
      target_keyword: 'example keyword here',
      location: 'Test City',
      content_type: 'blog_post',
      competitor_urls: ['https://example.com'],
      industry: 'demo industry'
    };
    
    // Should fail at validation step
    const validationResponse = await request(app)
      .post('/api/content/validate-input')
      .send(demoRequest)
      .expect(200);
    
    expect(validationResponse.body.validation.isValid).toBe(false);
    
    // Should also fail at generation step
    const generationResponse = await request(app)
      .post('/api/content/generate')
      .send(demoRequest)
      .expect(400);
    
    expect(generationResponse.body.success).toBe(false);
    expect(generationResponse.body.error).toContain('Demo data detected');
  });
});
