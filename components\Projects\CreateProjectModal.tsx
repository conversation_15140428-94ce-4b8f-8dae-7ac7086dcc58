/**
 * CreateProjectModal Component
 * Enterprise SEO SAAS - Project creation with real data validation
 */

'use client'

import { useState } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import { XMarkIcon, ExclamationTriangleIcon, CheckCircleIcon, ShieldExclamationIcon } from '@heroicons/react/24/outline'
import { Project, CreateProjectData, IndustryType, ProjectValidation } from '@/types/project'
import { getIndustryInfo, validateProjectName, validateKeyword, validateCompetitorUrl, validateKeywordsBatch, validateCompetitorsBatch, generateProjectId, getDefaultProjectSettings } from '@/utils/projectHelpers'
import { DemoDataValidator, formatErrors } from '@/utils/demoDataDetection'

interface CreateProjectModalProps {
  isOpen: boolean
  onClose: () => void
  onProjectCreated: (project: Project) => void
}

export default function CreateProjectModal({ isOpen, onClose, onProjectCreated }: CreateProjectModalProps) {
  const [step, setStep] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const [fieldValidation, setFieldValidation] = useState<{
    name: { isValid: boolean; error?: string; isDemo?: boolean }
    description: { isValid: boolean; error?: string; isDemo?: boolean }
    website: { isValid: boolean; error?: string; isDemo?: boolean }
    keywords: { valid: number; invalid: number; demo: number; total: number }
    competitors: { valid: number; invalid: number; demo: number; total: number }
  }>({
    name: { isValid: true },
    description: { isValid: true },
    website: { isValid: true },
    keywords: { valid: 0, invalid: 0, demo: 0, total: 0 },
    competitors: { valid: 0, invalid: 0, demo: 0, total: 0 }
  })
  const [keywordsBatchValidation, setKeywordsBatchValidation] = useState({ valid: 0, invalid: 0, demo: 0, total: 0 })
  const [competitorsBatchValidation, setCompetitorsBatchValidation] = useState({ valid: 0, invalid: 0, demo: 0, total: 0 })

  const [formData, setFormData] = useState<CreateProjectData>({
    name: '',
    description: '',
    industry: 'technology',
    website: '',
    targetLocation: '',
    targetLanguage: 'en',
    keywords: [''],
    competitorUrls: [''],
    settings: getDefaultProjectSettings()
  })

  const industryOptions: IndustryType[] = [
    'technology', 'healthcare', 'finance', 'education', 'ecommerce',
    'real_estate', 'automotive', 'food_beverage', 'fashion', 'travel',
    'fitness', 'marketing', 'consulting', 'manufacturing', 'retail',
    'construction', 'legal', 'entertainment', 'sports', 'nonprofit', 'other'
  ]

  const languageOptions = [
    { value: 'en', label: 'English' },
    { value: 'es', label: 'Spanish' },
    { value: 'fr', label: 'French' },
    { value: 'de', label: 'German' },
    { value: 'it', label: 'Italian' },
    { value: 'pt', label: 'Portuguese' },
    { value: 'ru', label: 'Russian' },
    { value: 'ja', label: 'Japanese' },
    { value: 'ko', label: 'Korean' },
    { value: 'zh', label: 'Chinese' }
  ]

  const handleInputChange = (field: keyof CreateProjectData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    setValidationErrors([])
    
    // Real-time validation for specific fields
    if (field === 'name') {
      validateProjectNameField(value)
    } else if (field === 'description') {
      validateDescriptionField(value)
    } else if (field === 'website') {
      validateWebsiteField(value)
    } else if (field === 'keywords') {
      validateKeywordsBatchField(value)
    } else if (field === 'competitorUrls') {
      validateCompetitorsBatchField(value)
    }
  }

  // Real-time validation functions
  const validateProjectNameField = (name: string) => {
    const result = validateProjectName(name)
    const demoResult = DemoDataValidator.validate(name, { strict: true })
    
    setFieldValidation(prev => ({
      ...prev,
      name: {
        isValid: result.isValid,
        error: result.error,
        isDemo: demoResult.isDemo
      }
    }))
  }

  const validateDescriptionField = (description: string) => {
    const validationResult = DemoDataValidator.validateContent(description, {
      strict: true,
      field: 'Project description'
    })
    
    setFieldValidation(prev => ({
      ...prev,
      description: {
        isValid: validationResult.isValid,
        error: validationResult.errors[0],
        isDemo: validationResult.isDemo
      }
    }))
  }

  const validateWebsiteField = (website: string) => {
    if (!website.trim()) {
      setFieldValidation(prev => ({
        ...prev,
        website: { isValid: true }
      }))
      return
    }

    const validationResult = DemoDataValidator.validateURL(website, {
      strict: true,
      field: 'Project website'
    })
    
    setFieldValidation(prev => ({
      ...prev,
      website: {
        isValid: validationResult.isValid,
        error: validationResult.errors[0],
        isDemo: validationResult.isDemo
      }
    }))
  }

  const validateKeywordsBatchField = (keywords: string[]) => {
    const filteredKeywords = keywords.filter(k => k.trim())
    if (filteredKeywords.length === 0) {
      setKeywordsBatchValidation({ valid: 0, invalid: 0, demo: 0, total: 0 })
      return
    }

    const batchResult = validateKeywordsBatch(filteredKeywords)
    setKeywordsBatchValidation(batchResult.summary)
    
    setFieldValidation(prev => ({
      ...prev,
      keywords: batchResult.summary
    }))
  }

  const validateCompetitorsBatchField = (urls: string[]) => {
    const filteredUrls = urls.filter(url => url.trim())
    if (filteredUrls.length === 0) {
      setCompetitorsBatchValidation({ valid: 0, invalid: 0, demo: 0, total: 0 })
      return
    }

    const batchResult = validateCompetitorsBatch(filteredUrls)
    setCompetitorsBatchValidation(batchResult.summary)
    
    setFieldValidation(prev => ({
      ...prev,
      competitors: batchResult.summary
    }))
  }

  const handleKeywordChange = (index: number, value: string) => {
    const newKeywords = [...formData.keywords]
    newKeywords[index] = value
    setFormData(prev => ({ ...prev, keywords: newKeywords }))
    
    // Trigger batch validation
    validateKeywordsBatchField(newKeywords)
  }

  const addKeyword = () => {
    if (formData.keywords.length < 10) {
      const newKeywords = [...formData.keywords, '']
      setFormData(prev => ({ ...prev, keywords: newKeywords }))
      validateKeywordsBatchField(newKeywords)
    }
  }

  const removeKeyword = (index: number) => {
    if (formData.keywords.length > 1) {
      const newKeywords = formData.keywords.filter((_, i) => i !== index)
      setFormData(prev => ({ ...prev, keywords: newKeywords }))
      validateKeywordsBatchField(newKeywords)
    }
  }

  const handleCompetitorChange = (index: number, value: string) => {
    const newCompetitors = [...formData.competitorUrls]
    newCompetitors[index] = value
    setFormData(prev => ({ ...prev, competitorUrls: newCompetitors }))
    
    // Trigger batch validation
    validateCompetitorsBatchField(newCompetitors)
  }

  const addCompetitor = () => {
    if (formData.competitorUrls.length < 10) {
      const newUrls = [...formData.competitorUrls, '']
      setFormData(prev => ({ ...prev, competitorUrls: newUrls }))
      validateCompetitorsBatchField(newUrls)
    }
  }

  const removeCompetitor = (index: number) => {
    if (formData.competitorUrls.length > 1) {
      const newUrls = formData.competitorUrls.filter((_, i) => i !== index)
      setFormData(prev => ({ ...prev, competitorUrls: newUrls }))
      validateCompetitorsBatchField(newUrls)
    }
  }

  const validateStep1 = (): boolean => {
    const errors: string[] = []
    
    // Use comprehensive validation for all fields
    if (!fieldValidation.name.isValid || fieldValidation.name.isDemo) {
      errors.push(fieldValidation.name.error || 'Project name contains demo content')
    }
    
    if (formData.description && (!fieldValidation.description.isValid || fieldValidation.description.isDemo)) {
      errors.push(fieldValidation.description.error || 'Project description contains demo content')
    }
    
    if (formData.website && (!fieldValidation.website.isValid || fieldValidation.website.isDemo)) {
      errors.push(fieldValidation.website.error || 'Website URL contains demo content')
    }

    setValidationErrors(errors)
    return errors.length === 0
  }

  const validateStep2 = (): boolean => {
    const errors: string[] = []
    
    // Use batch validation results
    if (keywordsBatchValidation.total === 0) {
      errors.push('At least one keyword is required')
    } else if (keywordsBatchValidation.valid === 0) {
      errors.push('All keywords contain demo data or are invalid. Please provide real keywords.')
    } else if (keywordsBatchValidation.demo > 0) {
      errors.push(`${keywordsBatchValidation.demo} keyword(s) contain demo data. Please provide real keywords.`)
    }
    
    if (competitorsBatchValidation.total === 0) {
      errors.push('At least one competitor URL is required')
    } else if (competitorsBatchValidation.valid === 0) {
      errors.push('All competitor URLs contain demo data or are invalid. Please provide real URLs.')
    } else if (competitorsBatchValidation.demo > 0) {
      errors.push(`${competitorsBatchValidation.demo} competitor URL(s) contain demo data. Please provide real URLs.`)
    }

    setValidationErrors(errors)
    return errors.length === 0
  }

  const handleNext = () => {
    if (step === 1 && validateStep1()) {
      setStep(2)
    } else if (step === 2 && validateStep2()) {
      setStep(3)
    }
  }

  const handlePrevious = () => {
    setStep(step - 1)
    setValidationErrors([])
  }

  const handleSubmit = async () => {
    if (!validateStep2()) return

    setIsSubmitting(true)
    
    try {
      // Create new project with validated data
      const newProject: Project = {
        id: generateProjectId(),
        userId: 'current-user-id', // Would come from auth context
        name: formData.name,
        description: formData.description,
        industry: formData.industry,
        website: formData.website,
        targetLocation: formData.targetLocation,
        targetLanguage: formData.targetLanguage || 'en',
        status: 'draft',
        keywords: formData.keywords
          .filter(k => k.trim())
          .map((keyword, index) => ({
            id: `kw_${Date.now()}_${index}`,
            keyword: keyword.trim(),
            priority: 'medium',
            createdAt: new Date().toISOString()
          })),
        competitors: formData.competitorUrls
          .filter(url => url.trim())
          .map((url, index) => ({
            id: `comp_${Date.now()}_${index}`,
            url: url.trim(),
            domain: new URL(url.trim()).hostname,
            lastAnalyzed: new Date().toISOString(),
            isValid: true
          })),
        settings: formData.settings || getDefaultProjectSettings(),
        metrics: {
          totalContent: 0,
          totalWords: 0,
          averageSeoScore: 0,
          averageWordCount: 0,
          keywordsTracked: formData.keywords.filter(k => k.trim()).length,
          competitorsAnalyzed: formData.competitorUrls.filter(url => url.trim()).length,
          contentByType: {
            blog_post: 0,
            product_description: 0,
            landing_page: 0,
            meta_description: 0,
            social_media: 0,
            email_marketing: 0
          }
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isValidated: true,
        validationErrors: []
      }

      // In real implementation, save to Supabase
      await new Promise(resolve => setTimeout(resolve, 1000))

      onProjectCreated(newProject)
      handleClose()
    } catch (error) {
      console.error('Error creating project:', error)
      setValidationErrors(['Failed to create project. Please try again.'])
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    setStep(1)
    setFormData({
      name: '',
      description: '',
      industry: 'technology',
      website: '',
      targetLocation: '',
      targetLanguage: 'en',
      keywords: [''],
      competitorUrls: [''],
      settings: getDefaultProjectSettings()
    })
    setFieldValidation({
      name: { isValid: true },
      description: { isValid: true },
      website: { isValid: true },
      keywords: { valid: 0, invalid: 0, demo: 0, total: 0 },
      competitors: { valid: 0, invalid: 0, demo: 0, total: 0 }
    })
    setKeywordsBatchValidation({ valid: 0, invalid: 0, demo: 0, total: 0 })
    setCompetitorsBatchValidation({ valid: 0, invalid: 0, demo: 0, total: 0 })
    setValidationErrors([])
    onClose()
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-lg bg-white p-6 text-left align-middle shadow-xl transition-all">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <Dialog.Title className="text-xl font-semibold text-gray-900">
                      Create New Project
                    </Dialog.Title>
                    <p className="text-sm text-gray-500 mt-1">
                      Step {step} of 3: {
                        step === 1 ? 'Basic Information' :
                        step === 2 ? 'Keywords & Competitors' :
                        'Review & Create'
                      }
                    </p>
                  </div>
                  <button
                    onClick={handleClose}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                {/* Progress Bar */}
                <div className="mb-8">
                  <div className="flex items-center">
                    {[1, 2, 3].map((stepNumber) => (
                      <div key={stepNumber} className="flex items-center">
                        <div className={`
                          w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                          ${stepNumber <= step 
                            ? 'bg-blue-600 text-white' 
                            : 'bg-gray-200 text-gray-600'
                          }
                        `}>
                          {stepNumber}
                        </div>
                        {stepNumber < 3 && (
                          <div className={`
                            w-12 h-1 mx-2
                            ${stepNumber < step ? 'bg-blue-600' : 'bg-gray-200'}
                          `} />
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Validation Errors */}
                {validationErrors.length > 0 && (
                  <div className="mb-6 space-y-4">
                    {/* Primary Error Summary */}
                    <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-start gap-3">
                        <ExclamationTriangleIcon className="h-5 w-5 text-red-400 flex-shrink-0 mt-0.5" />
                        <div className="flex-1">
                          <h4 className="text-sm font-medium text-red-800 mb-2">
                            ❌ Validation Failed - Please fix the following issues:
                          </h4>
                          <div className="space-y-2">
                            {validationErrors.map((error, index) => (
                              <div key={index} className="flex items-start gap-2">
                                <span className="inline-block w-1.5 h-1.5 bg-red-500 rounded-full mt-2 flex-shrink-0" />
                                <span className="text-sm text-red-700">{error}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Demo Data Warning */}
                    {(fieldValidation.name.isDemo || 
                      fieldValidation.description.isDemo || 
                      fieldValidation.website.isDemo ||
                      keywordsBatchValidation.demo > 0 ||
                      competitorsBatchValidation.demo > 0) && (
                      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div className="flex items-start gap-3">
                          <ShieldExclamationIcon className="h-5 w-5 text-yellow-500 flex-shrink-0 mt-0.5" />
                          <div>
                            <h4 className="text-sm font-medium text-yellow-800 mb-2">
                              ⚠️ Demo Content Detected
                            </h4>
                            <p className="text-sm text-yellow-700 mb-3">
                              This platform requires real data only. Demo, test, or placeholder content is not allowed to ensure genuine SEO insights.
                            </p>
                            <div className="space-y-1 text-xs text-yellow-600">
                              <div className="font-medium">Common demo patterns to avoid:</div>
                              <ul className="list-disc list-inside space-y-0.5 ml-2">
                                <li>Project names: "Test Project", "Demo Site", "Example Business"</li>
                                <li>Keywords: "example keyword", "sample search term", "test phrase"</li>
                                <li>URLs: example.com, test.com, demo.com, placeholder.com</li>
                                <li>Lorem ipsum or placeholder text in descriptions</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Validation Summary */}
                    {(keywordsBatchValidation.total > 0 || competitorsBatchValidation.total > 0) && (
                      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="flex items-start gap-3">
                          <CheckCircleIcon className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                          <div>
                            <h4 className="text-sm font-medium text-blue-800 mb-2">
                              📊 Validation Summary
                            </h4>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              {keywordsBatchValidation.total > 0 && (
                                <div>
                                  <div className="font-medium text-blue-800 mb-1">Keywords</div>
                                  <div className="space-y-1 text-blue-700">
                                    <div>✅ {keywordsBatchValidation.valid} Valid</div>
                                    {keywordsBatchValidation.demo > 0 && (
                                      <div>⚠️ {keywordsBatchValidation.demo} Demo detected</div>
                                    )}
                                    {(keywordsBatchValidation.total - keywordsBatchValidation.valid - keywordsBatchValidation.demo) > 0 && (
                                      <div>❌ {keywordsBatchValidation.total - keywordsBatchValidation.valid - keywordsBatchValidation.demo} Invalid</div>
                                    )}
                                  </div>
                                </div>
                              )}
                              {competitorsBatchValidation.total > 0 && (
                                <div>
                                  <div className="font-medium text-blue-800 mb-1">Competitors</div>
                                  <div className="space-y-1 text-blue-700">
                                    <div>✅ {competitorsBatchValidation.valid} Valid</div>
                                    {competitorsBatchValidation.demo > 0 && (
                                      <div>⚠️ {competitorsBatchValidation.demo} Demo detected</div>
                                    )}
                                    {(competitorsBatchValidation.total - competitorsBatchValidation.valid - competitorsBatchValidation.demo) > 0 && (
                                      <div>❌ {competitorsBatchValidation.total - competitorsBatchValidation.valid - competitorsBatchValidation.demo} Invalid</div>
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Step Content */}
                <div className="mb-8">
                  {step === 1 && (
                    <div className="space-y-6">
                      {/* Project Name */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Project Name *
                        </label>
                        <input
                          type="text"
                          value={formData.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          placeholder="Enter your project name (no demo data)"
                          className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent ${
                            fieldValidation.name.isValid === false
                              ? 'border-red-300 focus:ring-red-500'
                              : fieldValidation.name.isDemo
                              ? 'border-yellow-300 focus:ring-yellow-500'
                              : 'border-gray-300 focus:ring-blue-500'
                          }`}
                        />
                        {fieldValidation.name.error && (
                          <div className="mt-1 flex items-center text-sm text-red-600">
                            <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                            {fieldValidation.name.error}
                          </div>
                        )}
                        {fieldValidation.name.isDemo && (
                          <div className="mt-1 flex items-center text-sm text-yellow-600">
                            <ShieldExclamationIcon className="h-4 w-4 mr-1" />
                            Demo content detected. Please provide real project name.
                          </div>
                        )}
                        <p className="text-xs text-gray-500 mt-1">
                          Provide a real project name. Demo names like "Test Project" are not allowed.
                        </p>
                      </div>

                      {/* Description */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Description
                        </label>
                        <textarea
                          value={formData.description}
                          onChange={(e) => handleInputChange('description', e.target.value)}
                          placeholder="Describe your project goals and target audience"
                          rows={3}
                          className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent ${
                            fieldValidation.description.isValid === false
                              ? 'border-red-300 focus:ring-red-500'
                              : fieldValidation.description.isDemo
                              ? 'border-yellow-300 focus:ring-yellow-500'
                              : 'border-gray-300 focus:ring-blue-500'
                          }`}
                        />
                        {fieldValidation.description.error && (
                          <div className="mt-1 flex items-center text-sm text-red-600">
                            <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                            {fieldValidation.description.error}
                          </div>
                        )}
                        {fieldValidation.description.isDemo && (
                          <div className="mt-1 flex items-center text-sm text-yellow-600">
                            <ShieldExclamationIcon className="h-4 w-4 mr-1" />
                            Demo content detected. Please provide real description.
                          </div>
                        )}
                      </div>

                      {/* Industry */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Industry *
                        </label>
                        <select
                          value={formData.industry}
                          onChange={(e) => handleInputChange('industry', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          {industryOptions.map((industry) => {
                            const info = getIndustryInfo(industry)
                            return (
                              <option key={industry} value={industry}>
                                {info.icon} {info.label}
                              </option>
                            )
                          })}
                        </select>
                      </div>

                      {/* Website */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Website URL
                        </label>
                        <input
                          type="url"
                          value={formData.website}
                          onChange={(e) => handleInputChange('website', e.target.value)}
                          placeholder="https://yourwebsite.com"
                          className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent ${
                            fieldValidation.website.isValid === false
                              ? 'border-red-300 focus:ring-red-500'
                              : fieldValidation.website.isDemo
                              ? 'border-yellow-300 focus:ring-yellow-500'
                              : 'border-gray-300 focus:ring-blue-500'
                          }`}
                        />
                        {fieldValidation.website.error && (
                          <div className="mt-1 flex items-center text-sm text-red-600">
                            <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                            {fieldValidation.website.error}
                          </div>
                        )}
                        {fieldValidation.website.isDemo && (
                          <div className="mt-1 flex items-center text-sm text-yellow-600">
                            <ShieldExclamationIcon className="h-4 w-4 mr-1" />
                            Demo URL detected. Please provide real website URL.
                          </div>
                        )}
                        <p className="text-xs text-gray-500 mt-1">
                          Your actual website URL. Demo sites like example.com are not allowed.
                        </p>
                      </div>

                      {/* Target Location and Language */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Target Location
                          </label>
                          <input
                            type="text"
                            value={formData.targetLocation}
                            onChange={(e) => handleInputChange('targetLocation', e.target.value)}
                            placeholder="e.g., United States, New York, Global"
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Target Language
                          </label>
                          <select
                            value={formData.targetLanguage}
                            onChange={(e) => handleInputChange('targetLanguage', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          >
                            {languageOptions.map((lang) => (
                              <option key={lang.value} value={lang.value}>
                                {lang.label}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>
                    </div>
                  )}

                  {step === 2 && (
                    <div className="space-y-6">
                      {/* Keywords */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Target Keywords * (Max 10)
                        </label>
                        <div className="space-y-3">
                          {formData.keywords.map((keyword, index) => (
                            <div key={index} className="flex gap-2">
                              <input
                                type="text"
                                value={keyword}
                                onChange={(e) => handleKeywordChange(index, e.target.value)}
                                placeholder="Enter a real target keyword"
                                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              />
                              {formData.keywords.length > 1 && (
                                <button
                                  onClick={() => removeKeyword(index)}
                                  className="px-3 py-2 text-red-600 hover:text-red-800"
                                >
                                  Remove
                                </button>
                              )}
                            </div>
                          ))}
                          {formData.keywords.length < 10 && (
                            <button
                              onClick={addKeyword}
                              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                            >
                              + Add Another Keyword
                            </button>
                          )}
                        </div>
                        {/* Keywords Validation Summary */}
                        {keywordsBatchValidation.total > 0 && (
                          <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-gray-700">Keywords Validation</span>
                              <div className="flex items-center gap-4 text-xs">
                                <span className="text-green-600">✓ {keywordsBatchValidation.valid} Valid</span>
                                {keywordsBatchValidation.demo > 0 && (
                                  <span className="text-yellow-600">⚠ {keywordsBatchValidation.demo} Demo</span>
                                )}
                                {keywordsBatchValidation.invalid > 0 && (
                                  <span className="text-red-600">✗ {keywordsBatchValidation.invalid} Errors</span>
                                )}
                              </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-green-500 h-2 rounded-full transition-all"
                                style={{ width: `${keywordsBatchValidation.total > 0 ? (keywordsBatchValidation.valid / keywordsBatchValidation.total) * 100 : 0}%` }}
                              />
                            </div>
                          </div>
                        )}
                        <p className="text-xs text-gray-500 mt-1">
                          Enter real keywords you want to rank for. Demo keywords like "example keyword" are not allowed.
                        </p>
                      </div>

                      {/* Competitors */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Competitor URLs * (Max 10)
                        </label>
                        <div className="space-y-3">
                          {formData.competitorUrls.map((url, index) => (
                            <div key={index} className="flex gap-2">
                              <input
                                type="url"
                                value={url}
                                onChange={(e) => handleCompetitorChange(index, e.target.value)}
                                placeholder="https://competitor-website.com"
                                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              />
                              {formData.competitorUrls.length > 1 && (
                                <button
                                  onClick={() => removeCompetitor(index)}
                                  className="px-3 py-2 text-red-600 hover:text-red-800"
                                >
                                  Remove
                                </button>
                              )}
                            </div>
                          ))}
                          {formData.competitorUrls.length < 10 && (
                            <button
                              onClick={addCompetitor}
                              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                            >
                              + Add Another Competitor
                            </button>
                          )}
                        </div>
                        {/* Competitors Validation Summary */}
                        {competitorsBatchValidation.total > 0 && (
                          <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-gray-700">Competitors Validation</span>
                              <div className="flex items-center gap-4 text-xs">
                                <span className="text-green-600">✓ {competitorsBatchValidation.valid} Valid</span>
                                {competitorsBatchValidation.demo > 0 && (
                                  <span className="text-yellow-600">⚠ {competitorsBatchValidation.demo} Demo</span>
                                )}
                                {competitorsBatchValidation.invalid > 0 && (
                                  <span className="text-red-600">✗ {competitorsBatchValidation.invalid} Errors</span>
                                )}
                              </div>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-green-500 h-2 rounded-full transition-all"
                                style={{ width: `${competitorsBatchValidation.total > 0 ? (competitorsBatchValidation.valid / competitorsBatchValidation.total) * 100 : 0}%` }}
                              />
                            </div>
                          </div>
                        )}
                        <p className="text-xs text-gray-500 mt-1">
                          Enter real competitor websites. Demo sites like example.com are not allowed.
                        </p>
                      </div>
                    </div>
                  )}

                  {step === 3 && (
                    <div className="space-y-6">
                      <h3 className="text-lg font-medium text-gray-900">Review Project Details</h3>
                      
                      <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                        <div>
                          <label className="text-sm font-medium text-gray-500">Project Name</label>
                          <p className="text-gray-900">{formData.name}</p>
                        </div>
                        
                        <div>
                          <label className="text-sm font-medium text-gray-500">Industry</label>
                          <p className="text-gray-900">
                            {getIndustryInfo(formData.industry).icon} {getIndustryInfo(formData.industry).label}
                          </p>
                        </div>
                        
                        <div>
                          <label className="text-sm font-medium text-gray-500">Keywords ({formData.keywords.filter(k => k.trim()).length})</label>
                          <p className="text-gray-900">
                            {formData.keywords.filter(k => k.trim()).join(', ')}
                          </p>
                        </div>
                        
                        <div>
                          <label className="text-sm font-medium text-gray-500">Competitors ({formData.competitorUrls.filter(url => url.trim()).length})</label>
                          <div className="text-gray-900 space-y-1">
                            {formData.competitorUrls.filter(url => url.trim()).map((url, index) => (
                              <div key={index} className="text-sm">
                                {new URL(url.trim()).hostname}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                      
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 className="text-sm font-medium text-blue-800 mb-2">What happens next?</h4>
                        <ul className="text-sm text-blue-700 space-y-1">
                          <li>• Your project will be created with draft status</li>
                          <li>• We'll analyze your competitors and keywords</li>
                          <li>• You can start generating SEO-optimized content</li>
                          <li>• All data will be validated for authenticity</li>
                        </ul>
                      </div>
                    </div>
                  )}
                </div>

                {/* Footer */}
                <div className="flex items-center justify-between">
                  <div>
                    {step > 1 && (
                      <button
                        onClick={handlePrevious}
                        className="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium"
                      >
                        Previous
                      </button>
                    )}
                  </div>
                  
                  <div className="flex gap-3">
                    <button
                      onClick={handleClose}
                      className="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium"
                    >
                      Cancel
                    </button>
                    
                    {step < 3 ? (
                      <button
                        onClick={handleNext}
                        className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg"
                      >
                        Next
                      </button>
                    ) : (
                      <button
                        onClick={handleSubmit}
                        disabled={isSubmitting}
                        className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium rounded-lg inline-flex items-center gap-2"
                      >
                        {isSubmitting && (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        )}
                        {isSubmitting ? 'Creating...' : 'Create Project'}
                      </button>
                    )}
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}