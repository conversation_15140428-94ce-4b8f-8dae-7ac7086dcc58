'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

// Demo user data
const DEMO_USER = {
  id: 'demo-user-123',
  email: '<EMAIL>',
  user_metadata: {
    full_name: 'Demo User',
    avatar_url: '/images/default-avatar.png'
  }
};

const DEMO_SESSION = {
  access_token: 'demo-access-token',
  token_type: 'bearer',
  expires_in: 3600,
  refresh_token: 'demo-refresh-token',
  user: DEMO_USER
};

interface DemoAuthContextType {
  user: any | null;
  session: any | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: Error | null }>;
  signUp: (email: string, password: string, metadata?: any) => Promise<{ error: Error | null }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: Error | null }>;
  updateProfile: (data: any) => Promise<{ error: Error | null }>;
  isSubscribed: (tier?: 'free' | 'pro' | 'enterprise') => boolean;
  userTier: 'free' | 'pro' | 'enterprise';
  usageCount: number;
  usageLimit: number;
  refreshUserData: () => Promise<void>;
}

const DemoAuthContext = createContext<DemoAuthContextType | undefined>(undefined);

export function DemoAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<any | null>(null);
  const [session, setSession] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [userProfile, setUserProfile] = useState<any>({
    id: 'demo-user-123',
    email: '<EMAIL>',
    full_name: 'Demo User',
    subscription_tier: 'pro',
    usage_count: 5,
    usage_limit: 100,
    created_at: new Date().toISOString()
  });
  const router = useRouter();

  useEffect(() => {
    // Check for existing demo session
    const savedSession = localStorage.getItem('demo_session');
    if (savedSession) {
      setSession(DEMO_SESSION);
      setUser(DEMO_USER);
    }
    setLoading(false);
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      
      // Demo mode: Accept any email/password
      if (email && password) {
        const demoUser = {
          ...DEMO_USER,
          email: email,
          user_metadata: {
            ...DEMO_USER.user_metadata,
            full_name: email.split('@')[0]
          }
        };
        
        setUser(demoUser);
        setSession(DEMO_SESSION);
        localStorage.setItem('demo_session', 'true');
        
        return { error: null };
      }
      
      return { error: new Error('Please enter email and password') };
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, metadata?: any) => {
    // Same as sign in for demo
    return signIn(email, password);
  };

  const signOut = async () => {
    setLoading(true);
    setUser(null);
    setSession(null);
    localStorage.removeItem('demo_session');
    router.push('/');
    setLoading(false);
  };

  const resetPassword = async (email: string) => {
    // Demo mode: Just show success
    return { error: null };
  };

  const updateProfile = async (data: any) => {
    setUserProfile({ ...userProfile, ...data });
    return { error: null };
  };

  const refreshUserData = async () => {
    // Demo mode: No-op
  };

  const isSubscribed = (tier?: 'free' | 'pro' | 'enterprise') => {
    if (!tier) return userProfile.subscription_tier !== 'free';
    
    const tierLevels = {
      'free': 1,
      'pro': 2,
      'enterprise': 3
    };
    
    const userTierLevel = tierLevels[userProfile.subscription_tier] || 0;
    const requiredTierLevel = tierLevels[tier] || 0;
    
    return userTierLevel >= requiredTierLevel;
  };

  const value: DemoAuthContextType = {
    user,
    session,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updateProfile,
    isSubscribed,
    userTier: userProfile.subscription_tier,
    usageCount: userProfile.usage_count,
    usageLimit: userProfile.usage_limit,
    refreshUserData,
  };

  return (
    <DemoAuthContext.Provider value={value}>
      {children}
    </DemoAuthContext.Provider>
  );
}

export function useDemoAuth() {
  const context = useContext(DemoAuthContext);
  if (context === undefined) {
    throw new Error('useDemoAuth must be used within a DemoAuthProvider');
  }
  return context;
}

// Protected route wrapper for demo mode
export function DemoProtectedRoute({ 
  children,
  redirectTo = '/auth/login',
  requireSubscription,
}: {
  children: React.ReactNode;
  redirectTo?: string;
  requireSubscription?: 'free' | 'pro' | 'enterprise';
}) {
  const { user, loading, isSubscribed } = useDemoAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      if (!user) {
        router.push(redirectTo);
      } else if (requireSubscription && !isSubscribed(requireSubscription)) {
        router.push('/pricing');
      }
    }
  }, [user, loading, requireSubscription, isSubscribed, router, redirectTo]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  if (requireSubscription && !isSubscribed(requireSubscription)) {
    return null;
  }

  return <>{children}</>;
}