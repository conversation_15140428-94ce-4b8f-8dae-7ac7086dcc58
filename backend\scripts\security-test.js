import fetch from 'node-fetch';
import crypto from 'crypto';

/**
 * Security Testing & Penetration Testing Simulation
 * 
 * Tests various security vulnerabilities and attack vectors
 * to validate the security measures implemented in the application.
 */

const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:5000';
const API_BASE = `${BASE_URL}/api`;

class SecurityTester {
  constructor() {
    this.testResults = [];
    this.vulnerabilities = [];
    this.recommendations = [];
  }

  /**
   * Run all security tests
   */
  async runAllTests() {
    console.log('🔒 Starting Security Testing Suite...\n');
    
    try {
      await this.testSQLInjection();
      await this.testXSSAttacks();
      await this.testCSRFProtection();
      await this.testRateLimiting();
      await this.testInputValidation();
      await this.testSessionSecurity();
      await this.testAPIKeySecurity();
      await this.testDemoDataRejection();
      await this.testCORSConfiguration();
      await this.testSecurityHeaders();
      await this.testFileUploadSecurity();
      await this.testAuthenticationBypass();
      
      this.generateReport();
    } catch (error) {
      console.error('Security testing failed:', error);
    }
  }

  /**
   * Test SQL Injection vulnerabilities
   */
  async testSQLInjection() {
    console.log('🧪 Testing SQL Injection...');
    
    const sqlPayloads = [
      "' OR '1'='1",
      "'; DROP TABLE users; --",
      "' UNION SELECT * FROM users --",
      "admin'--",
      "' OR 1=1 --",
      "1; EXEC xp_cmdshell('dir')"
    ];

    for (const payload of sqlPayloads) {
      try {
        const response = await fetch(`${API_BASE}/content/generate`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            primaryKeyword: payload,
            contentType: 'blog_post'
          })
        });

        const result = await response.json();
        
        if (response.ok && result.success) {
          this.vulnerabilities.push({
            type: 'SQL Injection',
            severity: 'CRITICAL',
            payload: payload,
            description: 'SQL injection payload was accepted'
          });
        } else {
          this.testResults.push({
            test: 'SQL Injection',
            payload: payload,
            status: 'BLOCKED',
            message: 'Payload properly rejected'
          });
        }
      } catch (error) {
        this.testResults.push({
          test: 'SQL Injection',
          payload: payload,
          status: 'ERROR',
          message: error.message
        });
      }
    }
  }

  /**
   * Test XSS (Cross-Site Scripting) vulnerabilities
   */
  async testXSSAttacks() {
    console.log('🧪 Testing XSS Attacks...');
    
    const xssPayloads = [
      "<script>alert('XSS')</script>",
      "<img src=x onerror=alert('XSS')>",
      "javascript:alert('XSS')",
      "<svg onload=alert('XSS')>",
      "<iframe src=javascript:alert('XSS')>",
      "';alert('XSS');//",
      "<body onload=alert('XSS')>",
      "<<SCRIPT>alert('XSS');//<</SCRIPT>"
    ];

    for (const payload of xssPayloads) {
      try {
        const response = await fetch(`${API_BASE}/content/generate`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            primaryKeyword: payload,
            contentType: 'blog_post'
          })
        });

        const result = await response.json();
        
        if (response.ok && result.success && result.content && result.content.includes(payload)) {
          this.vulnerabilities.push({
            type: 'XSS',
            severity: 'HIGH',
            payload: payload,
            description: 'XSS payload was not sanitized'
          });
        } else {
          this.testResults.push({
            test: 'XSS',
            payload: payload,
            status: 'BLOCKED',
            message: 'Payload properly sanitized'
          });
        }
      } catch (error) {
        this.testResults.push({
          test: 'XSS',
          payload: payload,
          status: 'ERROR',
          message: error.message
        });
      }
    }
  }

  /**
   * Test CSRF Protection
   */
  async testCSRFProtection() {
    console.log('🧪 Testing CSRF Protection...');
    
    try {
      // Attempt to make a POST request without CSRF token
      const response = await fetch(`${API_BASE}/content/generate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          primaryKeyword: 'test keyword',
          contentType: 'blog_post'
        })
      });

      if (response.status === 403) {
        this.testResults.push({
          test: 'CSRF Protection',
          status: 'PASS',
          message: 'CSRF protection is working'
        });
      } else {
        this.vulnerabilities.push({
          type: 'CSRF',
          severity: 'MEDIUM',
          description: 'CSRF protection may be missing'
        });
      }
    } catch (error) {
      this.testResults.push({
        test: 'CSRF Protection',
        status: 'ERROR',
        message: error.message
      });
    }
  }

  /**
   * Test Rate Limiting
   */
  async testRateLimiting() {
    console.log('🧪 Testing Rate Limiting...');
    
    const requests = [];
    const startTime = Date.now();
    
    // Send 150 requests rapidly
    for (let i = 0; i < 150; i++) {
      requests.push(
        fetch(`${API_BASE}/health`, {
          method: 'GET'
        })
      );
    }

    try {
      const responses = await Promise.all(requests);
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      
      if (rateLimitedResponses.length > 0) {
        this.testResults.push({
          test: 'Rate Limiting',
          status: 'PASS',
          message: `Rate limiting triggered after ${responses.length - rateLimitedResponses.length} requests`
        });
      } else {
        this.vulnerabilities.push({
          type: 'Rate Limiting',
          severity: 'MEDIUM',
          description: 'Rate limiting may not be working properly'
        });
      }
    } catch (error) {
      this.testResults.push({
        test: 'Rate Limiting',
        status: 'ERROR',
        message: error.message
      });
    }
  }

  /**
   * Test Input Validation
   */
  async testInputValidation() {
    console.log('🧪 Testing Input Validation...');
    
    const invalidInputs = [
      { primaryKeyword: '', contentType: 'blog_post' }, // Empty required field
      { primaryKeyword: 'a', contentType: 'blog_post' }, // Too short
      { primaryKeyword: 'a'.repeat(101), contentType: 'blog_post' }, // Too long
      { primaryKeyword: 'test', contentType: 'invalid_type' }, // Invalid type
      { primaryKeyword: 'test', contentLength: -1 }, // Invalid number
      { primaryKeyword: 'test', competitorUrls: ['not-a-url'] }, // Invalid URL
    ];

    for (const input of invalidInputs) {
      try {
        const response = await fetch(`${API_BASE}/content/generate`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(input)
        });

        if (response.status === 400) {
          this.testResults.push({
            test: 'Input Validation',
            input: JSON.stringify(input),
            status: 'PASS',
            message: 'Invalid input properly rejected'
          });
        } else {
          this.vulnerabilities.push({
            type: 'Input Validation',
            severity: 'MEDIUM',
            input: JSON.stringify(input),
            description: 'Invalid input was accepted'
          });
        }
      } catch (error) {
        this.testResults.push({
          test: 'Input Validation',
          input: JSON.stringify(input),
          status: 'ERROR',
          message: error.message
        });
      }
    }
  }

  /**
   * Test Session Security
   */
  async testSessionSecurity() {
    console.log('🧪 Testing Session Security...');
    
    try {
      // Test session cookie security
      const response = await fetch(`${API_BASE}/health`);
      const cookies = response.headers.get('set-cookie');
      
      let sessionSecure = false;
      if (cookies) {
        sessionSecure = cookies.includes('HttpOnly') && cookies.includes('Secure');
      }

      if (sessionSecure || process.env.NODE_ENV === 'development') {
        this.testResults.push({
          test: 'Session Security',
          status: 'PASS',
          message: 'Session cookies are properly secured'
        });
      } else {
        this.vulnerabilities.push({
          type: 'Session Security',
          severity: 'MEDIUM',
          description: 'Session cookies may not be properly secured'
        });
      }
    } catch (error) {
      this.testResults.push({
        test: 'Session Security',
        status: 'ERROR',
        message: error.message
      });
    }
  }

  /**
   * Test API Key Security
   */
  async testAPIKeySecurity() {
    console.log('🧪 Testing API Key Security...');
    
    try {
      // Test if API keys are exposed in responses
      const response = await fetch(`${API_BASE}/health`);
      const text = await response.text();
      
      const exposedKeys = [
        'sk-', 'pk-', 'API_KEY', 'SECRET_KEY', 'OPENAI_API_KEY',
        'SUPABASE_', 'SERPER_', 'FIRECRAWL_'
      ];

      let keysExposed = false;
      for (const keyPattern of exposedKeys) {
        if (text.includes(keyPattern)) {
          keysExposed = true;
          break;
        }
      }

      if (!keysExposed) {
        this.testResults.push({
          test: 'API Key Security',
          status: 'PASS',
          message: 'API keys are not exposed in responses'
        });
      } else {
        this.vulnerabilities.push({
          type: 'API Key Exposure',
          severity: 'CRITICAL',
          description: 'API keys may be exposed in responses'
        });
      }
    } catch (error) {
      this.testResults.push({
        test: 'API Key Security',
        status: 'ERROR',
        message: error.message
      });
    }
  }

  /**
   * Test Demo Data Rejection
   */
  async testDemoDataRejection() {
    console.log('🧪 Testing Demo Data Rejection...');
    
    const demoDataInputs = [
      'test keyword here',
      'demo content example',
      'sample placeholder text',
      'lorem ipsum dolor',
      'your keyword here',
      'example.com',
      'fake data input'
    ];

    for (const input of demoDataInputs) {
      try {
        const response = await fetch(`${API_BASE}/content/generate`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            primaryKeyword: input,
            contentType: 'blog_post'
          })
        });

        if (response.status === 400) {
          this.testResults.push({
            test: 'Demo Data Rejection',
            input: input,
            status: 'PASS',
            message: 'Demo data properly rejected'
          });
        } else {
          this.vulnerabilities.push({
            type: 'Demo Data Acceptance',
            severity: 'LOW',
            input: input,
            description: 'Demo data was accepted'
          });
        }
      } catch (error) {
        this.testResults.push({
          test: 'Demo Data Rejection',
          input: input,
          status: 'ERROR',
          message: error.message
        });
      }
    }
  }

  /**
   * Test CORS Configuration
   */
  async testCORSConfiguration() {
    console.log('🧪 Testing CORS Configuration...');
    
    try {
      const response = await fetch(`${API_BASE}/health`, {
        method: 'OPTIONS',
        headers: {
          'Origin': 'https://malicious-site.com',
          'Access-Control-Request-Method': 'POST'
        }
      });

      const corsHeaders = response.headers.get('access-control-allow-origin');
      
      if (!corsHeaders || corsHeaders === 'null') {
        this.testResults.push({
          test: 'CORS Configuration',
          status: 'PASS',
          message: 'CORS properly restricts unauthorized origins'
        });
      } else if (corsHeaders === '*') {
        this.vulnerabilities.push({
          type: 'CORS Misconfiguration',
          severity: 'MEDIUM',
          description: 'CORS allows all origins'
        });
      }
    } catch (error) {
      this.testResults.push({
        test: 'CORS Configuration',
        status: 'ERROR',
        message: error.message
      });
    }
  }

  /**
   * Test Security Headers
   */
  async testSecurityHeaders() {
    console.log('🧪 Testing Security Headers...');
    
    try {
      const response = await fetch(`${API_BASE}/health`);
      const headers = response.headers;
      
      const requiredHeaders = [
        'x-content-type-options',
        'x-frame-options',
        'x-xss-protection',
        'strict-transport-security',
        'content-security-policy'
      ];

      const missingHeaders = [];
      for (const header of requiredHeaders) {
        if (!headers.get(header)) {
          missingHeaders.push(header);
        }
      }

      if (missingHeaders.length === 0) {
        this.testResults.push({
          test: 'Security Headers',
          status: 'PASS',
          message: 'All required security headers are present'
        });
      } else {
        this.vulnerabilities.push({
          type: 'Missing Security Headers',
          severity: 'MEDIUM',
          description: `Missing headers: ${missingHeaders.join(', ')}`
        });
      }
    } catch (error) {
      this.testResults.push({
        test: 'Security Headers',
        status: 'ERROR',
        message: error.message
      });
    }
  }

  /**
   * Test File Upload Security
   */
  async testFileUploadSecurity() {
    console.log('🧪 Testing File Upload Security...');
    
    // This would test file upload endpoints if they exist
    // For now, we'll just check if there are any upload endpoints
    
    const uploadEndpoints = [
      '/api/upload',
      '/api/files/upload',
      '/api/content/upload'
    ];

    for (const endpoint of uploadEndpoints) {
      try {
        const response = await fetch(`${BASE_URL}${endpoint}`, {
          method: 'POST',
          body: new FormData()
        });

        if (response.status !== 404) {
          this.recommendations.push({
            type: 'File Upload Security',
            message: `Upload endpoint ${endpoint} exists - ensure proper validation is implemented`
          });
        }
      } catch (error) {
        // Expected for non-existent endpoints
      }
    }
  }

  /**
   * Test Authentication Bypass
   */
  async testAuthenticationBypass() {
    console.log('🧪 Testing Authentication Bypass...');
    
    const bypassPayloads = [
      '../admin',
      'admin/../',
      '/admin',
      '?admin=true',
      '&admin=1',
      '#admin'
    ];

    for (const payload of bypassPayloads) {
      try {
        const response = await fetch(`${API_BASE}/admin${payload}`);
        
        if (response.status === 200) {
          this.vulnerabilities.push({
            type: 'Authentication Bypass',
            severity: 'CRITICAL',
            payload: payload,
            description: 'Possible authentication bypass detected'
          });
        } else {
          this.testResults.push({
            test: 'Authentication Bypass',
            payload: payload,
            status: 'BLOCKED',
            message: 'Bypass attempt properly blocked'
          });
        }
      } catch (error) {
        this.testResults.push({
          test: 'Authentication Bypass',
          payload: payload,
          status: 'ERROR',
          message: error.message
        });
      }
    }
  }

  /**
   * Generate comprehensive security report
   */
  generateReport() {
    console.log('\n📊 Security Testing Report');
    console.log('=' .repeat(50));
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.status === 'PASS').length;
    const failedTests = this.testResults.filter(r => r.status === 'BLOCKED').length;
    const errorTests = this.testResults.filter(r => r.status === 'ERROR').length;
    
    console.log(`\n📈 Test Summary:`);
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Passed: ${passedTests}`);
    console.log(`   Blocked: ${failedTests}`);
    console.log(`   Errors: ${errorTests}`);
    
    // Vulnerabilities
    if (this.vulnerabilities.length > 0) {
      console.log(`\n🚨 Vulnerabilities Found: ${this.vulnerabilities.length}`);
      this.vulnerabilities.forEach((vuln, index) => {
        console.log(`\n   ${index + 1}. ${vuln.type} (${vuln.severity})`);
        console.log(`      ${vuln.description}`);
        if (vuln.payload) {
          console.log(`      Payload: ${vuln.payload}`);
        }
      });
    } else {
      console.log(`\n✅ No critical vulnerabilities found!`);
    }
    
    // Recommendations
    if (this.recommendations.length > 0) {
      console.log(`\n💡 Recommendations:`);
      this.recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec.type}: ${rec.message}`);
      });
    }
    
    // Security score
    const securityScore = Math.round((passedTests / Math.max(totalTests, 1)) * 100);
    const vulnerabilityPenalty = this.vulnerabilities.length * 10;
    const finalScore = Math.max(0, securityScore - vulnerabilityPenalty);
    
    console.log(`\n🏆 Security Score: ${finalScore}/100`);
    
    if (finalScore >= 90) {
      console.log('   Status: Excellent Security 🛡️');
    } else if (finalScore >= 70) {
      console.log('   Status: Good Security 🔒');
    } else if (finalScore >= 50) {
      console.log('   Status: Needs Improvement ⚠️');
    } else {
      console.log('   Status: Critical Issues 🚨');
    }
    
    console.log(`\n✅ Security testing completed!`);
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new SecurityTester();
  tester.runAllTests();
}

export { SecurityTester };
export default SecurityTester;