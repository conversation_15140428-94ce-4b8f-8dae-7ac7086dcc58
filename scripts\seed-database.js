import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration. Please check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Database Seeding Script
 * Populates the database with initial data for development and testing
 */
async function seedDatabase() {
  console.log('🌱 Seeding database with initial data...');

  try {
    // Seed content templates
    console.log('📝 Seeding content templates...');
    await seedContentTemplates();
    
    // Seed authority links
    console.log('🔗 Seeding authority links...');
    await seedAuthorityLinks();
    
    // Seed demo projects (if in development mode)
    if (process.env.NODE_ENV === 'development') {
      console.log('🚀 Seeding demo projects...');
      await seedDemoProjects();
    }
    
    console.log('✅ Database seeding completed successfully!');
    console.log('🎉 Your SEO SAAS application is ready with initial data.');
    
  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    process.exit(1);
  }
}

/**
 * Seed content templates
 */
async function seedContentTemplates() {
  const templates = [
    {
      name: 'Blog Post Template',
      description: 'Standard blog post template with SEO optimization',
      template_content: `# {title}

## Introduction
{introduction}

## Main Content
{main_content}

## Key Points
{key_points}

## Conclusion
{conclusion}

---
*Published by {author} on {date}*`,
      template_type: 'blog_post',
      industry: 'general'
    },
    {
      name: 'Product Page Template',
      description: 'E-commerce product page template with conversion focus',
      template_content: `# {product_name}

## Product Overview
{product_overview}

## Features & Benefits
{features_benefits}

## Technical Specifications
{specifications}

## Customer Reviews
{reviews}

## Call to Action
{cta_section}`,
      template_type: 'product_page',
      industry: 'ecommerce'
    },
    {
      name: 'Service Page Template',
      description: 'Professional service page template with authority building',
      template_content: `# {service_name}

## Service Overview
{service_overview}

## Our Approach
{approach}

## Why Choose Us
{why_choose_us}

## Case Studies
{case_studies}

## Get Started
{get_started}`,
      template_type: 'service_page',
      industry: 'services'
    },
    {
      name: 'How-To Guide Template',
      description: 'Comprehensive how-to guide template for educational content',
      template_content: `# How to {topic}

## What You'll Learn
{learning_objectives}

## Prerequisites
{prerequisites}

## Step-by-Step Guide
{step_by_step}

## Common Mistakes to Avoid
{common_mistakes}

## Final Thoughts
{conclusion}`,
      template_type: 'how_to_guide',
      industry: 'education'
    },
    {
      name: 'Local Business Template',
      description: 'Local business content template with local SEO focus',
      template_content: `# {business_name} - {service} in {location}

## About Our {service}
{service_description}

## Serving {location} Area
{location_info}

## Our Services
{services_list}

## Contact Information
{contact_info}

## Customer Testimonials
{testimonials}`,
      template_type: 'local_business',
      industry: 'local'
    }
  ];

  for (const template of templates) {
    try {
      const { error } = await supabase
        .from('content_templates')
        .insert(template);
      
      if (error) {
        console.warn(`⚠️  Template seeding warning: ${error.message}`);
      } else {
        console.log(`✅ Seeded template: ${template.name}`);
      }
    } catch (error) {
      console.warn(`⚠️  Template seeding error: ${error.message}`);
    }
  }
}

/**
 * Seed authority links
 */
async function seedAuthorityLinks() {
  const authorityLinks = [
    {
      domain: 'wikipedia.org',
      url: 'https://en.wikipedia.org',
      authority_score: 95,
      industry: 'general',
      keywords: ['encyclopedia', 'knowledge', 'research', 'information'],
      is_active: true
    },
    {
      domain: 'hubspot.com',
      url: 'https://blog.hubspot.com',
      authority_score: 85,
      industry: 'marketing',
      keywords: ['marketing', 'sales', 'inbound', 'CRM'],
      is_active: true
    },
    {
      domain: 'moz.com',
      url: 'https://moz.com/blog',
      authority_score: 90,
      industry: 'seo',
      keywords: ['seo', 'search', 'optimization', 'marketing'],
      is_active: true
    },
    {
      domain: 'searchengineland.com',
      url: 'https://searchengineland.com',
      authority_score: 88,
      industry: 'seo',
      keywords: ['search', 'google', 'seo', 'sem'],
      is_active: true
    },
    {
      domain: 'harvard.edu',
      url: 'https://www.harvard.edu',
      authority_score: 98,
      industry: 'education',
      keywords: ['education', 'research', 'academic', 'university'],
      is_active: true
    },
    {
      domain: 'techcrunch.com',
      url: 'https://techcrunch.com',
      authority_score: 86,
      industry: 'technology',
      keywords: ['technology', 'startups', 'innovation', 'tech'],
      is_active: true
    },
    {
      domain: 'forbes.com',
      url: 'https://www.forbes.com',
      authority_score: 92,
      industry: 'business',
      keywords: ['business', 'finance', 'entrepreneur', 'leadership'],
      is_active: true
    },
    {
      domain: 'entrepreneur.com',
      url: 'https://www.entrepreneur.com',
      authority_score: 84,
      industry: 'business',
      keywords: ['entrepreneur', 'startup', 'business', 'small business'],
      is_active: true
    },
    {
      domain: 'webmd.com',
      url: 'https://www.webmd.com',
      authority_score: 87,
      industry: 'health',
      keywords: ['health', 'medical', 'wellness', 'healthcare'],
      is_active: true
    },
    {
      domain: 'mayoclinic.org',
      url: 'https://www.mayoclinic.org',
      authority_score: 94,
      industry: 'health',
      keywords: ['health', 'medical', 'clinic', 'healthcare'],
      is_active: true
    }
  ];

  for (const link of authorityLinks) {
    try {
      const { error } = await supabase
        .from('authority_links')
        .insert(link);
      
      if (error) {
        console.warn(`⚠️  Authority link seeding warning: ${error.message}`);
      } else {
        console.log(`✅ Seeded authority link: ${link.domain}`);
      }
    } catch (error) {
      console.warn(`⚠️  Authority link seeding error: ${error.message}`);
    }
  }
}

/**
 * Seed demo projects for development
 */
async function seedDemoProjects() {
  console.log('🚀 Creating demo user and projects for development...');
  
  // Note: In a real application, you would create these through the authentication system
  // This is just for development purposes
  const demoProjects = [
    {
      name: 'Digital Marketing Agency',
      description: 'Local digital marketing agency focusing on small businesses',
      website_url: 'https://example-marketing-agency.com',
      target_keywords: ['digital marketing', 'local seo', 'social media marketing'],
      location: 'San Francisco, CA',
      industry: 'marketing'
    },
    {
      name: 'E-commerce Fashion Store',
      description: 'Online fashion retailer specializing in sustainable clothing',
      website_url: 'https://example-fashion-store.com',
      target_keywords: ['sustainable fashion', 'organic clothing', 'eco-friendly apparel'],
      location: 'New York, NY',
      industry: 'ecommerce'
    },
    {
      name: 'Tech Startup Blog',
      description: 'Technology blog covering AI, blockchain, and startup trends',
      website_url: 'https://example-tech-blog.com',
      target_keywords: ['artificial intelligence', 'blockchain technology', 'startup trends'],
      location: 'Austin, TX',
      industry: 'technology'
    }
  ];

  console.log('ℹ️  Demo projects data prepared (requires authenticated user for actual creation)');
  console.log('📝 To create demo projects, sign up through the application interface');
}

// Run the seeding
seedDatabase();