/**
 * Real-time Notifications Component
 * Live notification system with toast notifications and notification center
 */

'use client'

import React, { useState, useEffect } from 'react'
import { useRealtimeNotifications } from '@/lib/realtime/realtime-hooks'
import { useRealtime } from './RealtimeProvider'
import Card from '@/components/UI/Card'
import Button from '@/components/UI/Button'
import Badge from '@/components/UI/Badge'
import {
  BellIcon,
  XMarkIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  EyeIcon,
  EyeSlashIcon,
  TrashIcon,
  Cog6ToothIcon,
  FunnelIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline'

interface NotificationItem {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  timestamp: number
  read: boolean
  userId: string
  metadata?: Record<string, any>
  actions?: Array<{
    label: string
    action: () => void
    variant?: 'primary' | 'secondary' | 'danger'
  }>
}

interface ToastNotificationProps {
  notification: NotificationItem
  onDismiss: (id: string) => void
  onAction?: (id: string, action: string) => void
}

const ToastNotification = ({ notification, onDismiss, onAction }: ToastNotificationProps) => {
  const [isVisible, setIsVisible] = useState(true)
  const [progress, setProgress] = useState(100)

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false)
      setTimeout(() => onDismiss(notification.id), 300)
    }, 5000)

    const progressInterval = setInterval(() => {
      setProgress(prev => Math.max(0, prev - 2))
    }, 100)

    return () => {
      clearTimeout(timer)
      clearInterval(progressInterval)
    }
  }, [notification.id, onDismiss])

  const getIcon = () => {
    switch (notification.type) {
      case 'success':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />
      case 'warning':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
      case 'error':
        return <XCircleIcon className="h-5 w-5 text-red-500" />
      default:
        return <InformationCircleIcon className="h-5 w-5 text-blue-500" />
    }
  }

  const getBorderColor = () => {
    switch (notification.type) {
      case 'success':
        return 'border-l-green-500'
      case 'warning':
        return 'border-l-yellow-500'
      case 'error':
        return 'border-l-red-500'
      default:
        return 'border-l-blue-500'
    }
  }

  const getProgressColor = () => {
    switch (notification.type) {
      case 'success':
        return 'bg-green-500'
      case 'warning':
        return 'bg-yellow-500'
      case 'error':
        return 'bg-red-500'
      default:
        return 'bg-blue-500'
    }
  }

  return (
    <div
      className={`
        transform transition-all duration-300 ease-in-out
        ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
      `}
    >
      <Card className={`p-4 border-l-4 ${getBorderColor()} shadow-lg max-w-sm`}>
        <div className="flex items-start space-x-3">
          {getIcon()}
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {notification.title}
              </h4>
              <button
                onClick={() => {
                  setIsVisible(false)
                  setTimeout(() => onDismiss(notification.id), 300)
                }}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {notification.message}
            </p>
            {notification.actions && notification.actions.length > 0 && (
              <div className="flex items-center space-x-2 mt-3">
                {notification.actions.map((action, index) => (
                  <Button
                    key={index}
                    variant={action.variant || 'secondary'}
                    size="sm"
                    onClick={() => {
                      action.action()
                      onAction?.(notification.id, action.label)
                    }}
                  >
                    {action.label}
                  </Button>
                ))}
              </div>
            )}
          </div>
        </div>
        
        {/* Progress bar */}
        <div className="mt-3">
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
            <div
              className={`h-1 rounded-full transition-all duration-100 ${getProgressColor()}`}
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
      </Card>
    </div>
  )
}

interface NotificationCenterProps {
  isOpen: boolean
  onClose: () => void
  userId: string
}

const NotificationCenter = ({ isOpen, onClose, userId }: NotificationCenterProps) => {
  const { notifications, markAsRead, markAllAsRead } = useRealtimeNotifications(userId)
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all')
  const [typeFilter, setTypeFilter] = useState<string>('all')

  const filteredNotifications = notifications.filter(notification => {
    const matchesReadFilter = 
      filter === 'all' || 
      (filter === 'unread' && !notification.read) ||
      (filter === 'read' && notification.read)
    
    const matchesTypeFilter = 
      typeFilter === 'all' || notification.type === typeFilter

    return matchesReadFilter && matchesTypeFilter
  })

  const unreadCount = notifications.filter(n => !n.read).length

  const formatTimeAgo = (timestamp: number) => {
    const diff = Date.now() - timestamp
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (days > 0) return `${days}d ago`
    if (hours > 0) return `${hours}h ago`
    if (minutes > 0) return `${minutes}m ago`
    return 'Just now'
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />
      case 'warning':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
      case 'error':
        return <XCircleIcon className="h-5 w-5 text-red-500" />
      default:
        return <InformationCircleIcon className="h-5 w-5 text-blue-500" />
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      
      <div className="absolute right-0 top-0 h-full w-full max-w-md bg-white dark:bg-gray-900 shadow-xl">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <BellIcon className="h-6 w-6 text-gray-700 dark:text-gray-300" />
                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Notifications
                </h2>
                {unreadCount > 0 && (
                  <Badge variant="error" size="sm">
                    {unreadCount}
                  </Badge>
                )}
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Controls */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <FunnelIcon className="h-4 w-4 text-gray-500" />
                <select
                  value={filter}
                  onChange={(e) => setFilter(e.target.value as any)}
                  className="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-800"
                >
                  <option value="all">All</option>
                  <option value="unread">Unread</option>
                  <option value="read">Read</option>
                </select>
                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-800"
                >
                  <option value="all">All Types</option>
                  <option value="info">Info</option>
                  <option value="success">Success</option>
                  <option value="warning">Warning</option>
                  <option value="error">Error</option>
                </select>
              </div>
              
              {unreadCount > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={markAllAsRead}
                >
                  <CheckIcon className="h-4 w-4 mr-1" />
                  Mark all read
                </Button>
              )}
            </div>
          </div>

          {/* Notifications List */}
          <div className="flex-1 overflow-y-auto">
            {filteredNotifications.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-center p-8">
                <BellIcon className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  No notifications
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {filter === 'unread' 
                    ? "You're all caught up!" 
                    : 'Notifications will appear here when you receive them.'}
                </p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {filteredNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors ${
                      !notification.read ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      {getNotificationIcon(notification.type)}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className={`text-sm font-medium ${
                            !notification.read 
                              ? 'text-gray-900 dark:text-gray-100' 
                              : 'text-gray-700 dark:text-gray-300'
                          }`}>
                            {notification.title}
                          </h4>
                          <div className="flex items-center space-x-2">
                            <span className="text-xs text-gray-500">
                              {formatTimeAgo(notification.timestamp)}
                            </span>
                            {!notification.read && (
                              <button
                                onClick={() => markAsRead(notification.id)}
                                className="text-blue-600 hover:text-blue-700 dark:text-blue-400"
                                title="Mark as read"
                              >
                                <EyeIcon className="h-4 w-4" />
                              </button>
                            )}
                          </div>
                        </div>
                        <p className={`text-sm mt-1 ${
                          !notification.read 
                            ? 'text-gray-700 dark:text-gray-300' 
                            : 'text-gray-600 dark:text-gray-400'
                        }`}>
                          {notification.message}
                        </p>
                        {notification.actions && notification.actions.length > 0 && (
                          <div className="flex items-center space-x-2 mt-2">
                            {notification.actions.map((action, index) => (
                              <Button
                                key={index}
                                variant={action.variant || 'secondary'}
                                size="sm"
                                onClick={action.action}
                              >
                                {action.label}
                              </Button>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500">
                {filteredNotifications.length} notifications
              </span>
              <Button variant="ghost" size="sm">
                <Cog6ToothIcon className="h-4 w-4 mr-1" />
                Settings
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

interface RealtimeNotificationsProps {
  userId: string
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
  maxToasts?: number
  enableSound?: boolean
}

export default function RealtimeNotifications({
  userId,
  position = 'top-right',
  maxToasts = 5,
  enableSound = true
}: RealtimeNotificationsProps) {
  const [toastNotifications, setToastNotifications] = useState<NotificationItem[]>([])
  const [showNotificationCenter, setShowNotificationCenter] = useState(false)
  const { notifications, unreadCount } = useRealtimeNotifications(userId)

  // Handle new notifications
  useEffect(() => {
    const latestNotification = notifications[0]
    if (latestNotification && !latestNotification.read) {
      // Add to toast notifications
      setToastNotifications(prev => {
        const updated = [latestNotification, ...prev].slice(0, maxToasts)
        return updated
      })

      // Play notification sound
      if (enableSound) {
        try {
          const audio = new Audio('/sounds/notification.mp3')
          audio.volume = 0.3
          audio.play().catch(() => {
            // Ignore audio play errors (user interaction required)
          })
        } catch (error) {
          // Ignore audio errors
        }
      }
    }
  }, [notifications, maxToasts, enableSound])

  const handleDismissToast = (id: string) => {
    setToastNotifications(prev => prev.filter(n => n.id !== id))
  }

  const handleToastAction = (id: string, action: string) => {
    console.log(`Toast action: ${action} for notification ${id}`)
    handleDismissToast(id)
  }

  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4'
      case 'bottom-right':
        return 'bottom-4 right-4'
      case 'bottom-left':
        return 'bottom-4 left-4'
      default:
        return 'top-4 right-4'
    }
  }

  return (
    <>
      {/* Toast Notifications */}
      <div className={`fixed ${getPositionClasses()} z-50 space-y-2`}>
        {toastNotifications.map((notification) => (
          <ToastNotification
            key={notification.id}
            notification={notification}
            onDismiss={handleDismissToast}
            onAction={handleToastAction}
          />
        ))}
      </div>

      {/* Notification Center Trigger */}
      <div className="relative">
        <button
          onClick={() => setShowNotificationCenter(true)}
          className="relative p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
          title="Notifications"
        >
          <BellIcon className="h-6 w-6" />
          {unreadCount > 0 && (
            <>
              <Badge
                variant="error"
                size="sm"
                className="absolute -top-1 -right-1 min-w-[1.25rem] h-5 flex items-center justify-center text-xs"
              >
                {unreadCount > 99 ? '99+' : unreadCount}
              </Badge>
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-ping" />
            </>
          )}
        </button>
      </div>

      {/* Notification Center */}
      <NotificationCenter
        isOpen={showNotificationCenter}
        onClose={() => setShowNotificationCenter(false)}
        userId={userId}
      />
    </>
  )
}

// Notification Helper Functions
export const notificationHelpers = {
  createNotification: (
    title: string,
    message: string,
    type: 'info' | 'success' | 'warning' | 'error' = 'info',
    actions?: Array<{
      label: string
      action: () => void
      variant?: 'primary' | 'secondary' | 'danger'
    }>
  ): NotificationItem => ({
    id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    title,
    message,
    type,
    timestamp: Date.now(),
    read: false,
    userId: '',
    actions
  }),

  createTaskNotification: (taskName: string, action: string, assignee?: string) => {
    return notificationHelpers.createNotification(
      `Task ${action}`,
      `${taskName}${assignee ? ` assigned to ${assignee}` : ''}`,
      action === 'completed' ? 'success' : 'info',
      [
        {
          label: 'View Task',
          action: () => console.log('View task'),
          variant: 'primary'
        }
      ]
    )
  },

  createProjectNotification: (projectName: string, action: string) => {
    return notificationHelpers.createNotification(
      `Project ${action}`,
      `${projectName} status has been updated`,
      'info',
      [
        {
          label: 'View Project',
          action: () => console.log('View project'),
          variant: 'primary'
        }
      ]
    )
  },

  createSystemNotification: (message: string, type: 'maintenance' | 'update' | 'error') => {
    return notificationHelpers.createNotification(
      type === 'maintenance' ? 'System Maintenance' : 
      type === 'update' ? 'System Update' : 'System Error',
      message,
      type === 'error' ? 'error' : type === 'maintenance' ? 'warning' : 'info'
    )
  }
}

// Export additional components
export { NotificationCenter, ToastNotification }