// Global Search Component - Real-time filtering and search functionality
class GlobalSearchComponent {
    constructor() {
        this.searchInput = null;
        this.searchResults = null;
        this.searchHistory = this.loadSearchHistory();
        this.recentSearches = this.loadRecentSearches();
        this.isSearchOpen = false;
        this.currentQuery = '';
        this.searchTimeout = null;
        this.searchData = {
            projects: [],
            content: [],
            keywords: []
        };
        
        this.init();
    }
    
    init() {
        this.createSearchElements();
        this.bindEvents();
        this.loadSearchData();
    }
    
    createSearchElements() {
        // Create search overlay if it doesn't exist
        if (!document.getElementById('globalSearchOverlay')) {
            const overlay = document.createElement('div');
            overlay.id = 'globalSearchOverlay';
            overlay.className = 'global-search-overlay hidden';
            overlay.innerHTML = this.getSearchHTML();
            document.body.appendChild(overlay);
        }
        
        // Initialize search input references
        this.searchInput = document.getElementById('globalSearchInput');
        this.searchResults = document.getElementById('searchResults');
        this.overlay = document.getElementById('globalSearchOverlay');
    }
    
    getSearchHTML() {
        return `
            <div class="search-modal">
                <div class="search-header">
                    <div class="search-input-container">
                        <svg class="search-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <input 
                            type="text" 
                            id="globalSearchInput" 
                            placeholder="Search projects, content, keywords..."
                            class="search-input"
                            autocomplete="off"
                        >
                        <div class="search-shortcuts">
                            <span class="shortcut-key">ESC</span>
                        </div>
                    </div>
                </div>
                
                <div class="search-content">
                    <div class="search-suggestions" id="searchSuggestions">
                        <div class="suggestions-section">
                            <h4>Recent Searches</h4>
                            <div class="recent-searches" id="recentSearches"></div>
                        </div>
                        
                        <div class="suggestions-section">
                            <h4>Quick Actions</h4>
                            <div class="quick-actions">
                                <button class="quick-action" onclick="globalSearch.quickAction('create-project')">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Create New Project
                                </button>
                                <button class="quick-action" onclick="globalSearch.quickAction('generate-content')">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                    Generate Content
                                </button>
                                <button class="quick-action" onclick="globalSearch.quickAction('view-analytics')">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    View Analytics
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="search-results hidden" id="searchResults">
                        <div class="results-section" id="projectResults">
                            <h4>Projects</h4>
                            <div class="results-list" id="projectResultsList"></div>
                        </div>
                        
                        <div class="results-section" id="contentResults">
                            <h4>Content</h4>
                            <div class="results-list" id="contentResultsList"></div>
                        </div>
                        
                        <div class="results-section" id="keywordResults">
                            <h4>Keywords</h4>
                            <div class="results-list" id="keywordResultsList"></div>
                        </div>
                    </div>
                </div>
                
                <div class="search-footer">
                    <div class="search-tips">
                        <span class="tip">
                            <kbd>↑</kbd><kbd>↓</kbd> to navigate
                        </span>
                        <span class="tip">
                            <kbd>Enter</kbd> to select
                        </span>
                        <span class="tip">
                            <kbd>ESC</kbd> to close
                        </span>
                    </div>
                </div>
            </div>
        `;
    }
    
    bindEvents() {
        // Keyboard shortcut to open search (Ctrl+K or Cmd+K)
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                this.openSearch();
            }
            
            if (e.key === 'Escape' && this.isSearchOpen) {
                this.closeSearch();
            }
        });
        
        // Search input events
        if (this.searchInput) {
            this.searchInput.addEventListener('input', (e) => {
                this.handleSearchInput(e.target.value);
            });
            
            this.searchInput.addEventListener('keydown', (e) => {
                this.handleSearchKeydown(e);
            });
        }
        
        // Close search when clicking overlay
        if (this.overlay) {
            this.overlay.addEventListener('click', (e) => {
                if (e.target === this.overlay) {
                    this.closeSearch();
                }
            });
        }
        
        // Bind to existing search inputs
        const existingSearchInputs = document.querySelectorAll('.search-input');
        existingSearchInputs.forEach(input => {
            if (input.id !== 'globalSearchInput') {
                input.addEventListener('focus', () => {
                    this.openSearch();
                    this.searchInput.focus();
                });
            }
        });
    }
    
    openSearch() {
        this.isSearchOpen = true;
        this.overlay.classList.remove('hidden');
        this.searchInput.focus();
        this.displayRecentSearches();
        
        // Add animation class
        setTimeout(() => {
            this.overlay.classList.add('search-open');
        }, 10);
    }
    
    closeSearch() {
        this.isSearchOpen = false;
        this.overlay.classList.remove('search-open');
        
        setTimeout(() => {
            this.overlay.classList.add('hidden');
            this.searchInput.value = '';
            this.showSuggestions();
        }, 200);
    }
    
    handleSearchInput(query) {
        this.currentQuery = query;
        
        // Clear previous timeout
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
        
        // Debounce search
        this.searchTimeout = setTimeout(() => {
            if (query.trim().length > 0) {
                this.performSearch(query.trim());
                this.showAutocompleteSuggestions(query.trim());
            } else {
                this.showSuggestions();
            }
        }, 300);
    }
    
    showAutocompleteSuggestions(query) {
        if (window.searchAutocomplete) {
            const suggestions = window.searchAutocomplete.generateSuggestions(query, this.getCurrentContext());
            this.displayAutocompleteSuggestions(suggestions);
        }
    }
    
    displayAutocompleteSuggestions(suggestions) {
        const container = document.getElementById('searchSuggestions');
        if (!container) return;
        
        if (suggestions.length === 0) {
            container.innerHTML = '<div class="no-suggestions">No suggestions found</div>';
            return;
        }
        
        container.innerHTML = suggestions.map(suggestion => {
            if (suggestion.type === 'section') {
                return `<div class="suggestion-section-header">${suggestion.text}</div>`;
            }
            
            return `
                <button class="suggestion-item suggestion-${suggestion.type}" onclick="globalSearch.selectSuggestion('${suggestion.text}', '${suggestion.action}')">
                    <div class="suggestion-icon">
                        ${this.getSuggestionIcon(suggestion.icon || suggestion.type)}
                    </div>
                    <div class="suggestion-content">
                        <div class="suggestion-title">${suggestion.text}</div>
                        ${suggestion.description ? `<div class="suggestion-description">${suggestion.description}</div>` : ''}
                    </div>
                    ${suggestion.score ? `<div class="suggestion-score">${Math.round(suggestion.score)}%</div>` : ''}
                </button>
            `;
        }).join('');
    }
    
    getSuggestionIcon(type) {
        const icons = {
            project: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>',
            keyword: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>',
            action: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>',
            content: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>',
            analytics: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>',
            clock: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>',
            trending: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>',
            plus: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>'
        };
        
        return `<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">${icons[type] || icons.action}</svg>`;
    }
    
    selectSuggestion(text, action) {
        this.addToSearchHistory(text);
        if (window.searchAutocomplete) {
            window.searchAutocomplete.addToHistory(text);
        }
        
        // Execute the action if it's a function call
        if (action && action.startsWith('globalSearch.')) {
            eval(action);
        } else {
            this.performSearch(text);
        }
        
        this.closeSearch();
    }
    
    getCurrentContext() {
        const path = window.location.pathname;
        const page = path.split('/').pop().replace('.html', '');
        return page || 'general';
    }
    
    handleSearchKeydown(e) {
        // TODO: Implement arrow key navigation through results
        if (e.key === 'Enter') {
            this.selectFirstResult();
        }
    }
    
    performSearch(query) {
        const results = this.searchAllData(query);
        this.displaySearchResults(results);
        this.hideSuggestions();
    }
    
    searchAllData(query) {
        const lowerQuery = query.toLowerCase();
        
        const projectResults = this.searchData.projects.filter(project => 
            project.name.toLowerCase().includes(lowerQuery) ||
            project.keyword.toLowerCase().includes(lowerQuery) ||
            project.industry.toLowerCase().includes(lowerQuery)
        );
        
        const contentResults = this.searchData.content.filter(content =>
            content.title.toLowerCase().includes(lowerQuery) ||
            content.description.toLowerCase().includes(lowerQuery) ||
            content.project.toLowerCase().includes(lowerQuery)
        );
        
        const keywordResults = this.searchData.keywords.filter(keyword =>
            keyword.term.toLowerCase().includes(lowerQuery) ||
            keyword.industry.toLowerCase().includes(lowerQuery)
        );
        
        return {
            projects: projectResults,
            content: contentResults,
            keywords: keywordResults
        };
    }
    
    displaySearchResults(results) {
        this.searchResults.classList.remove('hidden');
        
        // Display project results
        this.displayProjectResults(results.projects);
        this.displayContentResults(results.content);
        this.displayKeywordResults(results.keywords);
    }
    
    displayProjectResults(projects) {
        const container = document.getElementById('projectResultsList');
        
        if (projects.length === 0) {
            container.innerHTML = '<div class="no-results">No projects found</div>';
            return;
        }
        
        container.innerHTML = projects.map(project => `
            <div class="result-item" onclick="globalSearch.selectProject('${project.id}')">
                <div class="result-icon">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                </div>
                <div class="result-content">
                    <div class="result-title">${project.name}</div>
                    <div class="result-description">${project.keyword} • ${project.industry}</div>
                </div>
                <div class="result-meta">
                    <span class="score-badge score-${this.getScoreClass(project.score)}">${project.score}</span>
                </div>
            </div>
        `).join('');
    }
    
    displayContentResults(content) {
        const container = document.getElementById('contentResultsList');
        
        if (content.length === 0) {
            container.innerHTML = '<div class="no-results">No content found</div>';
            return;
        }
        
        container.innerHTML = content.map(item => `
            <div class="result-item" onclick="globalSearch.selectContent('${item.id}')">
                <div class="result-icon">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="result-content">
                    <div class="result-title">${item.title}</div>
                    <div class="result-description">${item.description} • ${item.project}</div>
                </div>
                <div class="result-meta">
                    <span class="content-type">${item.type}</span>
                </div>
            </div>
        `).join('');
    }
    
    displayKeywordResults(keywords) {
        const container = document.getElementById('keywordResultsList');
        
        if (keywords.length === 0) {
            container.innerHTML = '<div class="no-results">No keywords found</div>';
            return;
        }
        
        container.innerHTML = keywords.map(keyword => `
            <div class="result-item" onclick="globalSearch.selectKeyword('${keyword.id}')">
                <div class="result-icon">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                </div>
                <div class="result-content">
                    <div class="result-title">${keyword.term}</div>
                    <div class="result-description">Volume: ${keyword.volume} • Difficulty: ${keyword.difficulty}</div>
                </div>
                <div class="result-meta">
                    <span class="industry-tag">${keyword.industry}</span>
                </div>
            </div>
        `).join('');
    }
    
    showSuggestions() {
        document.getElementById('searchSuggestions').classList.remove('hidden');
        this.searchResults.classList.add('hidden');
    }
    
    hideSuggestions() {
        document.getElementById('searchSuggestions').classList.add('hidden');
    }
    
    displayRecentSearches() {
        const container = document.getElementById('recentSearches');
        
        if (this.recentSearches.length === 0) {
            container.innerHTML = '<div class="no-recent">No recent searches</div>';
            return;
        }
        
        container.innerHTML = this.recentSearches.map(search => `
            <button class="recent-search-item" onclick="globalSearch.performSearch('${search}')">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                ${search}
            </button>
        `).join('');
    }
    
    selectFirstResult() {
        const firstResult = document.querySelector('.result-item');
        if (firstResult) {
            firstResult.click();
        }
    }
    
    selectProject(projectId) {
        this.addToSearchHistory(this.currentQuery);
        this.closeSearch();
        window.location.href = `project-details.html?id=${projectId}`;
    }
    
    selectContent(contentId) {
        this.addToSearchHistory(this.currentQuery);
        this.closeSearch();
        window.location.href = `content-editor.html?id=${contentId}`;
    }
    
    selectKeyword(keywordId) {
        this.addToSearchHistory(this.currentQuery);
        this.closeSearch();
        // TODO: Navigate to keyword analysis page
        console.log('Navigate to keyword:', keywordId);
    }
    
    quickAction(action) {
        this.closeSearch();
        
        switch (action) {
            case 'create-project':
                window.location.href = 'content-creator.html';
                break;
            case 'generate-content':
                window.location.href = 'content-creator.html';
                break;
            case 'view-analytics':
                window.location.href = 'analytics.html';
                break;
        }
    }
    
    loadSearchData() {
        // Sample data - would come from API in real implementation
        this.searchData = {
            projects: [
                {
                    id: '1',
                    name: 'Digital Marketing Agency',
                    keyword: 'digital marketing services',
                    industry: 'Marketing',
                    score: 96
                },
                {
                    id: '2',
                    name: 'Property Investment Hub',
                    keyword: 'real estate investment',
                    industry: 'Real Estate',
                    score: 94
                },
                {
                    id: '3',
                    name: 'MedTech Solutions',
                    keyword: 'healthcare technology',
                    industry: 'Healthcare',
                    score: 98
                }
            ],
            content: [
                {
                    id: '1',
                    title: 'Ultimate Guide to Digital Marketing',
                    description: 'Comprehensive guide covering all digital marketing strategies',
                    project: 'Digital Marketing Agency',
                    type: 'Blog Post'
                },
                {
                    id: '2',
                    title: 'Real Estate Investment Strategies',
                    description: 'Expert tips for successful property investments',
                    project: 'Property Investment Hub',
                    type: 'Guide'
                }
            ],
            keywords: [
                {
                    id: '1',
                    term: 'digital marketing services',
                    volume: '22,000',
                    difficulty: 'Medium',
                    industry: 'Marketing'
                },
                {
                    id: '2',
                    term: 'real estate investment',
                    volume: '18,500',
                    difficulty: 'High',
                    industry: 'Real Estate'
                }
            ]
        };
    }
    
    addToSearchHistory(query) {
        if (!query || query.trim().length === 0) return;
        
        // Remove if already exists
        this.recentSearches = this.recentSearches.filter(search => search !== query);
        
        // Add to beginning
        this.recentSearches.unshift(query);
        
        // Keep only last 10
        this.recentSearches = this.recentSearches.slice(0, 10);
        
        // Save to localStorage
        localStorage.setItem('searchHistory', JSON.stringify(this.recentSearches));
    }
    
    loadSearchHistory() {
        try {
            const history = localStorage.getItem('searchHistory');
            return history ? JSON.parse(history) : [];
        } catch (error) {
            console.error('Error loading search history:', error);
            return [];
        }
    }
    
    loadRecentSearches() {
        return this.loadSearchHistory();
    }
    
    getScoreClass(score) {
        if (score >= 95) return 'excellent';
        if (score >= 85) return 'good';
        if (score >= 70) return 'average';
        return 'poor';
    }
}

// Initialize global search when DOM is loaded
let globalSearch;
document.addEventListener('DOMContentLoaded', function() {
    globalSearch = new GlobalSearchComponent();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GlobalSearchComponent;
}