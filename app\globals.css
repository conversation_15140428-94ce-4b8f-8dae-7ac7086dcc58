@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom CSS for professional design */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Mobile touch optimizations */
@layer utilities {
  .touch-manipulation {
    touch-action: manipulation;
  }
  
  .touch-pan-y {
    touch-action: pan-y;
  }
  
  .touch-pan-x {
    touch-action: pan-x;
  }
}

/* Mobile responsive table styling */
@layer components {
  .responsive-table {
    @apply overflow-x-auto;
  }
  
  .responsive-table table {
    @apply min-w-full;
  }
  
  @media (max-width: 768px) {
    .responsive-table {
      @apply border border-gray-200 rounded-lg;
    }
    
    .responsive-table table {
      @apply min-w-0 w-full;
    }
    
    .responsive-table th,
    .responsive-table td {
      @apply text-xs px-2 py-3;
    }
  }
}

/* Improved mobile navigation */
@layer components {
  .mobile-nav-button {
    @apply min-h-[44px] min-w-[44px] touch-manipulation;
  }
  
  .mobile-nav-item {
    @apply min-h-[48px] touch-manipulation;
  }
}

/* Better focus states for accessibility */
@layer components {
  .focus-outline {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }
  
  /* Improved button touch targets */
  .btn-touch {
    @apply min-h-[44px] min-w-[44px] touch-manipulation;
  }
  
  /* Mobile-optimized forms */
  .form-input-mobile {
    @apply py-3 text-base touch-manipulation;
  }
}