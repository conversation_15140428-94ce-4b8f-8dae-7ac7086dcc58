<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Reset your SEO SAAS account password securely with our enterprise-grade recovery system.">
    <title>Forgot Password - SEO SAAS</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/forms.css">
    <link rel="stylesheet" href="css/animations.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="container max-w-md mx-auto p-4">
        <!-- <PERSON><PERSON> and Back Link -->
        <div class="text-center mb-8 animate-fade-in-down">
            <a href="login.html" class="inline-flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-6 transition-colors">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to login
            </a>
            
            <div class="flex justify-center items-center gap-3 mb-2">
                <svg class="w-12 h-12 text-primary-600" viewBox="0 0 40 40" fill="currentColor">
                    <path d="M20 5L5 15v10l15 10 15-10V15L20 5z"/>
                    <path d="M20 15l-10 6v8l10 6 10-6v-8l-10-6z" fill="white" opacity="0.5"/>
                </svg>
                <h1 class="text-2xl font-bold text-gray-900">SEO SAAS</h1>
            </div>
            <p class="text-gray-600">Reset your password to regain access to your account</p>
        </div>
        
        <!-- Forgot Password Form -->
        <div class="form-container animate-fade-in-up">
            <!-- Step 1: Email Input -->
            <div id="emailStep" class="step-content">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-3.586l4.293-4.293A6 6 0 0119 9z"></path>
                        </svg>
                    </div>
                    <h2 class="text-xl font-semibold text-gray-900 mb-2">Forgot your password?</h2>
                    <p class="text-gray-600 text-sm">Enter your email address and we'll send you a secure link to reset your password.</p>
                </div>
                
                <form id="forgotPasswordForm" class="space-y-6">
                    <!-- Email Field -->
                    <div class="form-group">
                        <label for="email" class="form-label">Email Address</label>
                        <div class="input-with-icon">
                            <svg class="input-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            <input 
                                type="email" 
                                id="email" 
                                name="email"
                                class="form-input"
                                placeholder="<EMAIL>"
                                required
                                autofocus
                            >
                        </div>
                        <div class="form-help">We'll send reset instructions to this email address</div>
                    </div>
                    
                    <!-- Submit Button -->
                    <button type="submit" class="btn btn-primary w-full btn-lg">
                        <span>Send Reset Link</span>
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                    </button>
                </form>
            </div>
            
            <!-- Step 2: Success Message -->
            <div id="successStep" class="step-content hidden">
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h2 class="text-xl font-semibold text-gray-900 mb-2">Check your email</h2>
                    <p class="text-gray-600 text-sm mb-6">
                        We've sent password reset instructions to <span id="emailAddress" class="font-medium text-gray-900"></span>
                    </p>
                    
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <div class="flex items-start gap-3">
                            <svg class="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <h3 class="font-medium text-blue-900 mb-1">Didn't receive the email?</h3>
                                <ul class="text-sm text-blue-700 space-y-1">
                                    <li>• Check your spam or junk folder</li>
                                    <li>• Make sure you entered the correct email</li>
                                    <li>• Wait up to 5 minutes for delivery</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="space-y-3">
                        <button id="resendBtn" class="btn btn-secondary w-full" disabled>
                            <span id="resendText">Resend email in <span id="countdown">60</span>s</span>
                        </button>
                        
                        <button id="tryDifferentEmail" class="btn btn-ghost w-full">
                            Try a different email address
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Back to Login Link -->
            <p class="text-center text-sm text-gray-600 mt-6">
                Remember your password? 
                <a href="login.html" class="text-primary-600 hover:text-primary-700 font-medium">
                    Back to login
                </a>
            </p>
        </div>
        
        <!-- Security Notice -->
        <div class="mt-8 text-center">
            <p class="text-xs text-gray-500 flex items-center justify-center gap-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
                Reset links are valid for 15 minutes for your security
            </p>
        </div>
    </div>
    
    <!-- Background Pattern -->
    <div class="fixed inset-0 -z-10 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-primary-100 rounded-full opacity-20 blur-3xl"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-100 rounded-full opacity-20 blur-3xl"></div>
    </div>
    
    <!-- Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const forgotPasswordForm = document.getElementById('forgotPasswordForm');
            const emailStep = document.getElementById('emailStep');
            const successStep = document.getElementById('successStep');
            const emailAddressSpan = document.getElementById('emailAddress');
            const resendBtn = document.getElementById('resendBtn');
            const countdownSpan = document.getElementById('countdown');
            const resendText = document.getElementById('resendText');
            const tryDifferentEmailBtn = document.getElementById('tryDifferentEmail');
            
            let countdownTimer;
            
            // Handle form submission
            forgotPasswordForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const emailInput = document.getElementById('email');
                const email = emailInput.value.trim();
                
                if (!email) {
                    showError(emailInput, 'Please enter your email address');
                    return;
                }
                
                if (!isValidEmail(email)) {
                    showError(emailInput, 'Please enter a valid email address');
                    return;
                }
                
                // Simulate API call
                submitPasswordReset(email);
            });
            
            // Handle try different email
            tryDifferentEmailBtn.addEventListener('click', function() {
                showEmailStep();
            });
            
            // Submit password reset
            function submitPasswordReset(email) {
                const submitBtn = forgotPasswordForm.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                
                // Show loading state
                submitBtn.innerHTML = '<div class="spinner"></div><span>Sending...</span>';
                submitBtn.disabled = true;
                
                // Simulate API call
                setTimeout(() => {
                    showSuccessStep(email);
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 2000);
            }
            
            // Show success step
            function showSuccessStep(email) {
                emailAddressSpan.textContent = email;
                emailStep.classList.add('hidden');
                successStep.classList.remove('hidden');
                startResendCountdown();
            }
            
            // Show email step
            function showEmailStep() {
                successStep.classList.add('hidden');
                emailStep.classList.remove('hidden');
                document.getElementById('email').focus();
                clearInterval(countdownTimer);
            }
            
            // Start resend countdown
            function startResendCountdown() {
                let seconds = 60;
                
                countdownTimer = setInterval(() => {
                    seconds--;
                    countdownSpan.textContent = seconds;
                    
                    if (seconds <= 0) {
                        clearInterval(countdownTimer);
                        resendBtn.disabled = false;
                        resendText.innerHTML = 'Resend email';
                        
                        resendBtn.addEventListener('click', function() {
                            const email = emailAddressSpan.textContent;
                            submitPasswordReset(email);
                        });
                    }
                }, 1000);
            }
            
            // Email validation
            function isValidEmail(email) {
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
            }
            
            // Show error
            function showError(input, message) {
                // Remove existing error
                const existingError = input.parentNode.parentNode.querySelector('.form-error');
                if (existingError) {
                    existingError.remove();
                }
                
                // Add error class
                input.classList.add('error');
                
                // Create error element
                const errorDiv = document.createElement('div');
                errorDiv.className = 'form-error';
                errorDiv.textContent = message;
                
                // Insert error after input group
                input.parentNode.parentNode.appendChild(errorDiv);
                
                // Remove error on input
                input.addEventListener('input', function() {
                    input.classList.remove('error');
                    const errorElement = input.parentNode.parentNode.querySelector('.form-error');
                    if (errorElement) {
                        errorElement.remove();
                    }
                }, { once: true });
            }
        });
    </script>
</body>
</html>