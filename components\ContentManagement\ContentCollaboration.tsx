/**
 * Content Collaboration Component
 * Real-time collaboration features with comments, reviews, and team workflows
 */

'use client'

import React, { useState, useEffect, useRef } from 'react'
import { ContentItem } from '@/lib/api/types'
import { useNotifications } from '@/components/Notifications'
import Card from '@/components/UI/Card'
import Button from '@/components/UI/Button'
import Input from '@/components/UI/Input'
import Badge from '@/components/UI/Badge'
import LoadingSpinner from '@/components/UI/LoadingSpinner'
import {
  UserGroupIcon,
  ChatBubbleLeftRightIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  PencilIcon,
  ClockIcon,
  BellIcon,
  UserCircleIcon,
  PaperAirplaneIcon,
  ArrowUturnLeftIcon,
  FlagIcon,
  ShareIcon,
  AtSymbolIcon,
  HandThumbUpIcon,
  HandThumbDownIcon,
  DocumentDuplicateIcon
} from '@heroicons/react/24/outline'

interface ContentCollaborationProps {
  content: ContentItem
  onContentUpdate?: (content: ContentItem) => void
}

interface Comment {
  id: string
  author: {
    id: string
    name: string
    email: string
    avatar?: string
    role: 'editor' | 'reviewer' | 'admin' | 'contributor'
  }
  text: string
  timestamp: string
  resolved: boolean
  replies?: Comment[]
  mentions?: string[]
  attachments?: {
    name: string
    url: string
    type: string
  }[]
  reactions?: {
    emoji: string
    users: string[]
  }[]
}

interface ReviewRequest {
  id: string
  reviewer: {
    id: string
    name: string
    email: string
    avatar?: string
  }
  status: 'pending' | 'approved' | 'rejected' | 'changes_requested'
  requestedAt: string
  completedAt?: string
  feedback?: string
  priority: 'low' | 'medium' | 'high'
}

interface WorkflowStep {
  id: string
  name: string
  type: 'review' | 'approval' | 'edit' | 'publish'
  status: 'pending' | 'in_progress' | 'completed' | 'skipped'
  assignee?: {
    id: string
    name: string
    email: string
  }
  dueDate?: string
  completedAt?: string
}

export default function ContentCollaboration({
  content,
  onContentUpdate
}: ContentCollaborationProps) {
  const [activeTab, setActiveTab] = useState<'comments' | 'reviews' | 'workflow' | 'team'>('comments')
  const [comments, setComments] = useState<Comment[]>([])
  const [reviewRequests, setReviewRequests] = useState<ReviewRequest[]>([])
  const [workflowSteps, setWorkflowSteps] = useState<WorkflowStep[]>([])
  const [newComment, setNewComment] = useState('')
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [mentioning, setMentioning] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  
  const commentInputRef = useRef<HTMLTextAreaElement>(null)
  const { success: notifySuccess, error: notifyError } = useNotifications()

  // Mock data - in real app this would come from API
  useEffect(() => {
    setComments([
      {
        id: '1',
        author: {
          id: 'user1',
          name: 'Sarah Wilson',
          email: '<EMAIL>',
          role: 'editor'
        },
        text: 'Great content! I think we should add more statistics in the introduction section to strengthen the argument.',
        timestamp: '2024-01-15T10:30:00Z',
        resolved: false,
        reactions: [
          { emoji: '👍', users: ['user2', 'user3'] },
          { emoji: '💡', users: ['user2'] }
        ]
      },
      {
        id: '2',
        author: {
          id: 'user2',
          name: 'Mike Chen',
          email: '<EMAIL>',
          role: 'reviewer'
        },
        text: 'The SEO optimization looks good, but we might want to include more internal links to related content.',
        timestamp: '2024-01-15T11:15:00Z',
        resolved: true,
        replies: [
          {
            id: '2-1',
            author: {
              id: 'user1',
              name: 'Sarah Wilson',
              email: '<EMAIL>',
              role: 'editor'
            },
            text: 'Good point! I\'ll add links to our recent blog posts about SEO best practices.',
            timestamp: '2024-01-15T11:30:00Z',
            resolved: false
          }
        ]
      }
    ])

    setReviewRequests([
      {
        id: 'rev1',
        reviewer: {
          id: 'user3',
          name: 'Emma Davis',
          email: '<EMAIL>'
        },
        status: 'pending',
        requestedAt: '2024-01-15T09:00:00Z',
        priority: 'high'
      },
      {
        id: 'rev2',
        reviewer: {
          id: 'user4',
          name: 'John Smith',
          email: '<EMAIL>'
        },
        status: 'approved',
        requestedAt: '2024-01-14T15:00:00Z',
        completedAt: '2024-01-15T08:30:00Z',
        feedback: 'Content looks great! Ready for publication.',
        priority: 'medium'
      }
    ])

    setWorkflowSteps([
      {
        id: 'step1',
        name: 'Content Creation',
        type: 'edit',
        status: 'completed',
        assignee: { id: 'user1', name: 'Sarah Wilson', email: '<EMAIL>' },
        completedAt: '2024-01-14T16:00:00Z'
      },
      {
        id: 'step2',
        name: 'SEO Review',
        type: 'review',
        status: 'completed',
        assignee: { id: 'user2', name: 'Mike Chen', email: '<EMAIL>' },
        completedAt: '2024-01-15T11:15:00Z'
      },
      {
        id: 'step3',
        name: 'Editorial Review',
        type: 'review',
        status: 'in_progress',
        assignee: { id: 'user3', name: 'Emma Davis', email: '<EMAIL>' },
        dueDate: '2024-01-16T17:00:00Z'
      },
      {
        id: 'step4',
        name: 'Final Approval',
        type: 'approval',
        status: 'pending',
        assignee: { id: 'user4', name: 'John Smith', email: '<EMAIL>' }
      },
      {
        id: 'step5',
        name: 'Publish',
        type: 'publish',
        status: 'pending'
      }
    ])
  }, [])

  const handleAddComment = async () => {
    if (!newComment.trim()) return

    const comment: Comment = {
      id: `comment-${Date.now()}`,
      author: {
        id: 'current-user',
        name: 'Current User',
        email: '<EMAIL>',
        role: 'editor'
      },
      text: newComment,
      timestamp: new Date().toISOString(),
      resolved: false,
      mentions: mentioning
    }

    if (replyingTo) {
      setComments(prev => prev.map(c => 
        c.id === replyingTo 
          ? { ...c, replies: [...(c.replies || []), comment] }
          : c
      ))
      setReplyingTo(null)
    } else {
      setComments(prev => [...prev, comment])
    }

    setNewComment('')
    setMentioning([])
    notifySuccess('Comment added successfully')
  }

  const handleResolveComment = (commentId: string) => {
    setComments(prev => prev.map(c => 
      c.id === commentId ? { ...c, resolved: !c.resolved } : c
    ))
  }

  const handleReviewAction = (reviewId: string, action: 'approve' | 'reject' | 'request_changes', feedback?: string) => {
    setReviewRequests(prev => prev.map(r => 
      r.id === reviewId 
        ? { 
            ...r, 
            status: action === 'approve' ? 'approved' : action === 'reject' ? 'rejected' : 'changes_requested',
            completedAt: new Date().toISOString(),
            feedback
          }
        : r
    ))
    notifySuccess(`Review ${action}d successfully`)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
      case 'approved':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />
      case 'rejected':
      case 'changes_requested':
        return <XCircleIcon className="h-4 w-4 text-red-500" />
      case 'in_progress':
        return <ClockIcon className="h-4 w-4 text-blue-500" />
      case 'pending':
        return <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500" />
      default:
        return <ClockIcon className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
      case 'approved':
        return 'success'
      case 'rejected':
        return 'error'
      case 'changes_requested':
        return 'warning'
      case 'in_progress':
        return 'info'
      case 'pending':
        return 'secondary'
      default:
        return 'secondary'
    }
  }

  const formatTimeAgo = (timestamp: string) => {
    const diff = Date.now() - new Date(timestamp).getTime()
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(hours / 24)
    
    if (days > 0) return `${days}d ago`
    if (hours > 0) return `${hours}h ago`
    return 'Just now'
  }

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <div className="flex space-x-8">
          <button
            onClick={() => setActiveTab('comments')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'comments'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <ChatBubbleLeftRightIcon className="h-4 w-4 inline mr-2" />
            Comments ({comments.length})
          </button>
          <button
            onClick={() => setActiveTab('reviews')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'reviews'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <EyeIcon className="h-4 w-4 inline mr-2" />
            Reviews ({reviewRequests.length})
          </button>
          <button
            onClick={() => setActiveTab('workflow')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'workflow'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <FlagIcon className="h-4 w-4 inline mr-2" />
            Workflow ({workflowSteps.length})
          </button>
          <button
            onClick={() => setActiveTab('team')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'team'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <UserGroupIcon className="h-4 w-4 inline mr-2" />
            Team
          </button>
        </div>
      </div>

      {/* Comments Tab */}
      {activeTab === 'comments' && (
        <div className="space-y-4">
          {/* Add Comment */}
          <Card className="p-4">
            <div className="space-y-3">
              <textarea
                ref={commentInputRef}
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder={replyingTo ? 'Write a reply...' : 'Add a comment...'}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 resize-none"
              />
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                  <AtSymbolIcon className="h-3 w-3" />
                  <span>Use @ to mention team members</span>
                </div>
                <div className="flex items-center space-x-2">
                  {replyingTo && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setReplyingTo(null)}
                    >
                      Cancel Reply
                    </Button>
                  )}
                  <Button
                    size="sm"
                    onClick={handleAddComment}
                    disabled={!newComment.trim()}
                  >
                    <PaperAirplaneIcon className="h-4 w-4 mr-2" />
                    {replyingTo ? 'Reply' : 'Comment'}
                  </Button>
                </div>
              </div>
            </div>
          </Card>

          {/* Comments List */}
          <div className="space-y-4">
            {comments.map((comment) => (
              <Card key={comment.id} className={`p-4 ${
                comment.resolved ? 'bg-gray-50 dark:bg-gray-800 opacity-75' : ''
              }`}>
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-medium">
                      {comment.author.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        {comment.author.name}
                      </span>
                      <Badge variant="outline" size="sm">
                        {comment.author.role}
                      </Badge>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {formatTimeAgo(comment.timestamp)}
                      </span>
                      {comment.resolved && (
                        <Badge variant="success" size="sm">
                          Resolved
                        </Badge>
                      )}
                    </div>
                    <p className={`text-gray-700 dark:text-gray-300 ${
                      comment.resolved ? 'line-through' : ''
                    }`}>
                      {comment.text}
                    </p>
                    
                    {/* Reactions */}
                    {comment.reactions && comment.reactions.length > 0 && (
                      <div className="flex items-center space-x-2 mt-3">
                        {comment.reactions.map((reaction, index) => (
                          <button
                            key={index}
                            className="flex items-center space-x-1 px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full text-xs hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                          >
                            <span>{reaction.emoji}</span>
                            <span className="text-gray-600 dark:text-gray-400">
                              {reaction.users.length}
                            </span>
                          </button>
                        ))}
                        <button className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                          <HandThumbUpIcon className="h-4 w-4" />
                        </button>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex items-center space-x-3 mt-3">
                      <button
                        onClick={() => setReplyingTo(comment.id)}
                        className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
                      >
                        Reply
                      </button>
                      <button
                        onClick={() => handleResolveComment(comment.id)}
                        className="text-xs text-green-600 dark:text-green-400 hover:underline"
                      >
                        {comment.resolved ? 'Unresolve' : 'Resolve'}
                      </button>
                    </div>

                    {/* Replies */}
                    {comment.replies && comment.replies.length > 0 && (
                      <div className="mt-4 pl-4 border-l-2 border-gray-200 dark:border-gray-700 space-y-3">
                        {comment.replies.map((reply) => (
                          <div key={reply.id} className="flex items-start space-x-3">
                            <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                              <span className="text-white text-xs font-medium">
                                {reply.author.name.split(' ').map(n => n[0]).join('')}
                              </span>
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-1">
                                <span className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                                  {reply.author.name}
                                </span>
                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                  {formatTimeAgo(reply.timestamp)}
                                </span>
                              </div>
                              <p className="text-gray-700 dark:text-gray-300 text-sm">
                                {reply.text}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            ))}

            {comments.length === 0 && (
              <Card className="p-8 text-center">
                <ChatBubbleLeftRightIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  No comments yet
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Start a conversation about this content
                </p>
              </Card>
            )}
          </div>
        </div>
      )}

      {/* Reviews Tab */}
      {activeTab === 'reviews' && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Review Requests
            </h3>
            <Button size="sm">
              <UserGroupIcon className="h-4 w-4 mr-2" />
              Request Review
            </Button>
          </div>

          {reviewRequests.map((review) => (
            <Card key={review.id} className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-medium">
                      {review.reviewer.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <div className="font-medium text-gray-900 dark:text-gray-100">
                      {review.reviewer.name}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Requested {formatTimeAgo(review.requestedAt)}
                    </div>
                  </div>
                  <Badge variant={getStatusColor(review.status)}>
                    {review.status.replace('_', ' ')}
                  </Badge>
                  <Badge variant={review.priority === 'high' ? 'error' : review.priority === 'medium' ? 'warning' : 'secondary'} size="sm">
                    {review.priority}
                  </Badge>
                </div>
                
                <div className="flex items-center space-x-2">
                  {review.status === 'pending' && (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleReviewAction(review.id, 'request_changes')}
                      >
                        Request Changes
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleReviewAction(review.id, 'reject')}
                      >
                        Reject
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => handleReviewAction(review.id, 'approve')}
                      >
                        Approve
                      </Button>
                    </>
                  )}
                  {getStatusIcon(review.status)}
                </div>
              </div>
              
              {review.feedback && (
                <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    {review.feedback}
                  </p>
                </div>
              )}
            </Card>
          ))}
        </div>
      )}

      {/* Workflow Tab */}
      {activeTab === 'workflow' && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Content Workflow
          </h3>
          
          <div className="space-y-3">
            {workflowSteps.map((step, index) => (
              <Card key={step.id} className="p-4">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      step.status === 'completed' 
                        ? 'bg-green-100 dark:bg-green-900'
                        : step.status === 'in_progress'
                        ? 'bg-blue-100 dark:bg-blue-900'
                        : 'bg-gray-100 dark:bg-gray-800'
                    }`}>
                      {getStatusIcon(step.status)}
                    </div>
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-gray-100">
                          {step.name}
                        </h4>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge variant={getStatusColor(step.status)} size="sm">
                            {step.status.replace('_', ' ')}
                          </Badge>
                          <Badge variant="outline" size="sm">
                            {step.type}
                          </Badge>
                        </div>
                      </div>
                      
                      {step.assignee && (
                        <div className="text-right">
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {step.assignee.name}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {step.dueDate ? `Due ${formatTimeAgo(step.dueDate)}` : 'No due date'}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                
                {index < workflowSteps.length - 1 && (
                  <div className="ml-4 mt-3 w-px h-4 bg-gray-200 dark:bg-gray-700" />
                )}
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Team Tab */}
      {activeTab === 'team' && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Team Members
            </h3>
            <Button size="sm">
              <ShareIcon className="h-4 w-4 mr-2" />
              Share Content
            </Button>
          </div>
          
          <Card className="p-4">
            <div className="text-center py-8">
              <UserGroupIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                Team Management
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Manage team access, permissions, and collaboration settings
              </p>
              <Button>
                Manage Team
              </Button>
            </div>
          </Card>
        </div>
      )}
    </div>
  )
}