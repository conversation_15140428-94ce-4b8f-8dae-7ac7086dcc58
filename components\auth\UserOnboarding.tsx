'use client';

import React, { useState } from 'react';
import { <PERSON>, <PERSON>H<PERSON><PERSON>, CardContent, Button, Badge } from '@/components/UI';
import { useUnifiedAuth } from '@/hooks/useUnifiedAuth';
import { useNotifications } from '@/components/Notifications';
import {
  CheckCircleIcon,
  DocumentTextIcon,
  ChartBarIcon,
  CogIcon,
  UserIcon,
  GlobeAltIcon,
  SparklesIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  completed: boolean;
  action?: () => void;
  href?: string;
}

interface UserOnboardingProps {
  onComplete?: () => void;
  compact?: boolean;
}

export default function UserOnboarding({ onComplete, compact = false }: UserOnboardingProps) {
  const { 
    user, 
    userProfile, 
    userTier, 
    isDemoMode, 
    completeOnboarding, 
    isOnboardingComplete 
  } = useUnifiedAuth();
  const { success } = useNotifications();
  const [currentStep, setCurrentStep] = useState(0);
  const [completing, setCompleting] = useState(false);

  // Define onboarding steps based on user tier
  const getOnboardingSteps = (): OnboardingStep[] => {
    const baseSteps: OnboardingStep[] = [
      {
        id: 'profile',
        title: 'Complete Your Profile',
        description: 'Add your name and profile picture to personalize your experience',
        icon: UserIcon,
        completed: !!userProfile?.full_name,
        href: '/profile'
      },
      {
        id: 'first-content',
        title: 'Generate Your First Content',
        description: 'Try our content generator to create your first SEO-optimized piece',
        icon: DocumentTextIcon,
        completed: false, // This would check actual usage from analytics
        href: '/content-generator'
      },
      {
        id: 'explore-features',
        title: 'Explore Key Features',
        description: 'Discover analytics, competitor research, and content optimization tools',
        icon: ChartBarIcon,
        completed: false,
        href: '/analytics'
      }
    ];

    // Add tier-specific steps
    if (userTier === 'pro' || userTier === 'enterprise') {
      baseSteps.push({
        id: 'advanced-features',
        title: 'Unlock Advanced Features',
        description: 'Explore bulk generation, API access, and advanced analytics',
        icon: SparklesIcon,
        completed: false,
        href: '/settings/api'
      });
    }

    if (userTier === 'enterprise') {
      baseSteps.push({
        id: 'team-setup',
        title: 'Set Up Your Team',
        description: 'Invite team members and configure collaboration settings',
        icon: CogIcon,
        completed: false,
        href: '/settings/team'
      });
    }

    return baseSteps;
  };

  const steps = getOnboardingSteps();
  const completedSteps = steps.filter(step => step.completed).length;
  const progressPercentage = (completedSteps / steps.length) * 100;

  const handleStepClick = (step: OnboardingStep, index: number) => {
    if (step.action) {
      step.action();
    } else if (step.href) {
      window.location.href = step.href;
    }
    setCurrentStep(index);
  };

  const handleCompleteOnboarding = async () => {
    setCompleting(true);
    try {
      await completeOnboarding();
      success('Welcome aboard!', 'Your onboarding is complete. Start creating amazing content!');
      if (onComplete) {
        onComplete();
      }
    } catch (error) {
      console.error('Error completing onboarding:', error);
    } finally {
      setCompleting(false);
    }
  };

  // Don't show onboarding if already completed (unless in compact mode)
  if (isOnboardingComplete && !compact) {
    return null;
  }

  if (compact) {
    return (
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center">
                  <SparklesIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Complete your setup
                </h4>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {completedSteps} of {steps.length} steps completed
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progressPercentage}%` }}
                ></div>
              </div>
              <Button variant="ghost" size="sm" onClick={() => setCurrentStep(0)}>
                Continue
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      <Card>
        <CardHeader
          title={`Welcome to SEO SAAS${isDemoMode ? ' (Demo)' : ''}!`}
          subtitle="Let's get you set up in just a few quick steps"
          action={
            <Badge variant={userTier === 'enterprise' ? 'secondary' : userTier === 'pro' ? 'primary' : 'default'}>
              {userTier} Plan
            </Badge>
          }
        />

        <CardContent>
          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex justify-between text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <span>Setup Progress</span>
              <span>{completedSteps} of {steps.length} completed</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </div>
          </div>

          {/* Onboarding Steps */}
          <div className="space-y-4">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = index === currentStep;
              
              return (
                <div
                  key={step.id}
                  className={`relative flex items-start p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                    step.completed
                      ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
                      : isActive
                      ? 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                  }`}
                  onClick={() => handleStepClick(step, index)}
                >
                  <div className="flex-shrink-0">
                    {step.completed ? (
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <CheckCircleIcon className="h-5 w-5 text-white" />
                      </div>
                    ) : (
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        isActive
                          ? 'bg-blue-500'
                          : 'bg-gray-200 dark:bg-gray-700'
                      }`}>
                        <Icon className={`h-4 w-4 ${
                          isActive 
                            ? 'text-white' 
                            : 'text-gray-500 dark:text-gray-400'
                        }`} />
                      </div>
                    )}
                  </div>

                  <div className="ml-4 flex-1">
                    <h4 className={`text-sm font-medium ${
                      step.completed 
                        ? 'text-green-900 dark:text-green-100' 
                        : 'text-gray-900 dark:text-gray-100'
                    }`}>
                      {step.title}
                    </h4>
                    <p className={`text-sm mt-1 ${
                      step.completed
                        ? 'text-green-700 dark:text-green-300'
                        : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      {step.description}
                    </p>
                  </div>

                  <div className="flex-shrink-0">
                    <ArrowRightIcon className="h-4 w-4 text-gray-400 dark:text-gray-500" />
                  </div>
                </div>
              );
            })}
          </div>

          {/* Action Buttons */}
          <div className="mt-8 flex justify-between">
            <Button
              variant="ghost"
              onClick={handleCompleteOnboarding}
              disabled={completing}
            >
              Skip for now
            </Button>
            
            <div className="space-x-3">
              {completedSteps === steps.length ? (
                <Button
                  variant="primary"
                  onClick={handleCompleteOnboarding}
                  loading={completing}
                  leftIcon={<CheckCircleIcon className="h-4 w-4" />}
                >
                  Complete Setup
                </Button>
              ) : (
                <Button
                  variant="primary"
                  onClick={() => handleStepClick(steps[currentStep], currentStep)}
                  leftIcon={<ArrowRightIcon className="h-4 w-4" />}
                >
                  Continue Setup
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}