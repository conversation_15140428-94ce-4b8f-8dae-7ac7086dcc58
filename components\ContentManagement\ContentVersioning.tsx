/**
 * Content Versioning Component
 * Version control, revision history, and content comparison
 */

'use client'

import React, { useState, useEffect } from 'react'
import { ContentItem } from '@/lib/api/types'
import { useNotifications } from '@/components/Notifications'
import Card from '@/components/UI/Card'
import Button from '@/components/UI/Button'
import Badge from '@/components/UI/Badge'
import LoadingSpinner from '@/components/UI/LoadingSpinner'
import {
  ClockIcon,
  DocumentDuplicateIcon,
  ArrowUturnLeftIcon,
  EyeIcon,
  CodeBracketIcon,
  UserIcon,
  TagIcon,
  BranchingIcon,
  ArrowsRightLeftIcon,
  CheckCircleIcon,
  XMarkIcon,
  PlusIcon,
  ArchiveBoxIcon,
  ArrowDownIcon,
  ArrowUpIcon,
  DocumentTextIcon,
  PencilIcon
} from '@heroicons/react/24/outline'

interface ContentVersioningProps {
  content: ContentItem
  onVersionRestore?: (version: ContentVersion) => void
  onClose?: () => void
}

interface ContentVersion {
  id: string
  version: string
  title: string
  content: string
  metaDescription?: string
  keyword: string
  wordCount: number
  seoScore: number
  createdAt: string
  createdBy: {
    id: string
    name: string
    email: string
    avatar?: string
  }
  changeType: 'major' | 'minor' | 'patch' | 'draft'
  changeLog: string
  status: 'current' | 'archived' | 'draft'
  tags?: string[]
  isPublished: boolean
  publishedAt?: string
  contentHash: string
}

interface ContentDiff {
  type: 'added' | 'removed' | 'modified'
  section: 'title' | 'content' | 'meta' | 'keyword'
  oldValue?: string
  newValue?: string
  line?: number
}

export default function ContentVersioning({
  content,
  onVersionRestore,
  onClose
}: ContentVersioningProps) {
  const [versions, setVersions] = useState<ContentVersion[]>([])
  const [selectedVersions, setSelectedVersions] = useState<[string, string] | null>(null)
  const [showDiff, setShowDiff] = useState(false)
  const [contentDiffs, setContentDiffs] = useState<ContentDiff[]>([])
  const [activeView, setActiveView] = useState<'timeline' | 'comparison' | 'branches'>('timeline')
  const [isLoading, setIsLoading] = useState(false)
  const [newVersionNote, setNewVersionNote] = useState('')
  const [showCreateVersion, setShowCreateVersion] = useState(false)
  
  const { success: notifySuccess, error: notifyError } = useNotifications()

  // Mock version data - in real app this would come from API
  useEffect(() => {
    setVersions([
      {
        id: 'v1',
        version: '1.0.0',
        title: content.title,
        content: content.content,
        metaDescription: content.meta_description,
        keyword: content.keyword,
        wordCount: content.word_count,
        seoScore: content.seo_score || 85,
        createdAt: '2024-01-15T14:30:00Z',
        createdBy: {
          id: 'user1',
          name: 'Sarah Wilson',
          email: '<EMAIL>'
        },
        changeType: 'major',
        changeLog: 'Initial version - comprehensive SEO content creation',
        status: 'current',
        tags: ['published', 'seo-optimized'],
        isPublished: true,
        publishedAt: '2024-01-15T15:00:00Z',
        contentHash: 'abc123def456'
      },
      {
        id: 'v2',
        version: '0.9.0',
        title: 'SEO Content Creation Guide - Draft',
        content: content.content.substring(0, content.content.length - 200),
        metaDescription: 'Learn SEO content creation basics',
        keyword: content.keyword,
        wordCount: content.word_count - 50,
        seoScore: 78,
        createdAt: '2024-01-14T16:20:00Z',
        createdBy: {
          id: 'user1',
          name: 'Sarah Wilson',
          email: '<EMAIL>'
        },
        changeType: 'minor',
        changeLog: 'Added conclusion section and improved keyword density',
        status: 'archived',
        tags: ['draft', 'review-pending'],
        isPublished: false,
        contentHash: 'def456ghi789'
      },
      {
        id: 'v3',
        version: '0.8.0',
        title: 'SEO Content Guide',
        content: content.content.substring(0, content.content.length - 400),
        metaDescription: 'SEO content guide',
        keyword: content.keyword,
        wordCount: content.word_count - 120,
        seoScore: 65,
        createdAt: '2024-01-13T10:15:00Z',
        createdBy: {
          id: 'user2',
          name: 'Mike Chen',
          email: '<EMAIL>'
        },
        changeType: 'minor',
        changeLog: 'Initial draft with basic structure and content outline',
        status: 'archived',
        tags: ['initial-draft'],
        isPublished: false,
        contentHash: 'ghi789jkl012'
      },
      {
        id: 'v4',
        version: '1.1.0-draft',
        title: content.title + ' - Updated',
        content: content.content + '\n\nAdditional section for improved coverage.',
        metaDescription: content.meta_description,
        keyword: content.keyword,
        wordCount: content.word_count + 25,
        seoScore: 92,
        createdAt: '2024-01-16T09:00:00Z',
        createdBy: {
          id: 'user1',
          name: 'Sarah Wilson',
          email: '<EMAIL>'
        },
        changeType: 'draft',
        changeLog: 'Work in progress - adding more examples and case studies',
        status: 'draft',
        tags: ['work-in-progress', 'enhanced'],
        isPublished: false,
        contentHash: 'jkl012mno345'
      }
    ])
  }, [content])

  const generateDiff = (oldVersion: ContentVersion, newVersion: ContentVersion) => {
    const diffs: ContentDiff[] = []
    
    // Title comparison
    if (oldVersion.title !== newVersion.title) {
      diffs.push({
        type: 'modified',
        section: 'title',
        oldValue: oldVersion.title,
        newValue: newVersion.title
      })
    }
    
    // Meta description comparison
    if (oldVersion.metaDescription !== newVersion.metaDescription) {
      diffs.push({
        type: 'modified',
        section: 'meta',
        oldValue: oldVersion.metaDescription,
        newValue: newVersion.metaDescription
      })
    }
    
    // Keyword comparison
    if (oldVersion.keyword !== newVersion.keyword) {
      diffs.push({
        type: 'modified',
        section: 'keyword',
        oldValue: oldVersion.keyword,
        newValue: newVersion.keyword
      })
    }
    
    // Content comparison (simplified)
    if (oldVersion.content !== newVersion.content) {
      const oldLines = oldVersion.content.split('\n')
      const newLines = newVersion.content.split('\n')
      
      if (newLines.length > oldLines.length) {
        diffs.push({
          type: 'added',
          section: 'content',
          newValue: `+${newLines.length - oldLines.length} lines added`,
          line: oldLines.length
        })
      } else if (newLines.length < oldLines.length) {
        diffs.push({
          type: 'removed',
          section: 'content',
          oldValue: `${oldLines.length - newLines.length} lines removed`,
          line: newLines.length
        })
      } else {
        diffs.push({
          type: 'modified',
          section: 'content',
          oldValue: 'Content modified',
          newValue: 'See detailed comparison'
        })
      }
    }
    
    return diffs
  }

  const handleCompareVersions = (versionId1: string, versionId2: string) => {
    const version1 = versions.find(v => v.id === versionId1)
    const version2 = versions.find(v => v.id === versionId2)
    
    if (version1 && version2) {
      setSelectedVersions([versionId1, versionId2])
      const diffs = generateDiff(version1, version2)
      setContentDiffs(diffs)
      setShowDiff(true)
    }
  }

  const handleRestoreVersion = async (version: ContentVersion) => {
    setIsLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      if (onVersionRestore) {
        onVersionRestore(version)
      }
      
      notifySuccess(`Restored to version ${version.version}`)
    } catch (error) {
      notifyError('Failed to restore version')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateVersion = async () => {
    if (!newVersionNote.trim()) {
      notifyError('Please add a version note')
      return
    }

    setIsLoading(true)
    try {
      const newVersion: ContentVersion = {
        id: `v${Date.now()}`,
        version: '1.2.0',
        title: content.title,
        content: content.content,
        metaDescription: content.meta_description,
        keyword: content.keyword,
        wordCount: content.word_count,
        seoScore: content.seo_score || 0,
        createdAt: new Date().toISOString(),
        createdBy: {
          id: 'current-user',
          name: 'Current User',
          email: '<EMAIL>'
        },
        changeType: 'minor',
        changeLog: newVersionNote,
        status: 'current',
        tags: ['latest'],
        isPublished: false,
        contentHash: `hash${Date.now()}`
      }
      
      setVersions(prev => [newVersion, ...prev.map(v => ({ ...v, status: 'archived' as const }))])
      setNewVersionNote('')
      setShowCreateVersion(false)
      notifySuccess('New version created successfully')
    } catch (error) {
      notifyError('Failed to create version')
    } finally {
      setIsLoading(false)
    }
  }

  const getVersionBadgeVariant = (changeType: string) => {
    switch (changeType) {
      case 'major': return 'error'
      case 'minor': return 'warning'
      case 'patch': return 'info'
      case 'draft': return 'secondary'
      default: return 'secondary'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'current': return 'success'
      case 'draft': return 'warning'
      case 'archived': return 'secondary'
      default: return 'secondary'
    }
  }

  const formatTimeAgo = (timestamp: string) => {
    const diff = Date.now() - new Date(timestamp).getTime()
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(hours / 24)
    
    if (days > 0) return `${days}d ago`
    if (hours > 0) return `${hours}h ago`
    return 'Just now'
  }

  const getDiffIcon = (type: string) => {
    switch (type) {
      case 'added': return <PlusIcon className="h-3 w-3 text-green-500" />
      case 'removed': return <XMarkIcon className="h-3 w-3 text-red-500" />
      case 'modified': return <PencilIcon className="h-3 w-3 text-blue-500" />
      default: return null
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Version History
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Track changes, compare versions, and manage content revisions
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowCreateVersion(true)}
          >
            <DocumentDuplicateIcon className="h-4 w-4 mr-2" />
            Save Version
          </Button>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              <XMarkIcon className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Create Version Modal */}
      {showCreateVersion && (
        <Card className="p-4 border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20">
          <h3 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-3">
            Create New Version
          </h3>
          <div className="space-y-3">
            <textarea
              value={newVersionNote}
              onChange={(e) => setNewVersionNote(e.target.value)}
              placeholder="Describe the changes in this version..."
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 resize-none"
            />
            <div className="flex items-center justify-end space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCreateVersion(false)}
              >
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={handleCreateVersion}
                disabled={!newVersionNote.trim() || isLoading}
              >
                {isLoading ? <LoadingSpinner size="sm" className="mr-2" /> : null}
                Create Version
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* View Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <div className="flex space-x-8">
          <button
            onClick={() => setActiveView('timeline')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeView === 'timeline'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <ClockIcon className="h-4 w-4 inline mr-2" />
            Timeline
          </button>
          <button
            onClick={() => setActiveView('comparison')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeView === 'comparison'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <ArrowsRightLeftIcon className="h-4 w-4 inline mr-2" />
            Compare
          </button>
          <button
            onClick={() => setActiveView('branches')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeView === 'branches'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <BranchingIcon className="h-4 w-4 inline mr-2" />
            Analytics
          </button>
        </div>
      </div>

      {/* Timeline View */}
      {activeView === 'timeline' && (
        <div className="space-y-4">
          {versions.map((version, index) => (
            <Card key={version.id} className={`p-4 ${
              version.status === 'current' ? 'border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20' : ''
            }`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    version.status === 'current' ? 'bg-green-500' : 'bg-gray-500'
                  }`}>
                    <span className="text-white text-xs font-medium">
                      {version.createdBy.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h3 className="font-medium text-gray-900 dark:text-gray-100">
                        Version {version.version}
                      </h3>
                      <Badge variant={getVersionBadgeVariant(version.changeType)}>
                        {version.changeType}
                      </Badge>
                      <Badge variant={getStatusColor(version.status)}>
                        {version.status}
                      </Badge>
                      {version.isPublished && (
                        <Badge variant="success" size="sm">
                          Published
                        </Badge>
                      )}
                    </div>
                    
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      {version.changeLog}
                    </p>
                    
                    <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                      <span>By {version.createdBy.name}</span>
                      <span>{formatTimeAgo(version.createdAt)}</span>
                      <span>{version.wordCount} words</span>
                      <span>SEO: {version.seoScore}%</span>
                    </div>
                    
                    {version.tags && version.tags.length > 0 && (
                      <div className="flex items-center space-x-1 mt-2">
                        {version.tags.map((tag, tagIndex) => (
                          <Badge key={tagIndex} variant="outline" size="sm">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleCompareVersions(version.id, versions[0].id)}
                    disabled={version.status === 'current'}
                  >
                    <CodeBracketIcon className="h-4 w-4 mr-2" />
                    Compare
                  </Button>
                  
                  {version.status !== 'current' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRestoreVersion(version)}
                      disabled={isLoading}
                    >
                      <ArrowUturnLeftIcon className="h-4 w-4 mr-2" />
                      Restore
                    </Button>
                  )}
                  
                  <Button
                    variant="ghost"
                    size="sm"
                  >
                    <EyeIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Comparison View */}
      {activeView === 'comparison' && (
        <div className="space-y-4">
          {!showDiff ? (
            <Card className="p-6 text-center">
              <ArrowsRightLeftIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                Compare Versions
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Select two versions to see detailed changes and differences
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto">
                {versions.slice(0, 4).map((version) => (
                  <button
                    key={version.id}
                    onClick={() => {
                      if (!selectedVersions) {
                        setSelectedVersions([version.id, ''])
                      } else if (selectedVersions[1] === '') {
                        const newSelection: [string, string] = [selectedVersions[0], version.id]
                        setSelectedVersions(newSelection)
                        handleCompareVersions(newSelection[0], newSelection[1])
                      }
                    }}
                    className={`p-3 text-left rounded-lg border-2 transition-colors ${
                      selectedVersions && selectedVersions.includes(version.id)
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    }`}
                  >
                    <div className="font-medium text-gray-900 dark:text-gray-100">
                      Version {version.version}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {formatTimeAgo(version.createdAt)}
                    </div>
                  </button>
                ))}
              </div>
            </Card>
          ) : (
            <div className="space-y-4">
              <Card className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Version Comparison
                  </h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setShowDiff(false)
                      setSelectedVersions(null)
                      setContentDiffs([])
                    }}
                  >
                    New Comparison
                  </Button>
                </div>
                
                {selectedVersions && (
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    {selectedVersions.map((versionId) => {
                      const version = versions.find(v => v.id === versionId)!
                      return (
                        <div key={versionId} className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                          <div className="font-medium text-gray-900 dark:text-gray-100">
                            Version {version.version}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            {version.createdBy.name} • {formatTimeAgo(version.createdAt)}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                )}
                
                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3">
                    Changes ({contentDiffs.length})
                  </h4>
                  {contentDiffs.map((diff, index) => (
                    <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      {getDiffIcon(diff.type)}
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100 capitalize">
                          {diff.section} {diff.type}
                        </div>
                        {diff.oldValue && (
                          <div className="text-xs text-red-600 dark:text-red-400 mt-1">
                            - {diff.oldValue}
                          </div>
                        )}
                        {diff.newValue && (
                          <div className="text-xs text-green-600 dark:text-green-400 mt-1">
                            + {diff.newValue}
                          </div>
                        )}
                        {diff.line && (
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Line {diff.line}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                  
                  {contentDiffs.length === 0 && (
                    <div className="text-center py-6 text-gray-500 dark:text-gray-400">
                      No differences found between selected versions
                    </div>
                  )}
                </div>
              </Card>
            </div>
          )}
        </div>
      )}

      {/* Analytics View */}
      {activeView === 'branches' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="flex items-center">
              <DocumentTextIcon className="h-8 w-8 text-blue-600 dark:text-blue-400 mr-3" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Versions</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{versions.length}</p>
              </div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center">
              <CheckCircleIcon className="h-8 w-8 text-green-600 dark:text-green-400 mr-3" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Published</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {versions.filter(v => v.isPublished).length}
                </p>
              </div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center">
              <PencilIcon className="h-8 w-8 text-yellow-600 dark:text-yellow-400 mr-3" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Drafts</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {versions.filter(v => v.status === 'draft').length}
                </p>
              </div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center">
              <ArrowUpIcon className="h-8 w-8 text-purple-600 dark:text-purple-400 mr-3" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Avg SEO Score</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {Math.round(versions.reduce((acc, v) => acc + v.seoScore, 0) / versions.length)}%
                </p>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  )
}