/**
 * Sitemap Analysis Page
 * Enterprise SEO SAAS - Comprehensive sitemap analysis and internal linking opportunities
 */

'use client'

import { useState } from 'react'
import DashboardLayout from '@/components/Layout/DashboardLayout'
import SitemapAnalyzerComponent from '@/components/SitemapAnalysis/SitemapAnalyzer'
import { SitemapAnalysis } from '@/utils/sitemapAnalyzer'
import {
  MapIcon,
  LinkIcon,
  DocumentChartBarIcon,
  PuzzlePieceIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline'

export default function SitemapAnalysisPage() {
  const [currentAnalysis, setCurrentAnalysis] = useState<SitemapAnalysis | null>(null)

  const handleAnalysisComplete = (analysis: SitemapAnalysis) => {
    setCurrentAnalysis(analysis)
  }

  const exportAnalysis = () => {
    if (!currentAnalysis) return

    const exportData = {
      analysis: currentAnalysis,
      exportDate: new Date().toISOString(),
      summary: {
        totalPages: currentAnalysis.totalPages,
        linkingOpportunities: currentAnalysis.linkingOpportunities.length,
        topicClusters: currentAnalysis.topicClusters.length,
        technicalIssues: currentAnalysis.technicalInsights.length
      }
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `sitemap-analysis-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  return (
    <DashboardLayout
      title="Sitemap Analysis"
      subtitle="Analyze website sitemaps and discover internal linking opportunities"
    >
      <div className="space-y-8">
        {/* Page Header */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start gap-4">
            <div className="bg-blue-600 p-3 rounded-lg">
              <MapIcon className="h-8 w-8 text-white" />
            </div>
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-blue-900 mb-2">Professional Sitemap Analysis</h1>
              <p className="text-blue-800 mb-4">
                Discover intelligent internal linking opportunities, identify content gaps, and optimize your site structure with advanced sitemap analysis.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="bg-white bg-opacity-70 rounded-lg p-3 text-center">
                  <LinkIcon className="h-6 w-6 text-blue-600 mx-auto mb-1" />
                  <div className="text-sm font-medium text-blue-900">Link Discovery</div>
                </div>
                <div className="bg-white bg-opacity-70 rounded-lg p-3 text-center">
                  <PuzzlePieceIcon className="h-6 w-6 text-blue-600 mx-auto mb-1" />
                  <div className="text-sm font-medium text-blue-900">Topic Clusters</div>
                </div>
                <div className="bg-white bg-opacity-70 rounded-lg p-3 text-center">
                  <DocumentChartBarIcon className="h-6 w-6 text-blue-600 mx-auto mb-1" />
                  <div className="text-sm font-medium text-blue-900">Technical Insights</div>
                </div>
                <div className="bg-white bg-opacity-70 rounded-lg p-3 text-center">
                  <ChartBarIcon className="h-6 w-6 text-blue-600 mx-auto mb-1" />
                  <div className="text-sm font-medium text-blue-900">SEO Analysis</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Analysis Feature Info */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <LinkIcon className="h-6 w-6 text-green-600" />
              <h3 className="font-medium text-gray-900">Internal Link Discovery</h3>
            </div>
            <p className="text-gray-600 text-sm mb-4">
              Automatically identify contextual internal linking opportunities based on content relevance and keyword alignment.
            </p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Relevance scoring algorithm</li>
              <li>• Anchor text suggestions</li>
              <li>• Implementation difficulty assessment</li>
              <li>• Impact estimation</li>
            </ul>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <PuzzlePieceIcon className="h-6 w-6 text-purple-600" />
              <h3 className="font-medium text-gray-900">Topic Cluster Analysis</h3>
            </div>
            <p className="text-gray-600 text-sm mb-4">
              Identify content clusters and pillar page opportunities to build topical authority and improve site architecture.
            </p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Pillar page identification</li>
              <li>• Content gap analysis</li>
              <li>• Authority scoring</li>
              <li>• Cluster optimization</li>
            </ul>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <DocumentChartBarIcon className="h-6 w-6 text-orange-600" />
              <h3 className="font-medium text-gray-900">Technical SEO Insights</h3>
            </div>
            <p className="text-gray-600 text-sm mb-4">
              Discover technical optimization opportunities including URL structure, content distribution, and indexability issues.
            </p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• URL structure analysis</li>
              <li>• Content distribution review</li>
              <li>• Sitemap optimization</li>
              <li>• Indexability assessment</li>
            </ul>
          </div>
        </div>

        {/* Main Analyzer Component */}
        <SitemapAnalyzerComponent onAnalysisComplete={handleAnalysisComplete} />

        {/* Quick Export */}
        {currentAnalysis && (
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Analysis Complete</h3>
                <p className="text-gray-600">
                  Analysis completed for {currentAnalysis.siteUrl} with {currentAnalysis.linkingOpportunities.length} opportunities found
                </p>
              </div>
              <button
                onClick={exportAnalysis}
                className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors gap-2"
              >
                <ArrowDownTrayIcon className="h-4 w-4" />
                Export Analysis
              </button>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-gray-50 rounded-lg p-3 text-center">
                <div className="text-lg font-bold text-gray-900">{currentAnalysis.validPages}</div>
                <div className="text-sm text-gray-600">Pages Analyzed</div>
              </div>
              <div className="bg-gray-50 rounded-lg p-3 text-center">
                <div className="text-lg font-bold text-green-600">{currentAnalysis.linkingOpportunities.length}</div>
                <div className="text-sm text-gray-600">Link Opportunities</div>
              </div>
              <div className="bg-gray-50 rounded-lg p-3 text-center">
                <div className="text-lg font-bold text-purple-600">{currentAnalysis.topicClusters.length}</div>
                <div className="text-sm text-gray-600">Topic Clusters</div>
              </div>
              <div className="bg-gray-50 rounded-lg p-3 text-center">
                <div className="text-lg font-bold text-orange-600">{currentAnalysis.technicalInsights.length}</div>
                <div className="text-sm text-gray-600">Technical Issues</div>
              </div>
            </div>
          </div>
        )}

        {/* Best Practices */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-6">
          <div className="flex items-start gap-3">
            <CheckCircleIcon className="h-6 w-6 text-green-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-green-900 mb-2">Sitemap Analysis Best Practices</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-green-800">
                <div>
                  <div className="font-medium mb-1">Before Analysis:</div>
                  <ul className="space-y-1">
                    <li>• Ensure sitemap is up-to-date</li>
                    <li>• Check robots.txt configuration</li>
                    <li>• Verify all pages are crawlable</li>
                    <li>• Review site architecture</li>
                  </ul>
                </div>
                <div>
                  <div className="font-medium mb-1">After Analysis:</div>
                  <ul className="space-y-1">
                    <li>• Prioritize high-impact opportunities</li>
                    <li>• Implement internal links gradually</li>
                    <li>• Monitor performance metrics</li>
                    <li>• Re-analyze quarterly</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Important Notes */}
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-6">
          <div className="flex items-start gap-3">
            <InformationCircleIcon className="h-6 w-6 text-amber-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-amber-900 mb-2">Important Notes</h3>
              <div className="text-sm text-amber-800 space-y-2">
                <p>
                  <strong>Analysis Limitations:</strong> This tool analyzes publicly available sitemaps and page content. 
                  For complete internal linking analysis, consider using Google Analytics and Search Console data.
                </p>
                <p>
                  <strong>Implementation:</strong> Internal linking changes should be implemented gradually and monitored 
                  for their impact on user experience and search rankings.
                </p>
                <p>
                  <strong>Frequency:</strong> Re-run sitemap analysis after major content updates or site restructuring 
                  to discover new opportunities and maintain optimal internal linking.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}