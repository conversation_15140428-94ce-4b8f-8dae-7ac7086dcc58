import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration. Please check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Database Setup Script
 * Creates all necessary tables and indexes for the SEO SAAS application
 */
async function setupDatabase() {
  console.log('🚀 Setting up SEO SAAS database...');

  try {
    // Execute SQL to create tables
    console.log('📋 Creating database schema...');
    await executeSQL(`
      -- Enable necessary extensions
      CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
      CREATE EXTENSION IF NOT EXISTS "pg_trgm";

      -- Create custom types
      CREATE TYPE subscription_tier AS ENUM ('free', 'pro', 'enterprise');
      CREATE TYPE content_status AS ENUM ('draft', 'published', 'archived');

      -- Users table (extends Supabase auth.users)
      CREATE TABLE IF NOT EXISTS public.users (
          id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
          email VARCHAR(255) UNIQUE NOT NULL,
          full_name VARCHAR(255),
          avatar_url TEXT,
          subscription_tier subscription_tier DEFAULT 'free',
          usage_limit INTEGER DEFAULT 10,
          usage_count INTEGER DEFAULT 0,
          last_reset_date DATE DEFAULT CURRENT_DATE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Projects table
      CREATE TABLE IF NOT EXISTS public.projects (
          id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
          user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
          name VARCHAR(255) NOT NULL,
          description TEXT,
          website_url TEXT,
          target_keywords TEXT[] DEFAULT '{}',
          location VARCHAR(255),
          industry VARCHAR(255),
          settings JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Generated content table
      CREATE TABLE IF NOT EXISTS public.generated_content (
          id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
          project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE NOT NULL,
          user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
          title VARCHAR(500) NOT NULL,
          content TEXT NOT NULL,
          meta_description TEXT,
          target_keyword VARCHAR(255) NOT NULL,
          word_count INTEGER NOT NULL,
          seo_score DECIMAL(5,2),
          competitor_urls TEXT[] DEFAULT '{}',
          generation_settings JSONB DEFAULT '{}',
          status content_status DEFAULT 'draft',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- SEO analysis table
      CREATE TABLE IF NOT EXISTS public.seo_analysis (
          id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
          content_id UUID REFERENCES public.generated_content(id) ON DELETE CASCADE NOT NULL,
          keyword_density JSONB DEFAULT '{}',
          readability_score DECIMAL(5,2),
          meta_tags JSONB DEFAULT '{}',
          internal_links INTEGER DEFAULT 0,
          external_links INTEGER DEFAULT 0,
          headings_structure JSONB DEFAULT '{}',
          recommendations TEXT[] DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Usage tracking table
      CREATE TABLE IF NOT EXISTS public.usage_tracking (
          id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
          user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
          action VARCHAR(100) NOT NULL,
          resource_type VARCHAR(100) NOT NULL,
          resource_id VARCHAR(255),
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);
    console.log('✅ Database schema created successfully!');
    
    // Create indexes for performance
    console.log('🔍 Creating database indexes...');
    await createIndexes();
    
    // Set up Row Level Security (RLS)
    console.log('🔒 Setting up Row Level Security...');
    await setupRLS();
    
    console.log('✅ Database setup completed successfully!');
    console.log('🎉 Your SEO SAAS application is ready to use.');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  }
}

/**
 * Execute SQL command using Supabase
 */
async function executeSQL(sql) {
  try {
    const { data, error } = await supabase.rpc('exec_sql', { sql });
    if (error) {
      throw error;
    }
    return data;
  } catch (error) {
    console.error('SQL execution error:', error);
    throw error;
  }
}

/**
 * Create database indexes for performance optimization
 */
async function createIndexes() {
  const indexSQL = `
    -- Users table indexes
    CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
    CREATE INDEX IF NOT EXISTS idx_users_subscription_tier ON public.users(subscription_tier);
    
    -- Projects table indexes
    CREATE INDEX IF NOT EXISTS idx_projects_user_id ON public.projects(user_id);
    CREATE INDEX IF NOT EXISTS idx_projects_created_at ON public.projects(created_at);
    CREATE INDEX IF NOT EXISTS idx_projects_industry ON public.projects(industry);
    
    -- Generated content table indexes
    CREATE INDEX IF NOT EXISTS idx_generated_content_project_id ON public.generated_content(project_id);
    CREATE INDEX IF NOT EXISTS idx_generated_content_user_id ON public.generated_content(user_id);
    CREATE INDEX IF NOT EXISTS idx_generated_content_target_keyword ON public.generated_content(target_keyword);
    CREATE INDEX IF NOT EXISTS idx_generated_content_created_at ON public.generated_content(created_at);
    
    -- SEO analysis table indexes
    CREATE INDEX IF NOT EXISTS idx_seo_analysis_content_id ON public.seo_analysis(content_id);
    CREATE INDEX IF NOT EXISTS idx_seo_analysis_created_at ON public.seo_analysis(created_at);
    
    -- Usage tracking table indexes
    CREATE INDEX IF NOT EXISTS idx_usage_tracking_user_id ON public.usage_tracking(user_id);
    CREATE INDEX IF NOT EXISTS idx_usage_tracking_action ON public.usage_tracking(action);
    CREATE INDEX IF NOT EXISTS idx_usage_tracking_created_at ON public.usage_tracking(created_at);
  `;

  try {
    await executeSQL(indexSQL);
    console.log('✅ Database indexes created successfully!');
  } catch (error) {
    console.warn('⚠️  Index creation warning:', error.message);
  }
}

/**
 * Set up Row Level Security policies
 */
async function setupRLS() {
  const rlsSQL = `
    -- Enable RLS on all tables
    ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.generated_content ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.seo_analysis ENABLE ROW LEVEL SECURITY;
    ALTER TABLE public.usage_tracking ENABLE ROW LEVEL SECURITY;
    
    -- Users can only access their own data
    DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
    CREATE POLICY "Users can view own profile" ON public.users FOR SELECT USING (auth.uid() = id);
    
    DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
    CREATE POLICY "Users can update own profile" ON public.users FOR UPDATE USING (auth.uid() = id);
    
    -- Projects policies
    DROP POLICY IF EXISTS "Users can view own projects" ON public.projects;
    CREATE POLICY "Users can view own projects" ON public.projects FOR SELECT USING (auth.uid() = user_id);
    
    DROP POLICY IF EXISTS "Users can insert own projects" ON public.projects;
    CREATE POLICY "Users can insert own projects" ON public.projects FOR INSERT WITH CHECK (auth.uid() = user_id);
    
    DROP POLICY IF EXISTS "Users can update own projects" ON public.projects;
    CREATE POLICY "Users can update own projects" ON public.projects FOR UPDATE USING (auth.uid() = user_id);
    
    DROP POLICY IF EXISTS "Users can delete own projects" ON public.projects;
    CREATE POLICY "Users can delete own projects" ON public.projects FOR DELETE USING (auth.uid() = user_id);
    
    -- Generated content policies
    DROP POLICY IF EXISTS "Users can view own content" ON public.generated_content;
    CREATE POLICY "Users can view own content" ON public.generated_content FOR SELECT USING (auth.uid() = user_id);
    
    DROP POLICY IF EXISTS "Users can insert own content" ON public.generated_content;
    CREATE POLICY "Users can insert own content" ON public.generated_content FOR INSERT WITH CHECK (auth.uid() = user_id);
    
    DROP POLICY IF EXISTS "Users can update own content" ON public.generated_content;
    CREATE POLICY "Users can update own content" ON public.generated_content FOR UPDATE USING (auth.uid() = user_id);
    
    DROP POLICY IF EXISTS "Users can delete own content" ON public.generated_content;
    CREATE POLICY "Users can delete own content" ON public.generated_content FOR DELETE USING (auth.uid() = user_id);
    
    -- SEO analysis policies
    DROP POLICY IF EXISTS "Users can view own seo analysis" ON public.seo_analysis;
    CREATE POLICY "Users can view own seo analysis" ON public.seo_analysis FOR SELECT USING (
      EXISTS(SELECT 1 FROM public.generated_content WHERE generated_content.id = seo_analysis.content_id AND generated_content.user_id = auth.uid())
    );
    
    DROP POLICY IF EXISTS "Users can insert own seo analysis" ON public.seo_analysis;
    CREATE POLICY "Users can insert own seo analysis" ON public.seo_analysis FOR INSERT WITH CHECK (
      EXISTS(SELECT 1 FROM public.generated_content WHERE generated_content.id = seo_analysis.content_id AND generated_content.user_id = auth.uid())
    );
    
    -- Usage tracking policies
    DROP POLICY IF EXISTS "Users can view own usage" ON public.usage_tracking;
    CREATE POLICY "Users can view own usage" ON public.usage_tracking FOR SELECT USING (auth.uid() = user_id);
    
    DROP POLICY IF EXISTS "Users can insert own usage" ON public.usage_tracking;
    CREATE POLICY "Users can insert own usage" ON public.usage_tracking FOR INSERT WITH CHECK (auth.uid() = user_id);
  `;

  try {
    await executeSQL(rlsSQL);
    console.log('✅ Row Level Security policies applied successfully!');
  } catch (error) {
    console.warn('⚠️  RLS policy warning:', error.message);
  }
}

// Run the setup
setupDatabase();