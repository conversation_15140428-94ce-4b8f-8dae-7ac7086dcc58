/**
 * Editor Toolbar Component
 * Floating toolbar with editor controls and status indicators
 */

'use client'

import React from 'react'
import Button from '@/components/UI/Button'
import Badge from '@/components/UI/Badge'
import {
  ChartBarIcon,
  UserGroupIcon,
  Bars3BottomLeftIcon,
  EyeIcon,
  EyeSlashIcon,
  ClockIcon,
  WifiIcon,
  SignalIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline'

interface EditorToolbarProps {
  showSEOSidebar: boolean
  showCompetitorPanel: boolean
  showStructureAnalyzer: boolean
  onToggleSEOSidebar: () => void
  onToggleCompetitorPanel: () => void
  onToggleStructureAnalyzer: () => void
  hasUnsavedChanges: boolean
  seoScore: number
}

export default function EditorToolbar({
  showSEOSidebar,
  showCompetitorPanel,
  showStructureAnalyzer,
  onToggleSEOSidebar,
  onToggleCompetitorPanel,
  onToggleStructureAnalyzer,
  hasUnsavedChanges,
  seoScore
}: EditorToolbarProps) {
  const getSeoScoreBadgeVariant = (score: number) => {
    if (score >= 80) return 'success'
    if (score >= 60) return 'warning'
    return 'error'
  }

  const getSeoScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 dark:text-green-400'
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400'
    return 'text-red-600 dark:text-red-400'
  }

  return (
    <div className="sticky top-0 z-40 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 shadow-sm">
      <div className="px-6 py-3">
        <div className="flex items-center justify-between">
          {/* Left Side - Panel Toggles */}
          <div className="flex items-center space-x-2">
            <Button
              variant={showSEOSidebar ? 'primary' : 'outline'}
              size="sm"
              onClick={onToggleSEOSidebar}
              className="inline-flex items-center"
            >
              <ChartBarIcon className="h-4 w-4 mr-2" />
              SEO Analysis
              {showSEOSidebar ? (
                <EyeSlashIcon className="h-3 w-3 ml-2" />
              ) : (
                <EyeIcon className="h-3 w-3 ml-2" />
              )}
            </Button>

            <Button
              variant={showCompetitorPanel ? 'primary' : 'outline'}
              size="sm"
              onClick={onToggleCompetitorPanel}
              className="inline-flex items-center"
            >
              <UserGroupIcon className="h-4 w-4 mr-2" />
              Competitors
              {showCompetitorPanel ? (
                <EyeSlashIcon className="h-3 w-3 ml-2" />
              ) : (
                <EyeIcon className="h-3 w-3 ml-2" />
              )}
            </Button>

            <Button
              variant={showStructureAnalyzer ? 'primary' : 'outline'}
              size="sm"
              onClick={onToggleStructureAnalyzer}
              className="inline-flex items-center"
            >
              <Bars3BottomLeftIcon className="h-4 w-4 mr-2" />
              Structure
              {showStructureAnalyzer ? (
                <EyeSlashIcon className="h-3 w-3 ml-2" />
              ) : (
                <EyeIcon className="h-3 w-3 ml-2" />
              )}
            </Button>
          </div>

          {/* Center - SEO Score */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <ChartBarIcon className="h-4 w-4 text-gray-500 dark:text-gray-400" />
              <span className="text-sm text-gray-600 dark:text-gray-400">SEO Score:</span>
              <Badge variant={getSeoScoreBadgeVariant(seoScore)} size="sm">
                {seoScore}%
              </Badge>
            </div>
            
            {seoScore > 0 && (
              <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    seoScore >= 80
                      ? 'bg-green-500'
                      : seoScore >= 60
                      ? 'bg-yellow-500'
                      : 'bg-red-500'
                  }`}
                  style={{ width: `${Math.min(seoScore, 100)}%` }}
                />
              </div>
            )}
          </div>

          {/* Right Side - Status Indicators */}
          <div className="flex items-center space-x-3">
            {/* Auto-save Status */}
            <div className="flex items-center space-x-1">
              {hasUnsavedChanges ? (
                <>
                  <ClockIcon className="h-4 w-4 text-yellow-500" />
                  <span className="text-xs text-yellow-600 dark:text-yellow-400">
                    Unsaved changes
                  </span>
                </>
              ) : (
                <>
                  <WifiIcon className="h-4 w-4 text-green-500" />
                  <span className="text-xs text-green-600 dark:text-green-400">
                    Saved
                  </span>
                </>
              )}
            </div>

            {/* Connection Status */}
            <div className="flex items-center space-x-1">
              <SignalIcon className="h-4 w-4 text-green-500" />
              <span className="text-xs text-gray-500 dark:text-gray-400">
                Online
              </span>
            </div>
          </div>
        </div>

        {/* Progress Indicators */}
        {(showSEOSidebar || showCompetitorPanel || showStructureAnalyzer) && (
          <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-800">
            <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center space-x-4">
                {showSEOSidebar && (
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    <span>SEO analysis active</span>
                  </div>
                )}
                {showCompetitorPanel && (
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-purple-500 rounded-full" />
                    <span>Competitor comparison active</span>
                  </div>
                )}
                {showStructureAnalyzer && (
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <span>Structure analysis active</span>
                  </div>
                )}
              </div>
              
              <div className="text-xs text-gray-400">
                Real-time analysis enabled
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}