/* 
 * SEO SAAS Professional Animations
 * Smooth, enterprise-grade animations and transitions
 * Performance-optimized with reduced motion support
 */

/* ===========================
   ANIMATION FOUNDATIONS
   =========================== */

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Base transition for all interactive elements */
* {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===========================
   ENTRANCE ANIMATIONS
   =========================== */

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* ===========================
   EXIT ANIMATIONS
   =========================== */

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}

@keyframes slideOutUp {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-100%);
  }
}

@keyframes slideOutDown {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(100%);
  }
}

@keyframes slideOutLeft {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-100%);
  }
}

@keyframes slideOutRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100%);
  }
}

@keyframes scaleOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.9);
  }
}

/* ===========================
   LOADING ANIMATIONS
   =========================== */

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -10px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -5px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes skeleton {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* ===========================
   PROFESSIONAL TRANSITIONS
   =========================== */

.transition-all {
  transition: all 0.2s ease-in-out;
}

.transition-colors {
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

.transition-opacity {
  transition: opacity 0.2s ease-in-out;
}

.transition-transform {
  transition: transform 0.2s ease-in-out;
}

.transition-shadow {
  transition: box-shadow 0.2s ease-in-out;
}

.transition-slow {
  transition-duration: 0.3s;
}

.transition-slower {
  transition-duration: 0.5s;
}

.transition-fast {
  transition-duration: 0.1s;
}

/* ===========================
   ANIMATION CLASSES
   =========================== */

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-down {
  animation: fadeInDown 0.6s ease-out;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out;
}

.animate-slide-in-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-slide-in-down {
  animation: slideInDown 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

.animate-zoom-in {
  animation: zoomIn 0.4s ease-out;
}

.animate-fade-out {
  animation: fadeOut 0.3s ease-in;
}

.animate-slide-out-up {
  animation: slideOutUp 0.3s ease-in;
}

.animate-slide-out-down {
  animation: slideOutDown 0.3s ease-in;
}

.animate-slide-out-left {
  animation: slideOutLeft 0.3s ease-in;
}

.animate-slide-out-right {
  animation: slideOutRight 0.3s ease-in;
}

.animate-scale-out {
  animation: scaleOut 0.3s ease-in;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* ===========================
   STAGGERED ANIMATIONS
   =========================== */

.stagger-children > * {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.stagger-children > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-children > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-children > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-children > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-children > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-children > *:nth-child(6) { animation-delay: 0.6s; }
.stagger-children > *:nth-child(7) { animation-delay: 0.7s; }
.stagger-children > *:nth-child(8) { animation-delay: 0.8s; }

/* Fast stagger for grids */
.stagger-fast > * {
  animation: fadeInUp 0.4s ease-out;
  animation-fill-mode: both;
}

.stagger-fast > *:nth-child(1) { animation-delay: 0.05s; }
.stagger-fast > *:nth-child(2) { animation-delay: 0.1s; }
.stagger-fast > *:nth-child(3) { animation-delay: 0.15s; }
.stagger-fast > *:nth-child(4) { animation-delay: 0.2s; }
.stagger-fast > *:nth-child(5) { animation-delay: 0.25s; }
.stagger-fast > *:nth-child(6) { animation-delay: 0.3s; }
.stagger-fast > *:nth-child(7) { animation-delay: 0.35s; }
.stagger-fast > *:nth-child(8) { animation-delay: 0.4s; }

/* ===========================
   HOVER ANIMATIONS
   =========================== */

.hover-lift {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.hover-scale {
  transition: transform 0.2s ease-in-out;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-rotate {
  transition: transform 0.2s ease-in-out;
}

.hover-rotate:hover {
  transform: rotate(5deg);
}

.hover-brightness {
  transition: filter 0.2s ease-in-out;
}

.hover-brightness:hover {
  filter: brightness(1.1);
}

.hover-glow {
  transition: box-shadow 0.2s ease-in-out;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* ===========================
   FOCUS ANIMATIONS
   =========================== */

.focus-ring {
  transition: box-shadow 0.2s ease-in-out;
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.focus-scale {
  transition: transform 0.2s ease-in-out;
}

.focus-scale:focus {
  transform: scale(1.02);
}

/* ===========================
   LOADING STATES
   =========================== */

.loading-spinner {
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid var(--gray-200);
  border-top-color: var(--primary-600);
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

.loading-spinner.sm {
  width: 1rem;
  height: 1rem;
  border-width: 1.5px;
}

.loading-spinner.lg {
  width: 2rem;
  height: 2rem;
  border-width: 3px;
}

.loading-dots {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.loading-dot {
  width: 0.5rem;
  height: 0.5rem;
  background-color: var(--primary-600);
  border-radius: 50%;
  animation: pulse 1.4s ease-in-out infinite;
}

.loading-dot:nth-child(1) { animation-delay: 0s; }
.loading-dot:nth-child(2) { animation-delay: 0.2s; }
.loading-dot:nth-child(3) { animation-delay: 0.4s; }

.loading-bar {
  width: 100%;
  height: 4px;
  background-color: var(--gray-200);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.loading-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--primary-600), transparent);
  animation: shimmer 1.5s infinite;
}

/* ===========================
   SKELETON LOADING
   =========================== */

.skeleton {
  background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: skeleton 1.5s infinite;
  border-radius: var(--radius-md);
}

.skeleton-text {
  height: 1rem;
  margin-bottom: var(--space-2);
}

.skeleton-text:last-child {
  margin-bottom: 0;
}

.skeleton-title {
  height: 1.5rem;
  width: 75%;
  margin-bottom: var(--space-3);
}

.skeleton-paragraph {
  height: 0.875rem;
  margin-bottom: var(--space-2);
}

.skeleton-paragraph:nth-child(1) { width: 100%; }
.skeleton-paragraph:nth-child(2) { width: 85%; }
.skeleton-paragraph:nth-child(3) { width: 90%; }
.skeleton-paragraph:nth-child(4) { width: 60%; }

.skeleton-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
}

.skeleton-button {
  height: 2.5rem;
  width: 6rem;
  border-radius: var(--radius-lg);
}

.skeleton-card {
  height: 12rem;
  border-radius: var(--radius-xl);
}

/* ===========================
   PAGE TRANSITIONS
   =========================== */

.page-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

.page-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 0.3s ease-in, transform 0.3s ease-in;
}

/* ===========================
   MODAL ANIMATIONS
   =========================== */

.modal-backdrop-enter {
  opacity: 0;
}

.modal-backdrop-enter-active {
  opacity: 1;
  transition: opacity 0.3s ease-out;
}

.modal-backdrop-exit {
  opacity: 1;
}

.modal-backdrop-exit-active {
  opacity: 0;
  transition: opacity 0.3s ease-in;
}

.modal-content-enter {
  opacity: 0;
  transform: scale(0.95) translateY(-20px);
}

.modal-content-enter-active {
  opacity: 1;
  transform: scale(1) translateY(0);
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

.modal-content-exit {
  opacity: 1;
  transform: scale(1) translateY(0);
}

.modal-content-exit-active {
  opacity: 0;
  transform: scale(0.95) translateY(-20px);
  transition: opacity 0.3s ease-in, transform 0.3s ease-in;
}

/* ===========================
   NOTIFICATION ANIMATIONS
   =========================== */

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutToRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.notification-enter {
  animation: slideInFromRight 0.3s ease-out;
}

.notification-exit {
  animation: slideOutToRight 0.3s ease-in;
}

/* ===========================
   RIPPLE EFFECT
   =========================== */

.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
  pointer-events: none;
}

.ripple:active::before {
  width: 200px;
  height: 200px;
}

/* ===========================
   SCROLL ANIMATIONS
   =========================== */

.scroll-reveal {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

.scroll-reveal-left {
  opacity: 0;
  transform: translateX(-30px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-reveal-left.revealed {
  opacity: 1;
  transform: translateX(0);
}

.scroll-reveal-right {
  opacity: 0;
  transform: translateX(30px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-reveal-right.revealed {
  opacity: 1;
  transform: translateX(0);
}

/* ===========================
   PROGRESS ANIMATIONS
   =========================== */

@keyframes progressFill {
  from {
    width: 0%;
  }
}

.progress-animated .progress-fill {
  animation: progressFill 1.5s ease-out;
}

/* ===========================
   COUNT UP ANIMATION
   =========================== */

.count-up {
  animation: fadeInUp 0.8s ease-out;
}

/* ===========================
   UTILITY ANIMATION CLASSES
   =========================== */

.animate-delay-1 { animation-delay: 0.1s; }
.animate-delay-2 { animation-delay: 0.2s; }
.animate-delay-3 { animation-delay: 0.3s; }
.animate-delay-4 { animation-delay: 0.4s; }
.animate-delay-5 { animation-delay: 0.5s; }

.animate-duration-fast { animation-duration: 0.3s; }
.animate-duration-normal { animation-duration: 0.5s; }
.animate-duration-slow { animation-duration: 0.7s; }

.animate-ease-in { animation-timing-function: ease-in; }
.animate-ease-out { animation-timing-function: ease-out; }
.animate-ease-in-out { animation-timing-function: ease-in-out; }

.animate-fill-forwards { animation-fill-mode: forwards; }
.animate-fill-backwards { animation-fill-mode: backwards; }
.animate-fill-both { animation-fill-mode: both; }

.animate-infinite { animation-iteration-count: infinite; }
.animate-once { animation-iteration-count: 1; }

/* ===========================
   PERFORMANCE OPTIMIZATIONS
   =========================== */

.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-auto {
  will-change: auto;
}

/* GPU acceleration for smooth animations */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}