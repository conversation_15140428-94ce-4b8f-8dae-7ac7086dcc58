/* 
 * SEO SAAS Professional Design System
 * Enterprise-grade styles reflecting 20+ years of design expertise
 */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

/* ===========================
   CSS CUSTOM PROPERTIES (VARIABLES)
   =========================== */

:root {
  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  --font-display: 'Inter', sans-serif;

  /* Primary Brand Colors */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* Secondary Colors */
  --secondary-purple: #7c3aed;
  --secondary-green: #059669;
  --secondary-orange: #ea580c;
  --secondary-pink: #ec4899;

  /* Semantic Colors */
  --success-50: #ecfdf5;
  --success-500: #10b981;
  --success-600: #059669;
  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --info-50: #eff6ff;
  --info-500: #3b82f6;
  --info-600: #2563eb;

  /* Neutral Grays */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --bg-dark: #1f2937;

  /* Text Colors */
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --text-inverse: #ffffff;

  /* Spacing Scale */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* ===========================
   RESET & BASE STYLES
   =========================== */

* {
  box-sizing: border-box;
}

html {
  font-family: var(--font-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  line-height: 1.5;
}

/* ===========================
   TYPOGRAPHY SYSTEM
   =========================== */

.font-primary { font-family: var(--font-primary); }
.font-mono { font-family: var(--font-mono); }
.font-display { font-family: var(--font-display); }

/* Heading Styles */
h1, .h1 {
  font-family: var(--font-display);
  font-size: 2.25rem;
  font-weight: 700;
  line-height: 2.5rem;
  letter-spacing: -0.025em;
  color: var(--text-primary);
  margin: 0 0 var(--space-4) 0;
}

h2, .h2 {
  font-family: var(--font-display);
  font-size: 1.875rem;
  font-weight: 600;
  line-height: 2.25rem;
  letter-spacing: -0.025em;
  color: var(--text-primary);
  margin: 0 0 var(--space-3) 0;
}

h3, .h3 {
  font-family: var(--font-primary);
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 2rem;
  color: var(--text-primary);
  margin: 0 0 var(--space-3) 0;
}

h4, .h4 {
  font-family: var(--font-primary);
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.75rem;
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

h5, .h5 {
  font-family: var(--font-primary);
  font-size: 1.125rem;
  font-weight: 500;
  line-height: 1.75rem;
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

h6, .h6 {
  font-family: var(--font-primary);
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.5rem;
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

/* Text Sizes */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-5xl { font-size: 3rem; line-height: 1; }

/* Font Weights */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

/* Text Colors */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-inverse { color: var(--text-inverse); }

/* ===========================
   PROFESSIONAL BUTTON SYSTEM
   =========================== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-family: var(--font-primary);
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  border-radius: var(--radius-lg);
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Primary Button */
.btn-primary {
  background-color: var(--primary-600);
  color: white;
  border-color: var(--primary-600);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-700);
  border-color: var(--primary-700);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
}

/* Secondary Button */
.btn-secondary {
  background-color: white;
  color: var(--gray-700);
  border-color: var(--gray-300);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--gray-50);
  border-color: var(--gray-400);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Ghost Button */
.btn-ghost {
  background-color: transparent;
  color: var(--gray-600);
  border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  background-color: var(--gray-100);
  color: var(--gray-700);
}

/* Danger Button */
.btn-danger {
  background-color: var(--error-600);
  color: white;
  border-color: var(--error-600);
}

.btn-danger:hover:not(:disabled) {
  background-color: var(--error-700);
  border-color: var(--error-700);
  transform: translateY(-1px);
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: 0.75rem;
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: 1rem;
}

.btn-xl {
  padding: var(--space-5) var(--space-10);
  font-size: 1.125rem;
}

/* ===========================
   FORM COMPONENTS
   =========================== */

.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: var(--space-2);
}

.form-input,
.form-select,
.form-textarea {
  display: block;
  width: 100%;
  padding: var(--space-3);
  font-size: 0.875rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-lg);
  background-color: white;
  transition: all 0.2s ease-in-out;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--gray-400);
}

.form-help {
  margin-top: var(--space-1);
  font-size: 0.75rem;
  color: var(--gray-500);
}

.form-error {
  margin-top: var(--space-1);
  font-size: 0.75rem;
  color: var(--error-600);
}

/* ===========================
   CARD COMPONENTS
   =========================== */

.card {
  background-color: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  overflow: hidden;
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--gray-200);
  background-color: var(--gray-50);
}

/* Metric Cards */
.metric-card {
  background: white;
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
  padding: var(--space-6);
  transition: all 0.2s ease-in-out;
}

.metric-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.metric-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.metric-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-600);
  margin: 0;
}

.metric-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: var(--gray-400);
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-2);
}

.metric-change {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  font-weight: 500;
}

.metric-change.positive {
  color: var(--success-600);
}

.metric-change.negative {
  color: var(--error-600);
}

/* ===========================
   LAYOUT UTILITIES
   =========================== */

.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.grid {
  display: grid;
  gap: var(--space-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* ===========================
   RESPONSIVE DESIGN
   =========================== */

@media (min-width: 640px) {
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .sm\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
}

@media (min-width: 768px) {
  .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .lg\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
}

/* ===========================
   PROFESSIONAL ANIMATIONS
   =========================== */

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: translateY(20px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from { 
    opacity: 0; 
    transform: translateY(30px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  from { 
    opacity: 0; 
    transform: scale(0.95); 
  }
  to { 
    opacity: 1; 
    transform: scale(1); 
  }
}

/* ===========================
   UTILITY CLASSES
   =========================== */

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus\:outline-none:focus {
  outline: none;
}

.transition-all {
  transition: all 0.2s ease-in-out;
}

.transition-colors {
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

/* ===========================
   PROFESSIONAL STATES
   =========================== */

.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* ===========================
   ENTERPRISE DASHBOARD STYLES
   =========================== */

.dashboard-layout {
  min-height: 100vh;
  background-color: var(--bg-secondary);
}

.sidebar {
  background-color: white;
  border-right: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
}

.nav-item {
  display: flex;
  align-items: center;
  padding: var(--space-3);
  color: var(--gray-700);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all 0.2s ease-in-out;
  font-weight: 500;
}

.nav-item:hover {
  background-color: var(--gray-100);
  color: var(--gray-900);
}

.nav-item.active {
  background-color: var(--primary-100);
  color: var(--primary-700);
  border-right: 2px solid var(--primary-700);
}

.top-navigation {
  background-color: white;
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
}

/* ===========================
   DARK MODE SUPPORT
   =========================== */

@media (prefers-color-scheme: dark) {
  .dark-mode {
    --bg-primary: #1f2937;
    --bg-secondary: #111827;
    --bg-tertiary: #374151;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-tertiary: #9ca3af;
  }
}