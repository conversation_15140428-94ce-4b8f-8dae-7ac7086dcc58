import axios from 'axios';
import * as cheerio from 'cheerio';
import winston from 'winston';
import { createClient } from '@supabase/supabase-js';

// Initialize logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/link-quality-validator.log' })
  ]
});

/**
 * Link Quality Validator
 * Comprehensive E-E-A-T scoring and quality assessment for authority links
 */
export class LinkQualityValidator {
  constructor() {
    this.supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    // E-E-A-T scoring criteria
    this.eatCriteria = {
      // Experience indicators
      experience: {
        patterns: [
          /\d+\s*years?\s*(of\s*)?(experience|expertise|working|practicing)/gi,
          /(experienced|seasoned|veteran|expert|specialist|professional)/gi,
          /(worked\s*with|served|helped|assisted|consulted)/gi,
          /(case\s*stud(y|ies)|real\s*world|hands\s*on|practical)/gi,
          /(first\s*hand|personal|direct|actual)/gi
        ],
        weights: [25, 20, 15, 20, 20]
      },

      // Expertise indicators
      expertise: {
        patterns: [
          /(phd|doctorate|md|professor|doctor|expert|specialist)/gi,
          /(certified|licensed|accredited|qualified|board\s*certified)/gi,
          /(research|studies|published|peer\s*reviewed|journal)/gi,
          /(advanced\s*degree|masters|fellowship|residency)/gi,
          /(industry\s*leader|thought\s*leader|recognized\s*expert)/gi
        ],
        weights: [30, 25, 25, 15, 5]
      },

      // Authoritativeness indicators
      authoritativeness: {
        patterns: [
          /(\.gov|\.edu|\.org)/gi,
          /(official|authoritative|standard|guideline|regulation)/gi,
          /(association|institution|organization|society|board)/gi,
          /(established|founded|since\s*\d{4}|reputation)/gi,
          /(recognized|accredited|endorsed|approved|certified)/gi
        ],
        weights: [35, 25, 20, 10, 10]
      },

      // Trustworthiness indicators
      trustworthiness: {
        patterns: [
          /(contact|about\s*us|privacy\s*policy|terms|disclaimer)/gi,
          /(https|secure|verified|authenticated|trusted)/gi,
          /(transparent|honest|ethical|responsible|accountable)/gi,
          /(updated|current|recent|latest|\d{4})/gi,
          /(references|citations|sources|bibliography)/gi
        ],
        weights: [20, 25, 20, 15, 20]
      }
    };

    // Quality assessment criteria
    this.qualityCriteria = {
      accessibility: {
        weight: 0.15,
        indicators: ['response_time', 'status_code', 'ssl_certificate', 'mobile_friendly']
      },
      content: {
        weight: 0.25,
        indicators: ['word_count', 'headings', 'structure', 'readability']
      },
      technical: {
        weight: 0.20,
        indicators: ['page_speed', 'meta_tags', 'schema_markup', 'valid_html']
      },
      authority: {
        weight: 0.25,
        indicators: ['domain_age', 'backlinks', 'domain_authority', 'page_authority']
      },
      freshness: {
        weight: 0.15,
        indicators: ['last_updated', 'publication_date', 'content_freshness']
      }
    };

    // Validation cache
    this.validationCache = new Map();
    this.cacheTimeout = 1800000; // 30 minutes
  }

  /**
   * Validate and score link quality comprehensively
   * @param {Object} link - Link object to validate
   * @param {Object} options - Validation options
   * @returns {Object} Comprehensive validation results
   */
  async validateLinkQuality(link, options = {}) {
    try {
      logger.info('Starting link quality validation', {
        url: link.url,
        domain: link.domain
      });

      // Check cache first
      const cacheKey = this.generateCacheKey(link.url, options);
      const cachedResult = this.getCachedValidation(cacheKey);
      if (cachedResult) {
        logger.info('Returning cached validation result', { url: link.url });
        return cachedResult;
      }

      const validationConfig = {
        validateAccessibility: options.validateAccessibility !== false,
        analyzeContent: options.analyzeContent !== false,
        checkTechnical: options.checkTechnical !== false,
        assessEEAT: options.assessEEAT !== false,
        timeout: options.timeout || 15000,
        followRedirects: options.followRedirects !== false
      };

      const validationResults = {
        url: link.url,
        domain: link.domain,
        sourceType: link.sourceType,
        timestamp: new Date().toISOString(),
        validation: {
          accessibility: null,
          content: null,
          technical: null,
          eeat: null
        },
        qualityScore: 0,
        issues: [],
        recommendations: [],
        status: 'pending'
      };

      // Step 1: Validate accessibility
      if (validationConfig.validateAccessibility) {
        validationResults.validation.accessibility = await this.validateAccessibility(link, validationConfig);
      }

      // Step 2: Analyze content quality
      if (validationConfig.analyzeContent) {
        validationResults.validation.content = await this.analyzeContentQuality(link, validationConfig);
      }

      // Step 3: Check technical aspects
      if (validationConfig.checkTechnical) {
        validationResults.validation.technical = await this.checkTechnicalQuality(link, validationConfig);
      }

      // Step 4: Assess E-E-A-T
      if (validationConfig.assessEEAT) {
        validationResults.validation.eeat = await this.assessEEAT(link, validationConfig);
      }

      // Step 5: Calculate overall quality score
      validationResults.qualityScore = this.calculateOverallQualityScore(validationResults.validation);

      // Step 6: Generate issues and recommendations
      const analysis = this.analyzeValidationResults(validationResults);
      validationResults.issues = analysis.issues;
      validationResults.recommendations = analysis.recommendations;
      validationResults.status = analysis.status;

      // Step 7: Store results in database
      await this.storeValidationResults(validationResults);

      // Step 8: Cache results
      this.cacheValidation(cacheKey, validationResults);

      logger.info('Link quality validation completed', {
        url: link.url,
        qualityScore: validationResults.qualityScore,
        status: validationResults.status
      });

      return validationResults;

    } catch (error) {
      logger.error('Link quality validation failed', {
        url: link.url,
        error: error.message
      });

      return {
        url: link.url,
        domain: link.domain,
        status: 'error',
        error: error.message,
        qualityScore: 0,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Validate link accessibility
   */
  async validateAccessibility(link, config) {
    try {
      const startTime = Date.now();
      
      const response = await axios.get(link.url, {
        timeout: config.timeout,
        validateStatus: (status) => status < 500, // Accept redirects and client errors
        maxRedirects: config.followRedirects ? 5 : 0,
        headers: {
          'User-Agent': 'SEO-SAAS-LinkValidator/1.0 (Quality Assessment Bot)'
        }
      });

      const responseTime = Date.now() - startTime;

      const accessibility = {
        isAccessible: response.status >= 200 && response.status < 400,
        statusCode: response.status,
        responseTime: responseTime,
        hasSSL: link.url.startsWith('https://'),
        redirectCount: response.request?._redirectCount || 0,
        finalUrl: response.request?.res?.responseUrl || link.url,
        contentType: response.headers['content-type'],
        contentLength: parseInt(response.headers['content-length']) || response.data.length,
        lastModified: response.headers['last-modified'],
        server: response.headers['server'],
        score: 0
      };

      // Calculate accessibility score
      let score = 0;
      
      // Status code scoring
      if (accessibility.statusCode === 200) score += 40;
      else if (accessibility.statusCode < 300) score += 30;
      else if (accessibility.statusCode < 400) score += 20;
      else score += 0;

      // Response time scoring
      if (responseTime < 1000) score += 30;
      else if (responseTime < 3000) score += 20;
      else if (responseTime < 5000) score += 10;
      else score += 0;

      // SSL scoring
      if (accessibility.hasSSL) score += 20;

      // Redirect scoring
      if (accessibility.redirectCount === 0) score += 10;
      else if (accessibility.redirectCount <= 2) score += 5;

      accessibility.score = Math.min(score, 100);

      logger.info('Accessibility validation completed', {
        url: link.url,
        score: accessibility.score,
        responseTime
      });

      return accessibility;

    } catch (error) {
      logger.error('Accessibility validation failed', {
        url: link.url,
        error: error.message
      });

      return {
        isAccessible: false,
        statusCode: 0,
        responseTime: config.timeout,
        hasSSL: link.url.startsWith('https://'),
        error: error.message,
        score: 0
      };
    }
  }

  /**
   * Analyze content quality
   */
  async analyzeContentQuality(link, config) {
    try {
      const response = await axios.get(link.url, {
        timeout: config.timeout,
        headers: {
          'User-Agent': 'SEO-SAAS-ContentAnalyzer/1.0'
        }
      });

      const $ = cheerio.load(response.data);

      const content = {
        title: $('title').text().trim(),
        metaDescription: $('meta[name="description"]').attr('content') || '',
        headings: {
          h1: $('h1').length,
          h2: $('h2').length,
          h3: $('h3').length,
          h4: $('h4').length,
          h5: $('h5').length,
          h6: $('h6').length
        },
        wordCount: this.calculateWordCount($('body').text()),
        imageCount: $('img').length,
        linkCount: {
          internal: $('a[href^="/"], a[href*="' + link.domain + '"]').length,
          external: $('a[href^="http"]').not('[href*="' + link.domain + '"]').length
        },
        hasContactInfo: this.hasContactInformation($),
        hasAuthorInfo: this.hasAuthorInformation($),
        hasDateInfo: this.hasDateInformation($),
        structuredData: $('script[type="application/ld+json"]').length > 0,
        readabilityScore: this.calculateReadabilityScore($('body').text()),
        contentFreshness: this.assessContentFreshness($),
        score: 0
      };

      // Calculate content quality score
      let score = 0;

      // Title scoring
      if (content.title.length >= 30 && content.title.length <= 60) score += 10;
      else if (content.title.length > 0) score += 5;

      // Meta description scoring
      if (content.metaDescription.length >= 120 && content.metaDescription.length <= 160) score += 10;
      else if (content.metaDescription.length > 0) score += 5;

      // Heading structure scoring
      if (content.headings.h1 === 1) score += 10;
      if (content.headings.h2 >= 3) score += 10;
      if (content.headings.h3 >= 2) score += 5;

      // Word count scoring
      if (content.wordCount >= 1000) score += 15;
      else if (content.wordCount >= 500) score += 10;
      else if (content.wordCount >= 200) score += 5;

      // Content quality indicators
      if (content.hasContactInfo) score += 5;
      if (content.hasAuthorInfo) score += 5;
      if (content.hasDateInfo) score += 5;
      if (content.structuredData) score += 5;

      // Readability scoring
      if (content.readabilityScore >= 80) score += 10;
      else if (content.readabilityScore >= 60) score += 7;
      else if (content.readabilityScore >= 40) score += 4;

      // Content freshness scoring
      if (content.contentFreshness >= 80) score += 10;
      else if (content.contentFreshness >= 60) score += 7;
      else if (content.contentFreshness >= 40) score += 4;

      content.score = Math.min(score, 100);

      logger.info('Content quality analysis completed', {
        url: link.url,
        score: content.score,
        wordCount: content.wordCount
      });

      return content;

    } catch (error) {
      logger.error('Content quality analysis failed', {
        url: link.url,
        error: error.message
      });

      return {
        title: '',
        wordCount: 0,
        score: 0,
        error: error.message
      };
    }
  }

  /**
   * Check technical quality aspects
   */
  async checkTechnicalQuality(link, config) {
    try {
      const response = await axios.get(link.url, {
        timeout: config.timeout,
        headers: {
          'User-Agent': 'SEO-SAAS-TechnicalAnalyzer/1.0'
        }
      });

      const $ = cheerio.load(response.data);

      const technical = {
        hasMetaTags: {
          viewport: $('meta[name="viewport"]').length > 0,
          charset: $('meta[charset]').length > 0,
          description: $('meta[name="description"]').length > 0,
          keywords: $('meta[name="keywords"]').length > 0,
          robots: $('meta[name="robots"]').length > 0
        },
        hasStructuredData: $('script[type="application/ld+json"]').length > 0,
        hasOpenGraph: $('meta[property^="og:"]').length > 0,
        hasTwitterCard: $('meta[name^="twitter:"]').length > 0,
        hasCanonical: $('link[rel="canonical"]').length > 0,
        mobileOptimized: this.checkMobileOptimization($),
        validHTML: this.checkHTMLValidity(response.data),
        securityHeaders: this.checkSecurityHeaders(response.headers),
        performance: this.assessPerformance(response, $),
        score: 0
      };

      // Calculate technical quality score
      let score = 0;

      // Meta tags scoring
      const metaTagsCount = Object.values(technical.hasMetaTags).filter(Boolean).length;
      score += metaTagsCount * 4; // 4 points per meta tag

      // Structured data scoring
      if (technical.hasStructuredData) score += 10;
      if (technical.hasOpenGraph) score += 5;
      if (technical.hasTwitterCard) score += 5;
      if (technical.hasCanonical) score += 5;

      // Mobile optimization scoring
      if (technical.mobileOptimized) score += 15;

      // HTML validity scoring
      if (technical.validHTML) score += 10;

      // Security headers scoring
      score += technical.securityHeaders.score;

      // Performance scoring
      score += technical.performance.score;

      technical.score = Math.min(score, 100);

      logger.info('Technical quality check completed', {
        url: link.url,
        score: technical.score
      });

      return technical;

    } catch (error) {
      logger.error('Technical quality check failed', {
        url: link.url,
        error: error.message
      });

      return {
        score: 0,
        error: error.message
      };
    }
  }

  /**
   * Assess E-E-A-T (Experience, Expertise, Authoritativeness, Trustworthiness)
   */
  async assessEEAT(link, config) {
    try {
      const response = await axios.get(link.url, {
        timeout: config.timeout,
        headers: {
          'User-Agent': 'SEO-SAAS-EEATAnalyzer/1.0'
        }
      });

      const $ = cheerio.load(response.data);
      const fullText = $('body').text().toLowerCase();

      const eeat = {
        experience: this.scoreEEATCategory(fullText, this.eatCriteria.experience),
        expertise: this.scoreEEATCategory(fullText, this.eatCriteria.expertise),
        authoritativeness: this.scoreEEATCategory(fullText, this.eatCriteria.authoritativeness),
        trustworthiness: this.scoreEEATCategory(fullText, this.eatCriteria.trustworthiness),
        overallScore: 0,
        indicators: {
          hasAuthorBio: this.hasAuthorBio($),
          hasCredentials: this.hasCredentials(fullText),
          hasReferences: this.hasReferences($),
          hasContactInfo: this.hasContactInformation($),
          hasCertifications: this.hasCertifications(fullText),
          hasExpertQuotes: this.hasExpertQuotes(fullText),
          hasDateStamps: this.hasDateInformation($),
          hasDisclaimer: this.hasDisclaimer(fullText)
        }
      };

      // Additional scoring based on source type
      eeat.sourceTypeBonus = this.getSourceTypeEEATBonus(link.sourceType);

      // Calculate overall E-E-A-T score
      const weights = { experience: 0.2, expertise: 0.3, authoritativeness: 0.3, trustworthiness: 0.2 };
      eeat.overallScore = (
        (eeat.experience.score * weights.experience) +
        (eeat.expertise.score * weights.expertise) +
        (eeat.authoritativeness.score * weights.authoritativeness) +
        (eeat.trustworthiness.score * weights.trustworthiness)
      ) + eeat.sourceTypeBonus;

      eeat.overallScore = Math.min(eeat.overallScore, 100);

      logger.info('E-E-A-T assessment completed', {
        url: link.url,
        overallScore: eeat.overallScore,
        sourceType: link.sourceType
      });

      return eeat;

    } catch (error) {
      logger.error('E-E-A-T assessment failed', {
        url: link.url,
        error: error.message
      });

      return {
        experience: { score: 0 },
        expertise: { score: 0 },
        authoritativeness: { score: 0 },
        trustworthiness: { score: 0 },
        overallScore: 0,
        error: error.message
      };
    }
  }

  /**
   * Score E-E-A-T category based on patterns and weights
   */
  scoreEEATCategory(text, criteria) {
    let totalScore = 0;
    const matchedPatterns = [];

    criteria.patterns.forEach((pattern, index) => {
      const matches = text.match(pattern);
      if (matches) {
        const weight = criteria.weights[index];
        const score = Math.min(matches.length * weight, weight);
        totalScore += score;
        
        matchedPatterns.push({
          pattern: pattern.toString(),
          matches: matches.length,
          weight: weight,
          score: score,
          examples: matches.slice(0, 3)
        });
      }
    });

    return {
      score: Math.min(totalScore, 100),
      patterns: matchedPatterns,
      patternCount: matchedPatterns.length
    };
  }

  /**
   * Get E-E-A-T bonus score based on source type
   */
  getSourceTypeEEATBonus(sourceType) {
    const bonuses = {
      government: 15,
      educational: 12,
      research: 10,
      international: 8,
      news: 5,
      professional: 3,
      knowledge: 2
    };

    return bonuses[sourceType] || 0;
  }

  /**
   * Calculate overall quality score from all validation components
   */
  calculateOverallQualityScore(validation) {
    const weights = this.qualityCriteria;
    let totalScore = 0;

    if (validation.accessibility?.score) {
      totalScore += validation.accessibility.score * weights.accessibility.weight;
    }

    if (validation.content?.score) {
      totalScore += validation.content.score * weights.content.weight;
    }

    if (validation.technical?.score) {
      totalScore += validation.technical.score * weights.technical.weight;
    }

    if (validation.eeat?.overallScore) {
      totalScore += validation.eeat.overallScore * weights.authority.weight;
    }

    return Math.round(Math.min(totalScore, 100));
  }

  /**
   * Analyze validation results and generate issues/recommendations
   */
  analyzeValidationResults(results) {
    const issues = [];
    const recommendations = [];
    let status = 'excellent';

    const score = results.qualityScore;

    // Determine overall status
    if (score >= 90) status = 'excellent';
    else if (score >= 80) status = 'good';
    else if (score >= 70) status = 'fair';
    else if (score >= 60) status = 'poor';
    else status = 'critical';

    // Accessibility issues
    if (results.validation.accessibility) {
      const acc = results.validation.accessibility;
      if (!acc.isAccessible) {
        issues.push('Link is not accessible');
        recommendations.push('Ensure the link is working and accessible');
      }
      if (acc.responseTime > 5000) {
        issues.push('Slow response time');
        recommendations.push('Optimize server response time');
      }
      if (!acc.hasSSL) {
        issues.push('No SSL certificate');
        recommendations.push('Implement HTTPS encryption');
      }
    }

    // Content issues
    if (results.validation.content) {
      const content = results.validation.content;
      if (content.wordCount < 300) {
        issues.push('Low word count');
        recommendations.push('Add more comprehensive content');
      }
      if (content.headings.h1 !== 1) {
        issues.push('Improper H1 structure');
        recommendations.push('Use exactly one H1 heading');
      }
      if (!content.hasAuthorInfo) {
        issues.push('Missing author information');
        recommendations.push('Add author bio and credentials');
      }
    }

    // E-E-A-T issues
    if (results.validation.eeat) {
      const eeat = results.validation.eeat;
      if (eeat.expertise.score < 50) {
        issues.push('Low expertise signals');
        recommendations.push('Add more expert credentials and qualifications');
      }
      if (eeat.trustworthiness.score < 50) {
        issues.push('Low trustworthiness signals');
        recommendations.push('Add contact information, privacy policy, and transparency elements');
      }
    }

    return { issues, recommendations, status };
  }

  // Helper methods for content analysis
  calculateWordCount(text) {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  calculateReadabilityScore(text) {
    // Simplified readability calculation
    const sentences = text.split(/[.!?]+/).length;
    const words = this.calculateWordCount(text);
    const avgWordsPerSentence = words / sentences;
    
    if (avgWordsPerSentence < 15) return 90;
    if (avgWordsPerSentence < 20) return 80;
    if (avgWordsPerSentence < 25) return 70;
    return 60;
  }

  hasContactInformation($) {
    const contactIndicators = [
      'contact', 'email', 'phone', 'address', 'location',
      '@', 'mailto:', 'tel:', 'contact us', 'get in touch'
    ];
    
    const text = $('body').text().toLowerCase();
    return contactIndicators.some(indicator => text.includes(indicator));
  }

  hasAuthorInformation($) {
    const authorIndicators = [
      'author', 'by', 'written by', 'created by', 'published by',
      'bio', 'profile', 'about the author', 'contributor'
    ];
    
    const text = $('body').text().toLowerCase();
    return authorIndicators.some(indicator => text.includes(indicator));
  }

  hasDateInformation($) {
    const dateElements = $('time, .date, .published, .updated, [datetime]');
    if (dateElements.length > 0) return true;
    
    const text = $('body').text();
    const datePattern = /\b(january|february|march|april|may|june|july|august|september|october|november|december|\d{1,2}\/\d{1,2}\/\d{4}|\d{4}-\d{2}-\d{2})\b/i;
    return datePattern.test(text);
  }

  hasAuthorBio($) {
    const bioIndicators = ['bio', 'biography', 'about', 'profile', 'credentials'];
    const text = $('body').text().toLowerCase();
    return bioIndicators.some(indicator => text.includes(indicator));
  }

  hasCredentials(text) {
    const credentialPatterns = [
      /\b(phd|md|jd|llm|mba|ms|ma|bs|ba)\b/gi,
      /\b(professor|doctor|director|manager|specialist|expert)\b/gi,
      /\b(certified|licensed|board certified|accredited)\b/gi
    ];
    
    return credentialPatterns.some(pattern => pattern.test(text));
  }

  hasReferences($) {
    const refIndicators = ['references', 'citations', 'sources', 'bibliography', 'footnotes'];
    const text = $('body').text().toLowerCase();
    return refIndicators.some(indicator => text.includes(indicator)) || $('a[href*="doi.org"], a[href*="pubmed"]').length > 0;
  }

  hasCertifications(text) {
    const certificationPatterns = [
      /\b(certified|certification|accredited|accreditation)\b/gi,
      /\b(board certified|licensed|registered)\b/gi
    ];
    
    return certificationPatterns.some(pattern => pattern.test(text));
  }

  hasExpertQuotes(text) {
    const quotePatterns = [
      /"[^"]*"/g,
      /according to/gi,
      /expert says/gi,
      /research shows/gi
    ];
    
    return quotePatterns.some(pattern => pattern.test(text));
  }

  hasDisclaimer(text) {
    const disclaimerKeywords = ['disclaimer', 'disclosure', 'terms', 'privacy', 'legal'];
    return disclaimerKeywords.some(keyword => text.includes(keyword));
  }

  assessContentFreshness($) {
    const currentYear = new Date().getFullYear();
    const text = $('body').text();
    
    // Look for current year mentions
    if (text.includes(currentYear.toString())) return 90;
    if (text.includes((currentYear - 1).toString())) return 70;
    if (text.includes((currentYear - 2).toString())) return 50;
    
    return 30;
  }

  checkMobileOptimization($) {
    const viewport = $('meta[name="viewport"]').attr('content');
    const hasResponsiveViewport = viewport && viewport.includes('width=device-width');
    
    return hasResponsiveViewport;
  }

  checkHTMLValidity(html) {
    // Simplified HTML validation
    const hasDoctype = html.includes('<!DOCTYPE');
    const hasClosingTags = html.includes('</html>');
    
    return hasDoctype && hasClosingTags;
  }

  checkSecurityHeaders(headers) {
    const securityHeaders = {
      'x-frame-options': headers['x-frame-options'],
      'x-content-type-options': headers['x-content-type-options'],
      'x-xss-protection': headers['x-xss-protection'],
      'strict-transport-security': headers['strict-transport-security']
    };
    
    const presentHeaders = Object.values(securityHeaders).filter(Boolean).length;
    
    return {
      headers: securityHeaders,
      score: presentHeaders * 2.5, // Max 10 points for security headers
      count: presentHeaders
    };
  }

  assessPerformance(response, $) {
    const contentLength = parseInt(response.headers['content-length']) || response.data.length;
    const imageCount = $('img').length;
    const scriptCount = $('script').length;
    
    let score = 100;
    
    // Content size penalty
    if (contentLength > 1000000) score -= 20; // > 1MB
    else if (contentLength > 500000) score -= 10; // > 500KB
    
    // Image count penalty
    if (imageCount > 20) score -= 10;
    else if (imageCount > 10) score -= 5;
    
    // Script count penalty
    if (scriptCount > 10) score -= 10;
    else if (scriptCount > 5) score -= 5;
    
    return {
      score: Math.max(score, 0),
      contentLength,
      imageCount,
      scriptCount
    };
  }

  // Cache management
  generateCacheKey(url, options) {
    return `${url}_${JSON.stringify(options)}`;
  }

  getCachedValidation(cacheKey) {
    const cached = this.validationCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.result;
    }
    return null;
  }

  cacheValidation(cacheKey, result) {
    this.validationCache.set(cacheKey, {
      result,
      timestamp: Date.now()
    });

    // Clean up old cache entries
    if (this.validationCache.size > 500) {
      const oldestKeys = Array.from(this.validationCache.keys()).slice(0, 100);
      oldestKeys.forEach(key => this.validationCache.delete(key));
    }
  }

  /**
   * Store validation results in database
   */
  async storeValidationResults(results) {
    try {
      const { error } = await this.supabase
        .from('authority_link_validations')
        .insert([{
          authority_link_id: results.linkId,
          validation_type: 'comprehensive_quality',
          validation_result: results,
          issues_found: results.issues,
          recommendations: results.recommendations,
          validated_by: 'link_quality_validator'
        }]);

      if (error) {
        logger.error('Failed to store validation results', { error });
      }
    } catch (error) {
      logger.error('Database storage error', { error: error.message });
    }
  }
}

export default LinkQualityValidator;