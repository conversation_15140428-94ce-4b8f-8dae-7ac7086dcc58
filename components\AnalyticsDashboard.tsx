/**
 * Analytics Dashboard Components
 * 
 * Interactive dashboard with charts, metrics, and real-time updates
 * for comprehensive analytics visualization.
 */

import React, { useState, useEffect, useRef } from 'react';
import { 
  analyticsSystem, 
  AnalyticsReport, 
  UserBehaviorMetrics, 
  ContentPerformanceMetrics, 
  BusinessMetrics 
} from '../lib/analytics-system';
import { useLoading } from '../lib/loading-hooks';
import { LoadingSpinner, Skeleton } from './LoadingComponents';

// Main Analytics Dashboard Component
interface AnalyticsDashboardProps {
  userId?: string;
  timeRange?: 'day' | 'week' | 'month' | 'quarter' | 'year';
  realTime?: boolean;
  className?: string;
}

export const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
  userId,
  timeRange = 'month',
  realTime = false,
  className = ''
}) => {
  const [report, setReport] = useState<AnalyticsReport | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'users' | 'content' | 'business'>('overview');
  const [dateRange, setDateRange] = useState(() => {
    const end = Date.now();
    const start = end - (timeRange === 'day' ? 86400000 : 
                       timeRange === 'week' ? 604800000 : 
                       timeRange === 'month' ? 2629746000 : 
                       timeRange === 'quarter' ? 7889238000 : 
                       ***********);
    return { start, end };
  });
  
  const { loading, startLoading, stopLoading } = useLoading();
  
  useEffect(() => {
    loadAnalyticsData();
  }, [timeRange, dateRange]);
  
  useEffect(() => {
    if (realTime) {
      const interval = setInterval(() => {
        loadAnalyticsData();
      }, 30000);
      return () => clearInterval(interval);
    }
  }, [realTime]);
  
  const loadAnalyticsData = async () => {
    startLoading();
    try {
      const newReport = await analyticsSystem.generateReport('custom', dateRange);
      setReport(newReport);
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    } finally {
      stopLoading();
    }
  };
  
  const renderTabContent = () => {
    if (!report) return null;
    
    switch (activeTab) {
      case 'overview':
        return <OverviewTab report={report} />;
      case 'users':
        return <UsersTab metrics={report.metrics.users} />;
      case 'content':
        return <ContentTab metrics={report.metrics.content} />;
      case 'business':
        return <BusinessTab metrics={report.metrics.business} />;
      default:
        return <OverviewTab report={report} />;
    }
  };
  
  return (
    <div className={`analytics-dashboard ${className}`}>
      <div className="dashboard-header">
        <h1>Analytics Dashboard</h1>
        <div className="dashboard-controls">
          <TimeRangeSelector
            value={timeRange}
            onChange={(range) => {
              const end = Date.now();
              const start = end - (range === 'day' ? 86400000 : 
                                 range === 'week' ? 604800000 : 
                                 range === 'month' ? 2629746000 : 
                                 range === 'quarter' ? 7889238000 : 
                                 ***********);
              setDateRange({ start, end });
            }}
          />
          <button 
            className="btn btn-primary"
            onClick={loadAnalyticsData}
            disabled={loading}
          >
            {loading ? <LoadingSpinner size="small" inline /> : 'Refresh'}
          </button>
        </div>
      </div>
      
      <div className="dashboard-tabs">
        {(['overview', 'users', 'content', 'business'] as const).map(tab => (
          <button
            key={tab}
            className={`tab-button ${activeTab === tab ? 'active' : ''}`}
            onClick={() => setActiveTab(tab)}
          >
            {tab.charAt(0).toUpperCase() + tab.slice(1)}
          </button>
        ))}
      </div>
      
      <div className="dashboard-content">
        {loading ? (
          <div className="loading-container">
            <DashboardSkeleton />
          </div>
        ) : (
          renderTabContent()
        )}
      </div>
    </div>
  );
};

// Overview Tab Component
interface OverviewTabProps {
  report: AnalyticsReport;
}

const OverviewTab: React.FC<OverviewTabProps> = ({ report }) => {
  return (
    <div className="overview-tab">
      <div className="metrics-grid">
        <MetricCard
          title="Total Users"
          value={report.metrics.business.users.total}
          change={report.metrics.business.users.new}
          changeLabel="New users"
          icon="👥"
        />
        <MetricCard
          title="Page Views"
          value={report.metrics.business.content.totalViews}
          change={report.metrics.business.engagement.pagesPerSession}
          changeLabel="Pages per session"
          icon="📈"
        />
        <MetricCard
          title="Conversion Rate"
          value={`${(report.metrics.business.conversion.rate * 100).toFixed(1)}%`}
          change={report.metrics.business.revenue.growth}
          changeLabel="Revenue growth"
          icon="💰"
        />
        <MetricCard
          title="Bounce Rate"
          value={`${(report.metrics.business.engagement.bounceRate * 100).toFixed(1)}%`}
          change={-0.05}
          changeLabel="Improvement"
          icon="🎯"
        />
      </div>
      
      <div className="charts-grid">
        <ChartContainer title="User Growth">
          <LineChart data={report.charts.find(c => c.title === 'User Growth Over Time')?.data || []} />
        </ChartContainer>
        <ChartContainer title="Content Performance">
          <BarChart data={report.charts.find(c => c.title === 'Top Performing Content')?.data || []} />
        </ChartContainer>
      </div>
      
      <div className="insights-section">
        <h3>Key Insights</h3>
        <div className="insights-grid">
          {report.insights.map((insight, index) => (
            <InsightCard key={index} insight={insight} />
          ))}
        </div>
      </div>
    </div>
  );
};

// Users Tab Component
interface UsersTabProps {
  metrics: UserBehaviorMetrics[];
}

const UsersTab: React.FC<UsersTabProps> = ({ metrics }) => {
  return (
    <div className="users-tab">
      <div className="user-metrics-grid">
        {metrics.map((userMetric, index) => (
          <UserMetricCard key={index} metric={userMetric} />
        ))}
      </div>
      
      <div className="user-charts">
        <ChartContainer title="Device Types">
          <PieChart data={metrics[0]?.devices || []} />
        </ChartContainer>
        <ChartContainer title="Traffic Sources">
          <PieChart data={metrics[0]?.referrers || []} />
        </ChartContainer>
      </div>
    </div>
  );
};

// Content Tab Component
interface ContentTabProps {
  metrics: ContentPerformanceMetrics[];
}

const ContentTab: React.FC<ContentTabProps> = ({ metrics }) => {
  return (
    <div className="content-tab">
      <div className="content-metrics-grid">
        {metrics.map((contentMetric, index) => (
          <ContentMetricCard key={index} metric={contentMetric} />
        ))}
      </div>
      
      <div className="content-charts">
        <ChartContainer title="SEO Performance">
          <AreaChart data={metrics.map(m => ({
            title: m.title,
            traffic: m.seoMetrics.organicTraffic,
            position: m.seoMetrics.averagePosition
          }))} />
        </ChartContainer>
      </div>
    </div>
  );
};

// Business Tab Component
interface BusinessTabProps {
  metrics: BusinessMetrics;
}

const BusinessTab: React.FC<BusinessTabProps> = ({ metrics }) => {
  return (
    <div className="business-tab">
      <div className="business-metrics-grid">
        <MetricCard
          title="Total Revenue"
          value={`$${metrics.revenue.total.toLocaleString()}`}
          change={metrics.revenue.growth}
          changeLabel="Growth"
          icon="💰"
        />
        <MetricCard
          title="Active Users"
          value={metrics.users.active}
          change={metrics.users.new}
          changeLabel="New users"
          icon="👥"
        />
        <MetricCard
          title="Conversion Rate"
          value={`${(metrics.conversion.rate * 100).toFixed(1)}%`}
          change={0.02}
          changeLabel="Improvement"
          icon="🎯"
        />
        <MetricCard
          title="Customer Churn"
          value={`${(metrics.users.churn * 100).toFixed(1)}%`}
          change={-0.01}
          changeLabel="Reduction"
          icon="🔄"
        />
      </div>
      
      <div className="business-charts">
        <ChartContainer title="Revenue Breakdown">
          <PieChart data={[
            { name: 'Recurring', value: metrics.revenue.recurring },
            { name: 'One-time', value: metrics.revenue.oneTime }
          ]} />
        </ChartContainer>
        <ChartContainer title="Conversion Funnel">
          <FunnelChart data={metrics.conversion.funnel} />
        </ChartContainer>
      </div>
    </div>
  );
};

// Metric Card Component
interface MetricCardProps {
  title: string;
  value: string | number;
  change?: number;
  changeLabel?: string;
  icon?: string;
  className?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({ 
  title, 
  value, 
  change, 
  changeLabel, 
  icon, 
  className = '' 
}) => {
  const changeColor = change && change > 0 ? 'positive' : change && change < 0 ? 'negative' : 'neutral';
  
  return (
    <div className={`metric-card ${className}`}>
      <div className="metric-header">
        <span className="metric-title">{title}</span>
        {icon && <span className="metric-icon">{icon}</span>}
      </div>
      <div className="metric-value">{value}</div>
      {change !== undefined && (
        <div className={`metric-change ${changeColor}`}>
          <span className="change-value">
            {change > 0 ? '+' : ''}{typeof change === 'number' ? (change * 100).toFixed(1) : change}%
          </span>
          {changeLabel && <span className="change-label">{changeLabel}</span>}
        </div>
      )}
    </div>
  );
};

// Chart Container Component
interface ChartContainerProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

const ChartContainer: React.FC<ChartContainerProps> = ({ title, children, className = '' }) => {
  return (
    <div className={`chart-container ${className}`}>
      <h3 className="chart-title">{title}</h3>
      <div className="chart-content">{children}</div>
    </div>
  );
};

// Simple Chart Components
const LineChart: React.FC<{ data: any[] }> = ({ data }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  useEffect(() => {
    if (!canvasRef.current || !data.length) return;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Simple line chart implementation
    const width = canvas.width;
    const height = canvas.height;
    const padding = 40;
    
    ctx.clearRect(0, 0, width, height);
    
    // Draw axes
    ctx.strokeStyle = '#e0e0e0';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(padding, height - padding);
    ctx.lineTo(width - padding, height - padding);
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, height - padding);
    ctx.stroke();
    
    // Draw data
    if (data.length > 1) {
      ctx.strokeStyle = '#007bff';
      ctx.lineWidth = 2;
      ctx.beginPath();
      
      const maxValue = Math.max(...data.map(d => d.users || d.value || 0));
      const minValue = Math.min(...data.map(d => d.users || d.value || 0));
      const range = maxValue - minValue || 1;
      
      data.forEach((point, index) => {
        const x = padding + (index / (data.length - 1)) * (width - 2 * padding);
        const y = height - padding - ((point.users || point.value || 0) - minValue) / range * (height - 2 * padding);
        
        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });
      
      ctx.stroke();
    }
  }, [data]);
  
  return <canvas ref={canvasRef} width={400} height={200} className="chart-canvas" />;
};

const BarChart: React.FC<{ data: any[] }> = ({ data }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  useEffect(() => {
    if (!canvasRef.current || !data.length) return;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Simple bar chart implementation
    const width = canvas.width;
    const height = canvas.height;
    const padding = 40;
    
    ctx.clearRect(0, 0, width, height);
    
    const maxValue = Math.max(...data.map(d => d.views || d.value || 0));
    const barWidth = (width - 2 * padding) / data.length * 0.8;
    const barSpacing = (width - 2 * padding) / data.length * 0.2;
    
    data.forEach((item, index) => {
      const value = item.views || item.value || 0;
      const barHeight = (value / maxValue) * (height - 2 * padding);
      const x = padding + index * ((width - 2 * padding) / data.length) + barSpacing / 2;
      const y = height - padding - barHeight;
      
      ctx.fillStyle = '#007bff';
      ctx.fillRect(x, y, barWidth, barHeight);
      
      // Add value label
      ctx.fillStyle = '#333';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(value.toString(), x + barWidth / 2, y - 5);
    });
  }, [data]);
  
  return <canvas ref={canvasRef} width={400} height={200} className="chart-canvas" />;
};

const PieChart: React.FC<{ data: any[] }> = ({ data }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  useEffect(() => {
    if (!canvasRef.current || !data.length) return;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Simple pie chart implementation
    const width = canvas.width;
    const height = canvas.height;
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) / 2 - 20;
    
    ctx.clearRect(0, 0, width, height);
    
    const total = data.reduce((sum, item) => sum + (item.value || item.count || 0), 0);
    const colors = ['#007bff', '#28a745', '#ffc107', '#dc3545', '#17a2b8'];
    
    let currentAngle = 0;
    
    data.forEach((item, index) => {
      const value = item.value || item.count || 0;
      const sliceAngle = (value / total) * 2 * Math.PI;
      
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
      ctx.closePath();
      
      ctx.fillStyle = colors[index % colors.length];
      ctx.fill();
      
      // Add label
      const labelAngle = currentAngle + sliceAngle / 2;
      const labelX = centerX + Math.cos(labelAngle) * (radius * 0.7);
      const labelY = centerY + Math.sin(labelAngle) * (radius * 0.7);
      
      ctx.fillStyle = '#fff';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(item.name || item.type, labelX, labelY);
      
      currentAngle += sliceAngle;
    });
  }, [data]);
  
  return <canvas ref={canvasRef} width={300} height={300} className="chart-canvas" />;
};

const AreaChart: React.FC<{ data: any[] }> = ({ data }) => {
  // Similar to LineChart but with filled area
  return <div className="area-chart">Area Chart Coming Soon</div>;
};

const FunnelChart: React.FC<{ data: any[] }> = ({ data }) => {
  return (
    <div className="funnel-chart">
      {data.map((stage, index) => (
        <div key={index} className="funnel-stage">
          <div className="stage-bar" style={{ width: `${stage.rate * 100}%` }}>
            <span className="stage-label">{stage.stage}</span>
            <span className="stage-value">{stage.count}</span>
          </div>
        </div>
      ))}
    </div>
  );
};

// User Metric Card Component
const UserMetricCard: React.FC<{ metric: UserBehaviorMetrics }> = ({ metric }) => {
  return (
    <div className="user-metric-card">
      <h4>User {metric.userId}</h4>
      <div className="metric-row">
        <span>Total Visits:</span>
        <span>{metric.totalVisits}</span>
      </div>
      <div className="metric-row">
        <span>Total Time:</span>
        <span>{Math.round(metric.totalTimeSpent / 60000)}m</span>
      </div>
      <div className="metric-row">
        <span>Conversion Rate:</span>
        <span>{(metric.conversionRate * 100).toFixed(1)}%</span>
      </div>
    </div>
  );
};

// Content Metric Card Component
const ContentMetricCard: React.FC<{ metric: ContentPerformanceMetrics }> = ({ metric }) => {
  return (
    <div className="content-metric-card">
      <h4>{metric.title}</h4>
      <div className="metric-row">
        <span>Views:</span>
        <span>{metric.views}</span>
      </div>
      <div className="metric-row">
        <span>Avg. Time:</span>
        <span>{Math.round(metric.averageTimeOnPage / 1000)}s</span>
      </div>
      <div className="metric-row">
        <span>Conversions:</span>
        <span>{metric.conversions}</span>
      </div>
    </div>
  );
};

// Insight Card Component
const InsightCard: React.FC<{ insight: AnalyticsReport['insights'][0] }> = ({ insight }) => {
  const priorityClass = `priority-${insight.priority}`;
  const typeClass = `type-${insight.type}`;
  
  return (
    <div className={`insight-card ${priorityClass} ${typeClass}`}>
      <div className="insight-header">
        <span className="insight-type">{insight.type}</span>
        <span className="insight-priority">{insight.priority}</span>
      </div>
      <h4 className="insight-title">{insight.title}</h4>
      <p className="insight-description">{insight.description}</p>
      <p className="insight-recommendation">{insight.recommendation}</p>
      <div className="insight-impact">
        <span>Impact: {insight.impact}/100</span>
      </div>
    </div>
  );
};

// Time Range Selector Component
const TimeRangeSelector: React.FC<{
  value: string;
  onChange: (value: 'day' | 'week' | 'month' | 'quarter' | 'year') => void;
}> = ({ value, onChange }) => {
  return (
    <select 
      value={value} 
      onChange={(e) => onChange(e.target.value as any)}
      className="time-range-selector"
    >
      <option value="day">Last 24 Hours</option>
      <option value="week">Last 7 Days</option>
      <option value="month">Last 30 Days</option>
      <option value="quarter">Last 90 Days</option>
      <option value="year">Last 365 Days</option>
    </select>
  );
};

// Dashboard Skeleton Component
const DashboardSkeleton: React.FC = () => {
  return (
    <div className="dashboard-skeleton">
      <div className="metrics-skeleton">
        {Array.from({ length: 4 }, (_, index) => (
          <div key={index} className="metric-card-skeleton">
            <Skeleton width="60%" height="16px" />
            <Skeleton width="80%" height="32px" />
            <Skeleton width="50%" height="14px" />
          </div>
        ))}
      </div>
      <div className="charts-skeleton">
        <Skeleton width="100%" height="200px" />
        <Skeleton width="100%" height="200px" />
      </div>
    </div>
  );
};

// Export all components
export {
  AnalyticsDashboard,
  MetricCard,
  ChartContainer,
  LineChart,
  BarChart,
  PieChart,
  AreaChart,
  FunnelChart,
  InsightCard,
  TimeRangeSelector,
  DashboardSkeleton
};