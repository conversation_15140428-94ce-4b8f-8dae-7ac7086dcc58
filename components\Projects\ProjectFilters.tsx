/**
 * ProjectFilters Component
 * Enterprise SEO SAAS - Advanced filtering and sorting for projects
 */

import { ProjectFilters as FilterType, ProjectStats, ProjectStatus, IndustryType } from '@/types/project'
import { getIndustryInfo, formatProjectStatus } from '@/utils/projectHelpers'
import { ChevronDownIcon, XMarkIcon } from '@heroicons/react/24/outline'

interface ProjectFiltersProps {
  filters: FilterType
  onFilterChange: (filters: Partial<FilterType>) => void
  projectStats: ProjectStats
}

export default function ProjectFilters({ filters, onFilterChange, projectStats }: ProjectFiltersProps) {
  const statusOptions: ProjectStatus[] = ['draft', 'active', 'paused', 'completed', 'archived']
  
  const industryOptions: IndustryType[] = [
    'technology', 'healthcare', 'finance', 'education', 'ecommerce',
    'real_estate', 'automotive', 'food_beverage', 'fashion', 'travel',
    'fitness', 'marketing', 'consulting', 'manufacturing', 'retail',
    'construction', 'legal', 'entertainment', 'sports', 'nonprofit', 'other'
  ]

  const sortOptions = [
    { value: 'name', label: 'Project Name' },
    { value: 'createdAt', label: 'Created Date' },
    { value: 'updatedAt', label: 'Updated Date' },
    { value: 'status', label: 'Status' },
    { value: 'metrics.totalContent', label: 'Total Content' }
  ]

  const handleStatusToggle = (status: ProjectStatus) => {
    const currentStatuses = filters.status || []
    const newStatuses = currentStatuses.includes(status)
      ? currentStatuses.filter(s => s !== status)
      : [...currentStatuses, status]
    
    onFilterChange({ status: newStatuses })
  }

  const handleIndustryToggle = (industry: IndustryType) => {
    const currentIndustries = filters.industry || []
    const newIndustries = currentIndustries.includes(industry)
      ? currentIndustries.filter(i => i !== industry)
      : [...currentIndustries, industry]
    
    onFilterChange({ industry: newIndustries })
  }

  const clearAllFilters = () => {
    onFilterChange({
      status: [],
      industry: [],
      sortBy: 'updatedAt',
      sortOrder: 'desc'
    })
  }

  const hasActiveFilters = (filters.status?.length || 0) > 0 || (filters.industry?.length || 0) > 0

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-medium text-gray-900">Filters</h3>
        {hasActiveFilters && (
          <button
            onClick={clearAllFilters}
            className="inline-flex items-center gap-1 text-sm text-gray-500 hover:text-gray-700"
          >
            <XMarkIcon className="h-4 w-4" />
            Clear all
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Status Filter */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Status</h4>
          <div className="space-y-2">
            {statusOptions.map((status) => {
              const statusInfo = formatProjectStatus(status)
              const count = projectStats.projectsByStatus[status] || 0
              const isSelected = filters.status?.includes(status) || false
              
              return (
                <label key={status} className="flex items-center gap-3 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={isSelected}
                    onChange={() => handleStatusToggle(status)}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className={`
                    flex-1 flex items-center justify-between text-sm
                    ${isSelected ? 'text-gray-900 font-medium' : 'text-gray-700'}
                  `}>
                    <span className="flex items-center gap-2">
                      <span className={`
                        w-2 h-2 rounded-full
                        ${statusInfo.bgColor === 'bg-gray-100' ? 'bg-gray-400' : 
                          statusInfo.bgColor === 'bg-green-100' ? 'bg-green-500' :
                          statusInfo.bgColor === 'bg-yellow-100' ? 'bg-yellow-500' :
                          statusInfo.bgColor === 'bg-blue-100' ? 'bg-blue-500' :
                          'bg-gray-300'}
                      `} />
                      {statusInfo.label}
                    </span>
                    <span className="text-gray-500">({count})</span>
                  </span>
                </label>
              )
            })}
          </div>
        </div>

        {/* Industry Filter */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Industry</h4>
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {industryOptions.map((industry) => {
              const industryInfo = getIndustryInfo(industry)
              const count = projectStats.projectsByIndustry[industry] || 0
              const isSelected = filters.industry?.includes(industry) || false
              
              if (count === 0 && !isSelected) return null
              
              return (
                <label key={industry} className="flex items-center gap-3 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={isSelected}
                    onChange={() => handleIndustryToggle(industry)}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className={`
                    flex-1 flex items-center justify-between text-sm
                    ${isSelected ? 'text-gray-900 font-medium' : 'text-gray-700'}
                  `}>
                    <span className="flex items-center gap-2">
                      <span className="text-base">{industryInfo.icon}</span>
                      {industryInfo.label}
                    </span>
                    <span className="text-gray-500">({count})</span>
                  </span>
                </label>
              )
            })}
          </div>
        </div>

        {/* Sort Options */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Sort By</h4>
          <div className="space-y-3">
            <div>
              <label className="block text-sm text-gray-700 mb-1">Field</label>
              <select
                value={filters.sortBy || 'updatedAt'}
                onChange={(e) => onFilterChange({ sortBy: e.target.value as any })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {sortOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm text-gray-700 mb-1">Order</label>
              <select
                value={filters.sortOrder || 'desc'}
                onChange={(e) => onFilterChange({ sortOrder: e.target.value as 'asc' | 'desc' })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="desc">Descending</option>
                <option value="asc">Ascending</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="flex flex-wrap gap-2">
            {filters.status?.map((status) => {
              const statusInfo = formatProjectStatus(status)
              return (
                <span
                  key={status}
                  className="inline-flex items-center gap-1 px-2.5 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full"
                >
                  Status: {statusInfo.label}
                  <button
                    onClick={() => handleStatusToggle(status)}
                    className="ml-1 hover:text-blue-600"
                  >
                    <XMarkIcon className="h-3 w-3" />
                  </button>
                </span>
              )
            })}
            
            {filters.industry?.map((industry) => {
              const industryInfo = getIndustryInfo(industry)
              return (
                <span
                  key={industry}
                  className="inline-flex items-center gap-1 px-2.5 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full"
                >
                  <span>{industryInfo.icon}</span>
                  {industryInfo.label}
                  <button
                    onClick={() => handleIndustryToggle(industry)}
                    className="ml-1 hover:text-purple-600"
                  >
                    <XMarkIcon className="h-3 w-3" />
                  </button>
                </span>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}