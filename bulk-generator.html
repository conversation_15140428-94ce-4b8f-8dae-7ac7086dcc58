<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Generate multiple SEO-optimized content pieces simultaneously with advanced queue management and real-time progress tracking.">
    <title>Bulk Content Generator - SEO SAAS</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/forms.css">
    <link rel="stylesheet" href="css/data-visualization.css">
    <link rel="stylesheet" href="css/animations.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
</head>
<body class="dashboard-layout">
    <!-- Dashboard App Container -->
    <div class="dashboard-app">
        <!-- Global Top Navigation -->
        <header class="top-navigation">
            <div class="nav-left">
                <button class="sidebar-toggle" aria-label="Toggle sidebar" onclick="toggleSidebar()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
                <div class="brand-section">
                    <svg class="brand-logo w-8 h-8 text-primary-600" viewBox="0 0 40 40" fill="currentColor">
                        <path d="M20 5L5 15v10l15 10 15-10V15L20 5z"/>
                        <path d="M20 15l-10 6v8l10 6 10-6v-8l-10-6z" fill="white" opacity="0.5"/>
                    </svg>
                    <span class="brand-name">SEO SAAS</span>
                    <span class="environment-badge">Pro</span>
                </div>
            </div>
            
            <div class="nav-center">
                <div class="bulk-status" id="bulkStatus">
                    <div class="status-indicator status-ready"></div>
                    <span class="status-text">Ready for bulk generation</span>
                    <div class="queue-counter" id="queueCounter">0 in queue</div>
                </div>
            </div>
            
            <div class="nav-right">
                <button class="quick-action-btn" onclick="pauseQueue()" id="pauseBtn" disabled>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>Pause</span>
                </button>
                
                <button class="quick-action-btn btn-primary" onclick="startBulkGeneration()" id="startBtn" disabled>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2-10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>Start Generation</span>
                </button>
                
                <div class="user-menu-dropdown">
                    <button class="user-menu-btn">
                        <div class="user-avatar">
                            <span>JD</span>
                        </div>
                        <div class="user-info">
                            <span class="user-name">John Doe</span>
                            <span class="user-plan">Professional</span>
                        </div>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <aside class="sidebar-navigation" id="sidebarNav">
            <nav class="nav-menu">
                <!-- Dashboard Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Overview</h3>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="dashboard.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z"></path>
                                </svg>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="analytics.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                <span>Analytics</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Content Creation Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Content Creation</h3>
                        <button class="section-toggle" aria-label="Toggle section" onclick="toggleSection(this)">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="content-creator.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                <span>Create Content</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="content-optimizer.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                <span>Optimize Content</span>
                            </a>
                        </li>
                        <li class="nav-item active">
                            <a href="bulk-generator.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                <span>Bulk Generator</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="content-editor.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                                </svg>
                                <span>Content Editor</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Projects Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Projects</h3>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="projects.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                <span>All Projects</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="templates.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <span>Templates</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Settings Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Settings</h3>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="account-settings.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <span>Account</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="billing.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                </svg>
                                <span>Billing</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Sidebar Footer -->
            <div class="sidebar-footer">
                <div class="usage-indicator">
                    <div class="usage-label">API Usage</div>
                    <div class="usage-bar">
                        <div class="usage-fill" style="width: 65%"></div>
                    </div>
                    <div class="usage-text">325 / 500 requests</div>
                </div>
                
                <button class="upgrade-btn" onclick="window.location.href='pricing.html'">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    <span>Upgrade Plan</span>
                </button>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="page-title">Bulk Content Generator</h1>
                        <p class="page-subtitle">Generate multiple SEO-optimized content pieces simultaneously with advanced queue management</p>
                    </div>
                    <div class="header-right">
                        <div class="generation-stats">
                            <div class="stat-item">
                                <div class="stat-value" id="totalGenerated">0</div>
                                <div class="stat-label">Generated</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" id="totalPending">0</div>
                                <div class="stat-label">Pending</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" id="totalFailed">0</div>
                                <div class="stat-label">Failed</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bulk Generator Layout -->
            <div class="bulk-generator-layout">
                <!-- Setup Panel -->
                <div class="setup-panel">
                    <div class="panel-header">
                        <h2>Bulk Setup Configuration</h2>
                        <p>Configure your bulk content generation settings</p>
                    </div>
                    
                    <div class="setup-tabs">
                        <button class="tab-btn active" onclick="switchSetupTab('upload')" data-tab="upload">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            Upload Keywords
                        </button>
                        <button class="tab-btn" onclick="switchSetupTab('manual')" data-tab="manual">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Manual Entry
                        </button>
                        <button class="tab-btn" onclick="switchSetupTab('template')" data-tab="template">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            From Template
                        </button>
                    </div>
                    
                    <div class="setup-content">
                        <!-- Upload Tab -->
                        <div class="setup-tab active" id="uploadTab">
                            <div class="upload-area" onclick="triggerFileUpload()" ondrop="handleFileDrop(event)" ondragover="handleDragOver(event)">
                                <div class="upload-icon">
                                    <svg class="w-16 h-16 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                    </svg>
                                </div>
                                <h3>Upload Keyword List</h3>
                                <p>Drop your CSV file here or click to browse</p>
                                <div class="upload-formats">Supports: CSV, TXT, XLSX</div>
                                <input type="file" id="fileInput" accept=".csv,.txt,.xlsx" onchange="handleFileUpload(event)" style="display: none;">
                            </div>
                            
                            <div class="upload-settings">
                                <h4>File Settings</h4>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Column Mapping</label>
                                        <select class="form-select" id="keywordColumn">
                                            <option value="0">Column A (Keywords)</option>
                                            <option value="1">Column B (Keywords)</option>
                                            <option value="2">Column C (Keywords)</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Location Column (Optional)</label>
                                        <select class="form-select" id="locationColumn">
                                            <option value="">None</option>
                                            <option value="1">Column B (Location)</option>
                                            <option value="2">Column C (Location)</option>
                                            <option value="3">Column D (Location)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Manual Tab -->
                        <div class="setup-tab hidden" id="manualTab">
                            <div class="manual-input">
                                <h4>Enter Keywords Manually</h4>
                                <textarea 
                                    id="manualKeywords" 
                                    class="form-textarea" 
                                    rows="10"
                                    placeholder="Enter keywords one per line:&#10;digital marketing services&#10;SEO optimization&#10;content marketing strategy&#10;social media management"
                                ></textarea>
                                <div class="form-help">Enter one keyword per line. You can paste up to 1000 keywords.</div>
                            </div>
                        </div>
                        
                        <!-- Template Tab -->
                        <div class="setup-tab hidden" id="templateTab">
                            <div class="template-selection">
                                <h4>Select Template</h4>
                                <div class="template-grid">
                                    <div class="template-card" onclick="selectTemplate('service-pages')">
                                        <div class="template-icon">
                                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                                            </svg>
                                        </div>
                                        <h5>Service Pages</h5>
                                        <p>Generate service-focused content</p>
                                    </div>
                                    <div class="template-card" onclick="selectTemplate('blog-posts')">
                                        <div class="template-icon">
                                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                        </div>
                                        <h5>Blog Posts</h5>
                                        <p>Create informational blog content</p>
                                    </div>
                                    <div class="template-card" onclick="selectTemplate('product-pages')">
                                        <div class="template-icon">
                                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                            </svg>
                                        </div>
                                        <h5>Product Pages</h5>
                                        <p>Generate product descriptions</p>
                                    </div>
                                    <div class="template-card" onclick="selectTemplate('landing-pages')">
                                        <div class="template-icon">
                                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                                            </svg>
                                        </div>
                                        <h5>Landing Pages</h5>
                                        <p>Create conversion-focused pages</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Global Settings -->
                    <div class="global-settings">
                        <h4>Global Settings</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Default Location</label>
                                <select class="form-select" id="defaultLocation">
                                    <option value="us">United States</option>
                                    <option value="uk">United Kingdom</option>
                                    <option value="ca">Canada</option>
                                    <option value="au">Australia</option>
                                    <option value="de">Germany</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Content Type</label>
                                <select class="form-select" id="defaultContentType">
                                    <option value="service-page">Service Page</option>
                                    <option value="blog-post">Blog Post</option>
                                    <option value="product-page">Product Page</option>
                                    <option value="landing-page">Landing Page</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Writing Tone</label>
                                <select class="form-select" id="defaultTone">
                                    <option value="professional">Professional</option>
                                    <option value="conversational">Conversational</option>
                                    <option value="authoritative">Authoritative</option>
                                    <option value="friendly">Friendly</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Word Count Target</label>
                                <select class="form-select" id="defaultWordCount">
                                    <option value="auto">Auto (based on competitors)</option>
                                    <option value="800-1200">800-1,200 words</option>
                                    <option value="1200-2000">1,200-2,000 words</option>
                                    <option value="2000+">2,000+ words</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Website URL</label>
                            <input type="url" class="form-input" id="websiteUrl" placeholder="https://yourwebsite.com">
                        </div>
                    </div>
                    
                    <div class="setup-actions">
                        <button class="btn btn-secondary" onclick="saveTemplate()">Save as Template</button>
                        <button class="btn btn-primary" onclick="addToQueue()">Add to Queue</button>
                    </div>
                </div>

                <!-- Queue Management Panel -->
                <div class="queue-panel">
                    <div class="panel-header">
                        <h2>Generation Queue</h2>
                        <div class="queue-controls">
                            <button class="btn btn-ghost btn-sm" onclick="clearQueue()">Clear All</button>
                            <button class="btn btn-secondary btn-sm" onclick="exportQueue()">Export</button>
                        </div>
                    </div>
                    
                    <!-- Queue Progress -->
                    <div class="queue-progress" id="queueProgress">
                        <div class="progress-header">
                            <div class="progress-info">
                                <span id="currentProgress">0</span> of <span id="totalItems">0</span> completed
                            </div>
                            <div class="progress-percentage" id="progressPercentage">0%</div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                        </div>
                        <div class="estimated-time" id="estimatedTime">Estimated time: --</div>
                    </div>
                    
                    <!-- Queue Items -->
                    <div class="queue-items" id="queueItems">
                        <div class="queue-placeholder">
                            <div class="placeholder-icon">
                                <svg class="w-12 h-12 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                            </div>
                            <h3>Queue is Empty</h3>
                            <p>Add keywords to start bulk content generation</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results Section -->
            <div class="results-section hidden" id="resultsSection">
                <div class="section-header">
                    <h2>Generation Results</h2>
                    <div class="results-actions">
                        <button class="btn btn-secondary" onclick="exportResults()">Export All</button>
                        <button class="btn btn-primary" onclick="downloadResults()">Download ZIP</button>
                    </div>
                </div>
                
                <div class="results-grid" id="resultsGrid">
                    <!-- Results will be populated here -->
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script>
        // Bulk Generator JavaScript functionality
        let queueItems = [];
        let isGenerating = false;
        let currentIndex = 0;
        let generationInterval;
        let selectedTemplate = null;
        
        document.addEventListener('DOMContentLoaded', function() {
            initializeBulkGenerator();
            setupEventListeners();
        });

        function initializeBulkGenerator() {
            // Initialize sidebar state
            const sidebar = document.getElementById('sidebarNav');
            const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (isCollapsed) {
                sidebar.classList.add('collapsed');
            }
            
            // Load saved queue if exists
            loadSavedQueue();
            updateQueueDisplay();
        }

        function setupEventListeners() {
            // File input events
            document.getElementById('fileInput').addEventListener('change', handleFileUpload);
            
            // Drag and drop events
            const uploadArea = document.querySelector('.upload-area');
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('drop', handleFileDrop);
        }

        function switchSetupTab(tabName) {
            // Remove active class from all tabs and content
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.setup-tab').forEach(tab => {
                tab.classList.remove('active');
                tab.classList.add('hidden');
            });
            
            // Activate selected tab
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
            document.getElementById(`${tabName}Tab`).classList.remove('hidden');
            document.getElementById(`${tabName}Tab`).classList.add('active');
        }

        function triggerFileUpload() {
            document.getElementById('fileInput').click();
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('drag-over');
        }

        function handleFileDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('drag-over');
            
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                processFile(files[0]);
            }
        }

        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (file) {
                processFile(file);
            }
        }

        function processFile(file) {
            const allowedTypes = ['.csv', '.txt', '.xlsx'];
            const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
            
            if (!allowedTypes.includes(fileExtension)) {
                showNotification('Please upload a CSV, TXT, or XLSX file', 'error');
                return;
            }
            
            showNotification('Processing file...', 'info');
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const content = e.target.result;
                parseFileContent(content, fileExtension);
            };
            reader.readAsText(file);
        }

        function parseFileContent(content, fileType) {
            let keywords = [];
            
            if (fileType === '.csv' || fileType === '.txt') {
                const lines = content.split('\n').filter(line => line.trim());
                const keywordColumn = parseInt(document.getElementById('keywordColumn').value);
                
                lines.forEach(line => {
                    const columns = line.split(',');
                    if (columns[keywordColumn] && columns[keywordColumn].trim()) {
                        keywords.push(columns[keywordColumn].trim());
                    }
                });
            }
            
            if (keywords.length > 0) {
                addKeywordsToQueue(keywords);
                showNotification(`Added ${keywords.length} keywords to queue`, 'success');
            } else {
                showNotification('No valid keywords found in file', 'error');
            }
        }

        function addKeywordsToQueue(keywords) {
            const defaultSettings = {
                location: document.getElementById('defaultLocation').value,
                contentType: document.getElementById('defaultContentType').value,
                tone: document.getElementById('defaultTone').value,
                wordCount: document.getElementById('defaultWordCount').value,
                websiteUrl: document.getElementById('websiteUrl').value
            };
            
            keywords.forEach(keyword => {
                const queueItem = {
                    id: generateId(),
                    keyword: keyword,
                    ...defaultSettings,
                    status: 'pending',
                    progress: 0,
                    result: null,
                    error: null,
                    createdAt: new Date()
                };
                
                queueItems.push(queueItem);
            });
            
            updateQueueDisplay();
            saveQueue();
        }

        function addToQueue() {
            const activeTab = document.querySelector('.setup-tab.active').id;
            let keywords = [];
            
            if (activeTab === 'manualTab') {
                const manualText = document.getElementById('manualKeywords').value;
                keywords = manualText.split('\n')
                    .map(line => line.trim())
                    .filter(line => line.length > 0);
            }
            
            if (keywords.length > 0) {
                addKeywordsToQueue(keywords);
                // Clear the input
                document.getElementById('manualKeywords').value = '';
                showNotification(`Added ${keywords.length} keywords to queue`, 'success');
            } else {
                showNotification('Please enter some keywords', 'error');
            }
        }

        function updateQueueDisplay() {
            const queueContainer = document.getElementById('queueItems');
            const queueCounter = document.getElementById('queueCounter');
            const totalPending = document.getElementById('totalPending');
            const totalGenerated = document.getElementById('totalGenerated');
            const totalFailed = document.getElementById('totalFailed');
            const startBtn = document.getElementById('startBtn');
            
            // Update counters
            const pending = queueItems.filter(item => item.status === 'pending').length;
            const completed = queueItems.filter(item => item.status === 'completed').length;
            const failed = queueItems.filter(item => item.status === 'failed').length;
            
            queueCounter.textContent = `${queueItems.length} in queue`;
            totalPending.textContent = pending;
            totalGenerated.textContent = completed;
            totalFailed.textContent = failed;
            
            // Enable/disable start button
            startBtn.disabled = pending === 0 || isGenerating;
            
            if (queueItems.length === 0) {
                queueContainer.innerHTML = `
                    <div class="queue-placeholder">
                        <div class="placeholder-icon">
                            <svg class="w-12 h-12 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </div>
                        <h3>Queue is Empty</h3>
                        <p>Add keywords to start bulk content generation</p>
                    </div>
                `;
            } else {
                queueContainer.innerHTML = queueItems.map(item => `
                    <div class="queue-item ${item.status}" data-id="${item.id}">
                        <div class="item-info">
                            <div class="item-keyword">${item.keyword}</div>
                            <div class="item-meta">
                                ${item.contentType} • ${item.location.toUpperCase()} • ${item.tone}
                            </div>
                        </div>
                        <div class="item-status">
                            <div class="status-indicator ${item.status}"></div>
                            <span class="status-text">${formatStatus(item.status)}</span>
                        </div>
                        <div class="item-actions">
                            ${item.status === 'pending' ? 
                                `<button class="btn-icon" onclick="removeFromQueue('${item.id}')" title="Remove">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>` :
                                item.status === 'completed' ?
                                `<button class="btn-icon" onclick="viewResult('${item.id}')" title="View">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </button>` :
                                `<button class="btn-icon" onclick="retryGeneration('${item.id}')" title="Retry">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                </button>`
                            }
                        </div>
                    </div>
                `).join('');
            }
            
            updateProgress();
        }

        function updateProgress() {
            const totalItems = queueItems.length;
            const completedItems = queueItems.filter(item => item.status === 'completed').length;
            const progressPercentage = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;
            
            document.getElementById('currentProgress').textContent = completedItems;
            document.getElementById('totalItems').textContent = totalItems;
            document.getElementById('progressPercentage').textContent = `${progressPercentage}%`;
            document.getElementById('progressFill').style.width = `${progressPercentage}%`;
            
            // Estimate remaining time
            if (isGenerating && completedItems > 0) {
                const avgTimePerItem = 3; // minutes
                const remainingItems = queueItems.filter(item => item.status === 'pending').length;
                const estimatedMinutes = remainingItems * avgTimePerItem;
                document.getElementById('estimatedTime').textContent = `Estimated time: ${estimatedMinutes} minutes`;
            } else {
                document.getElementById('estimatedTime').textContent = 'Estimated time: --';
            }
        }

        function startBulkGeneration() {
            if (isGenerating) return;
            
            const pendingItems = queueItems.filter(item => item.status === 'pending');
            if (pendingItems.length === 0) {
                showNotification('No pending items to generate', 'warning');
                return;
            }
            
            isGenerating = true;
            currentIndex = 0;
            
            updateBulkStatus('generating', 'Generating content...');
            document.getElementById('startBtn').disabled = true;
            document.getElementById('pauseBtn').disabled = false;
            
            processNextItem();
        }

        function processNextItem() {
            if (!isGenerating) return;
            
            const pendingItems = queueItems.filter(item => item.status === 'pending');
            if (pendingItems.length === 0) {
                finishGeneration();
                return;
            }
            
            const currentItem = pendingItems[0];
            generateContentForItem(currentItem);
        }

        function generateContentForItem(item) {
            // Update item status
            item.status = 'generating';
            updateQueueDisplay();
            
            // Simulate content generation
            setTimeout(() => {
                // Simulate success/failure
                const success = Math.random() > 0.1; // 90% success rate
                
                if (success) {
                    item.status = 'completed';
                    item.result = {
                        title: `Complete Guide to ${item.keyword}`,
                        content: `Professional content about ${item.keyword}...`,
                        seoScore: Math.floor(Math.random() * 20) + 80,
                        wordCount: Math.floor(Math.random() * 1000) + 1500
                    };
                } else {
                    item.status = 'failed';
                    item.error = 'Failed to generate content';
                }
                
                updateQueueDisplay();
                saveQueue();
                
                // Process next item
                setTimeout(() => {
                    processNextItem();
                }, 1000);
                
            }, Math.random() * 3000 + 2000); // 2-5 second generation time
        }

        function pauseQueue() {
            isGenerating = false;
            updateBulkStatus('paused', 'Generation paused');
            document.getElementById('startBtn').disabled = false;
            document.getElementById('pauseBtn').disabled = true;
        }

        function finishGeneration() {
            isGenerating = false;
            updateBulkStatus('completed', 'Generation completed');
            document.getElementById('startBtn').disabled = false;
            document.getElementById('pauseBtn').disabled = true;
            
            showNotification('Bulk generation completed!', 'success');
            showResults();
        }

        function updateBulkStatus(status, text) {
            const statusIndicator = document.querySelector('.bulk-status .status-indicator');
            const statusText = document.querySelector('.bulk-status .status-text');
            
            statusIndicator.className = `status-indicator status-${status}`;
            statusText.textContent = text;
        }

        function removeFromQueue(itemId) {
            queueItems = queueItems.filter(item => item.id !== itemId);
            updateQueueDisplay();
            saveQueue();
            showNotification('Item removed from queue', 'info');
        }

        function retryGeneration(itemId) {
            const item = queueItems.find(item => item.id === itemId);
            if (item) {
                item.status = 'pending';
                item.error = null;
                updateQueueDisplay();
                saveQueue();
                showNotification('Item added back to queue', 'info');
            }
        }

        function viewResult(itemId) {
            const item = queueItems.find(item => item.id === itemId);
            if (item && item.result) {
                // In a real implementation, this would open the content editor
                window.location.href = `content-editor.html?generated=true&title=${encodeURIComponent(item.result.title)}`;
            }
        }

        function clearQueue() {
            if (confirm('Are you sure you want to clear the entire queue?')) {
                queueItems = [];
                updateQueueDisplay();
                saveQueue();
                showNotification('Queue cleared', 'info');
            }
        }

        function exportQueue() {
            const csvContent = "data:text/csv;charset=utf-8," 
                + "Keyword,Status,Content Type,Location,Tone\n"
                + queueItems.map(item => 
                    `"${item.keyword}","${item.status}","${item.contentType}","${item.location}","${item.tone}"`
                ).join('\n');
            
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement('a');
            link.setAttribute('href', encodedUri);
            link.setAttribute('download', 'bulk_queue.csv');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function showResults() {
            const resultsSection = document.getElementById('resultsSection');
            const completedItems = queueItems.filter(item => item.status === 'completed');
            
            if (completedItems.length > 0) {
                resultsSection.classList.remove('hidden');
                
                const resultsGrid = document.getElementById('resultsGrid');
                resultsGrid.innerHTML = completedItems.map(item => `
                    <div class="result-card">
                        <div class="result-header">
                            <h3>${item.result.title}</h3>
                            <div class="result-score">
                                <div class="score-badge score-${item.result.seoScore >= 90 ? 'excellent' : 'good'}">${item.result.seoScore}</div>
                            </div>
                        </div>
                        <div class="result-meta">
                            <span>${item.keyword}</span>
                            <span>•</span>
                            <span>${item.result.wordCount} words</span>
                        </div>
                        <div class="result-actions">
                            <button class="btn btn-secondary btn-sm" onclick="viewResult('${item.id}')">Edit</button>
                            <button class="btn btn-primary btn-sm" onclick="downloadResult('${item.id}')">Download</button>
                        </div>
                    </div>
                `).join('');
            }
        }

        function selectTemplate(templateType) {
            selectedTemplate = templateType;
            
            // Remove selection from all cards
            document.querySelectorAll('.template-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Select clicked card
            event.target.closest('.template-card').classList.add('selected');
            
            showNotification(`${templateType} template selected`, 'info');
        }

        function saveTemplate() {
            const templateData = {
                location: document.getElementById('defaultLocation').value,
                contentType: document.getElementById('defaultContentType').value,
                tone: document.getElementById('defaultTone').value,
                wordCount: document.getElementById('defaultWordCount').value,
                websiteUrl: document.getElementById('websiteUrl').value
            };
            
            localStorage.setItem('bulkTemplate', JSON.stringify(templateData));
            showNotification('Template saved successfully', 'success');
        }

        function saveQueue() {
            localStorage.setItem('bulkQueue', JSON.stringify(queueItems));
        }

        function loadSavedQueue() {
            const savedQueue = localStorage.getItem('bulkQueue');
            if (savedQueue) {
                queueItems = JSON.parse(savedQueue);
            }
        }

        function formatStatus(status) {
            switch (status) {
                case 'pending': return 'Pending';
                case 'generating': return 'Generating...';
                case 'completed': return 'Completed';
                case 'failed': return 'Failed';
                default: return status;
            }
        }

        function generateId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Navigation functions
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebarNav');
            sidebar.classList.toggle('collapsed');
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }

        function toggleSection(button) {
            const section = button.closest('.nav-section');
            const items = section.querySelector('.nav-items');
            const icon = button.querySelector('svg');
            
            section.classList.toggle('collapsed');
            
            if (section.classList.contains('collapsed')) {
                items.style.display = 'none';
                icon.style.transform = 'rotate(-90deg)';
            } else {
                items.style.display = 'block';
                icon.style.transform = 'rotate(0deg)';
            }
        }
    </script>
</body>
</html>