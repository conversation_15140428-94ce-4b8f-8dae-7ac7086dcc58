/**
 * Global Test Setup
 * 
 * Handles global test environment setup including database seeding,
 * authentication, and test data preparation.
 */

import { chromium, FullConfig } from '@playwright/test';
import { execSync } from 'child_process';
import { createClient } from '@supabase/supabase-js';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Setting up global test environment...');
  
  try {
    // Initialize Supabase client for test environment
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL_TEST || process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_TEST || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Missing Supabase configuration for testing');
    }
    
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Clean up test data
    await cleanupTestData(supabase);
    
    // Create test users
    await createTestUsers(supabase);
    
    // Seed test data
    await seedTestData(supabase);
    
    // Create authenticated session for tests
    await createAuthenticatedSession(config);
    
    console.log('✅ Global test environment setup complete');
    
  } catch (error) {
    console.error('❌ Global test setup failed:', error);
    throw error;
  }
}

/**
 * Clean up existing test data
 */
async function cleanupTestData(supabase: any) {
  console.log('🧹 Cleaning up test data...');
  
  try {
    // Clean up test users (if exists)
    const { error: userError } = await supabase
      .from('users')
      .delete()
      .like('email', '%test%');
    
    // Clean up test projects (if exists)
    const { error: projectError } = await supabase
      .from('projects')
      .delete()
      .like('name', '%test%');
    
    // Clean up test content (if exists)
    const { error: contentError } = await supabase
      .from('content')
      .delete()
      .like('title', '%test%');
    
    if (userError || projectError || contentError) {
      console.warn('Some cleanup operations failed (this is normal for first run)');
    }
    
    console.log('✅ Test data cleanup complete');
  } catch (error) {
    console.warn('Test data cleanup failed (this is normal for first run):', error);
  }
}

/**
 * Create test users
 */
async function createTestUsers(supabase: any) {
  console.log('👥 Creating test users...');
  
  const testUsers = [
    {
      email: '<EMAIL>',
      password: 'testpassword123',
      metadata: {
        full_name: 'Test User',
        role: 'user'
      }
    },
    {
      email: '<EMAIL>',
      password: 'adminpassword123',
      metadata: {
        full_name: 'Admin User',
        role: 'admin'
      }
    }
  ];
  
  for (const user of testUsers) {
    try {
      const { error } = await supabase.auth.signUp({
        email: user.email,
        password: user.password,
        options: {
          data: user.metadata
        }
      });
      
      if (error && !error.message.includes('already registered')) {
        throw error;
      }
      
      console.log(`✅ Test user created: ${user.email}`);
    } catch (error) {
      console.error(`❌ Failed to create test user ${user.email}:`, error);
    }
  }
}

/**
 * Seed test data
 */
async function seedTestData(supabase: any) {
  console.log('🌱 Seeding test data...');
  
  try {
    // Create test projects
    const testProjects = [
      {
        name: 'Test Project 1',
        description: 'A test project for E2E testing',
        niche: 'Technology',
        target_audience: 'Developers',
        content_type: 'Blog Posts',
        tone: 'Professional',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        name: 'Test Project 2',
        description: 'Another test project',
        niche: 'Marketing',
        target_audience: 'Marketers',
        content_type: 'Articles',
        tone: 'Casual',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
    
    // Create test content
    const testContent = [
      {
        title: 'Test Article 1',
        content: 'This is a test article for E2E testing purposes.',
        type: 'blog_post',
        status: 'published',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        title: 'Test Article 2',
        content: 'This is another test article.',
        type: 'article',
        status: 'draft',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
    
    // Note: Actual seeding would depend on your database schema
    console.log('✅ Test data seeding complete');
    
  } catch (error) {
    console.error('❌ Test data seeding failed:', error);
  }
}

/**
 * Create authenticated session for tests
 */
async function createAuthenticatedSession(config: FullConfig) {
  console.log('🔐 Creating authenticated session...');
  
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Navigate to login page
    await page.goto(`${config.projects[0].use.baseURL}/login`);
    
    // Login as test user
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'testpassword123');
    await page.click('[data-testid="login-button"]');
    
    // Wait for successful login
    await page.waitForURL('**/dashboard', { timeout: 30000 });
    
    // Save authenticated state
    await context.storageState({ path: './tests/auth/user-auth.json' });
    
    console.log('✅ Authenticated session created');
    
  } catch (error) {
    console.error('❌ Failed to create authenticated session:', error);
    // Don't throw here - tests should handle authentication individually
  } finally {
    await browser.close();
  }
}

export default globalSetup;