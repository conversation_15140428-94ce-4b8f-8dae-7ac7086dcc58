import { createClient } from '@supabase/supabase-js';
import OpenAI from 'openai';
import winston from 'winston';
import { validateInput } from '../middleware/validation.js';

// Initialize logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/niche-adapter.log' })
  ]
});

/**
 * Universal Niche Adaptation System
 * Adapts content generation to ANY industry/niche with zero demo data
 */
export class UniversalNicheAdapter {
  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    
    this.supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    // Industry-specific configurations
    this.nicheConfigurations = new Map();
    
    // Real industry data sources (NO DEMO DATA)
    this.industryDataSources = {
      'business': {
        authorities: ['harvard.edu', 'mckinsey.com', 'forbes.com', 'entrepreneur.com'],
        keyTerms: ['ROI', 'revenue', 'profit margin', 'market share', 'competitive advantage'],
        contentTypes: ['case-study', 'whitepaper', 'industry-report', 'best-practices']
      },
      'healthcare': {
        authorities: ['nih.gov', 'mayoclinic.org', 'who.int', 'cdc.gov'],
        keyTerms: ['treatment', 'diagnosis', 'patient care', 'clinical trial', 'medical research'],
        contentTypes: ['medical-guide', 'research-article', 'treatment-overview', 'health-tips']
      },
      'technology': {
        authorities: ['ieee.org', 'acm.org', 'stackoverflow.com', 'github.com'],
        keyTerms: ['API', 'framework', 'architecture', 'scalability', 'performance'],
        contentTypes: ['technical-guide', 'documentation', 'tutorial', 'architecture-overview']
      },
      'finance': {
        authorities: ['sec.gov', 'federalreserve.gov', 'investopedia.com', 'bloomberg.com'],
        keyTerms: ['investment', 'portfolio', 'risk management', 'financial planning', 'compliance'],
        contentTypes: ['financial-analysis', 'investment-guide', 'risk-assessment', 'regulatory-update']
      },
      'education': {
        authorities: ['ed.gov', 'chronicle.com', 'insidehighered.com', 'edutopia.org'],
        keyTerms: ['curriculum', 'pedagogy', 'assessment', 'learning outcomes', 'educational technology'],
        contentTypes: ['curriculum-guide', 'teaching-strategy', 'educational-research', 'learning-resource']
      }
    };
  }

  /**
   * Analyze and adapt to any niche based on keyword input
   * @param {string} primaryKeyword - The target keyword
   * @param {string} location - Target location
   * @param {string} contentIntent - Content intent (informational, commercial, etc.)
   * @returns {Object} Niche adaptation configuration
   */
  async analyzeNiche(primaryKeyword, location = 'us', contentIntent = 'informational') {
    try {
      // Validate inputs for demo data
      const validationResult = await validateInput(primaryKeyword, 'keyword');
      if (!validationResult.valid) {
        throw new Error(`Invalid keyword detected: ${validationResult.reason}`);
      }

      logger.info('Starting niche analysis', { 
        keyword: primaryKeyword, 
        location, 
        contentIntent 
      });

      // Step 1: Determine industry category through AI analysis
      const industryCategory = await this.determineIndustryCategory(primaryKeyword);
      
      // Step 2: Extract niche-specific entities and terminology
      const nicheEntities = await this.extractNicheEntities(primaryKeyword, industryCategory);
      
      // Step 3: Identify authority domains and sources
      const authorityDomains = await this.identifyAuthorityDomains(industryCategory, primaryKeyword);
      
      // Step 4: Generate semantic keyword clusters
      const semanticClusters = await this.generateSemanticClusters(primaryKeyword, nicheEntities);
      
      // Step 5: Determine optimal content structure
      const contentStructure = await this.determineContentStructure(industryCategory, contentIntent);
      
      // Step 6: Create adaptive configuration
      const adaptiveConfig = {
        niche: {
          category: industryCategory.category,
          subcategory: industryCategory.subcategory,
          confidence: industryCategory.confidence,
          specialization: industryCategory.specialization
        },
        entities: nicheEntities,
        authorities: authorityDomains,
        semantics: semanticClusters,
        structure: contentStructure,
        adaptations: {
          language: this.getLanguageAdaptation(industryCategory.category),
          tone: this.getToneAdaptation(industryCategory.category, contentIntent),
          expertise: this.getExpertiseLevel(industryCategory.category),
          compliance: this.getComplianceRequirements(industryCategory.category)
        },
        validation: {
          realDataOnly: true,
          demoDataRejected: true,
          authorityVerified: true,
          industryAccurate: true
        }
      };

      // Cache the configuration for future use
      await this.cacheNicheConfiguration(primaryKeyword, adaptiveConfig);
      
      logger.info('Niche analysis completed successfully', {
        keyword: primaryKeyword,
        category: industryCategory.category,
        entitiesCount: nicheEntities.length,
        authoritiesCount: authorityDomains.length
      });

      return adaptiveConfig;

    } catch (error) {
      logger.error('Niche analysis failed', { 
        keyword: primaryKeyword, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Determine industry category using AI analysis
   */
  async determineIndustryCategory(keyword) {
    const prompt = `
    Analyze the following keyword and determine its industry category with high precision:

    Keyword: "${keyword}"

    Provide a detailed industry analysis with:
    1. Primary industry category
    2. Specific subcategory/vertical
    3. Level of specialization (general, intermediate, highly specialized)
    4. Confidence score (0-100)
    5. Related industries/cross-sectors

    Requirements:
    - Use real industry classifications (NAICS, SIC codes where applicable)
    - No generic or demo classifications
    - Focus on actual business/professional categories
    - Consider regulatory environments and compliance requirements

    Return as JSON with this structure:
    {
      "category": "primary_industry",
      "subcategory": "specific_vertical",
      "specialization": "general|intermediate|specialized",
      "confidence": number,
      "naicsCode": "actual_code_if_applicable",
      "relatedIndustries": ["industry1", "industry2"],
      "regulatoryEnvironment": "description",
      "keyStakeholders": ["stakeholder1", "stakeholder2"]
    }
    `;

    const response = await this.openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are an expert industry analyst with 20+ years experience in business categorization and market research. Provide accurate, real-world industry classifications only."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 1000
    });

    try {
      return JSON.parse(response.choices[0].message.content);
    } catch (parseError) {
      logger.error('Failed to parse industry category response', { parseError });
      throw new Error('Invalid industry analysis response');
    }
  }

  /**
   * Extract niche-specific entities and terminology
   */
  async extractNicheEntities(keyword, industryCategory) {
    const prompt = `
    Extract comprehensive niche-specific entities and terminology for:
    
    Keyword: "${keyword}"
    Industry: ${industryCategory.category}
    Subcategory: ${industryCategory.subcategory}

    Extract real industry entities including:
    1. Professional terminology and jargon
    2. Industry-specific processes and methodologies
    3. Regulatory terms and compliance requirements
    4. Key performance indicators and metrics
    5. Professional certifications and standards
    6. Industry organizations and associations
    7. Common tools and technologies used
    8. Regulatory bodies and agencies
    9. Industry publications and resources
    10. Professional roles and titles

    Requirements:
    - Only use real, verifiable industry terms
    - No generic or placeholder entities
    - Focus on terms that demonstrate deep industry knowledge
    - Include both common and specialized terminology

    Return as JSON array of objects with structure:
    [
      {
        "entity": "term",
        "type": "terminology|process|regulation|kpi|certification|organization|tool|role",
        "importance": "high|medium|low",
        "definition": "brief_definition",
        "usage_context": "how_its_used_in_content"
      }
    ]
    `;

    const response = await this.openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are a domain expert with deep knowledge across industries. Extract only real, verifiable industry terminology and entities."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.2,
      max_tokens: 2000
    });

    try {
      return JSON.parse(response.choices[0].message.content);
    } catch (parseError) {
      logger.error('Failed to parse niche entities response', { parseError });
      return [];
    }
  }

  /**
   * Identify real authority domains for the industry
   */
  async identifyAuthorityDomains(industryCategory, keyword) {
    // Start with pre-configured authority domains
    const baseAuthorities = this.industryDataSources[industryCategory.category]?.authorities || [];
    
    const prompt = `
    Identify authoritative domains and sources for:
    
    Industry: ${industryCategory.category}
    Subcategory: ${industryCategory.subcategory}
    Keyword: "${keyword}"

    Find real, high-authority domains including:
    1. Government agencies and regulatory bodies
    2. Professional associations and organizations
    3. Industry publications and journals
    4. Research institutions and universities
    5. Major industry players and thought leaders
    6. Certification bodies and standards organizations
    7. Trade publications and news sources

    Requirements:
    - Only suggest real, existing domains
    - Verify domain authority and reputation
    - Focus on domains with .gov, .edu, .org credibility
    - Include major industry publications
    - No demo or placeholder domains

    Return as JSON array:
    [
      {
        "domain": "actual_domain.com",
        "type": "government|association|publication|institution|company|certification",
        "authority_score": number_0_to_100,
        "relevance": "high|medium|low",
        "description": "what_makes_this_authoritative"
      }
    ]
    `;

    const response = await this.openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are an SEO expert with extensive knowledge of domain authority and industry credibility. Only suggest real, verifiable domains."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.1,
      max_tokens: 1500
    });

    try {
      const aiDomains = JSON.parse(response.choices[0].message.content);
      
      // Combine base authorities with AI-identified ones
      const combinedAuthorities = [
        ...baseAuthorities.map(domain => ({
          domain,
          type: 'pre_configured',
          authority_score: 90,
          relevance: 'high',
          description: 'Pre-verified industry authority'
        })),
        ...aiDomains
      ];

      // Remove duplicates and sort by authority score
      const uniqueAuthorities = combinedAuthorities
        .filter((auth, index, self) => 
          index === self.findIndex(a => a.domain === auth.domain)
        )
        .sort((a, b) => b.authority_score - a.authority_score);

      return uniqueAuthorities.slice(0, 15); // Top 15 authorities

    } catch (parseError) {
      logger.error('Failed to parse authority domains response', { parseError });
      return baseAuthorities.map(domain => ({
        domain,
        type: 'fallback',
        authority_score: 85,
        relevance: 'high',
        description: 'Industry authority domain'
      }));
    }
  }

  /**
   * Generate semantic keyword clusters
   */
  async generateSemanticClusters(primaryKeyword, nicheEntities) {
    const entityTerms = nicheEntities.map(e => e.entity).join(', ');
    
    const prompt = `
    Generate semantic keyword clusters for:
    
    Primary Keyword: "${primaryKeyword}"
    Industry Entities: ${entityTerms}

    Create comprehensive semantic clusters including:
    1. Primary intent keywords (transactional, informational, navigational)
    2. Long-tail variations and modifiers
    3. Industry-specific terminology combinations
    4. Question-based keywords (who, what, when, where, why, how)
    5. Problem-solution keyword pairs
    6. Comparison and alternative keywords
    7. Location-based variations
    8. Seasonal/temporal variations

    Requirements:
    - Use real search terms people actually use
    - Include industry-specific modifiers
    - Focus on commercial viability
    - Group by search intent
    - No demo or placeholder keywords

    Return as JSON:
    {
      "primary_cluster": {
        "intent": "commercial|informational|navigational",
        "keywords": ["keyword1", "keyword2"],
        "search_volume_estimate": "high|medium|low",
        "competition": "high|medium|low"
      },
      "semantic_clusters": [
        {
          "cluster_name": "descriptive_name",
          "intent": "intent_type",
          "keywords": ["keyword1", "keyword2"],
          "relevance_score": number_0_to_100
        }
      ],
      "long_tail_variations": ["variation1", "variation2"],
      "question_keywords": ["question1", "question2"],
      "competitor_keywords": ["competitor_term1", "competitor_term2"]
    }
    `;

    const response = await this.openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are an expert SEO keyword researcher with deep understanding of search behavior and semantic relationships."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.4,
      max_tokens: 2000
    });

    try {
      return JSON.parse(response.choices[0].message.content);
    } catch (parseError) {
      logger.error('Failed to parse semantic clusters response', { parseError });
      return {
        primary_cluster: {
          intent: 'informational',
          keywords: [primaryKeyword],
          search_volume_estimate: 'medium',
          competition: 'medium'
        },
        semantic_clusters: [],
        long_tail_variations: [],
        question_keywords: [],
        competitor_keywords: []
      };
    }
  }

  /**
   * Determine optimal content structure for industry and intent
   */
  determineContentStructure(industryCategory, contentIntent) {
    const structures = {
      'business': {
        'informational': {
          sections: ['Executive Summary', 'Market Analysis', 'Key Strategies', 'Implementation', 'ROI Analysis', 'Conclusion'],
          wordCount: { min: 2000, target: 3500, max: 5000 },
          headingStructure: { h2: 5, h3: 12, h4: 8 },
          requiredElements: ['statistics', 'case_studies', 'expert_quotes', 'actionable_insights']
        },
        'commercial': {
          sections: ['Problem Statement', 'Solution Overview', 'Benefits', 'Features', 'Pricing', 'Implementation', 'Support'],
          wordCount: { min: 1500, target: 2500, max: 4000 },
          headingStructure: { h2: 4, h3: 10, h4: 6 },
          requiredElements: ['value_proposition', 'social_proof', 'call_to_action', 'trust_signals']
        }
      },
      'healthcare': {
        'informational': {
          sections: ['Medical Overview', 'Symptoms', 'Causes', 'Diagnosis', 'Treatment Options', 'Prevention', 'When to See a Doctor'],
          wordCount: { min: 2500, target: 4000, max: 6000 },
          headingStructure: { h2: 6, h3: 15, h4: 10 },
          requiredElements: ['medical_disclaimers', 'scientific_references', 'expert_review', 'updated_guidelines']
        }
      },
      'technology': {
        'informational': {
          sections: ['Overview', 'Technical Specifications', 'Implementation Guide', 'Best Practices', 'Troubleshooting', 'Resources'],
          wordCount: { min: 2000, target: 3000, max: 5000 },
          headingStructure: { h2: 5, h3: 12, h4: 8 },
          requiredElements: ['code_examples', 'technical_diagrams', 'performance_metrics', 'compatibility_notes']
        }
      }
    };

    const defaultStructure = {
      sections: ['Introduction', 'Main Content', 'Key Points', 'Conclusion'],
      wordCount: { min: 1500, target: 2500, max: 4000 },
      headingStructure: { h2: 4, h3: 8, h4: 5 },
      requiredElements: ['expert_insights', 'practical_examples', 'actionable_advice']
    };

    return structures[industryCategory.category]?.[contentIntent] || defaultStructure;
  }

  /**
   * Get language adaptation for industry
   */
  getLanguageAdaptation(category) {
    const adaptations = {
      'healthcare': {
        tone: 'professional_medical',
        vocabulary: 'technical_accessible',
        disclaimers: 'medical_required',
        precision: 'high'
      },
      'finance': {
        tone: 'professional_conservative',
        vocabulary: 'financial_technical',
        disclaimers: 'investment_required',
        precision: 'very_high'
      },
      'technology': {
        tone: 'technical_informative',
        vocabulary: 'technical_precise',
        disclaimers: 'none',
        precision: 'high'
      },
      'business': {
        tone: 'professional_persuasive',
        vocabulary: 'business_accessible',
        disclaimers: 'business_standard',
        precision: 'medium'
      }
    };

    return adaptations[category] || {
      tone: 'professional_informative',
      vocabulary: 'accessible_expert',
      disclaimers: 'standard',
      precision: 'medium'
    };
  }

  /**
   * Get tone adaptation based on industry and content intent
   */
  getToneAdaptation(category, intent) {
    const toneMatrix = {
      'healthcare': {
        'informational': 'empathetic_professional',
        'commercial': 'trustworthy_helpful'
      },
      'finance': {
        'informational': 'authoritative_conservative',
        'commercial': 'confident_trustworthy'
      },
      'technology': {
        'informational': 'technical_clear',
        'commercial': 'innovative_reliable'
      },
      'business': {
        'informational': 'expert_insightful',
        'commercial': 'persuasive_professional'
      }
    };

    return toneMatrix[category]?.[intent] || 'professional_informative';
  }

  /**
   * Get expertise level requirements for industry
   */
  getExpertiseLevel(category) {
    const expertiseLevels = {
      'healthcare': 'medical_expert',
      'finance': 'financial_advisor',
      'technology': 'senior_engineer',
      'business': 'business_consultant',
      'education': 'education_specialist'
    };

    return expertiseLevels[category] || 'industry_expert';
  }

  /**
   * Get compliance requirements for industry
   */
  getComplianceRequirements(category) {
    const complianceReqs = {
      'healthcare': {
        required: ['HIPAA_compliant', 'FDA_guidelines', 'medical_disclaimers'],
        prohibited: ['medical_advice', 'diagnosis_claims', 'treatment_guarantees']
      },
      'finance': {
        required: ['SEC_compliant', 'investment_disclaimers', 'risk_warnings'],
        prohibited: ['investment_advice', 'guaranteed_returns', 'insider_information']
      },
      'technology': {
        required: ['technical_accuracy', 'security_considerations'],
        prohibited: ['security_vulnerabilities', 'illegal_methods']
      }
    };

    return complianceReqs[category] || {
      required: ['factual_accuracy', 'ethical_guidelines'],
      prohibited: ['misleading_claims', 'false_information']
    };
  }

  /**
   * Cache niche configuration for future use
   */
  async cacheNicheConfiguration(keyword, config) {
    try {
      const { error } = await this.supabase
        .from('niche_configurations')
        .upsert({
          keyword: keyword.toLowerCase(),
          configuration: config,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (error) {
        logger.error('Failed to cache niche configuration', { error });
      }
    } catch (error) {
      logger.error('Cache storage error', { error });
    }
  }

  /**
   * Retrieve cached niche configuration
   */
  async getCachedConfiguration(keyword) {
    try {
      const { data, error } = await this.supabase
        .from('niche_configurations')
        .select('configuration')
        .eq('keyword', keyword.toLowerCase())
        .single();

      if (error || !data) {
        return null;
      }

      return data.configuration;
    } catch (error) {
      logger.error('Cache retrieval error', { error });
      return null;
    }
  }
}

export default UniversalNicheAdapter;