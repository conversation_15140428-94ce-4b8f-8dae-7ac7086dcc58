'use client';

import React, { useState, forwardRef } from 'react';
import Image from 'next/image';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  fill?: boolean;
  className?: string;
  sizes?: string;
  priority?: boolean;
  quality?: number;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  onLoad?: () => void;
  onError?: () => void;
  fallback?: React.ReactNode;
}

const OptimizedImage = forwardRef<HTMLImageElement, OptimizedImageProps>(
  ({
    src,
    alt,
    width,
    height,
    fill = false,
    className = '',
    sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
    priority = false,
    quality = 85,
    placeholder = 'blur',
    blurDataURL,
    onLoad,
    onError,
    fallback = <div className={`bg-gray-200 animate-pulse ${className}`} />
  }, ref) => {
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);

    // Generate blur data URL if not provided
    const defaultBlurDataURL = blurDataURL || 
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjwvc3ZnPg==';

    const handleLoad = () => {
      setIsLoading(false);
      onLoad?.();
    };

    const handleError = () => {
      setHasError(true);
      setIsLoading(false);
      onError?.();
    };

    if (hasError) {
      return fallback;
    }

    const imageProps = {
      src,
      alt,
      quality,
      sizes,
      priority,
      placeholder,
      blurDataURL: defaultBlurDataURL,
      onLoad: handleLoad,
      onError: handleError,
      className: `${className} ${isLoading ? 'opacity-0' : 'opacity-100 transition-opacity duration-300'}`,
      ref
    };

    if (fill) {
      return (
        <div className="relative overflow-hidden">
          <Image {...imageProps} fill />
        </div>
      );
    }

    return (
      <Image
        {...imageProps}
        width={width}
        height={height}
      />
    );
  }
);

OptimizedImage.displayName = 'OptimizedImage';

// Avatar component with optimized images
interface AvatarProps {
  src?: string;
  alt: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  fallback?: React.ReactNode;
}

export function Avatar({ 
  src, 
  alt, 
  size = 'md', 
  className = '', 
  fallback 
}: AvatarProps) {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  };

  const sizeValues = {
    sm: 32,
    md: 48,
    lg: 64,
    xl: 96
  };

  const defaultFallback = (
    <div className={`${sizeClasses[size]} rounded-full bg-gray-300 flex items-center justify-center`}>
      <span className="text-gray-600 font-medium">
        {alt.charAt(0).toUpperCase()}
      </span>
    </div>
  );

  if (!src) {
    return fallback || defaultFallback;
  }

  return (
    <div className={`${sizeClasses[size]} rounded-full overflow-hidden ${className}`}>
      <OptimizedImage
        src={src}
        alt={alt}
        width={sizeValues[size]}
        height={sizeValues[size]}
        className="object-cover"
        fallback={fallback || defaultFallback}
      />
    </div>
  );
}

// Logo component with optimized images
interface LogoProps {
  src?: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
}

export function Logo({ 
  src = '/images/logo.svg', 
  alt, 
  width = 120, 
  height = 40, 
  className = '', 
  priority = true 
}: LogoProps) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      priority={priority}
      quality={100}
      placeholder="empty"
    />
  );
}

// Hero image component with responsive optimization
interface HeroImageProps {
  src: string;
  alt: string;
  className?: string;
  priority?: boolean;
}

export function HeroImage({ 
  src, 
  alt, 
  className = '', 
  priority = true 
}: HeroImageProps) {
  return (
    <div className={`relative w-full h-64 md:h-96 lg:h-[500px] ${className}`}>
      <OptimizedImage
        src={src}
        alt={alt}
        fill
        priority={priority}
        quality={90}
        sizes="100vw"
        className="object-cover"
      />
    </div>
  );
}

// Thumbnail component for cards and lists
interface ThumbnailProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  aspectRatio?: 'square' | 'video' | 'portrait' | 'landscape';
}

export function Thumbnail({ 
  src, 
  alt, 
  width = 200, 
  height = 150, 
  className = '', 
  aspectRatio = 'landscape' 
}: ThumbnailProps) {
  const aspectRatioClasses = {
    square: 'aspect-square',
    video: 'aspect-video',
    portrait: 'aspect-[3/4]',
    landscape: 'aspect-[4/3]'
  };

  return (
    <div className={`relative overflow-hidden rounded-lg ${aspectRatioClasses[aspectRatio]} ${className}`}>
      <OptimizedImage
        src={src}
        alt={alt}
        fill
        quality={80}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
        className="object-cover"
      />
    </div>
  );
}

export default OptimizedImage;