-- SEO SAAS Database Schema
-- Enterprise-grade database design for AI-powered SEO content generation platform

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create custom types
CREATE TYPE subscription_tier AS ENUM ('free', 'pro', 'enterprise');
CREATE TYPE content_status AS ENUM ('draft', 'published', 'archived');
CREATE TYPE generation_status AS ENUM ('pending', 'processing', 'completed', 'failed');

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VA<PERSON>HAR(255),
    avatar_url TEXT,
    subscription_tier subscription_tier DEFAULT 'free',
    usage_limit INTEGER DEFAULT 10, -- Content generation limit per month
    usage_count INTEGER DEFAULT 0, -- Current month usage
    last_reset_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Projects table
CREATE TABLE public.projects (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    website_url TEXT,
    target_keywords TEXT[] DEFAULT '{}',
    location VARCHAR(255),
    industry VARCHAR(255),
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Generated content table
CREATE TABLE public.generated_content (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    meta_description TEXT,
    target_keyword VARCHAR(255) NOT NULL,
    word_count INTEGER NOT NULL,
    seo_score DECIMAL(5,2),
    competitor_urls TEXT[] DEFAULT '{}',
    generation_settings JSONB DEFAULT '{}',
    status content_status DEFAULT 'draft',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- SEO analysis table
CREATE TABLE public.seo_analysis (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    content_id UUID REFERENCES public.generated_content(id) ON DELETE CASCADE NOT NULL,
    keyword_density JSONB DEFAULT '{}', -- {keyword: density_percentage}
    readability_score DECIMAL(5,2),
    meta_tags JSONB DEFAULT '{}', -- {tag_name: tag_content}
    internal_links INTEGER DEFAULT 0,
    external_links INTEGER DEFAULT 0,
    headings_structure JSONB DEFAULT '{}', -- {h1: count, h2: count, etc}
    recommendations TEXT[] DEFAULT '{}',
    ai_detection_score DECIMAL(5,2), -- AI detection resistance score
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Competitor analysis table
CREATE TABLE public.competitor_analysis (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE NOT NULL,
    keyword VARCHAR(255) NOT NULL,
    competitor_url TEXT NOT NULL,
    title VARCHAR(500),
    meta_description TEXT,
    content_preview TEXT,
    word_count INTEGER,
    headings_structure JSONB DEFAULT '{}',
    ranking_position INTEGER,
    domain_authority INTEGER,
    backlinks_count INTEGER,
    analysis_data JSONB DEFAULT '{}',
    scraped_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Usage tracking table
CREATE TABLE public.usage_tracking (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    action VARCHAR(100) NOT NULL, -- 'content_generation', 'seo_analysis', etc.
    resource_type VARCHAR(100) NOT NULL, -- 'content', 'project', 'analysis'
    resource_id UUID,
    metadata JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Bulk operations table
CREATE TABLE public.bulk_operations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    operation_type VARCHAR(100) NOT NULL, -- 'bulk_content_generation', 'bulk_analysis'
    status generation_status DEFAULT 'pending',
    total_items INTEGER NOT NULL,
    completed_items INTEGER DEFAULT 0,
    failed_items INTEGER DEFAULT 0,
    settings JSONB DEFAULT '{}',
    results JSONB DEFAULT '{}',
    error_log TEXT[],
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Bulk keywords table
CREATE TABLE public.bulk_keywords (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    bulk_operation_id UUID REFERENCES public.bulk_operations(id) ON DELETE CASCADE NOT NULL,
    keyword VARCHAR(255) NOT NULL,
    location VARCHAR(255),
    status generation_status DEFAULT 'pending',
    generated_content_id UUID REFERENCES public.generated_content(id) ON DELETE SET NULL,
    error_message TEXT,
    processing_time INTEGER, -- in seconds
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE
);

-- Content performance table
CREATE TABLE public.content_performance (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    content_id UUID REFERENCES public.generated_content(id) ON DELETE CASCADE NOT NULL,
    keyword VARCHAR(255) NOT NULL,
    ranking_position INTEGER,
    search_volume INTEGER,
    click_through_rate DECIMAL(5,2),
    impressions INTEGER,
    clicks INTEGER,
    tracked_date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- API keys table (for external integrations)
CREATE TABLE public.api_keys (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    service_name VARCHAR(100) NOT NULL, -- 'openai', 'serper', etc.
    api_key_encrypted TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, service_name)
);

-- Subscriptions table
CREATE TABLE public.subscriptions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    tier subscription_tier NOT NULL,
    stripe_subscription_id VARCHAR(255) UNIQUE,
    stripe_customer_id VARCHAR(255),
    current_period_start TIMESTAMP WITH TIME ZONE,
    current_period_end TIMESTAMP WITH TIME ZONE,
    cancel_at_period_end BOOLEAN DEFAULT false,
    status VARCHAR(50), -- 'active', 'canceled', 'past_due', etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_projects_user_id ON public.projects(user_id);
CREATE INDEX idx_generated_content_project_id ON public.generated_content(project_id);
CREATE INDEX idx_generated_content_user_id ON public.generated_content(user_id);
CREATE INDEX idx_generated_content_keyword ON public.generated_content(target_keyword);
CREATE INDEX idx_seo_analysis_content_id ON public.seo_analysis(content_id);
CREATE INDEX idx_competitor_analysis_project_id ON public.competitor_analysis(project_id);
CREATE INDEX idx_competitor_analysis_keyword ON public.competitor_analysis(keyword);
CREATE INDEX idx_usage_tracking_user_id ON public.usage_tracking(user_id);
CREATE INDEX idx_usage_tracking_action ON public.usage_tracking(action);
CREATE INDEX idx_bulk_operations_user_id ON public.bulk_operations(user_id);
CREATE INDEX idx_bulk_keywords_operation_id ON public.bulk_keywords(bulk_operation_id);
CREATE INDEX idx_content_performance_content_id ON public.content_performance(content_id);
CREATE INDEX idx_api_keys_user_id ON public.api_keys(user_id);
CREATE INDEX idx_subscriptions_user_id ON public.subscriptions(user_id);

-- Full-text search indexes
CREATE INDEX idx_generated_content_fts ON public.generated_content USING gin(to_tsvector('english', title || ' ' || content));
CREATE INDEX idx_projects_fts ON public.projects USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON public.projects
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_generated_content_updated_at BEFORE UPDATE ON public.generated_content
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_api_keys_updated_at BEFORE UPDATE ON public.api_keys
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON public.subscriptions
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Create function to reset monthly usage
CREATE OR REPLACE FUNCTION public.reset_monthly_usage()
RETURNS void AS $$
BEGIN
    UPDATE public.users 
    SET usage_count = 0, last_reset_date = CURRENT_DATE
    WHERE last_reset_date < DATE_TRUNC('month', CURRENT_DATE);
END;
$$ language 'plpgsql';

-- Row Level Security (RLS) Policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.generated_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.seo_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.competitor_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.usage_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bulk_operations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bulk_keywords ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.content_performance ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;

-- Users can only see their own data
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Projects policies
CREATE POLICY "Users can view own projects" ON public.projects
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own projects" ON public.projects
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own projects" ON public.projects
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own projects" ON public.projects
    FOR DELETE USING (auth.uid() = user_id);

-- Generated content policies
CREATE POLICY "Users can view own content" ON public.generated_content
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own content" ON public.generated_content
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own content" ON public.generated_content
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own content" ON public.generated_content
    FOR DELETE USING (auth.uid() = user_id);

-- SEO analysis policies
CREATE POLICY "Users can view seo analysis of own content" ON public.seo_analysis
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.generated_content 
            WHERE id = content_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create seo analysis for own content" ON public.seo_analysis
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.generated_content 
            WHERE id = content_id AND user_id = auth.uid()
        )
    );

-- Similar policies for other tables...
-- (Additional policies would be created for all other tables following the same pattern)

-- Create a function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
    INSERT INTO public.users (id, email, full_name, avatar_url)
    VALUES (
        NEW.id,
        NEW.email,
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ language 'plpgsql' security definer;

-- Create trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA public TO postgres, service_role;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO postgres, authenticated, service_role;