/**
 * Authority Link Embedding System
 * Enterprise SEO SAAS - Intelligent embedding of authority links in generated content
 */

import { AuthorityLink } from './authorityLinkDiscovery'

export interface EmbeddingContext {
  content: string
  primaryKeyword: string
  secondaryKeywords: string[]
  contentType: string
  targetSection?: string
}

export interface EmbeddedLink {
  originalText: string
  embeddedText: string
  authorityLink: AuthorityLink
  position: number
  section: string
  contextRelevance: number
}

export interface EmbeddingResult {
  enhancedContent: string
  embeddedLinks: EmbeddedLink[]
  linkDensity: number
  authorityScore: number
  warnings: string[]
}

export interface EmbeddingSettings {
  maxLinksPerSection: number
  minWordsBetweenLinks: number
  preferredAnchorLength: { min: number; max: number }
  avoidSections: string[]
  linkDensityLimit: number // Max percentage of content that can be links
}

export class AuthorityLinkEmbedding {
  private defaultSettings: EmbeddingSettings = {
    maxLinksPerSection: 2,
    minWordsBetweenLinks: 100,
    preferredAnchorLength: { min: 2, max: 6 },
    avoidSections: ['conclusion', 'footer', 'header'],
    linkDensityLimit: 0.03 // 3% max
  }

  /**
   * Embed authority links intelligently into content
   */
  async embedAuthorityLinks(
    context: EmbeddingContext,
    authorityLinks: AuthorityLink[],
    settings: Partial<EmbeddingSettings> = {}
  ): Promise<EmbeddingResult> {
    const embeddingSettings = { ...this.defaultSettings, ...settings }
    
    // Validate inputs
    this.validateEmbeddingInputs(context, authorityLinks)
    
    // Parse content structure
    const contentStructure = this.parseContentStructure(context.content)
    
    // Score and rank links for embedding
    const rankedLinks = this.rankLinksForEmbedding(authorityLinks, context)
    
    // Find optimal embedding positions
    const embeddingPositions = this.findEmbeddingPositions(
      contentStructure,
      rankedLinks,
      embeddingSettings
    )
    
    // Embed links with natural anchor text
    const embeddingResult = this.performEmbedding(
      context.content,
      embeddingPositions,
      embeddingSettings
    )
    
    // Validate and optimize the result
    return this.validateAndOptimizeResult(embeddingResult, embeddingSettings)
  }

  /**
   * Parse content structure to identify sections and embedding opportunities
   */
  private parseContentStructure(content: string) {
    const structure = {
      sections: [] as Array<{
        title: string
        startIndex: number
        endIndex: number
        wordCount: number
        type: 'header' | 'paragraph' | 'list' | 'conclusion'
        priority: number
      }>,
      sentences: [] as Array<{
        text: string
        startIndex: number
        endIndex: number
        section: string
        complexity: number
      }>,
      keywords: [] as Array<{
        term: string
        positions: number[]
        frequency: number
      }>
    }

    // Split content into sections based on headers
    const headerRegex = /^(#{1,6})\s+(.+)$/gm
    let lastIndex = 0
    let match

    while ((match = headerRegex.exec(content)) !== null) {
      const headerLevel = match[1].length
      const headerText = match[2]
      const headerStart = match.index
      
      // Add previous section if exists
      if (lastIndex < headerStart) {
        const sectionContent = content.slice(lastIndex, headerStart)
        if (sectionContent.trim()) {
          structure.sections.push({
            title: 'Content',
            startIndex: lastIndex,
            endIndex: headerStart,
            wordCount: this.countWords(sectionContent),
            type: 'paragraph',
            priority: 3
          })
        }
      }

      // Find end of this section (next header or end of content)
      const nextHeaderMatch = headerRegex.exec(content)
      const sectionEnd = nextHeaderMatch ? nextHeaderMatch.index : content.length
      headerRegex.lastIndex = match.index + match[0].length // Reset regex position

      const sectionContent = content.slice(match.index, sectionEnd)
      structure.sections.push({
        title: headerText,
        startIndex: match.index,
        endIndex: sectionEnd,
        wordCount: this.countWords(sectionContent),
        type: this.categorizeSection(headerText),
        priority: this.calculateSectionPriority(headerText, headerLevel)
      })

      lastIndex = sectionEnd
    }

    // Add final section if exists
    if (lastIndex < content.length) {
      const finalContent = content.slice(lastIndex)
      if (finalContent.trim()) {
        structure.sections.push({
          title: 'Final Content',
          startIndex: lastIndex,
          endIndex: content.length,
          wordCount: this.countWords(finalContent),
          type: 'paragraph',
          priority: 2
        })
      }
    }

    // Parse sentences for fine-grained embedding
    structure.sentences = this.parseSentences(content, structure.sections)

    return structure
  }

  /**
   * Rank authority links based on relevance and embedding potential
   */
  private rankLinksForEmbedding(
    authorityLinks: AuthorityLink[],
    context: EmbeddingContext
  ): Array<AuthorityLink & { embeddingScore: number }> {
    return authorityLinks.map(link => {
      let embeddingScore = 0

      // Base authority score (0-40 points)
      embeddingScore += (link.authorityScore / 100) * 40

      // Relevance score (0-30 points)
      embeddingScore += link.relevanceScore * 30

      // Source type preference (0-20 points)
      const sourceTypeScores = {
        wikipedia: 20,
        academic: 18,
        government: 16,
        news: 12,
        industry: 10,
        reference: 15
      }
      embeddingScore += sourceTypeScores[link.sourceType] || 5

      // Context relevance (0-10 points)
      const contextRelevance = this.calculateContextRelevance(link, context)
      embeddingScore += contextRelevance * 10

      return {
        ...link,
        embeddingScore
      }
    }).sort((a, b) => b.embeddingScore - a.embeddingScore)
  }

  /**
   * Find optimal positions for embedding links
   */
  private findEmbeddingPositions(
    contentStructure: any,
    rankedLinks: Array<AuthorityLink & { embeddingScore: number }>,
    settings: EmbeddingSettings
  ) {
    const positions: Array<{
      link: AuthorityLink & { embeddingScore: number }
      position: number
      anchorText: string
      section: string
      confidence: number
    }> = []

    const usedPositions: number[] = []

    for (const link of rankedLinks) {
      // Find best embedding position for this link
      const candidates = this.findEmbeddingCandidates(
        contentStructure,
        link,
        usedPositions,
        settings
      )

      if (candidates.length > 0) {
        const bestCandidate = candidates[0] // Already sorted by confidence
        positions.push({
          link,
          position: bestCandidate.position,
          anchorText: bestCandidate.anchorText,
          section: bestCandidate.section,
          confidence: bestCandidate.confidence
        })

        // Mark area around this position as used
        const buffer = settings.minWordsBetweenLinks * 6 // Approximate characters
        for (let i = bestCandidate.position - buffer; i <= bestCandidate.position + buffer; i++) {
          usedPositions.push(i)
        }
      }
    }

    return positions
  }

  /**
   * Find embedding candidates for a specific link
   */
  private findEmbeddingCandidates(
    contentStructure: any,
    link: AuthorityLink & { embeddingScore: number },
    usedPositions: number[],
    settings: EmbeddingSettings
  ) {
    const candidates: Array<{
      position: number
      anchorText: string
      section: string
      confidence: number
    }> = []

    // Look for contextually relevant phrases
    const contextPatterns = this.generateContextPatterns(link)
    
    for (const sentence of contentStructure.sentences) {
      // Skip sections that should be avoided
      if (settings.avoidSections.some(avoid => 
        sentence.section.toLowerCase().includes(avoid.toLowerCase())
      )) {
        continue
      }

      // Check if position is too close to existing links
      if (usedPositions.some(used => 
        Math.abs(used - sentence.startIndex) < settings.minWordsBetweenLinks * 6
      )) {
        continue
      }

      // Find matching patterns in sentence
      for (const pattern of contextPatterns) {
        const matches = sentence.text.matchAll(new RegExp(pattern.regex, 'gi'))
        
        for (const match of matches) {
          if (match.index !== undefined) {
            const position = sentence.startIndex + match.index
            const anchorText = this.optimizeAnchorText(match[0], settings)
            
            candidates.push({
              position,
              anchorText,
              section: sentence.section,
              confidence: pattern.confidence
            })
          }
        }
      }
    }

    // Sort by confidence and return top candidates
    return candidates
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 5) // Limit candidates
  }

  /**
   * Generate context patterns for finding embedding opportunities
   */
  private generateContextPatterns(link: AuthorityLink & { embeddingScore: number }) {
    const patterns: Array<{ regex: string; confidence: number }> = []

    // Extract key terms from link title and description
    const keyTerms = this.extractKeyTerms(link.title + ' ' + link.description)

    // Generate patterns based on source type
    switch (link.sourceType) {
      case 'wikipedia':
        patterns.push(
          { regex: `(?:according to|as stated in|wikipedia notes|research shows)\\s+[^.]*?(${keyTerms.join('|')})`, confidence: 0.9 },
          { regex: `(${keyTerms.join('|')})\\s*(?:definition|explanation|overview)`, confidence: 0.8 }
        )
        break

      case 'academic':
        patterns.push(
          { regex: `(?:studies show|research indicates|academic research)\\s+[^.]*?(${keyTerms.join('|')})`, confidence: 0.9 },
          { regex: `(${keyTerms.join('|')})\\s*(?:study|research|analysis)`, confidence: 0.8 }
        )
        break

      case 'government':
        patterns.push(
          { regex: `(?:government data|official statistics|regulatory information)\\s+[^.]*?(${keyTerms.join('|')})`, confidence: 0.9 },
          { regex: `(${keyTerms.join('|')})\\s*(?:regulations|guidelines|standards)`, confidence: 0.8 }
        )
        break

      case 'news':
        patterns.push(
          { regex: `(?:news reports|recent developments|industry news)\\s+[^.]*?(${keyTerms.join('|')})`, confidence: 0.7 },
          { regex: `(${keyTerms.join('|')})\\s*(?:trends|developments|updates)`, confidence: 0.6 }
        )
        break
    }

    // Generic patterns
    patterns.push(
      { regex: `(${keyTerms.join('|')})(?:\\s+(?:information|details|insights))?`, confidence: 0.5 },
      { regex: `(?:learn more about|additional information on|further reading)\\s+[^.]*?(${keyTerms.join('|')})`, confidence: 0.6 }
    )

    return patterns.filter(p => keyTerms.length > 0)
  }

  /**
   * Perform the actual embedding of links
   */
  private performEmbedding(
    content: string,
    embeddingPositions: any[],
    settings: EmbeddingSettings
  ): EmbeddingResult {
    let enhancedContent = content
    const embeddedLinks: EmbeddedLink[] = []
    let positionOffset = 0

    // Sort positions by index to maintain correct positions during embedding
    const sortedPositions = embeddingPositions.sort((a, b) => a.position - b.position)

    for (const embedding of sortedPositions) {
      const actualPosition = embedding.position + positionOffset
      const { link, anchorText } = embedding

      // Create the embedded link HTML
      const linkHtml = `<a href="${link.url}" target="_blank" rel="noopener noreferrer" title="${this.escapeHtml(link.description)}">${anchorText}</a>`

      // Find the original text that will be replaced
      const originalText = content.substr(embedding.position, anchorText.length)

      // Replace the text with the link
      enhancedContent = enhancedContent.slice(0, actualPosition) + 
                       linkHtml + 
                       enhancedContent.slice(actualPosition + anchorText.length)

      // Track the embedded link
      embeddedLinks.push({
        originalText,
        embeddedText: linkHtml,
        authorityLink: link,
        position: actualPosition,
        section: embedding.section,
        contextRelevance: embedding.confidence
      })

      // Update position offset for subsequent embeddings
      positionOffset += linkHtml.length - anchorText.length
    }

    // Calculate metrics
    const linkDensity = this.calculateLinkDensity(enhancedContent)
    const authorityScore = this.calculateContentAuthorityScore(embeddedLinks)

    return {
      enhancedContent,
      embeddedLinks,
      linkDensity,
      authorityScore,
      warnings: []
    }
  }

  /**
   * Validate and optimize the embedding result
   */
  private validateAndOptimizeResult(
    result: EmbeddingResult,
    settings: EmbeddingSettings
  ): EmbeddingResult {
    const warnings: string[] = []

    // Check link density
    if (result.linkDensity > settings.linkDensityLimit) {
      warnings.push(`Link density (${(result.linkDensity * 100).toFixed(1)}%) exceeds recommended limit (${(settings.linkDensityLimit * 100).toFixed(1)}%)`)
    }

    // Check for too many links in close proximity
    const proximityWarnings = this.checkLinkProximity(result.embeddedLinks, settings)
    warnings.push(...proximityWarnings)

    // Validate link quality
    const qualityWarnings = this.validateLinkQuality(result.embeddedLinks)
    warnings.push(...qualityWarnings)

    return {
      ...result,
      warnings
    }
  }

  // Helper methods
  private validateEmbeddingInputs(context: EmbeddingContext, authorityLinks: AuthorityLink[]): void {
    if (!context.content || context.content.length < 100) {
      throw new Error('Content must be at least 100 characters long')
    }

    if (!context.primaryKeyword) {
      throw new Error('Primary keyword is required')
    }

    if (authorityLinks.length === 0) {
      throw new Error('At least one authority link is required')
    }

    // Validate no demo content
    const demoPatterns = [
      /lorem ipsum/i, /placeholder/i, /example/i, /demo/i, /test content/i
    ]

    if (demoPatterns.some(pattern => pattern.test(context.content))) {
      throw new Error('Demo or placeholder content detected. Please provide real content.')
    }
  }

  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length
  }

  private categorizeSection(headerText: string): 'header' | 'paragraph' | 'list' | 'conclusion' {
    const conclusionKeywords = ['conclusion', 'summary', 'final', 'wrap', 'closing']
    if (conclusionKeywords.some(keyword => headerText.toLowerCase().includes(keyword))) {
      return 'conclusion'
    }
    return 'header'
  }

  private calculateSectionPriority(headerText: string, headerLevel: number): number {
    let priority = 6 - headerLevel // H1=5, H2=4, etc.
    
    // Boost priority for key sections
    const highPriorityKeywords = ['introduction', 'overview', 'main', 'key', 'important']
    if (highPriorityKeywords.some(keyword => headerText.toLowerCase().includes(keyword))) {
      priority += 2
    }
    
    return Math.max(1, Math.min(5, priority))
  }

  private parseSentences(content: string, sections: any[]) {
    const sentences: Array<{
      text: string
      startIndex: number
      endIndex: number
      section: string
      complexity: number
    }> = []

    // Simple sentence splitting (could be enhanced with NLP)
    const sentenceRegex = /[.!?]+\s+/g
    let lastIndex = 0
    let match

    while ((match = sentenceRegex.exec(content)) !== null) {
      const sentenceEnd = match.index + match[0].length
      const sentenceText = content.slice(lastIndex, sentenceEnd).trim()
      
      if (sentenceText.length > 10) { // Minimum sentence length
        const currentSection = sections.find(section => 
          lastIndex >= section.startIndex && lastIndex < section.endIndex
        )

        sentences.push({
          text: sentenceText,
          startIndex: lastIndex,
          endIndex: sentenceEnd,
          section: currentSection ? currentSection.title : 'Unknown',
          complexity: this.calculateSentenceComplexity(sentenceText)
        })
      }

      lastIndex = sentenceEnd
    }

    // Add final sentence if exists
    if (lastIndex < content.length) {
      const finalSentence = content.slice(lastIndex).trim()
      if (finalSentence.length > 10) {
        const currentSection = sections.find(section => 
          lastIndex >= section.startIndex && lastIndex < section.endIndex
        )

        sentences.push({
          text: finalSentence,
          startIndex: lastIndex,
          endIndex: content.length,
          section: currentSection ? currentSection.title : 'Unknown',
          complexity: this.calculateSentenceComplexity(finalSentence)
        })
      }
    }

    return sentences
  }

  private calculateContextRelevance(link: AuthorityLink, context: EmbeddingContext): number {
    let relevance = 0

    // Check keyword overlap
    const linkText = (link.title + ' ' + link.description).toLowerCase()
    const contextText = (context.primaryKeyword + ' ' + context.secondaryKeywords.join(' ')).toLowerCase()
    
    const linkWords = new Set(linkText.split(/\s+/))
    const contextWords = new Set(contextText.split(/\s+/))
    
    const overlap = [...linkWords].filter(word => contextWords.has(word)).length
    const total = Math.max(linkWords.size, contextWords.size)
    
    relevance = overlap / total

    return Math.min(1, relevance)
  }

  private extractKeyTerms(text: string): string[] {
    // Simple key term extraction (could be enhanced with NLP)
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3)
      .filter(word => !this.isStopWord(word))

    // Remove duplicates and return most relevant terms
    return [...new Set(words)].slice(0, 5)
  }

  private isStopWord(word: string): boolean {
    const stopWords = new Set([
      'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above',
      'below', 'between', 'among', 'this', 'that', 'these', 'those', 'is', 'are', 'was',
      'were', 'been', 'be', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would',
      'could', 'should', 'may', 'might', 'must', 'can', 'shall'
    ])
    return stopWords.has(word.toLowerCase())
  }

  private optimizeAnchorText(originalText: string, settings: EmbeddingSettings): string {
    const words = originalText.trim().split(/\s+/)
    const { min, max } = settings.preferredAnchorLength

    if (words.length < min) {
      return originalText // Too short, use as is
    }

    if (words.length > max) {
      // Truncate to preferred length, keeping most important words
      return words.slice(0, max).join(' ')
    }

    return originalText // Perfect length
  }

  private calculateLinkDensity(content: string): number {
    const linkMatches = content.match(/<a[^>]*>.*?<\/a>/g) || []
    const totalLinks = linkMatches.length
    const totalWords = this.countWords(content.replace(/<[^>]*>/g, ''))
    
    return totalWords > 0 ? totalLinks / totalWords : 0
  }

  private calculateContentAuthorityScore(embeddedLinks: EmbeddedLink[]): number {
    if (embeddedLinks.length === 0) return 0

    const averageAuthority = embeddedLinks.reduce((sum, link) => 
      sum + link.authorityLink.authorityScore, 0
    ) / embeddedLinks.length

    const averageRelevance = embeddedLinks.reduce((sum, link) => 
      sum + link.contextRelevance, 0
    ) / embeddedLinks.length

    return (averageAuthority + averageRelevance * 100) / 2
  }

  private checkLinkProximity(embeddedLinks: EmbeddedLink[], settings: EmbeddingSettings): string[] {
    const warnings: string[] = []
    const sortedLinks = embeddedLinks.sort((a, b) => a.position - b.position)

    for (let i = 1; i < sortedLinks.length; i++) {
      const currentLink = sortedLinks[i]
      const previousLink = sortedLinks[i - 1]
      const distance = currentLink.position - (previousLink.position + previousLink.embeddedText.length)

      if (distance < settings.minWordsBetweenLinks * 6) { // Approximate character count
        warnings.push(`Links "${previousLink.authorityLink.title}" and "${currentLink.authorityLink.title}" are too close together`)
      }
    }

    return warnings
  }

  private validateLinkQuality(embeddedLinks: EmbeddedLink[]): string[] {
    const warnings: string[] = []

    for (const link of embeddedLinks) {
      if (link.authorityLink.authorityScore < 70) {
        warnings.push(`Low authority score (${link.authorityLink.authorityScore}) for link: ${link.authorityLink.title}`)
      }

      if (link.contextRelevance < 0.3) {
        warnings.push(`Low context relevance for link: ${link.authorityLink.title}`)
      }
    }

    return warnings
  }

  private calculateSentenceComplexity(sentence: string): number {
    const wordCount = this.countWords(sentence)
    const commaCount = (sentence.match(/,/g) || []).length
    const clauseCount = (sentence.match(/[;:]/g) || []).length
    
    return (wordCount / 10) + commaCount + (clauseCount * 2)
  }

  private escapeHtml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;')
  }
}