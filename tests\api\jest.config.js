/**
 * Jest Configuration for API Testing
 * 
 * Configuration for API integration tests using Jest and Supertest
 * with proper setup and teardown for database and external services.
 */

const nextJest = require('next/jest');

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: './',
});

// Add any custom config to be passed to Jest
const customJestConfig = {
  // Test environment setup
  testEnvironment: 'node',
  
  // Test directories
  testMatch: [
    '<rootDir>/tests/api/**/*.test.js',
    '<rootDir>/tests/api/**/*.spec.js'
  ],
  
  // Setup files
  setupFilesAfterEnv: ['<rootDir>/tests/api/setup.js'],
  
  // Global setup and teardown
  globalSetup: '<rootDir>/tests/api/global-setup.js',
  globalTeardown: '<rootDir>/tests/api/global-teardown.js',
  
  // Test timeout
  testTimeout: 30000,
  
  // Coverage configuration
  collectCoverage: true,
  coverageDirectory: '<rootDir>/coverage/api',
  collectCoverageFrom: [
    'pages/api/**/*.js',
    'lib/**/*.js',
    'utils/**/*.js',
    '!**/node_modules/**',
    '!**/coverage/**',
    '!**/.next/**'
  ],
  coverageReporters: ['text', 'lcov', 'html', 'json-summary'],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },
  
  // Module mapping
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1',
    '^lib/(.*)$': '<rootDir>/lib/$1',
    '^utils/(.*)$': '<rootDir>/utils/$1',
    '^components/(.*)$': '<rootDir>/components/$1'
  },
  
  // Transform ignore patterns
  transformIgnorePatterns: [
    'node_modules/(?!(.*\\.mjs$|@supabase|jose))'
  ],
  
  // Reporters
  reporters: [
    'default',
    ['jest-junit', {
      outputDirectory: '<rootDir>/test-results',
      outputName: 'api-results.xml'
    }],
    ['jest-html-reporters', {
      publicPath: '<rootDir>/test-results',
      filename: 'api-report.html',
      expand: true
    }]
  ],
  
  // Verbose output
  verbose: true,
  
  // Detect open handles
  detectOpenHandles: true,
  
  // Force exit
  forceExit: true,
  
  // Clear mocks between tests
  clearMocks: true,
  
  // Restore mocks after each test
  restoreMocks: true,
  
  // Test environment variables
  testEnvironment: 'node',
  testEnvironmentOptions: {
    NODE_ENV: 'test'
  }
};

// Export Jest configuration
module.exports = createJestConfig(customJestConfig);