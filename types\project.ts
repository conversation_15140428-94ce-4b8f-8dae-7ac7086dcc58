/**
 * Project Management Type Definitions
 * SEO SAAS - Enterprise-grade project management with real data validation
 */

export type ProjectStatus = 'draft' | 'active' | 'paused' | 'completed' | 'archived';

export type ContentType = 'blog_post' | 'product_description' | 'landing_page' | 'meta_description' | 'social_media' | 'email_marketing';

export type IndustryType = 
  | 'technology' | 'healthcare' | 'finance' | 'education' | 'ecommerce' 
  | 'real_estate' | 'automotive' | 'food_beverage' | 'fashion' | 'travel'
  | 'fitness' | 'marketing' | 'consulting' | 'manufacturing' | 'retail'
  | 'construction' | 'legal' | 'entertainment' | 'sports' | 'nonprofit'
  | 'other';

export interface ProjectKeyword {
  id: string;
  keyword: string;
  searchVolume?: number;
  difficulty?: number;
  cpc?: number;
  intent?: 'informational' | 'commercial' | 'navigational' | 'transactional';
  priority: 'high' | 'medium' | 'low';
  currentRanking?: number;
  targetRanking?: number;
  createdAt: string;
}

export interface CompetitorAnalysis {
  id: string;
  url: string;
  domain: string;
  title?: string;
  metaDescription?: string;
  wordCount?: number;
  domainAuthority?: number;
  backlinks?: number;
  contentScore?: number;
  lastAnalyzed: string;
  isValid: boolean; // Tracks if URL is genuine (not demo/mock)
  analysisData?: {
    headingStructure: Record<string, number>;
    keywordDensity: Record<string, number>;
    internalLinks: number;
    externalLinks: number;
    technicalScore: number;
    contentDepth: number;
  };
}

export interface ProjectSettings {
  defaultContentType: ContentType;
  defaultWordCount: number;
  defaultTone: 'professional' | 'casual' | 'technical' | 'friendly' | 'persuasive';
  includeInternalLinks: boolean;
  includeExternalLinks: boolean;
  includeFAQs: boolean;
  includeSchemaMarkup: boolean;
  targetAudience?: string;
  brandVoice?: string;
  contentGuidelines?: string[];
}

export interface ProjectMetrics {
  totalContent: number;
  totalWords: number;
  averageSeoScore: number;
  averageWordCount: number;
  keywordsTracked: number;
  competitorsAnalyzed: number;
  contentByType: Record<ContentType, number>;
  performanceData?: {
    avgRanking: number;
    rankingChanges: number;
    trafficIncrease: number;
    conversionRate: number;
  };
}

export interface Project {
  id: string;
  userId: string;
  name: string;
  description?: string;
  industry: IndustryType;
  website?: string;
  targetLocation?: string;
  targetLanguage: string;
  status: ProjectStatus;
  
  // Keywords and competitors
  keywords: ProjectKeyword[];
  competitors: CompetitorAnalysis[];
  
  // Configuration
  settings: ProjectSettings;
  
  // Analytics and performance
  metrics: ProjectMetrics;
  
  // Metadata
  createdAt: string;
  updatedAt: string;
  lastAnalyzed?: string;
  
  // Real data validation
  isValidated: boolean; // Ensures no demo/mock data
  validationErrors?: string[];
}

export interface CreateProjectData {
  name: string;
  description?: string;
  industry: IndustryType;
  website?: string;
  targetLocation?: string;
  targetLanguage?: string;
  keywords: string[];
  competitorUrls: string[];
  settings?: Partial<ProjectSettings>;
}

export interface UpdateProjectData {
  name?: string;
  description?: string;
  industry?: IndustryType;
  website?: string;
  targetLocation?: string;
  status?: ProjectStatus;
  settings?: Partial<ProjectSettings>;
}

export interface ProjectFilters {
  status?: ProjectStatus[];
  industry?: IndustryType[];
  search?: string;
  sortBy?: 'name' | 'createdAt' | 'updatedAt' | 'status' | 'metrics.totalContent';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface ProjectStats {
  totalProjects: number;
  activeProjects: number;
  totalContent: number;
  totalKeywords: number;
  averageSeoScore: number;
  projectsByStatus: Record<ProjectStatus, number>;
  projectsByIndustry: Record<IndustryType, number>;
  recentActivity: Array<{
    projectId: string;
    projectName: string;
    action: string;
    timestamp: string;
  }>;
}

// Validation types for real data enforcement
export interface DataValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

export interface KeywordValidation extends DataValidationResult {
  keyword: string;
  searchVolume?: number;
  competition?: string;
  relatedKeywords?: string[];
}

export interface CompetitorValidation extends DataValidationResult {
  url: string;
  domain: string;
  isAccessible: boolean;
  contentType?: string;
  hasContent: boolean;
  isDemoSite: boolean; // Critical: detects demo/mock websites
}

export interface ProjectValidation extends DataValidationResult {
  projectData: CreateProjectData;
  keywordValidations: KeywordValidation[];
  competitorValidations: CompetitorValidation[];
  websiteValidation?: CompetitorValidation;
}