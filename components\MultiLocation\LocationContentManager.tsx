/**
 * Multi-Location Content Manager
 * Manage location-specific content strategies with visual mapping
 */

'use client'

import React, { useState, useEffect } from 'react'
import { ContentItem } from '@/lib/api/types'
import { useNotifications } from '@/components/Notifications'
import Card from '@/components/UI/Card'
import Button from '@/components/UI/Button'
import Input from '@/components/UI/Input'
import Badge from '@/components/UI/Badge'
import LoadingSpinner from '@/components/UI/LoadingSpinner'
import {
  MapIcon,
  GlobeAltIcon,
  BuildingOfficeIcon,
  MapPinIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  DocumentTextIcon,
  FlagIcon,
  ClockIcon,
  UsersIcon,
  TrendingUpIcon,
  LanguageIcon,
  CurrencyDollarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

interface LocationContentManagerProps {
  onLocationSelect?: (location: LocationData) => void
}

interface LocationData {
  id: string
  name: string
  country: string
  region: string
  city: string
  coordinates: {
    lat: number
    lng: number
  }
  market: {
    language: string
    currency: string
    timezone: string
    population: number
  }
  seo: {
    primaryKeywords: string[]
    localKeywords: string[]
    competitorDensity: 'low' | 'medium' | 'high'
    searchVolume: number
  }
  content: {
    strategy: string
    contentTypes: string[]
    publishedCount: number
    draftCount: number
    averageSeoScore: number
  }
  performance: {
    monthlyTraffic: number
    conversionRate: number
    ranking: number
    growth: number
  }
  status: 'active' | 'planning' | 'paused' | 'completed'
  lastUpdated: string
}

interface ContentTemplate {
  id: string
  name: string
  type: string
  description: string
  localizations: {
    country: string
    language: string
    adaptations: string[]
  }[]
}

export default function LocationContentManager({
  onLocationSelect
}: LocationContentManagerProps) {
  const [locations, setLocations] = useState<LocationData[]>([])
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null)
  const [activeView, setActiveView] = useState<'overview' | 'map' | 'strategy' | 'performance'>('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [isLoading, setIsLoading] = useState(false)
  const [showAddLocation, setShowAddLocation] = useState(false)
  const [templates, setTemplates] = useState<ContentTemplate[]>([])
  
  const { success: notifySuccess, error: notifyError } = useNotifications()

  // Mock data - in real app this would come from API
  useEffect(() => {
    setLocations([
      {
        id: 'loc1',
        name: 'New York Metro',
        country: 'United States',
        region: 'North America',
        city: 'New York',
        coordinates: { lat: 40.7128, lng: -74.0060 },
        market: {
          language: 'English',
          currency: 'USD',
          timezone: 'EST',
          population: 8400000
        },
        seo: {
          primaryKeywords: ['digital marketing nyc', 'seo services new york', 'content marketing manhattan'],
          localKeywords: ['nyc seo', 'manhattan marketing', 'brooklyn digital agency'],
          competitorDensity: 'high',
          searchVolume: 125000
        },
        content: {
          strategy: 'Local Authority Building',
          contentTypes: ['Local Guides', 'Industry News', 'Case Studies'],
          publishedCount: 45,
          draftCount: 12,
          averageSeoScore: 87
        },
        performance: {
          monthlyTraffic: 45000,
          conversionRate: 3.2,
          ranking: 3,
          growth: 15.5
        },
        status: 'active',
        lastUpdated: '2024-01-15T10:30:00Z'
      },
      {
        id: 'loc2',
        name: 'London Central',
        country: 'United Kingdom',
        region: 'Europe',
        city: 'London',
        coordinates: { lat: 51.5074, lng: -0.1278 },
        market: {
          language: 'English',
          currency: 'GBP',
          timezone: 'GMT',
          population: 9000000
        },
        seo: {
          primaryKeywords: ['digital marketing london', 'seo services uk', 'content strategy london'],
          localKeywords: ['london seo', 'uk digital marketing', 'british content agency'],
          competitorDensity: 'medium',
          searchVolume: 89000
        },
        content: {
          strategy: 'International Expansion',
          contentTypes: ['Market Analysis', 'Cultural Guides', 'Compliance Content'],
          publishedCount: 32,
          draftCount: 8,
          averageSeoScore: 82
        },
        performance: {
          monthlyTraffic: 32000,
          conversionRate: 2.8,
          ranking: 5,
          growth: 22.1
        },
        status: 'active',
        lastUpdated: '2024-01-14T16:45:00Z'
      },
      {
        id: 'loc3',
        name: 'Tokyo Business District',
        country: 'Japan',
        region: 'Asia',
        city: 'Tokyo',
        coordinates: { lat: 35.6762, lng: 139.6503 },
        market: {
          language: 'Japanese',
          currency: 'JPY',
          timezone: 'JST',
          population: 14000000
        },
        seo: {
          primaryKeywords: ['デジタルマーケティング東京', 'SEOサービス日本', 'コンテンツ戦略'],
          localKeywords: ['東京SEO', '日本デジタル', 'マーケティング会社'],
          competitorDensity: 'medium',
          searchVolume: 67000
        },
        content: {
          strategy: 'Cultural Adaptation',
          contentTypes: ['Cultural Content', 'Local Partnerships', 'Language Optimization'],
          publishedCount: 18,
          draftCount: 15,
          averageSeoScore: 76
        },
        performance: {
          monthlyTraffic: 18500,
          conversionRate: 4.1,
          ranking: 8,
          growth: 31.2
        },
        status: 'planning',
        lastUpdated: '2024-01-13T09:20:00Z'
      },
      {
        id: 'loc4',
        name: 'Sydney Harbor',
        country: 'Australia',
        region: 'Oceania',
        city: 'Sydney',
        coordinates: { lat: -33.8688, lng: 151.2093 },
        market: {
          language: 'English',
          currency: 'AUD',
          timezone: 'AEST',
          population: 5300000
        },
        seo: {
          primaryKeywords: ['digital marketing sydney', 'seo australia', 'content marketing australia'],
          localKeywords: ['sydney seo', 'aussie marketing', 'australian digital'],
          competitorDensity: 'low',
          searchVolume: 34000
        },
        content: {
          strategy: 'Market Entry',
          contentTypes: ['Market Research', 'Local Insights', 'Competitive Analysis'],
          publishedCount: 8,
          draftCount: 22,
          averageSeoScore: 71
        },
        performance: {
          monthlyTraffic: 8200,
          conversionRate: 2.1,
          ranking: 12,
          growth: 45.8
        },
        status: 'planning',
        lastUpdated: '2024-01-12T14:15:00Z'
      }
    ])

    setTemplates([
      {
        id: 'temp1',
        name: 'Local Business Guide',
        type: 'Guide',
        description: 'Comprehensive guide for local business optimization',
        localizations: [
          {
            country: 'US',
            language: 'English',
            adaptations: ['Local regulations', 'Cultural preferences', 'Market trends']
          },
          {
            country: 'UK',
            language: 'English',
            adaptations: ['GDPR compliance', 'British terminology', 'Local partnerships']
          }
        ]
      },
      {
        id: 'temp2',
        name: 'SEO Localization Kit',
        type: 'Technical',
        description: 'Complete SEO optimization for local markets',
        localizations: [
          {
            country: 'JP',
            language: 'Japanese',
            adaptations: ['Character encoding', 'Search behavior', 'Mobile-first approach']
          },
          {
            country: 'AU',
            language: 'English',
            adaptations: ['Local search patterns', 'Regional preferences', 'Seasonal content']
          }
        ]
      }
    ])
  }, [])

  const filteredLocations = locations.filter(location => {
    const matchesSearch = location.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         location.country.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         location.city.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = filterStatus === 'all' || location.status === filterStatus
    return matchesSearch && matchesStatus
  })

  const handleLocationSelect = (location: LocationData) => {
    setSelectedLocation(location)
    if (onLocationSelect) {
      onLocationSelect(location)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success'
      case 'planning': return 'warning'
      case 'paused': return 'secondary'
      case 'completed': return 'info'
      default: return 'secondary'
    }
  }

  const getCompetitorDensityColor = (density: string) => {
    switch (density) {
      case 'high': return 'text-red-600 dark:text-red-400'
      case 'medium': return 'text-yellow-600 dark:text-yellow-400'
      case 'low': return 'text-green-600 dark:text-green-400'
      default: return 'text-gray-600 dark:text-gray-400'
    }
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return num.toString()
  }

  const LocationCard = ({ location }: { location: LocationData }) => (
    <Card 
      className={`p-4 cursor-pointer transition-all hover:shadow-md ${
        selectedLocation?.id === location.id ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : ''
      }`}
      onClick={() => handleLocationSelect(location)}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
            <MapPinIcon className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="font-medium text-gray-900 dark:text-gray-100">
              {location.name}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {location.city}, {location.country}
            </p>
          </div>
        </div>
        <Badge variant={getStatusColor(location.status)}>
          {location.status}
        </Badge>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-3">
        <div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Monthly Traffic</div>
          <div className="font-medium text-gray-900 dark:text-gray-100">
            {formatNumber(location.performance.monthlyTraffic)}
          </div>
        </div>
        <div>
          <div className="text-sm text-gray-500 dark:text-gray-400">SEO Score</div>
          <div className="font-medium text-gray-900 dark:text-gray-100">
            {location.content.averageSeoScore}%
          </div>
        </div>
        <div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Content</div>
          <div className="font-medium text-gray-900 dark:text-gray-100">
            {location.content.publishedCount} published
          </div>
        </div>
        <div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Growth</div>
          <div className={`font-medium flex items-center ${
            location.performance.growth > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
          }`}>
            <TrendingUpIcon className="h-3 w-3 mr-1" />
            {location.performance.growth}%
          </div>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <FlagIcon className="h-4 w-4 text-gray-400" />
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {location.market.language}
          </span>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {location.market.currency}
          </span>
        </div>
        <div className={`text-xs font-medium ${getCompetitorDensityColor(location.seo.competitorDensity)}`}>
          {location.seo.competitorDensity} competition
        </div>
      </div>
    </Card>
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Multi-Location Content Manager
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage content strategies across global markets with location-specific optimization
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            onClick={() => setShowAddLocation(true)}
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Location
          </Button>
          <Button>
            <MapIcon className="h-4 w-4 mr-2" />
            View Map
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center">
            <GlobeAltIcon className="h-8 w-8 text-blue-600 dark:text-blue-400 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Locations</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{locations.length}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center">
            <CheckCircleIcon className="h-8 w-8 text-green-600 dark:text-green-400 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Active Markets</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {locations.filter(l => l.status === 'active').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center">
            <UsersIcon className="h-8 w-8 text-purple-600 dark:text-purple-400 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Traffic</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {formatNumber(locations.reduce((acc, l) => acc + l.performance.monthlyTraffic, 0))}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center">
            <DocumentTextIcon className="h-8 w-8 text-orange-600 dark:text-orange-400 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Published Content</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {locations.reduce((acc, l) => acc + l.content.publishedCount, 0)}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card className="p-4">
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              type="text"
              placeholder="Search locations, countries, or cities..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="planning">Planning</option>
            <option value="paused">Paused</option>
            <option value="completed">Completed</option>
          </select>
          <Badge variant="secondary">
            {filteredLocations.length} locations
          </Badge>
        </div>
      </Card>

      {/* View Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <div className="flex space-x-8">
          <button
            onClick={() => setActiveView('overview')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeView === 'overview'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <BuildingOfficeIcon className="h-4 w-4 inline mr-2" />
            Overview
          </button>
          <button
            onClick={() => setActiveView('map')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeView === 'map'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <MapIcon className="h-4 w-4 inline mr-2" />
            Map View
          </button>
          <button
            onClick={() => setActiveView('strategy')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeView === 'strategy'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <ChartBarIcon className="h-4 w-4 inline mr-2" />
            Strategy
          </button>
          <button
            onClick={() => setActiveView('performance')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeView === 'performance'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <TrendingUpIcon className="h-4 w-4 inline mr-2" />
            Performance
          </button>
        </div>
      </div>

      {/* Content based on active view */}
      {activeView === 'overview' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredLocations.map((location) => (
            <LocationCard key={location.id} location={location} />
          ))}
          
          {filteredLocations.length === 0 && (
            <div className="col-span-full">
              <Card className="p-8 text-center">
                <MapPinIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  No locations found
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {searchQuery ? 'Try adjusting your search terms' : 'Add your first location to get started'}
                </p>
                <Button onClick={() => setShowAddLocation(true)}>
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Add Location
                </Button>
              </Card>
            </div>
          )}
        </div>
      )}

      {activeView === 'map' && (
        <Card className="p-8 text-center">
          <MapIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-900 dark:text-gray-100 mb-2">
            Interactive Map View
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Visual representation of your global content strategy with location-based performance metrics
          </p>
          <div className="bg-gray-100 dark:bg-gray-800 rounded-lg h-96 flex items-center justify-center">
            <div className="text-center">
              <GlobeAltIcon className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500 dark:text-gray-400">Interactive map will be rendered here</p>
              <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                Integration with mapping services (Google Maps, Mapbox, etc.)
              </p>
            </div>
          </div>
        </Card>
      )}

      {activeView === 'strategy' && (
        <div className="space-y-6">
          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Content Strategy Templates
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {templates.map((template) => (
                <Card key={template.id} className="p-4 border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-gray-900 dark:text-gray-100">
                      {template.name}
                    </h4>
                    <Badge variant="outline">{template.type}</Badge>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    {template.description}
                  </p>
                  <div className="space-y-2">
                    <div className="text-xs font-medium text-gray-700 dark:text-gray-300">
                      Available Localizations:
                    </div>
                    {template.localizations.map((loc, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <FlagIcon className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-600 dark:text-gray-400">
                          {loc.country} ({loc.language})
                        </span>
                      </div>
                    ))}
                  </div>
                  <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                    <Button size="sm" variant="outline" className="w-full">
                      Apply Template
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          </Card>

          {selectedLocation && (
            <Card className="p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Strategy Details: {selectedLocation.name}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3">SEO Keywords</h4>
                  <div className="space-y-2">
                    <div>
                      <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">Primary Keywords</div>
                      <div className="flex flex-wrap gap-1">
                        {selectedLocation.seo.primaryKeywords.map((keyword, index) => (
                          <Badge key={index} variant="outline" size="sm">
                            {keyword}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">Local Keywords</div>
                      <div className="flex flex-wrap gap-1">
                        {selectedLocation.seo.localKeywords.map((keyword, index) => (
                          <Badge key={index} variant="secondary" size="sm">
                            {keyword}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3">Content Strategy</h4>
                  <div className="space-y-3">
                    <div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Strategy Type</div>
                      <div className="font-medium text-gray-900 dark:text-gray-100">
                        {selectedLocation.content.strategy}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">Content Types</div>
                      <div className="flex flex-wrap gap-1">
                        {selectedLocation.content.contentTypes.map((type, index) => (
                          <Badge key={index} variant="info" size="sm">
                            {type}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          )}
        </div>
      )}

      {activeView === 'performance' && (
        <div className="space-y-4">
          {filteredLocations.map((location) => (
            <Card key={location.id} className="p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                    <MapPinIcon className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-gray-100">
                      {location.name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {location.city}, {location.country}
                    </p>
                  </div>
                </div>
                <Badge variant={getStatusColor(location.status)}>
                  {location.status}
                </Badge>
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-sm text-gray-500 dark:text-gray-400">Traffic</div>
                  <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                    {formatNumber(location.performance.monthlyTraffic)}
                  </div>
                  <div className={`text-xs flex items-center justify-center mt-1 ${
                    location.performance.growth > 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    <TrendingUpIcon className="h-3 w-3 mr-1" />
                    {location.performance.growth}%
                  </div>
                </div>
                
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-sm text-gray-500 dark:text-gray-400">Conversion</div>
                  <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                    {location.performance.conversionRate}%
                  </div>
                </div>
                
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-sm text-gray-500 dark:text-gray-400">Ranking</div>
                  <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                    #{location.performance.ranking}
                  </div>
                </div>
                
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-sm text-gray-500 dark:text-gray-400">SEO Score</div>
                  <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                    {location.content.averageSeoScore}%
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}