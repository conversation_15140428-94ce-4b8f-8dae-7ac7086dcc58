/**
 * Real-time Collaboration Component
 * Live collaboration features including cursors, selections, and presence indicators
 */

'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { useRealtimeCollaboration, useTeamPresence } from '@/lib/realtime/realtime-hooks'
import { useRealtime } from './RealtimeProvider'
import Card from '@/components/UI/Card'
import Badge from '@/components/UI/Badge'
import {
  UserIcon,
  EyeIcon,
  CursorArrowRaysIcon,
  ChatBubbleLeftIcon,
  HandRaisedIcon,
  PencilIcon,
  DocumentTextIcon,
  ClockIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline'

interface CollaboratorCursor {
  userId: string
  name: string
  color: string
  position: { x: number; y: number }
  timestamp: number
  componentId?: string
}

interface CollaboratorSelection {
  userId: string
  name: string
  color: string
  selection: {
    start: number
    end: number
    text?: string
  }
  timestamp: number
}

interface CollaborationCursorProps {
  cursor: CollaboratorCursor
  containerRef: React.RefObject<HTMLElement>
}

const CollaborationCursor = ({ cursor, containerRef }: CollaborationCursorProps) => {
  const [position, setPosition] = useState({ x: cursor.position.x, y: cursor.position.y })
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    setPosition({ x: cursor.position.x, y: cursor.position.y })
    setIsVisible(true)

    // Hide cursor after 3 seconds of inactivity
    const timer = setTimeout(() => {
      setIsVisible(false)
    }, 3000)

    return () => clearTimeout(timer)
  }, [cursor.position])

  if (!isVisible) return null

  return (
    <div
      className="fixed pointer-events-none z-50 transition-all duration-200 ease-out"
      style={{
        left: position.x,
        top: position.y,
        transform: 'translate(-2px, -2px)'
      }}
    >
      {/* Cursor */}
      <div className="relative">
        <svg width="20" height="20" viewBox="0 0 20 20" className="drop-shadow-md">
          <path
            d="M2 2l6 14 3-6 6-3L2 2z"
            fill={cursor.color}
            stroke="white"
            strokeWidth="1"
          />
        </svg>
        
        {/* Name label */}
        <div
          className="absolute top-5 left-2 px-2 py-1 text-xs font-medium text-white rounded shadow-md whitespace-nowrap"
          style={{ backgroundColor: cursor.color }}
        >
          {cursor.name}
        </div>
      </div>
    </div>
  )
}

interface CollaborationSelectionProps {
  selection: CollaboratorSelection
  containerRef: React.RefObject<HTMLElement>
}

const CollaborationSelection = ({ selection, containerRef }: CollaborationSelectionProps) => {
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    setIsVisible(true)

    // Hide selection after 5 seconds
    const timer = setTimeout(() => {
      setIsVisible(false)
    }, 5000)

    return () => clearTimeout(timer)
  }, [selection])

  if (!isVisible) return null

  return (
    <div
      className="absolute pointer-events-none z-40"
      style={{
        backgroundColor: `${selection.color}20`,
        border: `2px solid ${selection.color}`,
        borderRadius: '2px'
      }}
    >
      <div
        className="absolute -top-6 left-0 px-2 py-1 text-xs font-medium text-white rounded shadow-md whitespace-nowrap"
        style={{ backgroundColor: selection.color }}
      >
        {selection.name} selected
      </div>
    </div>
  )
}

interface PresenceAvatarProps {
  user: {
    id: string
    name: string
    avatar?: string
    status: 'online' | 'away' | 'busy'
    lastSeen?: number
  }
  size?: 'sm' | 'md' | 'lg'
  showStatus?: boolean
  showTooltip?: boolean
}

const PresenceAvatar = ({ 
  user, 
  size = 'md', 
  showStatus = true, 
  showTooltip = true 
}: PresenceAvatarProps) => {
  const sizeClasses = {
    sm: 'w-6 h-6 text-xs',
    md: 'w-8 h-8 text-sm',
    lg: 'w-10 h-10 text-base'
  }

  const statusColors = {
    online: 'bg-green-500',
    away: 'bg-yellow-500',
    busy: 'bg-red-500'
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  return (
    <div className="relative" title={showTooltip ? `${user.name} - ${user.status}` : undefined}>
      <div className={`
        ${sizeClasses[size]} 
        rounded-full bg-gray-300 dark:bg-gray-600 
        flex items-center justify-center 
        font-medium text-gray-700 dark:text-gray-300
        border-2 border-white dark:border-gray-800
        shadow-sm
      `}>
        {user.avatar ? (
          <img
            src={user.avatar}
            alt={user.name}
            className="w-full h-full rounded-full object-cover"
          />
        ) : (
          getInitials(user.name)
        )}
      </div>
      
      {showStatus && (
        <div className={`
          absolute -bottom-0.5 -right-0.5 
          w-3 h-3 rounded-full border-2 border-white dark:border-gray-800
          ${statusColors[user.status]}
        `} />
      )}
    </div>
  )
}

interface TypingIndicatorProps {
  typingUsers: Array<{
    userId: string
    name: string
    componentId: string
    timestamp: number
  }>
  currentComponentId?: string
}

const TypingIndicator = ({ typingUsers, currentComponentId }: TypingIndicatorProps) => {
  const relevantUsers = typingUsers.filter(user => 
    !currentComponentId || user.componentId === currentComponentId
  )

  if (relevantUsers.length === 0) return null

  const formatTypingMessage = () => {
    if (relevantUsers.length === 1) {
      return `${relevantUsers[0].name} is typing...`
    } else if (relevantUsers.length === 2) {
      return `${relevantUsers[0].name} and ${relevantUsers[1].name} are typing...`
    } else {
      return `${relevantUsers[0].name} and ${relevantUsers.length - 1} others are typing...`
    }
  }

  return (
    <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 py-2">
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" />
        <div 
          className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" 
          style={{ animationDelay: '0.1s' }}
        />
        <div 
          className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" 
          style={{ animationDelay: '0.2s' }}
        />
      </div>
      <span>{formatTypingMessage()}</span>
    </div>
  )
}

interface RealtimeCollaborationProps {
  documentId: string
  userId: string
  userName: string
  children: React.ReactNode
  enableCursors?: boolean
  enableSelections?: boolean
  enablePresence?: boolean
  enableTyping?: boolean
  cursorColor?: string
}

export default function RealtimeCollaboration({
  documentId,
  userId,
  userName,
  children,
  enableCursors = true,
  enableSelections = true,
  enablePresence = true,
  enableTyping = true,
  cursorColor = '#3B82F6'
}: RealtimeCollaborationProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [cursors, setCursors] = useState<Map<string, CollaboratorCursor>>(new Map())
  const [selections, setSelections] = useState<Map<string, CollaboratorSelection>>(new Map())
  const [isTyping, setIsTyping] = useState(false)
  const typingTimeoutRef = useRef<NodeJS.Timeout>()

  const {
    collaborators,
    selections: realtimeSelections,
    joinDocument,
    leaveDocument,
    updateSelection
  } = useRealtimeCollaboration(documentId)

  const {
    onlineUsers,
    typingUsers,
    userCursors,
    sendTypingIndicator,
    sendCursorPosition
  } = useTeamPresence()

  // Join document on mount
  useEffect(() => {
    joinDocument(userId, {
      name: userName,
      color: cursorColor
    })

    return () => {
      leaveDocument(userId)
    }
  }, [documentId, userId, userName, cursorColor, joinDocument, leaveDocument])

  // Handle mouse movement for cursor tracking
  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!enableCursors) return

    const rect = containerRef.current?.getBoundingClientRect()
    if (rect) {
      const x = event.clientX
      const y = event.clientY
      
      sendCursorPosition(userId, documentId, { x, y })
    }
  }, [enableCursors, userId, documentId, sendCursorPosition])

  // Handle typing indicators
  const handleTypingStart = useCallback(() => {
    if (!enableTyping || isTyping) return

    setIsTyping(true)
    sendTypingIndicator(userId, documentId, true)

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }

    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false)
      sendTypingIndicator(userId, documentId, false)
    }, 2000)
  }, [enableTyping, isTyping, userId, documentId, sendTypingIndicator])

  const handleTypingStop = useCallback(() => {
    if (!enableTyping) return

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }

    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false)
      sendTypingIndicator(userId, documentId, false)
    }, 500)
  }, [enableTyping, userId, documentId, sendTypingIndicator])

  // Handle text selection
  const handleSelectionChange = useCallback(() => {
    if (!enableSelections) return

    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      const text = selection.toString()
      
      if (text.length > 0) {
        updateSelection(userId, {
          start: range.startOffset,
          end: range.endOffset,
          text
        })
      }
    }
  }, [enableSelections, userId, updateSelection])

  // Set up event listeners
  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    if (enableCursors) {
      container.addEventListener('mousemove', handleMouseMove)
    }

    if (enableSelections) {
      document.addEventListener('selectionchange', handleSelectionChange)
    }

    if (enableTyping) {
      container.addEventListener('keydown', handleTypingStart)
      container.addEventListener('keyup', handleTypingStop)
    }

    return () => {
      if (enableCursors) {
        container.removeEventListener('mousemove', handleMouseMove)
      }
      if (enableSelections) {
        document.removeEventListener('selectionchange', handleSelectionChange)
      }
      if (enableTyping) {
        container.removeEventListener('keydown', handleTypingStart)
        container.removeEventListener('keyup', handleTypingStop)
      }
    }
  }, [
    enableCursors, 
    enableSelections, 
    enableTyping, 
    handleMouseMove, 
    handleSelectionChange, 
    handleTypingStart, 
    handleTypingStop
  ])

  // Update cursors from real-time data
  useEffect(() => {
    const updatedCursors = new Map<string, CollaboratorCursor>()
    
    userCursors
      .filter(cursor => cursor.userId !== userId && cursor.componentId === documentId)
      .forEach(cursor => {
        const collaborator = collaborators.find(c => c.userId === cursor.userId)
        if (collaborator) {
          updatedCursors.set(cursor.userId, {
            userId: cursor.userId,
            name: collaborator.userData.name,
            color: collaborator.userData.color || '#3B82F6',
            position: cursor.position,
            timestamp: cursor.timestamp,
            componentId: cursor.componentId
          })
        }
      })
    
    setCursors(updatedCursors)
  }, [userCursors, collaborators, userId, documentId])

  // Update selections from real-time data
  useEffect(() => {
    const updatedSelections = new Map<string, CollaboratorSelection>()
    
    realtimeSelections
      .filter(selection => selection.userId !== userId)
      .forEach(selection => {
        const collaborator = collaborators.find(c => c.userId === selection.userId)
        if (collaborator) {
          updatedSelections.set(selection.userId, {
            userId: selection.userId,
            name: collaborator.userData.name,
            color: collaborator.userData.color || '#3B82F6',
            selection: selection.selection,
            timestamp: selection.timestamp
          })
        }
      })
    
    setSelections(updatedSelections)
  }, [realtimeSelections, collaborators, userId])

  return (
    <div ref={containerRef} className="relative">
      {children}
      
      {/* Render collaboration cursors */}
      {enableCursors && Array.from(cursors.values()).map(cursor => (
        <CollaborationCursor
          key={cursor.userId}
          cursor={cursor}
          containerRef={containerRef}
        />
      ))}
      
      {/* Render collaboration selections */}
      {enableSelections && Array.from(selections.values()).map(selection => (
        <CollaborationSelection
          key={selection.userId}
          selection={selection}
          containerRef={containerRef}
        />
      ))}
      
      {/* Typing indicators */}
      {enableTyping && (
        <TypingIndicator
          typingUsers={typingUsers.filter(user => 
            user.userId !== userId && user.componentId === documentId
          )}
          currentComponentId={documentId}
        />
      )}
    </div>
  )
}

// Collaboration Status Bar Component
interface CollaborationStatusBarProps {
  documentId: string
  className?: string
}

export const CollaborationStatusBar = ({ documentId, className = '' }: CollaborationStatusBarProps) => {
  const { collaborators } = useRealtimeCollaboration(documentId)
  const { onlineUsers, typingUsers } = useTeamPresence()

  const activeCollaborators = collaborators.filter(c => 
    onlineUsers.some(u => u.id === c.userId)
  )

  const currentTypingUsers = typingUsers.filter(u => u.componentId === documentId)

  return (
    <div className={`flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 ${className}`}>
      <div className="flex items-center space-x-4">
        {/* Active collaborators */}
        <div className="flex items-center space-x-2">
          <UserGroupIcon className="h-4 w-4 text-gray-500" />
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {activeCollaborators.length} active
          </span>
          <div className="flex -space-x-2">
            {activeCollaborators.slice(0, 5).map(collaborator => {
              const user = onlineUsers.find(u => u.id === collaborator.userId)
              if (!user) return null
              
              return (
                <PresenceAvatar
                  key={collaborator.userId}
                  user={{
                    id: user.id,
                    name: user.name || collaborator.userData.name,
                    avatar: user.avatar,
                    status: user.status || 'online'
                  }}
                  size="sm"
                  showStatus={false}
                />
              )
            })}
            {activeCollaborators.length > 5 && (
              <div className="w-6 h-6 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center text-xs font-medium text-gray-700 dark:text-gray-300 border-2 border-white dark:border-gray-800">
                +{activeCollaborators.length - 5}
              </div>
            )}
          </div>
        </div>

        {/* Typing indicator */}
        {currentTypingUsers.length > 0 && (
          <div className="flex items-center space-x-2">
            <PencilIcon className="h-4 w-4 text-blue-500" />
            <TypingIndicator 
              typingUsers={currentTypingUsers}
              currentComponentId={documentId}
            />
          </div>
        )}
      </div>

      <div className="flex items-center space-x-2">
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 bg-green-500 rounded-full" />
          <span className="text-xs text-gray-500">Live</span>
        </div>
      </div>
    </div>
  )
}

// Collaboration Panel Component
interface CollaborationPanelProps {
  documentId: string
  isOpen: boolean
  onClose: () => void
}

export const CollaborationPanel = ({ documentId, isOpen, onClose }: CollaborationPanelProps) => {
  const { collaborators } = useRealtimeCollaboration(documentId)
  const { onlineUsers } = useTeamPresence()

  if (!isOpen) return null

  const activeCollaborators = collaborators.filter(c => 
    onlineUsers.some(u => u.id === c.userId)
  )

  return (
    <div className="fixed right-0 top-0 h-full w-80 bg-white dark:bg-gray-900 shadow-xl border-l border-gray-200 dark:border-gray-700 z-40">
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Collaboration
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              ×
            </button>
          </div>
        </div>

        {/* Active collaborators */}
        <div className="flex-1 overflow-y-auto p-4">
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">
                Active Collaborators ({activeCollaborators.length})
              </h4>
              <div className="space-y-2">
                {activeCollaborators.map(collaborator => {
                  const user = onlineUsers.find(u => u.id === collaborator.userId)
                  if (!user) return null

                  return (
                    <div key={collaborator.userId} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800">
                      <PresenceAvatar
                        user={{
                          id: user.id,
                          name: user.name || collaborator.userData.name,
                          avatar: user.avatar,
                          status: user.status || 'online'
                        }}
                        size="sm"
                      />
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {user.name || collaborator.userData.name}
                        </div>
                        <div className="text-xs text-gray-500">
                          Joined {new Date(collaborator.joinedAt).toLocaleTimeString()}
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Export additional components
export { PresenceAvatar, TypingIndicator, CollaborationStatusBar, CollaborationPanel }