/**
 * PageTransition Component
 * Smooth transitions between pages/routes
 */

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { AnimationType, ANIMATION_DURATION, ANIMATION_EASING } from '@/lib/animations/constants';
import { useAnimation } from '@/lib/animations/hooks';
import { cn } from '@/lib/utils';

interface PageTransitionProps {
  children: React.ReactNode;
  animation?: AnimationType;
  duration?: number;
  className?: string;
}

export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  animation = AnimationType.FADE_IN,
  duration = ANIMATION_DURATION.normal,
  className
}) => {
  const router = useRouter();
  const [isTransitioning, setIsTransitioning] = useState(false);
  const { animate } = useAnimation();
  const containerRef = React.useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleRouteChangeStart = () => {
      setIsTransitioning(true);
    };

    const handleRouteChangeComplete = () => {
      setIsTransitioning(false);
      
      // Animate in new content
      if (containerRef.current) {
        animate(containerRef.current, animation, {
          duration,
          easing: ANIMATION_EASING.easeOut
        });
      }
    };

    router.events.on('routeChangeStart', handleRouteChangeStart);
    router.events.on('routeChangeComplete', handleRouteChangeComplete);
    router.events.on('routeChangeError', handleRouteChangeComplete);

    return () => {
      router.events.off('routeChangeStart', handleRouteChangeStart);
      router.events.off('routeChangeComplete', handleRouteChangeComplete);
      router.events.off('routeChangeError', handleRouteChangeComplete);
    };
  }, [router, animate, animation, duration]);

  return (
    <div
      ref={containerRef}
      className={cn(
        'transition-opacity duration-200',
        isTransitioning && 'opacity-50',
        className
      )}
    >
      {children}
    </div>
  );
};

// Layout wrapper for page transitions
interface TransitionLayoutProps {
  children: React.ReactNode;
}

export const TransitionLayout: React.FC<TransitionLayoutProps> = ({ children }) => {
  return (
    <PageTransition
      animation={AnimationType.FADE_IN}
      duration={ANIMATION_DURATION.normal}
    >
      {children}
    </PageTransition>
  );
};