/**
 * Authority Links Manager
 * Comprehensive backlink management, link building campaigns, and authority tracking
 */

'use client'

import React, { useState, useEffect } from 'react'
import { useNotifications } from '@/components/Notifications'
import Card from '@/components/UI/Card'
import Button from '@/components/UI/Button'
import Input from '@/components/UI/Input'
import Badge from '@/components/UI/Badge'
import LoadingSpinner from '@/components/UI/LoadingSpinner'
import {
  LinkIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  ChartBarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  EyeIcon,
  ArrowTopRightOnSquareIcon,
  FunnelIcon,
  ArrowPathIcon,
  EnvelopeIcon,
  CalendarIcon,
  DocumentTextIcon,
  UserIcon,
  GlobeAltIcon,
  StarIcon,
  FireIcon,
  ShieldCheckIcon,
  BellIcon,
  CursorArrowRaysIcon,
  BuildingOfficeIcon,
  TagIcon,
  BanknotesIcon,
  PhoneIcon,
  ChatBubbleLeftIcon,
  PencilIcon,
  TrashIcon,
  ShareIcon,
  ArrowDownTrayIcon,
  Bars3Icon,
  TableCellsIcon,
  ListBulletIcon
} from '@heroicons/react/24/outline'

interface AuthorityLinksManagerProps {
  onLinkSelect?: (link: BacklinkData) => void
}

interface BacklinkData {
  id: string
  url: string
  targetUrl: string
  anchorText: string
  domain: string
  domainRating: number
  domainAuthority: number
  pageAuthority: number
  trustFlow: number
  citationFlow: number
  referringPage: {
    title: string
    url: string
    traffic: number
    keywords: number
  }
  linkType: 'dofollow' | 'nofollow' | 'sponsored' | 'ugc'
  linkPosition: 'content' | 'sidebar' | 'footer' | 'header' | 'comment'
  firstSeen: string
  lastSeen: string
  status: 'active' | 'lost' | 'broken' | 'pending' | 'disavowed'
  category: string
  notes?: string
  isCompetitor?: boolean
  campaign?: {
    id: string
    name: string
    type: 'outreach' | 'broken_link' | 'guest_post' | 'resource_page' | 'skyscraper'
  }
  metrics: {
    clicks: number
    impressions: number
    ctr: number
    position: number
    trafficValue: number
  }
  contact?: {
    name: string
    email: string
    title: string
    socialProfiles: string[]
  }
}

interface LinkCampaign {
  id: string
  name: string
  type: 'outreach' | 'broken_link' | 'guest_post' | 'resource_page' | 'skyscraper'
  status: 'active' | 'paused' | 'completed' | 'planning'
  targetKeywords: string[]
  targetDomains: string[]
  progress: {
    prospected: number
    contacted: number
    responded: number
    linked: number
    total: number
  }
  budget: number
  spent: number
  startDate: string
  endDate?: string
  manager: string
  notes?: string
  templates: {
    subject: string
    body: string
    followUp: string[]
  }
  metrics: {
    responseRate: number
    conversionRate: number
    avgDomainRating: number
    totalValue: number
  }
}

interface LinkProspect {
  id: string
  domain: string
  url: string
  title: string
  domainRating: number
  traffic: number
  category: string
  contactInfo: {
    name?: string
    email?: string
    socialProfiles: string[]
  }
  outreachStatus: 'not_contacted' | 'contacted' | 'followed_up' | 'responded' | 'accepted' | 'rejected'
  lastContact?: string
  notes?: string
  priority: 'high' | 'medium' | 'low'
  opportunity: 'guest_post' | 'resource_page' | 'broken_link' | 'mention' | 'partnership'
  estimatedValue: number
  campaign?: string
}

export default function AuthorityLinksManager({
  onLinkSelect
}: AuthorityLinksManagerProps) {
  const [backlinks, setBacklinks] = useState<BacklinkData[]>([])
  const [campaigns, setCampaigns] = useState<LinkCampaign[]>([])
  const [prospects, setProspects] = useState<LinkProspect[]>([])
  const [activeTab, setActiveTab] = useState<'backlinks' | 'campaigns' | 'prospects' | 'analytics'>('backlinks')
  const [searchQuery, setSearchQuery] = useState('')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [filterType, setFilterType] = useState<string>('all')
  const [sortBy, setSortBy] = useState<string>('domain_rating')
  const [viewMode, setViewMode] = useState<'grid' | 'table' | 'list'>('table')
  const [isLoading, setIsLoading] = useState(false)
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [showAddCampaign, setShowAddCampaign] = useState(false)
  const [showBulkActions, setShowBulkActions] = useState(false)
  
  const { success: notifySuccess, error: notifyError } = useNotifications()

  // Mock data - in real app this would come from API
  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    setIsLoading(true)
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock backlinks data
      setBacklinks([
        {
          id: 'bl1',
          url: 'https://techcrunch.com/article/seo-tools-2024',
          targetUrl: 'https://yoursite.com/seo-guide',
          anchorText: 'best SEO tools',
          domain: 'techcrunch.com',
          domainRating: 95,
          domainAuthority: 92,
          pageAuthority: 78,
          trustFlow: 85,
          citationFlow: 88,
          referringPage: {
            title: 'Top SEO Tools for 2024',
            url: 'https://techcrunch.com/article/seo-tools-2024',
            traffic: 45000,
            keywords: 230
          },
          linkType: 'dofollow',
          linkPosition: 'content',
          firstSeen: '2024-01-10T10:00:00Z',
          lastSeen: '2024-01-15T14:30:00Z',
          status: 'active',
          category: 'Technology',
          campaign: {
            id: 'camp1',
            name: 'Tech Outreach Q1',
            type: 'outreach'
          },
          metrics: {
            clicks: 1250,
            impressions: 25000,
            ctr: 5.0,
            position: 3,
            trafficValue: 2400
          },
          contact: {
            name: 'Sarah Johnson',
            email: '<EMAIL>',
            title: 'Senior Editor',
            socialProfiles: ['twitter.com/sarah_tech', 'linkedin.com/in/sarahjohnson']
          }
        },
        {
          id: 'bl2',
          url: 'https://moz.com/blog/seo-content-strategy',
          targetUrl: 'https://yoursite.com/content-marketing',
          anchorText: 'content marketing strategies',
          domain: 'moz.com',
          domainRating: 92,
          domainAuthority: 91,
          pageAuthority: 82,
          trustFlow: 89,
          citationFlow: 84,
          referringPage: {
            title: 'SEO Content Strategy Guide',
            url: 'https://moz.com/blog/seo-content-strategy',
            traffic: 32000,
            keywords: 180
          },
          linkType: 'dofollow',
          linkPosition: 'content',
          firstSeen: '2024-01-08T15:20:00Z',
          lastSeen: '2024-01-15T09:45:00Z',
          status: 'active',
          category: 'SEO',
          metrics: {
            clicks: 890,
            impressions: 18000,
            ctr: 4.9,
            position: 5,
            trafficValue: 1850
          }
        },
        {
          id: 'bl3',
          url: 'https://searchengineland.com/guides/link-building',
          targetUrl: 'https://yoursite.com/link-building',
          anchorText: 'link building guide',
          domain: 'searchengineland.com',
          domainRating: 88,
          domainAuthority: 85,
          pageAuthority: 76,
          trustFlow: 82,
          citationFlow: 79,
          referringPage: {
            title: 'Complete Link Building Guide',
            url: 'https://searchengineland.com/guides/link-building',
            traffic: 28000,
            keywords: 150
          },
          linkType: 'dofollow',
          linkPosition: 'content',
          firstSeen: '2024-01-05T11:15:00Z',
          lastSeen: '2024-01-14T16:20:00Z',
          status: 'active',
          category: 'SEO',
          metrics: {
            clicks: 670,
            impressions: 14000,
            ctr: 4.8,
            position: 7,
            trafficValue: 1420
          }
        },
        {
          id: 'bl4',
          url: 'https://example-blog.com/seo-tips',
          targetUrl: 'https://yoursite.com/seo-tips',
          anchorText: 'visit site',
          domain: 'example-blog.com',
          domainRating: 45,
          domainAuthority: 42,
          pageAuthority: 38,
          trustFlow: 35,
          citationFlow: 40,
          referringPage: {
            title: 'SEO Tips for Beginners',
            url: 'https://example-blog.com/seo-tips',
            traffic: 5000,
            keywords: 45
          },
          linkType: 'nofollow',
          linkPosition: 'sidebar',
          firstSeen: '2024-01-12T08:30:00Z',
          lastSeen: '2024-01-15T12:10:00Z',
          status: 'active',
          category: 'Blog',
          metrics: {
            clicks: 85,
            impressions: 2500,
            ctr: 3.4,
            position: 12,
            trafficValue: 120
          }
        },
        {
          id: 'bl5',
          url: 'https://oldsite.com/resources',
          targetUrl: 'https://yoursite.com/tools',
          anchorText: 'SEO tools',
          domain: 'oldsite.com',
          domainRating: 65,
          domainAuthority: 58,
          pageAuthority: 45,
          trustFlow: 52,
          citationFlow: 48,
          referringPage: {
            title: 'SEO Resources Page',
            url: 'https://oldsite.com/resources',
            traffic: 8000,
            keywords: 65
          },
          linkType: 'dofollow',
          linkPosition: 'content',
          firstSeen: '2024-01-01T12:00:00Z',
          lastSeen: '2024-01-10T14:30:00Z',
          status: 'lost',
          category: 'Directory',
          metrics: {
            clicks: 0,
            impressions: 0,
            ctr: 0,
            position: 0,
            trafficValue: 0
          }
        }
      ])

      // Mock campaigns data
      setCampaigns([
        {
          id: 'camp1',
          name: 'Tech Outreach Q1',
          type: 'outreach',
          status: 'active',
          targetKeywords: ['SEO tools', 'digital marketing', 'content strategy'],
          targetDomains: ['techcrunch.com', 'mashable.com', 'venturebeat.com'],
          progress: {
            prospected: 150,
            contacted: 120,
            responded: 45,
            linked: 12,
            total: 150
          },
          budget: 5000,
          spent: 2800,
          startDate: '2024-01-01T00:00:00Z',
          endDate: '2024-03-31T23:59:59Z',
          manager: 'Sarah Wilson',
          templates: {
            subject: 'Partnership Opportunity - {domain}',
            body: 'Hi {name},\n\nI noticed your recent article about {topic}...',
            followUp: ['Following up on my previous email...', 'Quick reminder about our discussion...']
          },
          metrics: {
            responseRate: 37.5,
            conversionRate: 26.7,
            avgDomainRating: 78,
            totalValue: 15000
          }
        },
        {
          id: 'camp2',
          name: 'Broken Link Recovery',
          type: 'broken_link',
          status: 'active',
          targetKeywords: ['SEO guide', 'link building', 'digital marketing'],
          targetDomains: ['various'],
          progress: {
            prospected: 200,
            contacted: 180,
            responded: 72,
            linked: 28,
            total: 200
          },
          budget: 3000,
          spent: 1500,
          startDate: '2024-01-15T00:00:00Z',
          manager: 'Mike Chen',
          templates: {
            subject: 'Broken Link Found on {domain}',
            body: 'Hi {name},\n\nI found a broken link on your page...',
            followUp: ['Hope you were able to fix the broken link...']
          },
          metrics: {
            responseRate: 40.0,
            conversionRate: 38.9,
            avgDomainRating: 65,
            totalValue: 8500
          }
        },
        {
          id: 'camp3',
          name: 'Guest Post Initiative',
          type: 'guest_post',
          status: 'planning',
          targetKeywords: ['content marketing', 'SEO strategy', 'digital growth'],
          targetDomains: ['contentmarketinginstitute.com', 'copyblogger.com', 'hubspot.com'],
          progress: {
            prospected: 50,
            contacted: 0,
            responded: 0,
            linked: 0,
            total: 50
          },
          budget: 8000,
          spent: 0,
          startDate: '2024-02-01T00:00:00Z',
          endDate: '2024-04-30T23:59:59Z',
          manager: 'Emily Rodriguez',
          templates: {
            subject: 'Guest Post Proposal for {domain}',
            body: 'Hi {name},\n\nI have a guest post idea that would be perfect for your audience...',
            followUp: ['Wanted to follow up on my guest post proposal...']
          },
          metrics: {
            responseRate: 0,
            conversionRate: 0,
            avgDomainRating: 0,
            totalValue: 0
          }
        }
      ])

      // Mock prospects data
      setProspects([
        {
          id: 'pr1',
          domain: 'marketingprofs.com',
          url: 'https://marketingprofs.com/articles/content-marketing',
          title: 'Content Marketing Best Practices',
          domainRating: 82,
          traffic: 45000,
          category: 'Marketing',
          contactInfo: {
            name: 'Jennifer Adams',
            email: '<EMAIL>',
            socialProfiles: ['twitter.com/jennifer_mp', 'linkedin.com/in/jennifer-adams']
          },
          outreachStatus: 'contacted',
          lastContact: '2024-01-14T10:30:00Z',
          notes: 'Interested in guest posting opportunities',
          priority: 'high',
          opportunity: 'guest_post',
          estimatedValue: 2500,
          campaign: 'camp3'
        },
        {
          id: 'pr2',
          domain: 'socialmediaexaminer.com',
          url: 'https://socialmediaexaminer.com/social-media-strategy',
          title: 'Social Media Strategy Guide',
          domainRating: 79,
          traffic: 38000,
          category: 'Social Media',
          contactInfo: {
            name: 'Michael Stelzner',
            email: '<EMAIL>',
            socialProfiles: ['twitter.com/michael_sme']
          },
          outreachStatus: 'not_contacted',
          priority: 'high',
          opportunity: 'resource_page',
          estimatedValue: 2200,
          campaign: 'camp1'
        },
        {
          id: 'pr3',
          domain: 'contentmarketinginstitute.com',
          url: 'https://contentmarketinginstitute.com/resources',
          title: 'Content Marketing Resources',
          domainRating: 85,
          traffic: 52000,
          category: 'Content Marketing',
          contactInfo: {
            name: 'Joe Pulizzi',
            email: '<EMAIL>',
            socialProfiles: ['twitter.com/joepulizzi', 'linkedin.com/in/joepulizzi']
          },
          outreachStatus: 'responded',
          lastContact: '2024-01-13T14:15:00Z',
          notes: 'Positive response, scheduling call next week',
          priority: 'high',
          opportunity: 'partnership',
          estimatedValue: 3500,
          campaign: 'camp3'
        }
      ])

    } catch (error) {
      notifyError('Failed to load link data')
    } finally {
      setIsLoading(false)
    }
  }

  const filteredBacklinks = backlinks.filter(link => {
    const matchesSearch = link.domain.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         link.anchorText.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         link.referringPage.title.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = filterStatus === 'all' || link.status === filterStatus
    const matchesType = filterType === 'all' || link.linkType === filterType
    return matchesSearch && matchesStatus && matchesType
  })

  const sortedBacklinks = [...filteredBacklinks].sort((a, b) => {
    switch (sortBy) {
      case 'domain_rating':
        return b.domainRating - a.domainRating
      case 'traffic_value':
        return b.metrics.trafficValue - a.metrics.trafficValue
      case 'first_seen':
        return new Date(b.firstSeen).getTime() - new Date(a.firstSeen).getTime()
      default:
        return 0
    }
  })

  const handleBulkAction = (action: string) => {
    switch (action) {
      case 'disavow':
        notifySuccess(`${selectedItems.length} links marked for disavowal`)
        break
      case 'monitor':
        notifySuccess(`${selectedItems.length} links added to monitoring`)
        break
      case 'export':
        notifySuccess(`${selectedItems.length} links exported`)
        break
      default:
        break
    }
    setSelectedItems([])
    setShowBulkActions(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success'
      case 'lost': return 'error'
      case 'broken': return 'warning'
      case 'pending': return 'info'
      case 'disavowed': return 'secondary'
      default: return 'secondary'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircleIcon className="h-4 w-4 text-green-500" />
      case 'lost': return <XCircleIcon className="h-4 w-4 text-red-500" />
      case 'broken': return <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500" />
      case 'pending': return <ClockIcon className="h-4 w-4 text-blue-500" />
      case 'disavowed': return <ShieldCheckIcon className="h-4 w-4 text-gray-500" />
      default: return null
    }
  }

  const getLinkTypeColor = (type: string) => {
    switch (type) {
      case 'dofollow': return 'success'
      case 'nofollow': return 'warning'
      case 'sponsored': return 'info'
      case 'ugc': return 'secondary'
      default: return 'secondary'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 dark:text-red-400'
      case 'medium': return 'text-yellow-600 dark:text-yellow-400'
      case 'low': return 'text-green-600 dark:text-green-400'
      default: return 'text-gray-600 dark:text-gray-400'
    }
  }

  const getOutreachStatusColor = (status: string) => {
    switch (status) {
      case 'not_contacted': return 'secondary'
      case 'contacted': return 'info'
      case 'followed_up': return 'warning'
      case 'responded': return 'success'
      case 'accepted': return 'success'
      case 'rejected': return 'error'
      default: return 'secondary'
    }
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return num.toString()
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const formatTimeAgo = (dateString: string) => {
    const diff = Date.now() - new Date(dateString).getTime()
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    if (days === 0) return 'Today'
    if (days === 1) return 'Yesterday'
    return `${days} days ago`
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading authority links data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Authority Links Manager
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage backlinks, track campaigns, and discover new link opportunities
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            onClick={() => setShowAddCampaign(true)}
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            New Campaign
          </Button>
          <Button>
            <LinkIcon className="h-4 w-4 mr-2" />
            Add Link
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center">
            <LinkIcon className="h-8 w-8 text-blue-600 dark:text-blue-400 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Backlinks</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {backlinks.length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center">
            <CheckCircleIcon className="h-8 w-8 text-green-600 dark:text-green-400 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Active Links</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {backlinks.filter(l => l.status === 'active').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center">
            <ChartBarIcon className="h-8 w-8 text-purple-600 dark:text-purple-400 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Avg Domain Rating</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {Math.round(backlinks.reduce((acc, l) => acc + l.domainRating, 0) / backlinks.length)}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center">
            <BanknotesIcon className="h-8 w-8 text-orange-600 dark:text-orange-400 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Traffic Value</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                ${formatNumber(backlinks.reduce((acc, l) => acc + l.metrics.trafficValue, 0))}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <div className="flex space-x-8">
          <button
            onClick={() => setActiveTab('backlinks')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'backlinks'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <LinkIcon className="h-4 w-4 inline mr-2" />
            Backlinks ({backlinks.length})
          </button>
          <button
            onClick={() => setActiveTab('campaigns')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'campaigns'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <FireIcon className="h-4 w-4 inline mr-2" />
            Campaigns ({campaigns.length})
          </button>
          <button
            onClick={() => setActiveTab('prospects')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'prospects'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <UserIcon className="h-4 w-4 inline mr-2" />
            Prospects ({prospects.length})
          </button>
          <button
            onClick={() => setActiveTab('analytics')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'analytics'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <ChartBarIcon className="h-4 w-4 inline mr-2" />
            Analytics
          </button>
        </div>
      </div>

      {/* Backlinks Tab */}
      {activeTab === 'backlinks' && (
        <div className="space-y-4">
          {/* Filters and Search */}
          <Card className="p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <div className="relative flex-1 min-w-64">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <Input
                    type="text"
                    placeholder="Search domains, anchor text, or titles..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="lost">Lost</option>
                  <option value="broken">Broken</option>
                  <option value="pending">Pending</option>
                  <option value="disavowed">Disavowed</option>
                </select>
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All Types</option>
                  <option value="dofollow">Dofollow</option>
                  <option value="nofollow">Nofollow</option>
                  <option value="sponsored">Sponsored</option>
                  <option value="ugc">UGC</option>
                </select>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                >
                  <option value="domain_rating">Domain Rating</option>
                  <option value="traffic_value">Traffic Value</option>
                  <option value="first_seen">First Seen</option>
                </select>
              </div>
              <div className="flex items-center space-x-2">
                <div className="flex items-center border border-gray-300 dark:border-gray-600 rounded-lg">
                  <button
                    onClick={() => setViewMode('table')}
                    className={`p-2 ${viewMode === 'table' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'text-gray-500'}`}
                  >
                    <TableCellsIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 ${viewMode === 'grid' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'text-gray-500'}`}
                  >
                    <Bars3Icon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 ${viewMode === 'list' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'text-gray-500'}`}
                  >
                    <ListBulletIcon className="h-4 w-4" />
                  </button>
                </div>
                {selectedItems.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowBulkActions(true)}
                  >
                    <Bars3Icon className="h-4 w-4 mr-2" />
                    Bulk Actions ({selectedItems.length})
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {/* Export functionality */}}
                >
                  <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>
            </div>
          </Card>

          {/* Bulk Actions Dropdown */}
          {showBulkActions && (
            <Card className="p-4 border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {selectedItems.length} items selected
                </span>
                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleBulkAction('disavow')}
                  >
                    <ShieldCheckIcon className="h-4 w-4 mr-2" />
                    Disavow
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleBulkAction('monitor')}
                  >
                    <BellIcon className="h-4 w-4 mr-2" />
                    Monitor
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleBulkAction('export')}
                  >
                    <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => {
                      setShowBulkActions(false)
                      setSelectedItems([])
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </Card>
          )}

          {/* Backlinks Table */}
          {viewMode === 'table' && (
            <Card className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th className="w-8 px-4 py-3">
                        <input
                          type="checkbox"
                          checked={selectedItems.length === sortedBacklinks.length}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedItems(sortedBacklinks.map(l => l.id))
                            } else {
                              setSelectedItems([])
                            }
                          }}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">Domain</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">Anchor Text</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">DR</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">Type</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">Status</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">Traffic Value</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">First Seen</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                    {sortedBacklinks.map((link) => (
                      <tr key={link.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="px-4 py-3">
                          <input
                            type="checkbox"
                            checked={selectedItems.includes(link.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedItems([...selectedItems, link.id])
                              } else {
                                setSelectedItems(selectedItems.filter(id => id !== link.id))
                              }
                            }}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-2">
                            <GlobeAltIcon className="h-4 w-4 text-gray-400" />
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                              {link.domain}
                            </span>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <span className="text-sm text-gray-600 dark:text-gray-400 max-w-xs truncate">
                            {link.anchorText}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <Badge variant={link.domainRating >= 70 ? 'success' : link.domainRating >= 40 ? 'warning' : 'secondary'}>
                            {link.domainRating}
                          </Badge>
                        </td>
                        <td className="py-3 px-4">
                          <Badge variant={getLinkTypeColor(link.linkType)}>
                            {link.linkType}
                          </Badge>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(link.status)}
                            <Badge variant={getStatusColor(link.status)}>
                              {link.status}
                            </Badge>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <span className="font-medium text-gray-900 dark:text-gray-100">
                            ${formatNumber(link.metrics.trafficValue)}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {formatDate(link.firstSeen)}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onLinkSelect?.(link)}
                            >
                              <EyeIcon className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => window.open(link.url, '_blank')}
                            >
                              <ArrowTopRightOnSquareIcon className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Card>
          )}

          {/* Grid View */}
          {viewMode === 'grid' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {sortedBacklinks.map((link) => (
                <Card key={link.id} className="p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <GlobeAltIcon className="h-5 w-5 text-gray-400" />
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        {link.domain}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(link.status)}
                      <Badge variant={getStatusColor(link.status)} size="sm">
                        {link.status}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="space-y-2 mb-3">
                    <div className="text-sm text-gray-600 dark:text-gray-400 truncate">
                      "{link.anchorText}"
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 truncate">
                      {link.referringPage.title}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2 mb-3">
                    <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
                      <div className="text-xs text-gray-500">DR</div>
                      <div className="font-bold text-gray-900 dark:text-gray-100">{link.domainRating}</div>
                    </div>
                    <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
                      <div className="text-xs text-gray-500">Value</div>
                      <div className="font-bold text-gray-900 dark:text-gray-100">${formatNumber(link.metrics.trafficValue)}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Badge variant={getLinkTypeColor(link.linkType)} size="sm">
                      {link.linkType}
                    </Badge>
                    <div className="flex items-center space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onLinkSelect?.(link)}
                      >
                        <EyeIcon className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.open(link.url, '_blank')}
                      >
                        <ArrowTopRightOnSquareIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}

          {/* List View */}
          {viewMode === 'list' && (
            <div className="space-y-2">
              {sortedBacklinks.map((link) => (
                <Card key={link.id} className="p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <GlobeAltIcon className="h-5 w-5 text-gray-400" />
                      <div>
                        <div className="font-medium text-gray-900 dark:text-gray-100">
                          {link.domain}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          "{link.anchorText}" • {formatDate(link.firstSeen)}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Badge variant={link.domainRating >= 70 ? 'success' : link.domainRating >= 40 ? 'warning' : 'secondary'}>
                        DR {link.domainRating}
                      </Badge>
                      <Badge variant={getLinkTypeColor(link.linkType)}>
                        {link.linkType}
                      </Badge>
                      <Badge variant={getStatusColor(link.status)}>
                        {link.status}
                      </Badge>
                      <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        ${formatNumber(link.metrics.trafficValue)}
                      </span>
                      <div className="flex items-center space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onLinkSelect?.(link)}
                        >
                          <EyeIcon className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(link.url, '_blank')}
                        >
                          <ArrowTopRightOnSquareIcon className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Campaigns Tab */}
      {activeTab === 'campaigns' && (
        <div className="space-y-4">
          {campaigns.map((campaign) => (
            <Card key={campaign.id} className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                    {campaign.name}
                  </h3>
                  <div className="flex items-center space-x-2 mt-1">
                    <Badge variant="outline">{campaign.type}</Badge>
                    <Badge variant={campaign.status === 'active' ? 'success' : campaign.status === 'paused' ? 'warning' : 'secondary'}>
                      {campaign.status}
                    </Badge>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-gray-600 dark:text-gray-400">Budget</div>
                  <div className="font-medium text-gray-900 dark:text-gray-100">
                    ${formatNumber(campaign.spent)} / ${formatNumber(campaign.budget)}
                  </div>
                  <div className="text-xs text-gray-500">
                    {Math.round((campaign.spent / campaign.budget) * 100)}% spent
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-sm text-gray-500">Prospected</div>
                  <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                    {campaign.progress.prospected}
                  </div>
                </div>
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-sm text-gray-500">Contacted</div>
                  <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                    {campaign.progress.contacted}
                  </div>
                </div>
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-sm text-gray-500">Responded</div>
                  <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                    {campaign.progress.responded}
                  </div>
                </div>
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-sm text-gray-500">Linked</div>
                  <div className="text-lg font-bold text-green-600 dark:text-green-400">
                    {campaign.progress.linked}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Manager: {campaign.manager}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Response Rate: {campaign.metrics.responseRate}%
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Conversion: {campaign.metrics.conversionRate}%
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm">
                    <PencilIcon className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                  <Button variant="outline" size="sm">
                    <EyeIcon className="h-4 w-4 mr-2" />
                    View
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Prospects Tab */}
      {activeTab === 'prospects' && (
        <div className="space-y-4">
          {prospects.map((prospect) => (
            <Card key={prospect.id} className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <GlobeAltIcon className="h-8 w-8 text-gray-400" />
                  <div>
                    <div className="font-medium text-gray-900 dark:text-gray-100">
                      {prospect.domain}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {prospect.title}
                    </div>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge variant="outline">{prospect.category}</Badge>
                      <Badge variant={getOutreachStatusColor(prospect.outreachStatus)}>
                        {prospect.outreachStatus.replace('_', ' ')}
                      </Badge>
                      <span className={`text-xs font-medium ${getPriorityColor(prospect.priority)}`}>
                        {prospect.priority} priority
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-6">
                  <div className="text-center">
                    <div className="text-sm text-gray-500">DR</div>
                    <div className="font-bold text-gray-900 dark:text-gray-100">{prospect.domainRating}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-gray-500">Traffic</div>
                    <div className="font-bold text-gray-900 dark:text-gray-100">{formatNumber(prospect.traffic)}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-gray-500">Value</div>
                    <div className="font-bold text-gray-900 dark:text-gray-100">${formatNumber(prospect.estimatedValue)}</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      <EnvelopeIcon className="h-4 w-4 mr-2" />
                      Contact
                    </Button>
                    <Button variant="outline" size="sm">
                      <EyeIcon className="h-4 w-4 mr-2" />
                      View
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Analytics Tab */}
      {activeTab === 'analytics' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Link Quality Distribution
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">High DR (70+)</span>
                <div className="flex items-center space-x-2">
                  <div className="w-20 h-2 bg-gray-200 dark:bg-gray-700 rounded-full">
                    <div className="w-3/4 h-2 bg-green-500 rounded-full"></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {backlinks.filter(l => l.domainRating >= 70).length}
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Medium DR (40-69)</span>
                <div className="flex items-center space-x-2">
                  <div className="w-20 h-2 bg-gray-200 dark:bg-gray-700 rounded-full">
                    <div className="w-1/2 h-2 bg-yellow-500 rounded-full"></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {backlinks.filter(l => l.domainRating >= 40 && l.domainRating < 70).length}
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Low DR (&lt;40)</span>
                <div className="flex items-center space-x-2">
                  <div className="w-20 h-2 bg-gray-200 dark:bg-gray-700 rounded-full">
                    <div className="w-1/4 h-2 bg-red-500 rounded-full"></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {backlinks.filter(l => l.domainRating < 40).length}
                  </span>
                </div>
              </div>
            </div>
          </Card>
          
          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Link Type Distribution
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Dofollow</span>
                <div className="flex items-center space-x-2">
                  <div className="w-20 h-2 bg-gray-200 dark:bg-gray-700 rounded-full">
                    <div className="w-4/5 h-2 bg-green-500 rounded-full"></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {backlinks.filter(l => l.linkType === 'dofollow').length}
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Nofollow</span>
                <div className="flex items-center space-x-2">
                  <div className="w-20 h-2 bg-gray-200 dark:bg-gray-700 rounded-full">
                    <div className="w-1/5 h-2 bg-yellow-500 rounded-full"></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {backlinks.filter(l => l.linkType === 'nofollow').length}
                  </span>
                </div>
              </div>
            </div>
          </Card>
          
          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Campaign Performance
            </h3>
            <div className="space-y-3">
              {campaigns.map((campaign) => (
                <div key={campaign.id} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400 truncate">
                    {campaign.name}
                  </span>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {campaign.progress.linked}
                    </span>
                    <span className="text-xs text-gray-500">
                      ({campaign.metrics.conversionRate}%)
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>
      )}
    </div>
  )
}