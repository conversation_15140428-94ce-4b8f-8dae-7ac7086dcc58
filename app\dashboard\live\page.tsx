'use client';

import React from 'react';
import { ProtectedRoute } from '@/hooks/useAuth';
import { AuthenticatedLayout, PageHeader } from '@/components/Layout/DashboardLayout';
import { LiveMetricsWidget } from '@/components/UI/LiveMetricsWidget';
import { ActivityFeed } from '@/components/UI/ActivityFeed';
import { RealTimeIndicator } from '@/hooks/useRealTimeUpdates';
import { 
  ChartBarIcon, 
  BoltIcon, 
  ClockIcon,
  SignalIcon
} from '@heroicons/react/24/outline';

export default function LiveDashboardPage() {
  return (
    <ProtectedRoute>
      <AuthenticatedLayout>
        <PageHeader
          title="Live Dashboard"
          description="Real-time SEO metrics and activity updates"
          actions={
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 px-3 py-2 bg-green-50 border border-green-200 rounded-lg">
                <SignalIcon className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">Live Mode</span>
              </div>
            </div>
          }
        />
        
        <div className="space-y-8">
          {/* Live Metrics Section */}
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <div className="flex items-center mb-6">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                <ChartBarIcon className="w-5 h-5 text-white" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900">Real-Time Metrics</h2>
            </div>
            
            <LiveMetricsWidget 
              className="space-y-6"
              showRefreshButton={true}
            />
          </div>

          {/* Live Activity Feed Section */}
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <div className="flex items-center mb-6">
              <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center mr-3">
                <BoltIcon className="w-5 h-5 text-white" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900">Live Activity Stream</h2>
            </div>
            
            <ActivityFeed 
              className="space-y-4"
              maxItems={10}
              showRealTimeIndicator={true}
            />
          </div>

          {/* Real-Time Features Info */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200 p-6">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <ClockIcon className="w-5 h-5 text-white" />
                </div>
              </div>
              <div className="ml-4 flex-1">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Real-Time Dashboard Features
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Live Metrics</h4>
                    <ul className="space-y-1">
                      <li>• Auto-refreshing every 60 seconds</li>
                      <li>• Visual change indicators</li>
                      <li>• Connection status monitoring</li>
                      <li>• Manual refresh capability</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Activity Feed</h4>
                    <ul className="space-y-1">
                      <li>• Live activity streaming</li>
                      <li>• Real-time notifications</li>
                      <li>• Activity type categorization</li>
                      <li>• Expandable detailed view</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </AuthenticatedLayout>
    </ProtectedRoute>
  );
}