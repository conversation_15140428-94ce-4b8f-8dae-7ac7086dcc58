/**
 * AnimatedButton Component
 * Button with micro-interactions and ripple effects
 */

import React, { useRef, useState } from 'react';
import { useAnimation } from '@/lib/animations/hooks';
import { ANIMATION_DURATION, ANIMATION_EASING, AnimationType } from '@/lib/animations/constants';
import { cn } from '@/lib/utils';

interface AnimatedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  ripple?: boolean;
  scaleOnPress?: boolean;
  children: React.ReactNode;
}

export const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  variant = 'primary',
  size = 'md',
  ripple = true,
  scaleOnPress = true,
  children,
  className,
  onClick,
  disabled,
  ...props
}) => {
  const buttonRef = useRef<HTMLButtonElement>(null);
  const { animate } = useAnimation();
  const [ripples, setRipples] = useState<Array<{ x: number; y: number; id: number }>>([]);

  const handleClick = async (e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled) return;

    const button = buttonRef.current;
    if (!button) return;

    // Create ripple effect
    if (ripple) {
      const rect = button.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const id = Date.now();

      setRipples(prev => [...prev, { x, y, id }]);

      // Remove ripple after animation
      setTimeout(() => {
        setRipples(prev => prev.filter(r => r.id !== id));
      }, 600);
    }

    // Scale animation on press
    if (scaleOnPress) {
      await animate(
        button,
        [
          { transform: 'scale(1)' },
          { transform: 'scale(0.95)' },
          { transform: 'scale(1)' }
        ],
        {
          duration: ANIMATION_DURATION.fast,
          easing: ANIMATION_EASING.easeOut
        }
      );
    }

    if (onClick) {
      onClick(e);
    }
  };

  const baseStyles = cn(
    'relative overflow-hidden transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    {
      // Variants
      'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500':
        variant === 'primary',
      'bg-white text-gray-900 border border-gray-300 hover:bg-gray-50 focus:ring-gray-500':
        variant === 'secondary',
      'bg-transparent hover:bg-gray-100 text-gray-700 focus:ring-gray-500':
        variant === 'ghost',
      'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500':
        variant === 'danger',
      
      // Sizes
      'px-3 py-1.5 text-sm': size === 'sm',
      'px-4 py-2 text-base': size === 'md',
      'px-6 py-3 text-lg': size === 'lg'
    }
  );

  return (
    <button
      ref={buttonRef}
      className={cn(baseStyles, className)}
      onClick={handleClick}
      disabled={disabled}
      {...props}
    >
      {children}
      
      {/* Ripple effects */}
      {ripples.map(({ x, y, id }) => (
        <span
          key={id}
          className="absolute pointer-events-none"
          style={{
            left: x,
            top: y,
            transform: 'translate(-50%, -50%)'
          }}
        >
          <span
            className={cn(
              'block rounded-full animate-ripple',
              variant === 'primary' || variant === 'danger'
                ? 'bg-white/30'
                : 'bg-gray-400/30'
            )}
            style={{
              width: '20px',
              height: '20px',
              animation: 'ripple 600ms ease-out forwards'
            }}
          />
        </span>
      ))}
      
      <style jsx>{`
        @keyframes ripple {
          from {
            transform: scale(0);
            opacity: 1;
          }
          to {
            transform: scale(20);
            opacity: 0;
          }
        }
      `}</style>
    </button>
  );
};