'use client'

import React from 'react'
import { Bars3Icon } from '@heroicons/react/24/outline'

interface MobileNavProps {
  sidebarOpen: boolean
  onSidebarToggle: () => void
}

export default function MobileNav({ sidebarOpen, onSidebarToggle }: MobileNavProps) {
  return (
    <div className="lg:hidden fixed top-0 left-0 right-0 z-40 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
      <div className="flex items-center justify-between">
        {/* Menu Toggle Button */}
        <button
          type="button"
          className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-colors"
          onClick={onSidebarToggle}
          aria-expanded={sidebarOpen}
          aria-label="Toggle sidebar"
        >
          <Bars3Icon className="h-6 w-6" />
        </button>

        {/* Logo */}
        <div className="flex items-center">
          <h1 className="text-xl font-bold text-gray-900 dark:text-white">
            SEO SAAS
          </h1>
        </div>

        {/* Quick Actions */}
        <div className="flex items-center space-x-2">
          {/* Quick Generate Button */}
          <button
            type="button"
            className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            Generate
          </button>
        </div>
      </div>
    </div>
  )
}