/**
 * SitemapAnalyzer Component
 * Enterprise SEO SAAS - Visual interface for sitemap analysis and internal linking
 */

import { useState, useEffect } from 'react'
import { SitemapAnalyzer, SitemapAnalysis, LinkingOpportunity } from '@/utils/sitemapAnalyzer'
import {
  GlobeAltIcon,
  MagnifyingGlassIcon,
  LinkIcon,
  ChartBarIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowPathIcon,
  ClockIcon,
  EyeIcon,
  ShareIcon,
  StarIcon,
  BoltIcon,
  PuzzlePieceIcon
} from '@heroicons/react/24/outline'

interface SitemapAnalyzerProps {
  siteUrl?: string
  onAnalysisComplete?: (analysis: SitemapAnalysis) => void
}

export default function SitemapAnalyzerComponent({ siteUrl, onAnalysisComplete }: SitemapAnalyzerProps) {
  const [targetUrl, setTargetUrl] = useState(siteUrl || '')
  const [customSitemapUrl, setCustomSitemapUrl] = useState('')
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysis, setAnalysis] = useState<SitemapAnalysis | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'overview' | 'opportunities' | 'clusters' | 'technical'>('overview')
  const [selectedOpportunity, setSelectedOpportunity] = useState<LinkingOpportunity | null>(null)
  const [analyzer] = useState(() => new SitemapAnalyzer())

  const handleAnalyze = async () => {
    if (!targetUrl.trim()) {
      setError('Please enter a website URL')
      return
    }

    setIsAnalyzing(true)
    setError(null)
    setAnalysis(null)

    try {
      const result = await analyzer.analyzeSitemap(targetUrl.trim(), customSitemapUrl || undefined)
      setAnalysis(result)
      onAnalysisComplete?.(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Analysis failed')
    } finally {
      setIsAnalyzing(false)
    }
  }

  const getOpportunityPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-green-100 text-green-800 border-green-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getTechnicalSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'text-red-600'
      case 'medium': return 'text-yellow-600'
      case 'low': return 'text-green-600'
      default: return 'text-gray-600'
    }
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: ChartBarIcon },
    { id: 'opportunities', label: 'Link Opportunities', icon: LinkIcon },
    { id: 'clusters', label: 'Topic Clusters', icon: PuzzlePieceIcon },
    { id: 'technical', label: 'Technical Insights', icon: BoltIcon }
  ]

  return (
    <div className="space-y-6">
      {/* Analysis Input */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Sitemap Analysis</h2>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Website URL *
            </label>
            <input
              type="url"
              value={targetUrl}
              onChange={(e) => setTargetUrl(e.target.value)}
              placeholder="https://example.com (no demo sites)"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isAnalyzing}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Custom Sitemap URL (Optional)
            </label>
            <input
              type="url"
              value={customSitemapUrl}
              onChange={(e) => setCustomSitemapUrl(e.target.value)}
              placeholder="https://example.com/custom-sitemap.xml"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isAnalyzing}
            />
          </div>

          <button
            onClick={handleAnalyze}
            disabled={isAnalyzing || !targetUrl.trim()}
            className="w-full md:w-auto px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white font-medium rounded-lg transition-colors flex items-center justify-center gap-2"
          >
            {isAnalyzing ? (
              <>
                <ArrowPathIcon className="h-5 w-5 animate-spin" />
                Analyzing Sitemap...
              </>
            ) : (
              <>
                <MagnifyingGlassIcon className="h-5 w-5" />
                Analyze Sitemap
              </>
            )}
          </button>
        </div>

        {error && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2 text-red-800">
              <ExclamationTriangleIcon className="h-5 w-5" />
              <span className="font-medium">Analysis Error</span>
            </div>
            <p className="text-red-700 mt-1">{error}</p>
          </div>
        )}
      </div>

      {/* Analysis Progress */}
      {isAnalyzing && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-center gap-3 mb-4">
            <ArrowPathIcon className="h-6 w-6 text-blue-600 animate-spin" />
            <span className="font-medium text-blue-900">Analyzing sitemap and discovering linking opportunities...</span>
          </div>
          <div className="space-y-2 text-sm text-blue-800">
            <div>• Discovering sitemap locations</div>
            <div>• Parsing sitemap entries</div>
            <div>• Analyzing content and metadata</div>
            <div>• Generating linking opportunities</div>
            <div>• Identifying topic clusters</div>
          </div>
        </div>
      )}

      {/* Analysis Results */}
      {analysis && (
        <div className="space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <DocumentTextIcon className="h-8 w-8 text-blue-600" />
                <div>
                  <div className="text-2xl font-bold text-gray-900">{analysis.validPages}</div>
                  <div className="text-sm text-gray-500">Pages Analyzed</div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <LinkIcon className="h-8 w-8 text-green-600" />
                <div>
                  <div className="text-2xl font-bold text-gray-900">{analysis.linkingOpportunities.length}</div>
                  <div className="text-sm text-gray-500">Link Opportunities</div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <PuzzlePieceIcon className="h-8 w-8 text-purple-600" />
                <div>
                  <div className="text-2xl font-bold text-gray-900">{analysis.topicClusters.length}</div>
                  <div className="text-sm text-gray-500">Topic Clusters</div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <BoltIcon className="h-8 w-8 text-orange-600" />
                <div>
                  <div className="text-2xl font-bold text-gray-900">{analysis.technicalInsights.length}</div>
                  <div className="text-sm text-gray-500">Tech Insights</div>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`
                      py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center gap-2
                      ${activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }
                    `}
                  >
                    <Icon className="h-4 w-4" />
                    {tab.label}
                  </button>
                )
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="space-y-6">
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Content Types Distribution */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Content Type Distribution</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {Object.entries(analysis.contentTypes).map(([type, count]) => (
                      <div key={type} className="text-center p-3 bg-gray-50 rounded-lg">
                        <div className="text-lg font-bold text-gray-900">{count}</div>
                        <div className="text-sm text-gray-600 capitalize">{type.replace('_', ' ')}</div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* SEO Recommendations */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">SEO Recommendations</h3>
                  <div className="space-y-3">
                    {analysis.seoRecommendations.map((recommendation, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                        <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5" />
                        <span className="text-green-800">{recommendation}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'opportunities' && (
              <div className="space-y-6">
                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900">
                      Internal Linking Opportunities ({analysis.linkingOpportunities.length})
                    </h3>
                  </div>
                  <div className="divide-y divide-gray-200">
                    {analysis.linkingOpportunities.slice(0, 20).map((opportunity, index) => (
                      <div
                        key={index}
                        className="p-6 hover:bg-gray-50 cursor-pointer transition-colors"
                        onClick={() => setSelectedOpportunity(opportunity)}
                      >
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <span className={`
                                inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border
                                ${getOpportunityPriorityColor(opportunity.priority)}
                              `}>
                                {opportunity.priority} priority
                              </span>
                              <span className="text-sm text-gray-500">
                                Impact: {opportunity.estimatedImpact}%
                              </span>
                            </div>
                            <h4 className="font-medium text-gray-900 mb-1">
                              "{opportunity.anchorText}"
                            </h4>
                            <p className="text-sm text-gray-600 mb-2">{opportunity.context}</p>
                            <div className="text-xs text-gray-500">
                              <div>From: {opportunity.sourceUrl}</div>
                              <div>To: {opportunity.targetUrl}</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold text-blue-600">
                              {Math.round(opportunity.relevanceScore * 100)}%
                            </div>
                            <div className="text-xs text-gray-500">Relevance</div>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>Difficulty: {opportunity.implementationDifficulty}%</span>
                          <EyeIcon className="h-4 w-4" />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'clusters' && (
              <div className="space-y-6">
                {analysis.topicClusters.map((cluster, index) => (
                  <div key={index} className="bg-white border border-gray-200 rounded-lg p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">{cluster.topic}</h3>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>Authority: {Math.round(cluster.authorityScore)}/100</span>
                          <span>Pages: {cluster.clusterPages.length + 1}</span>
                          <span>Link Density: {Math.round(cluster.linkingDensity * 100)}%</span>
                        </div>
                      </div>
                      <StarIcon className="h-6 w-6 text-yellow-500" />
                    </div>

                    <div className="space-y-4">
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Pillar Page</h4>
                        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                          <div className="text-sm text-blue-800">{cluster.pillarPage}</div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">
                          Cluster Pages ({cluster.clusterPages.length})
                        </h4>
                        <div className="space-y-2">
                          {cluster.clusterPages.slice(0, 5).map((page, pageIndex) => (
                            <div key={pageIndex} className="p-2 bg-gray-50 rounded text-sm text-gray-700">
                              {page}
                            </div>
                          ))}
                          {cluster.clusterPages.length > 5 && (
                            <div className="text-sm text-gray-500">
                              +{cluster.clusterPages.length - 5} more pages
                            </div>
                          )}
                        </div>
                      </div>

                      {cluster.contentGaps.length > 0 && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">Content Gaps</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                            {cluster.contentGaps.map((gap, gapIndex) => (
                              <div key={gapIndex} className="p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
                                {gap}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeTab === 'technical' && (
              <div className="space-y-6">
                {analysis.technicalInsights.map((insight, index) => (
                  <div key={index} className="bg-white border border-gray-200 rounded-lg p-6">
                    <div className="flex items-start gap-4">
                      <div className={`p-2 rounded-lg ${
                        insight.severity === 'high' ? 'bg-red-100' :
                        insight.severity === 'medium' ? 'bg-yellow-100' : 'bg-green-100'
                      }`}>
                        <ExclamationTriangleIcon className={`h-6 w-6 ${getTechnicalSeverityColor(insight.severity)}`} />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-medium text-gray-900">{insight.type.replace('_', ' ').toUpperCase()}</h3>
                          <span className={`
                            inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                            ${insight.severity === 'high' ? 'bg-red-100 text-red-800' :
                              insight.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-green-100 text-green-800'
                            }
                          `}>
                            {insight.severity} severity
                          </span>
                        </div>
                        
                        <p className="text-gray-700 mb-3">{insight.insight}</p>
                        
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                          <h4 className="text-sm font-medium text-blue-900 mb-1">Recommendation</h4>
                          <p className="text-sm text-blue-800">{insight.recommendation}</p>
                        </div>
                        
                        <div className="mt-3 flex items-center justify-between">
                          <span className="text-sm text-gray-500">
                            Potential Impact: {insight.impact}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Opportunity Detail Modal */}
      {selectedOpportunity && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-medium text-gray-900">Linking Opportunity Details</h2>
                <button
                  onClick={() => setSelectedOpportunity(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <span className="text-2xl">×</span>
                </button>
              </div>
            </div>
            
            <div className="p-6 space-y-6">
              <div>
                <h3 className="font-medium text-gray-900 mb-2">Suggested Anchor Text</h3>
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <span className="font-medium text-blue-900">"{selectedOpportunity.anchorText}"</span>
                </div>
              </div>

              <div>
                <h3 className="font-medium text-gray-900 mb-2">Context</h3>
                <p className="text-gray-700">{selectedOpportunity.context}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Relevance Score</h4>
                  <div className="text-2xl font-bold text-blue-600">
                    {Math.round(selectedOpportunity.relevanceScore * 100)}%
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Estimated Impact</h4>
                  <div className="text-2xl font-bold text-green-600">
                    {selectedOpportunity.estimatedImpact}%
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-medium text-gray-900 mb-2">URLs</h3>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium text-gray-600">From:</span>
                    <div className="text-sm text-gray-800 break-all">{selectedOpportunity.sourceUrl}</div>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-600">To:</span>
                    <div className="text-sm text-gray-800 break-all">{selectedOpportunity.targetUrl}</div>
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                <div className="flex items-center gap-2">
                  <span className={`
                    inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border
                    ${getOpportunityPriorityColor(selectedOpportunity.priority)}
                  `}>
                    {selectedOpportunity.priority} priority
                  </span>
                </div>
                <div className="text-sm text-gray-500">
                  Implementation Difficulty: {selectedOpportunity.implementationDifficulty}%
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}