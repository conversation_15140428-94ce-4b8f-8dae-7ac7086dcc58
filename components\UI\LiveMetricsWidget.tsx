'use client';

import React, { useState, useEffect } from 'react';
import { useMetricsUpdates } from '@/hooks/useRealTimeUpdates';
import { RealTimeIndicator } from '@/hooks/useRealTimeUpdates';
import MetricCard from './MetricCard';
import {
  DocumentTextIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  FolderIcon,
  TrendingUpIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

interface LiveMetricsWidgetProps {
  projectId?: string;
  className?: string;
  showRefreshButton?: boolean;
}

interface MetricData {
  contentGenerated: {
    total: number;
    thisMonth: number;
    change: number;
  };
  seoScoreAverage: {
    score: number;
    change: number;
  };
  keywordsTracked: {
    total: number;
    change: number;
  };
  projectsActive: {
    total: number;
    change: number;
  };
  wordsGenerated: {
    total: number;
    thisMonth: number;
    change: number;
  };
  averageRanking: {
    position: number;
    change: number;
  };
}

export default function LiveMetricsWidget({ 
  projectId, 
  className = '',
  showRefreshButton = true 
}: LiveMetricsWidgetProps) {
  const [previousMetrics, setPreviousMetrics] = useState<MetricData | null>(null);
  const [changedMetrics, setChangedMetrics] = useState<Set<string>>(new Set());
  
  const {
    metrics,
    isLoading,
    isConnected,
    error,
    lastUpdate,
    refresh
  } = useMetricsUpdates(projectId);

  // Track metric changes for visual feedback
  useEffect(() => {
    if (metrics && previousMetrics) {
      const changed = new Set<string>();
      
      // Check each metric for changes
      if (metrics.contentGenerated?.total !== previousMetrics.contentGenerated?.total) {
        changed.add('contentGenerated');
      }
      if (metrics.seoScoreAverage?.score !== previousMetrics.seoScoreAverage?.score) {
        changed.add('seoScoreAverage');
      }
      if (metrics.keywordsTracked?.total !== previousMetrics.keywordsTracked?.total) {
        changed.add('keywordsTracked');
      }
      if (metrics.projectsActive?.total !== previousMetrics.projectsActive?.total) {
        changed.add('projectsActive');
      }
      if (metrics.wordsGenerated?.total !== previousMetrics.wordsGenerated?.total) {
        changed.add('wordsGenerated');
      }
      if (metrics.averageRanking?.position !== previousMetrics.averageRanking?.position) {
        changed.add('averageRanking');
      }
      
      setChangedMetrics(changed);
      
      // Clear change indicators after 3 seconds
      setTimeout(() => setChangedMetrics(new Set()), 3000);
    }
    
    if (metrics) {
      setPreviousMetrics(metrics);
    }
  }, [metrics, previousMetrics]);

  // Simulate metric updates for demo purposes
  useEffect(() => {
    if (isConnected && metrics) {
      const interval = setInterval(() => {
        // Simulate occasional metric updates (5% chance every 60 seconds)
        if (Math.random() < 0.05) {
          refresh();
        }
      }, 60000); // Check every minute
      
      return () => clearInterval(interval);
    }
  }, [isConnected, metrics, refresh]);

  const getMetricCardProps = (metricKey: string) => {
    const isChanged = changedMetrics.has(metricKey);
    const baseClasses = isChanged ? 'ring-2 ring-blue-500 ring-opacity-50 bg-blue-50' : '';
    
    return {
      className: `transition-all duration-500 ${baseClasses}`,
      loading: isLoading && !metrics
    };
  };

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-6 ${className}`}>
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-red-800">Metrics Connection Error</h3>
            <p className="text-red-600 mt-1">{error.message}</p>
          </div>
          <button
            onClick={refresh}
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with real-time indicator */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900">Live Metrics</h2>
        <div className="flex items-center space-x-4">
          <RealTimeIndicator 
            isConnected={isConnected} 
            lastUpdate={lastUpdate}
          />
          {showRefreshButton && (
            <button
              onClick={refresh}
              disabled={isLoading}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
              title="Refresh metrics"
            >
              <ArrowPathIcon className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
            </button>
          )}
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <MetricCard
          {...getMetricCardProps('contentGenerated')}
          title="Content Generated"
          value={metrics?.contentGenerated?.total || 0}
          change={{
            value: metrics?.contentGenerated?.change || 0,
            type: 'increase',
            period: 'this month'
          }}
          icon={DocumentTextIcon}
        />
        
        <MetricCard
          {...getMetricCardProps('seoScoreAverage')}
          title="Average SEO Score"
          value={metrics?.seoScoreAverage?.score || 0}
          change={{
            value: metrics?.seoScoreAverage?.change || 0,
            type: 'increase',
            period: 'vs last month'
          }}
          icon={TrendingUpIcon}
        />
        
        <MetricCard
          {...getMetricCardProps('keywordsTracked')}
          title="Keywords Tracked"
          value={metrics?.keywordsTracked?.total || 0}
          change={{
            value: metrics?.keywordsTracked?.change || 0,
            type: 'increase'
          }}
          icon={MagnifyingGlassIcon}
        />
        
        <MetricCard
          {...getMetricCardProps('projectsActive')}
          title="Active Projects"
          value={metrics?.projectsActive?.total || 0}
          change={{
            value: metrics?.projectsActive?.change || 0,
            type: 'increase'
          }}
          icon={FolderIcon}
        />
        
        <MetricCard
          {...getMetricCardProps('wordsGenerated')}
          title="Words Generated"
          value={metrics?.wordsGenerated?.thisMonth || 0}
          change={{
            value: metrics?.wordsGenerated?.change || 0,
            type: 'increase',
            period: 'this month'
          }}
          icon={DocumentTextIcon}
        />
        
        <MetricCard
          {...getMetricCardProps('averageRanking')}
          title="Avg. Ranking"
          value={`#${metrics?.averageRanking?.position || 0}`}
          change={{
            value: Math.abs(metrics?.averageRanking?.change || 0),
            type: 'increase', // improved ranking is good
            period: 'positions up'
          }}
          icon={ChartBarIcon}
        />
      </div>

      {/* Performance indicators */}
      {changedMetrics.size > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse mr-3"></div>
            <span className="text-sm text-blue-800">
              {changedMetrics.size} metric{changedMetrics.size > 1 ? 's' : ''} updated
            </span>
          </div>
        </div>
      )}
    </div>
  );
}

// Export for use in other components
export { LiveMetricsWidget };