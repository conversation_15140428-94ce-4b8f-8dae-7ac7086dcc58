/**
 * AuthorityLinkAnalyzer Component
 * Enterprise SEO SAAS - Visual interface for authority link discovery and analysis
 */

import { useState, useEffect } from 'react'
import { AuthorityLinkDiscovery, LinkDiscoveryRequest, LinkDiscoveryResult, AuthorityLink } from '@/utils/authorityLinkDiscovery'
import { DomainAuthorityAssessment, DomainAuthorityMetrics } from '@/utils/domainAuthorityAssessment'
import {
  MagnifyingGlassIcon,
  LinkIcon,
  ChartBarIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowPathIcon,
  ClockIcon,
  EyeIcon,
  ShareIcon,
  StarIcon,
  BoltIcon,
  GlobeAltIcon,
  AcademicCapIcon,
  BuildingOfficeIcon,
  NewspaperIcon,
  BookOpenIcon,
  ShieldCheckIcon,
  ExclamationCircleIcon,
  ArrowTopRightOnSquareIcon,
  FunnelIcon,
  Bars3BottomLeftIcon,
  TableCellsIcon
} from '@heroicons/react/24/outline'

interface AuthorityLinkAnalyzerProps {
  initialTopic?: string
  initialKeywords?: string[]
  onLinksDiscovered?: (links: AuthorityLink[]) => void
}

export default function AuthorityLinkAnalyzer({ 
  initialTopic = '', 
  initialKeywords = [], 
  onLinksDiscovered 
}: AuthorityLinkAnalyzerProps) {
  const [activeTab, setActiveTab] = useState<'discovery' | 'wikipedia' | 'academic' | 'validation' | 'analysis'>('discovery')
  const [discoveryRequest, setDiscoveryRequest] = useState<LinkDiscoveryRequest>({
    topic: initialTopic,
    keywords: initialKeywords,
    targetLanguage: 'en',
    maxResults: 20,
    sourceTypes: [],
    minAuthorityScore: 70,
    includeWikipedia: true,
    includeReferences: true
  })
  
  const [discoveryResult, setDiscoveryResult] = useState<LinkDiscoveryResult | null>(null)
  const [selectedLinks, setSelectedLinks] = useState<Set<string>>(new Set())
  const [isDiscovering, setIsDiscovering] = useState(false)
  const [domainMetrics, setDomainMetrics] = useState<Record<string, DomainAuthorityMetrics>>({})
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid')
  const [filterType, setFilterType] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'authority' | 'relevance' | 'type'>('authority')
  
  const [authorityDiscovery] = useState(() => new AuthorityLinkDiscovery())
  const [domainAssessment] = useState(() => new DomainAuthorityAssessment())

  const handleDiscoverLinks = async () => {
    if (!discoveryRequest.topic.trim()) {
      alert('Please enter a topic to analyze')
      return
    }

    setIsDiscovering(true)
    setDiscoveryResult(null)
    setSelectedLinks(new Set())

    try {
      const result = await authorityDiscovery.discoverAuthorityLinks(discoveryRequest)
      setDiscoveryResult(result)
      onLinksDiscovered?.(result.discoveredLinks)

      // Assess domain authority for unique domains
      const uniqueDomains = [...new Set(result.discoveredLinks.map(link => link.domain))]
      const metricsPromises = uniqueDomains.slice(0, 10).map(async domain => {
        try {
          const metrics = await domainAssessment.assessDomainAuthority(domain)
          return { domain, metrics }
        } catch (error) {
          console.error(`Failed to assess ${domain}:`, error)
          return null
        }
      })

      const assessments = await Promise.all(metricsPromises)
      const newMetrics: Record<string, DomainAuthorityMetrics> = {}
      assessments.forEach(assessment => {
        if (assessment) {
          newMetrics[assessment.domain] = assessment.metrics
        }
      })
      setDomainMetrics(newMetrics)

    } catch (error) {
      alert(`Discovery failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsDiscovering(false)
    }
  }

  const handleLinkSelection = (linkId: string, selected: boolean) => {
    const newSelected = new Set(selectedLinks)
    if (selected) {
      newSelected.add(linkId)
    } else {
      newSelected.delete(linkId)
    }
    setSelectedLinks(newSelected)
  }

  const getFilteredAndSortedLinks = () => {
    if (!discoveryResult) return []

    let filtered = discoveryResult.discoveredLinks

    // Apply type filter
    if (filterType !== 'all') {
      filtered = filtered.filter(link => link.sourceType === filterType)
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'authority':
          return b.authorityScore - a.authorityScore
        case 'relevance':
          return b.relevanceScore - a.relevanceScore
        case 'type':
          return a.sourceType.localeCompare(b.sourceType)
        default:
          return 0
      }
    })

    return filtered
  }

  const getSourceTypeIcon = (sourceType: string) => {
    switch (sourceType) {
      case 'wikipedia': return BookOpenIcon
      case 'academic': return AcademicCapIcon
      case 'government': return BuildingOfficeIcon
      case 'news': return NewspaperIcon
      case 'industry': return ChartBarIcon
      case 'reference': return DocumentTextIcon
      default: return LinkIcon
    }
  }

  const getSourceTypeColor = (sourceType: string) => {
    switch (sourceType) {
      case 'wikipedia': return 'text-blue-600 bg-blue-100'
      case 'academic': return 'text-purple-600 bg-purple-100'
      case 'government': return 'text-green-600 bg-green-100'
      case 'news': return 'text-orange-600 bg-orange-100'
      case 'industry': return 'text-indigo-600 bg-indigo-100'
      case 'reference': return 'text-gray-600 bg-gray-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getAuthorityScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-100'
    if (score >= 80) return 'text-blue-600 bg-blue-100'
    if (score >= 70) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  const exportLinks = () => {
    if (!discoveryResult) return

    const selectedLinkData = discoveryResult.discoveredLinks
      .filter(link => selectedLinks.has(link.id))
      .map(link => ({
        url: link.url,
        title: link.title,
        authorityScore: link.authorityScore,
        relevanceScore: link.relevanceScore,
        sourceType: link.sourceType,
        anchor: link.anchor,
        domain: link.domain
      }))

    const exportData = {
      topic: discoveryResult.topic,
      exportDate: new Date().toISOString(),
      totalLinks: selectedLinkData.length,
      links: selectedLinkData
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `authority-links-${discoveryResult.topic.replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  const tabs = [
    { id: 'discovery', label: 'Link Discovery', icon: MagnifyingGlassIcon },
    { id: 'wikipedia', label: 'Wikipedia', icon: BookOpenIcon },
    { id: 'academic', label: 'Academic', icon: AcademicCapIcon },
    { id: 'validation', label: 'Link Validation', icon: ShieldCheckIcon },
    { id: 'analysis', label: 'Domain Analysis', icon: ChartBarIcon }
  ]

  const sourceTypes = [
    { value: 'all', label: 'All Sources' },
    { value: 'wikipedia', label: 'Wikipedia' },
    { value: 'academic', label: 'Academic' },
    { value: 'government', label: 'Government' },
    { value: 'news', label: 'News' },
    { value: 'industry', label: 'Industry' },
    { value: 'reference', label: 'Reference' }
  ]

  return (
    <div className="space-y-6">
      {/* Discovery Configuration */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Authority Link Discovery</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Research Topic *
            </label>
            <input
              type="text"
              value={discoveryRequest.topic}
              onChange={(e) => setDiscoveryRequest(prev => ({ ...prev, topic: e.target.value }))}
              placeholder="Enter topic (e.g., 'artificial intelligence', 'climate change')"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isDiscovering}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Keywords
            </label>
            <input
              type="text"
              value={discoveryRequest.keywords.join(', ')}
              onChange={(e) => setDiscoveryRequest(prev => ({ 
                ...prev, 
                keywords: e.target.value.split(',').map(k => k.trim()).filter(Boolean) 
              }))}
              placeholder="machine learning, AI, neural networks"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isDiscovering}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Max Results
            </label>
            <select
              value={discoveryRequest.maxResults}
              onChange={(e) => setDiscoveryRequest(prev => ({ ...prev, maxResults: parseInt(e.target.value) }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isDiscovering}
            >
              <option value={10}>10 links</option>
              <option value={20}>20 links</option>
              <option value={50}>50 links</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Min Authority Score
            </label>
            <select
              value={discoveryRequest.minAuthorityScore}
              onChange={(e) => setDiscoveryRequest(prev => ({ ...prev, minAuthorityScore: parseInt(e.target.value) }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isDiscovering}
            >
              <option value={60}>60+ (Good)</option>
              <option value={70}>70+ (High)</option>
              <option value={80}>80+ (Excellent)</option>
              <option value={90}>90+ (Premium)</option>
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={handleDiscoverLinks}
              disabled={isDiscovering || !discoveryRequest.topic.trim()}
              className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white font-medium rounded-lg transition-colors flex items-center justify-center gap-2"
            >
              {isDiscovering ? (
                <>
                  <ArrowPathIcon className="h-4 w-4 animate-spin" />
                  Discovering...
                </>
              ) : (
                <>
                  <MagnifyingGlassIcon className="h-4 w-4" />
                  Discover Links
                </>
              )}
            </button>
          </div>
        </div>

        <div className="mt-4 flex flex-wrap gap-3">
          <label className="inline-flex items-center">
            <input
              type="checkbox"
              checked={discoveryRequest.includeWikipedia}
              onChange={(e) => setDiscoveryRequest(prev => ({ ...prev, includeWikipedia: e.target.checked }))}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              disabled={isDiscovering}
            />
            <span className="ml-2 text-sm text-gray-700">Include Wikipedia</span>
          </label>
          <label className="inline-flex items-center">
            <input
              type="checkbox"
              checked={discoveryRequest.includeReferences}
              onChange={(e) => setDiscoveryRequest(prev => ({ ...prev, includeReferences: e.target.checked }))}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              disabled={isDiscovering}
            />
            <span className="ml-2 text-sm text-gray-700">Include References</span>
          </label>
        </div>
      </div>

      {/* Discovery Progress */}
      {isDiscovering && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-center gap-3 mb-4">
            <ArrowPathIcon className="h-6 w-6 text-blue-600 animate-spin" />
            <span className="font-medium text-blue-900">Discovering authority links...</span>
          </div>
          <div className="space-y-2 text-sm text-blue-800">
            <div>• Searching Wikipedia articles and references</div>
            <div>• Analyzing academic and government sources</div>
            <div>• Assessing domain authority and trust scores</div>
            <div>• Validating link quality and relevance</div>
          </div>
        </div>
      )}

      {/* Results Section */}
      {discoveryResult && (
        <div className="space-y-6">
          {/* Summary Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <LinkIcon className="h-8 w-8 text-blue-600" />
                <div>
                  <div className="text-2xl font-bold text-gray-900">{discoveryResult.totalSources}</div>
                  <div className="text-sm text-gray-500">Total Links Found</div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <BookOpenIcon className="h-8 w-8 text-green-600" />
                <div>
                  <div className="text-2xl font-bold text-gray-900">{discoveryResult.wikipediaArticles.length}</div>
                  <div className="text-sm text-gray-500">Wikipedia Articles</div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <StarIcon className="h-8 w-8 text-yellow-600" />
                <div>
                  <div className="text-2xl font-bold text-gray-900">{discoveryResult.averageAuthorityScore}</div>
                  <div className="text-sm text-gray-500">Avg Authority Score</div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <ClockIcon className="h-8 w-8 text-purple-600" />
                <div>
                  <div className="text-2xl font-bold text-gray-900">{Math.round(discoveryResult.discoveryTime / 1000)}s</div>
                  <div className="text-sm text-gray-500">Discovery Time</div>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`
                      py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center gap-2
                      ${activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }
                    `}
                  >
                    <Icon className="h-4 w-4" />
                    {tab.label}
                  </button>
                )
              })}
            </nav>
          </div>

          {/* Tab Content */}
          {activeTab === 'discovery' && (
            <div className="space-y-6">
              {/* Controls */}
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <FunnelIcon className="h-4 w-4 text-gray-500" />
                      <select
                        value={filterType}
                        onChange={(e) => setFilterType(e.target.value)}
                        className="text-sm border border-gray-300 rounded px-2 py-1"
                      >
                        {sourceTypes.map(type => (
                          <option key={type.value} value={type.value}>{type.label}</option>
                        ))}
                      </select>
                    </div>

                    <div className="flex items-center gap-2">
                      <Bars3BottomLeftIcon className="h-4 w-4 text-gray-500" />
                      <select
                        value={sortBy}
                        onChange={(e) => setSortBy(e.target.value as any)}
                        className="text-sm border border-gray-300 rounded px-2 py-1"
                      >
                        <option value="authority">Authority Score</option>
                        <option value="relevance">Relevance</option>
                        <option value="type">Source Type</option>
                      </select>
                    </div>

                    <div className="flex items-center border border-gray-300 rounded">
                      <button
                        onClick={() => setViewMode('grid')}
                        className={`p-1 ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-500'}`}
                      >
                        <Bars3BottomLeftIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => setViewMode('table')}
                        className={`p-1 ${viewMode === 'table' ? 'bg-blue-100 text-blue-600' : 'text-gray-500'}`}
                      >
                        <TableCellsIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <span className="text-sm text-gray-600">
                      {selectedLinks.size} selected
                    </span>
                    <button
                      onClick={exportLinks}
                      disabled={selectedLinks.size === 0}
                      className="px-3 py-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-300 text-white text-sm rounded-lg transition-colors flex items-center gap-1"
                    >
                      <ShareIcon className="h-3 w-3" />
                      Export Selected
                    </button>
                  </div>
                </div>
              </div>

              {/* Links Display */}
              {viewMode === 'grid' ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {getFilteredAndSortedLinks().map((link) => {
                    const Icon = getSourceTypeIcon(link.sourceType)
                    return (
                      <div
                        key={link.id}
                        className={`
                          border-2 rounded-lg p-6 transition-all cursor-pointer
                          ${selectedLinks.has(link.id)
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300 bg-white'
                          }
                        `}
                        onClick={() => handleLinkSelection(link.id, !selectedLinks.has(link.id))}
                      >
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center gap-3">
                            <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getSourceTypeColor(link.sourceType)}`}>
                              <Icon className="h-5 w-5" />
                            </div>
                            <div>
                              <div className="font-medium text-gray-900">{link.domain}</div>
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSourceTypeColor(link.sourceType)}`}>
                                {link.sourceType}
                              </span>
                            </div>
                          </div>
                          
                          <input
                            type="checkbox"
                            checked={selectedLinks.has(link.id)}
                            onChange={() => {}}
                            className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          />
                        </div>

                        <h3 className="font-medium text-gray-900 mb-2 line-clamp-2">{link.title}</h3>
                        <p className="text-sm text-gray-600 mb-4 line-clamp-3">{link.description}</p>

                        <div className="grid grid-cols-2 gap-4 mb-4">
                          <div>
                            <div className="text-xs text-gray-500 mb-1">Authority Score</div>
                            <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getAuthorityScoreColor(link.authorityScore)}`}>
                              {link.authorityScore}/100
                            </div>
                          </div>
                          <div>
                            <div className="text-xs text-gray-500 mb-1">Relevance</div>
                            <div className="text-xs font-medium text-gray-900">
                              {Math.round(link.relevanceScore * 100)}%
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500 truncate flex-1">
                            {link.anchor}
                          </span>
                          <ArrowTopRightOnSquareIcon className="h-4 w-4 text-gray-400 ml-2" />
                        </div>
                      </div>
                    )
                  })}
                </div>
              ) : (
                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          <input
                            type="checkbox"
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedLinks(new Set(getFilteredAndSortedLinks().map(l => l.id)))
                              } else {
                                setSelectedLinks(new Set())
                              }
                            }}
                          />
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Source</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Authority</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Relevance</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {getFilteredAndSortedLinks().map((link) => {
                        const Icon = getSourceTypeIcon(link.sourceType)
                        return (
                          <tr key={link.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <input
                                type="checkbox"
                                checked={selectedLinks.has(link.id)}
                                onChange={(e) => handleLinkSelection(link.id, e.target.checked)}
                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center gap-2">
                                <Icon className="h-4 w-4 text-gray-500" />
                                <span className="text-sm text-gray-900">{link.domain}</span>
                              </div>
                            </td>
                            <td className="px-6 py-4">
                              <div className="text-sm text-gray-900 line-clamp-2">{link.title}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getAuthorityScoreColor(link.authorityScore)}`}>
                                {link.authorityScore}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className="text-sm text-gray-900">{Math.round(link.relevanceScore * 100)}%</span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSourceTypeColor(link.sourceType)}`}>
                                {link.sourceType}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <button
                                onClick={() => window.open(link.url, '_blank')}
                                className="text-blue-600 hover:text-blue-800"
                              >
                                <ArrowTopRightOnSquareIcon className="h-4 w-4" />
                              </button>
                            </td>
                          </tr>
                        )
                      })}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}

          {/* Wikipedia Tab */}
          {activeTab === 'wikipedia' && (
            <div className="space-y-6">
              {discoveryResult.wikipediaArticles.length === 0 ? (
                <div className="text-center py-12">
                  <BookOpenIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Wikipedia Articles Found</h3>
                  <p className="text-gray-600">
                    Try adjusting your topic or keywords to find relevant Wikipedia articles
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {discoveryResult.wikipediaArticles.map((article, index) => (
                    <div key={index} className="bg-white border border-gray-200 rounded-lg p-6">
                      <div className="flex items-start gap-4">
                        <div className="bg-blue-100 p-3 rounded-lg">
                          <BookOpenIcon className="h-6 w-6 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900 mb-2">{article.title}</h3>
                          <p className="text-sm text-gray-600 mb-4 line-clamp-4">{article.extract}</p>
                          
                          <div className="grid grid-cols-2 gap-4 mb-4">
                            <div>
                              <div className="text-xs text-gray-500 mb-1">Quality Score</div>
                              <div className="text-sm font-medium text-gray-900">{article.qualityScore}/100</div>
                            </div>
                            <div>
                              <div className="text-xs text-gray-500 mb-1">View Count</div>
                              <div className="text-sm font-medium text-gray-900">{article.viewCount.toLocaleString()}</div>
                            </div>
                          </div>
                          
                          <button
                            onClick={() => window.open(article.url, '_blank')}
                            className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 text-sm font-medium"
                          >
                            View Article
                            <ArrowTopRightOnSquareIcon className="h-3 w-3" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Other tabs would be implemented similarly */}
          {activeTab === 'academic' && (
            <div className="text-center py-12">
              <AcademicCapIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Academic Sources Analysis</h3>
              <p className="text-gray-600">
                Academic source filtering and analysis features coming soon
              </p>
            </div>
          )}

          {/* Suggestions */}
          {discoveryResult.suggestions.length > 0 && (
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-6">
              <div className="flex items-start gap-3">
                <BoltIcon className="h-6 w-6 text-amber-600 mt-0.5" />
                <div>
                  <h3 className="font-medium text-amber-900 mb-2">Discovery Suggestions</h3>
                  <ul className="space-y-1 text-sm text-amber-800">
                    {discoveryResult.suggestions.map((suggestion, index) => (
                      <li key={index}>• {suggestion}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}