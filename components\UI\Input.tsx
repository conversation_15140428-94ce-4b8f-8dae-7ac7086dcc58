'use client';

import React from 'react';
import { ComponentProps, forwardRef } from 'react';
import { componentTokens } from '@/lib/design-system';

interface InputProps extends ComponentProps<'input'> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  variant?: 'default' | 'filled';
  fullWidth?: boolean;
}

const Input = forwardRef<HTMLInputElement, InputProps>(({
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  variant = 'default',
  fullWidth = true,
  className = '',
  id,
  ...props
}, ref) => {
  
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
  const hasError = !!error;
  
  // Base input classes
  const baseInputClasses = [
    'block rounded-lg border transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-offset-0',
    'disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed',
    'placeholder:text-gray-500 dark:placeholder:text-gray-400',
    fullWidth ? 'w-full' : '',
  ].filter(Boolean).join(' ');

  // Variant styles
  const variantClasses = {
    default: [
      'bg-white dark:bg-gray-800',
      'border-gray-300 dark:border-gray-600',
      'text-gray-900 dark:text-gray-100',
      'focus:border-blue-500 dark:focus:border-blue-400',
      'focus:ring-blue-500/20 dark:focus:ring-blue-400/20'
    ].join(' '),
    
    filled: [
      'bg-gray-50 dark:bg-gray-700',
      'border-gray-200 dark:border-gray-600',
      'text-gray-900 dark:text-gray-100',
      'focus:bg-white dark:focus:bg-gray-800',
      'focus:border-blue-500 dark:focus:border-blue-400',
      'focus:ring-blue-500/20 dark:focus:ring-blue-400/20'
    ].join(' ')
  };

  // Error state classes
  const errorClasses = hasError ? [
    'border-red-500 dark:border-red-400',
    'focus:border-red-500 dark:focus:border-red-400',
    'focus:ring-red-500/20 dark:focus:ring-red-400/20'
  ].join(' ') : '';

  // Padding classes based on icons
  const paddingClasses = (() => {
    if (leftIcon && rightIcon) return 'pl-10 pr-10 py-3';
    if (leftIcon) return 'pl-10 pr-4 py-3';
    if (rightIcon) return 'pl-4 pr-10 py-3';
    return 'px-4 py-3';
  })();

  const inputClassName = [
    baseInputClasses,
    variantClasses[variant],
    errorClasses,
    paddingClasses,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={fullWidth ? 'w-full' : 'inline-block'}>
      {/* Label */}
      {label && (
        <label 
          htmlFor={inputId}
          className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
        >
          {label}
        </label>
      )}
      
      {/* Input container */}
      <div className="relative">
        {/* Left icon */}
        {leftIcon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span className="h-5 w-5 text-gray-400 dark:text-gray-500">
              {leftIcon}
            </span>
          </div>
        )}
        
        {/* Input field */}
        <input
          ref={ref}
          id={inputId}
          className={inputClassName}
          {...props}
        />
        
        {/* Right icon */}
        {rightIcon && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <span className="h-5 w-5 text-gray-400 dark:text-gray-500">
              {rightIcon}
            </span>
          </div>
        )}
      </div>
      
      {/* Error message */}
      {error && (
        <p className="mt-2 text-sm text-red-600 dark:text-red-400">
          {error}
        </p>
      )}
      
      {/* Helper text */}
      {helperText && !error && (
        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
          {helperText}
        </p>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;