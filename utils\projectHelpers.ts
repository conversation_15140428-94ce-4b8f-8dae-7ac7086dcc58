/**
 * Project Helper Functions
 * Utilities for project data manipulation, validation, and analysis
 */

import { 
  Project, 
  ProjectStatus, 
  ProjectKeyword, 
  CompetitorAnalysis, 
  ProjectMetrics,
  CreateProjectData,
  ProjectValidation,
  KeywordValidation,
  CompetitorValidation,
  IndustryType
} from '@/types/project';

import { DemoDataValidator, ValidationResult } from '@/utils/demoDataDetection';

/**
 * Generate a unique project ID
 */
export function generateProjectId(): string {
  return `proj_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Generate a unique keyword ID
 */
export function generateKeywordId(): string {
  return `kw_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Generate a unique competitor ID
 */
export function generateCompetitorId(): string {
  return `comp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Validate project name - must be real, not demo data
 * Uses comprehensive DemoDataValidator for enterprise-grade validation
 */
export function validateProjectName(name: string): { isValid: boolean; error?: string } {
  // Additional project-specific patterns
  const projectSpecificPatterns = [
    /untitled/i,
    /project\s*\d+$/i,
    /new\s*project/i,
    /unnamed\s*project/i
  ];

  // Use DemoDataValidator with project-specific patterns
  const validationResult = DemoDataValidator.validate(name, {
    strict: true,
    customPatterns: projectSpecificPatterns,
    field: 'Project name'
  });

  // Handle length validation separately for specific error messages
  if (name && name.trim().length > 100) {
    return { isValid: false, error: 'Project name must be less than 100 characters' };
  }

  // Return validation result
  if (!validationResult.isValid) {
    return { 
      isValid: false, 
      error: validationResult.errors[0] || 'Invalid project name'
    };
  }

  return { isValid: true };
}

/**
 * Validate keyword - must be real search term, not demo data
 * Uses comprehensive DemoDataValidator for enterprise-grade validation
 */
export function validateKeyword(keyword: string): KeywordValidation {
  const result: KeywordValidation = {
    keyword: keyword.trim(),
    isValid: true,
    errors: [],
    warnings: [],
    suggestions: []
  };

  // Use DemoDataValidator for comprehensive validation
  const validationResult = DemoDataValidator.validate(keyword, {
    strict: true,
    field: 'Keyword'
  });

  // Handle length validation separately for specific error messages
  if (keyword && keyword.trim().length > 100) {
    result.isValid = false;
    result.errors.push('Keyword must be less than 100 characters');
    return result;
  }

  // Apply validation results
  if (!validationResult.isValid) {
    result.isValid = false;
    result.errors.push(...validationResult.errors);
    return result;
  }

  // Add warnings from validation
  result.warnings.push(...validationResult.warnings);

  // Additional keyword-specific quality checks
  if (keyword.split(' ').length > 10) {
    result.warnings.push('Very long keywords may have limited search volume');
  }

  if (/^\d+$/.test(keyword)) {
    result.warnings.push('Numeric-only keywords may not be effective for SEO');
  }

  // Suggest improvements
  if (keyword === keyword.toUpperCase()) {
    result.suggestions.push('Consider using normal case instead of all caps');
  }

  if (keyword.includes('_') || keyword.includes('-')) {
    result.suggestions.push('Consider using spaces instead of underscores or hyphens');
  }

  // Check for single-word keywords
  if (keyword.split(' ').length === 1 && keyword.length > 15) {
    result.suggestions.push('Consider breaking long single words into phrases for better targeting');
  }

  return result;
}

/**
 * Validate competitor URL - must be real website, not demo data
 * Uses comprehensive DemoDataValidator for enterprise-grade validation
 */
export function validateCompetitorUrl(url: string): CompetitorValidation {
  const result: CompetitorValidation = {
    url: url.trim(),
    domain: '',
    isValid: true,
    errors: [],
    warnings: [],
    suggestions: [],
    isAccessible: false,
    hasContent: false,
    isDemoSite: false
  };

  // Use DemoDataValidator for comprehensive URL validation
  const validationResult = DemoDataValidator.validateURL(url, {
    strict: true,
    field: 'Competitor URL'
  });

  // Apply validation results
  if (!validationResult.isValid) {
    result.isValid = false;
    result.isDemoSite = validationResult.isDemo;
    result.errors.push(...validationResult.errors);
    return result;
  }

  // Add warnings from validation
  result.warnings.push(...validationResult.warnings);

  // Basic URL validation and domain extraction
  try {
    const urlObj = new URL(url);
    result.domain = urlObj.hostname;

    // Protocol validation
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      result.isValid = false;
      result.errors.push('URL must use HTTP or HTTPS protocol');
      return result;
    }

    // Domain validation
    if (urlObj.hostname.split('.').length < 2) {
      result.isValid = false;
      result.errors.push('URL must have a valid domain name');
      return result;
    }

    // Additional quality checks
    if (!urlObj.protocol.includes('https')) {
      result.warnings.push('Consider HTTPS URLs for better security and SEO benefits');
    }

    // Check for suspicious TLDs
    const suspiciousTlds = ['.tk', '.ml', '.ga', '.cf'];
    if (suspiciousTlds.some(tld => urlObj.hostname.endsWith(tld))) {
      result.warnings.push('Free domain TLD detected - may have lower authority');
    }

  } catch (error) {
    result.isValid = false;
    result.errors.push('Invalid URL format');
    return result;
  }

  return result;
}

/**
 * Batch validate keywords using DemoDataValidator
 */
export function validateKeywordsBatch(keywords: string[]): {
  valid: KeywordValidation[];
  invalid: KeywordValidation[];
  summary: {
    totalChecked: number;
    validCount: number;
    demoCount: number;
    errorCount: number;
  };
} {
  const valid: KeywordValidation[] = [];
  const invalid: KeywordValidation[] = [];
  let demoCount = 0;
  let errorCount = 0;

  keywords.forEach(keyword => {
    const validation = validateKeyword(keyword);
    
    if (validation.isValid) {
      valid.push(validation);
    } else {
      invalid.push(validation);
      errorCount += validation.errors.length;
      
      // Check if it's demo data
      const demoValidation = DemoDataValidator.validate(keyword, { strict: true });
      if (demoValidation.isDemo) {
        demoCount++;
      }
    }
  });

  return {
    valid,
    invalid,
    summary: {
      totalChecked: keywords.length,
      validCount: valid.length,
      demoCount,
      errorCount
    }
  };
}

/**
 * Batch validate competitor URLs using DemoDataValidator
 */
export function validateCompetitorsBatch(urls: string[]): {
  valid: CompetitorValidation[];
  invalid: CompetitorValidation[];
  summary: {
    totalChecked: number;
    validCount: number;
    demoCount: number;
    errorCount: number;
  };
} {
  const valid: CompetitorValidation[] = [];
  const invalid: CompetitorValidation[] = [];
  let demoCount = 0;
  let errorCount = 0;

  urls.forEach(url => {
    const validation = validateCompetitorUrl(url);
    
    if (validation.isValid) {
      valid.push(validation);
    } else {
      invalid.push(validation);
      errorCount += validation.errors.length;
      
      if (validation.isDemoSite) {
        demoCount++;
      }
    }
  });

  return {
    valid,
    invalid,
    summary: {
      totalChecked: urls.length,
      validCount: valid.length,
      demoCount,
      errorCount
    }
  };
}

/**
 * Calculate project metrics from content and keyword data
 */
export function calculateProjectMetrics(project: Partial<Project>): ProjectMetrics {
  const metrics: ProjectMetrics = {
    totalContent: 0,
    totalWords: 0,
    averageSeoScore: 0,
    averageWordCount: 0,
    keywordsTracked: project.keywords?.length || 0,
    competitorsAnalyzed: project.competitors?.length || 0,
    contentByType: {
      blog_post: 0,
      product_description: 0,
      landing_page: 0,
      meta_description: 0,
      social_media: 0,
      email_marketing: 0
    }
  };

  // These would be calculated from actual content data in real implementation
  // For now, we return the basic structure

  return metrics;
}

/**
 * Format project status for display
 */
export function formatProjectStatus(status: ProjectStatus): { label: string; color: string; bgColor: string } {
  const statusConfig = {
    draft: { label: 'Draft', color: 'text-gray-600', bgColor: 'bg-gray-100' },
    active: { label: 'Active', color: 'text-green-600', bgColor: 'bg-green-100' },
    paused: { label: 'Paused', color: 'text-yellow-600', bgColor: 'bg-yellow-100' },
    completed: { label: 'Completed', color: 'text-blue-600', bgColor: 'bg-blue-100' },
    archived: { label: 'Archived', color: 'text-gray-500', bgColor: 'bg-gray-50' }
  };

  return statusConfig[status] || statusConfig.draft;
}

/**
 * Get industry display name and icon
 */
export function getIndustryInfo(industry: IndustryType): { label: string; icon: string; color: string } {
  const industryConfig: Record<IndustryType, { label: string; icon: string; color: string }> = {
    technology: { label: 'Technology', icon: '💻', color: 'text-blue-600' },
    healthcare: { label: 'Healthcare', icon: '🏥', color: 'text-red-600' },
    finance: { label: 'Finance', icon: '💰', color: 'text-green-600' },
    education: { label: 'Education', icon: '🎓', color: 'text-purple-600' },
    ecommerce: { label: 'E-commerce', icon: '🛒', color: 'text-orange-600' },
    real_estate: { label: 'Real Estate', icon: '🏠', color: 'text-indigo-600' },
    automotive: { label: 'Automotive', icon: '🚗', color: 'text-gray-600' },
    food_beverage: { label: 'Food & Beverage', icon: '🍽️', color: 'text-yellow-600' },
    fashion: { label: 'Fashion', icon: '👗', color: 'text-pink-600' },
    travel: { label: 'Travel', icon: '✈️', color: 'text-sky-600' },
    fitness: { label: 'Fitness', icon: '💪', color: 'text-green-500' },
    marketing: { label: 'Marketing', icon: '📈', color: 'text-blue-500' },
    consulting: { label: 'Consulting', icon: '💼', color: 'text-gray-700' },
    manufacturing: { label: 'Manufacturing', icon: '🏭', color: 'text-slate-600' },
    retail: { label: 'Retail', icon: '🏪', color: 'text-emerald-600' },
    construction: { label: 'Construction', icon: '🏗️', color: 'text-amber-600' },
    legal: { label: 'Legal', icon: '⚖️', color: 'text-gray-800' },
    entertainment: { label: 'Entertainment', icon: '🎬', color: 'text-purple-500' },
    sports: { label: 'Sports', icon: '⚽', color: 'text-green-600' },
    nonprofit: { label: 'Non-profit', icon: '❤️', color: 'text-red-500' },
    other: { label: 'Other', icon: '📂', color: 'text-gray-500' }
  };

  return industryConfig[industry] || industryConfig.other;
}

/**
 * Generate default project settings
 */
export function getDefaultProjectSettings(): Required<CreateProjectData>['settings'] {
  return {
    defaultContentType: 'blog_post',
    defaultWordCount: 1500,
    defaultTone: 'professional',
    includeInternalLinks: true,
    includeExternalLinks: true,
    includeFAQs: true,
    includeSchemaMarkup: true
  };
}

/**
 * Sort projects by specified criteria
 */
export function sortProjects(projects: Project[], sortBy: string, sortOrder: 'asc' | 'desc'): Project[] {
  return [...projects].sort((a, b) => {
    let aVal: any;
    let bVal: any;

    switch (sortBy) {
      case 'name':
        aVal = a.name.toLowerCase();
        bVal = b.name.toLowerCase();
        break;
      case 'createdAt':
        aVal = new Date(a.createdAt);
        bVal = new Date(b.createdAt);
        break;
      case 'updatedAt':
        aVal = new Date(a.updatedAt);
        bVal = new Date(b.updatedAt);
        break;
      case 'status':
        aVal = a.status;
        bVal = b.status;
        break;
      case 'metrics.totalContent':
        aVal = a.metrics.totalContent;
        bVal = b.metrics.totalContent;
        break;
      default:
        aVal = a.createdAt;
        bVal = b.createdAt;
    }

    if (aVal < bVal) return sortOrder === 'asc' ? -1 : 1;
    if (aVal > bVal) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  });
}

/**
 * Filter projects based on criteria
 */
export function filterProjects(projects: Project[], filters: any): Project[] {
  return projects.filter(project => {
    // Status filter
    if (filters.status && filters.status.length > 0) {
      if (!filters.status.includes(project.status)) return false;
    }

    // Industry filter
    if (filters.industry && filters.industry.length > 0) {
      if (!filters.industry.includes(project.industry)) return false;
    }

    // Search filter
    if (filters.search && filters.search.trim()) {
      const searchTerm = filters.search.toLowerCase();
      const projectText = `${project.name} ${project.description || ''} ${project.keywords.map(k => k.keyword).join(' ')}`.toLowerCase();
      if (!projectText.includes(searchTerm)) return false;
    }

    return true;
  });
}