import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

/**
 * Project Database Service
 * Handles all database operations for projects
 */
class ProjectService {
  /**
   * Get all projects for a user
   */
  async getUserProjects(userId, options = {}) {
    try {
      const { limit = 50, offset = 0, sortBy = 'created_at', sortOrder = 'desc' } = options;
      
      let query = supabase
        .from('projects')
        .select('*')
        .eq('user_id', userId)
        .order(sortBy, { ascending: sortOrder === 'asc' });

      if (limit) query = query.limit(limit);
      if (offset) query = query.range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      if (error) {
        throw new Error(`Failed to fetch projects: ${error.message}`);
      }

      return {
        success: true,
        data,
        count,
        pagination: {
          limit,
          offset,
          total: count
        }
      };
    } catch (error) {
      console.error('ProjectService.getUserProjects error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get a specific project by ID
   */
  async getProjectById(projectId, userId) {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('id', projectId)
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          throw new Error('Project not found');
        }
        throw new Error(`Failed to fetch project: ${error.message}`);
      }

      return {
        success: true,
        data
      };
    } catch (error) {
      console.error('ProjectService.getProjectById error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create a new project
   */
  async createProject(userId, projectData) {
    try {
      const {
        name,
        description,
        website_url,
        target_keywords,
        location,
        industry,
        status = 'active'
      } = projectData;

      const newProject = {
        user_id: userId,
        name,
        description,
        website_url,
        target_keywords: target_keywords || [],
        location,
        industry,
        status,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('projects')
        .insert(newProject)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to create project: ${error.message}`);
      }

      return {
        success: true,
        data
      };
    } catch (error) {
      console.error('ProjectService.createProject error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update a project
   */
  async updateProject(projectId, userId, updateData) {
    try {
      // First verify project exists and belongs to user
      const existingProject = await this.getProjectById(projectId, userId);
      if (!existingProject.success) {
        throw new Error('Project not found or access denied');
      }

      const updatedData = {
        ...updateData,
        updated_at: new Date().toISOString()
      };

      // Remove undefined values
      Object.keys(updatedData).forEach(key => {
        if (updatedData[key] === undefined) {
          delete updatedData[key];
        }
      });

      const { data, error } = await supabase
        .from('projects')
        .update(updatedData)
        .eq('id', projectId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to update project: ${error.message}`);
      }

      return {
        success: true,
        data
      };
    } catch (error) {
      console.error('ProjectService.updateProject error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Delete a project
   */
  async deleteProject(projectId, userId) {
    try {
      // First verify project exists and belongs to user
      const existingProject = await this.getProjectById(projectId, userId);
      if (!existingProject.success) {
        throw new Error('Project not found or access denied');
      }

      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', projectId)
        .eq('user_id', userId);

      if (error) {
        throw new Error(`Failed to delete project: ${error.message}`);
      }

      return {
        success: true,
        message: 'Project deleted successfully'
      };
    } catch (error) {
      console.error('ProjectService.deleteProject error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get project statistics
   */
  async getProjectStats(projectId, userId) {
    try {
      // Verify project exists and belongs to user
      const existingProject = await this.getProjectById(projectId, userId);
      if (!existingProject.success) {
        throw new Error('Project not found or access denied');
      }

      // Get content statistics
      const { data: contentData, error: contentError } = await supabase
        .from('generated_content')
        .select('id, content_type, word_count, seo_score, created_at')
        .eq('project_id', projectId)
        .eq('user_id', userId);

      if (contentError) {
        console.error('Content stats error:', contentError);
      }

      const content = contentData || [];
      const totalContent = content.length;
      const avgWordCount = content.length > 0 
        ? Math.round(content.reduce((sum, item) => sum + (item.word_count || 0), 0) / content.length)
        : 0;
      const avgSeoScore = content.length > 0 
        ? Math.round(content.reduce((sum, item) => sum + (item.seo_score || 0), 0) / content.length)
        : 0;

      // Content by type
      const contentByType = content.reduce((acc, item) => {
        acc[item.content_type] = (acc[item.content_type] || 0) + 1;
        return acc;
      }, {});

      // Recent activity (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const recentContent = content.filter(item => 
        new Date(item.created_at) > thirtyDaysAgo
      );

      return {
        success: true,
        data: {
          project: existingProject.data,
          stats: {
            totalContent,
            avgWordCount,
            avgSeoScore,
            contentByType,
            recentActivity: recentContent.length
          },
          timeline: {
            last30Days: recentContent.length,
            totalDays: Math.ceil((new Date() - new Date(existingProject.data.created_at)) / (1000 * 60 * 60 * 24))
          }
        }
      };
    } catch (error) {
      console.error('ProjectService.getProjectStats error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get project content
   */
  async getProjectContent(projectId, userId, options = {}) {
    try {
      // Verify project exists and belongs to user
      const existingProject = await this.getProjectById(projectId, userId);
      if (!existingProject.success) {
        throw new Error('Project not found or access denied');
      }

      const { limit = 20, offset = 0, contentType = null, sortBy = 'created_at', sortOrder = 'desc' } = options;

      let query = supabase
        .from('generated_content')
        .select('*')
        .eq('project_id', projectId)
        .eq('user_id', userId)
        .order(sortBy, { ascending: sortOrder === 'asc' });

      if (contentType) {
        query = query.eq('content_type', contentType);
      }

      if (limit) query = query.limit(limit);
      if (offset) query = query.range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      if (error) {
        throw new Error(`Failed to fetch project content: ${error.message}`);
      }

      return {
        success: true,
        data,
        count,
        pagination: {
          limit,
          offset,
          total: count
        }
      };
    } catch (error) {
      console.error('ProjectService.getProjectContent error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Search projects
   */
  async searchProjects(userId, searchTerm, options = {}) {
    try {
      const { limit = 20, offset = 0 } = options;

      let query = supabase
        .from('projects')
        .select('*')
        .eq('user_id', userId)
        .or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,industry.ilike.%${searchTerm}%`)
        .order('created_at', { ascending: false });

      if (limit) query = query.limit(limit);
      if (offset) query = query.range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      if (error) {
        throw new Error(`Failed to search projects: ${error.message}`);
      }

      return {
        success: true,
        data,
        count,
        searchTerm,
        pagination: {
          limit,
          offset,
          total: count
        }
      };
    } catch (error) {
      console.error('ProjectService.searchProjects error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get project analytics
   */
  async getProjectAnalytics(projectId, userId, dateRange = 30) {
    try {
      // Verify project exists and belongs to user
      const existingProject = await this.getProjectById(projectId, userId);
      if (!existingProject.success) {
        throw new Error('Project not found or access denied');
      }

      const startDate = new Date();
      startDate.setDate(startDate.getDate() - dateRange);

      // Get content creation over time
      const { data: contentTimeline, error: timelineError } = await supabase
        .from('generated_content')
        .select('created_at, content_type, word_count, seo_score')
        .eq('project_id', projectId)
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: true });

      if (timelineError) {
        console.error('Content timeline error:', timelineError);
      }

      // Group by date
      const timeline = {};
      (contentTimeline || []).forEach(item => {
        const date = new Date(item.created_at).toISOString().split('T')[0];
        if (!timeline[date]) {
          timeline[date] = {
            date,
            content_count: 0,
            total_words: 0,
            avg_seo_score: 0,
            content_types: {}
          };
        }
        timeline[date].content_count++;
        timeline[date].total_words += item.word_count || 0;
        timeline[date].avg_seo_score = (timeline[date].avg_seo_score + (item.seo_score || 0)) / 2;
        timeline[date].content_types[item.content_type] = (timeline[date].content_types[item.content_type] || 0) + 1;
      });

      return {
        success: true,
        data: {
          project: existingProject.data,
          analytics: {
            dateRange,
            timeline: Object.values(timeline),
            summary: {
              totalContent: contentTimeline?.length || 0,
              totalWords: (contentTimeline || []).reduce((sum, item) => sum + (item.word_count || 0), 0),
              avgSeoScore: contentTimeline?.length > 0 
                ? Math.round((contentTimeline || []).reduce((sum, item) => sum + (item.seo_score || 0), 0) / contentTimeline.length)
                : 0
            }
          }
        }
      };
    } catch (error) {
      console.error('ProjectService.getProjectAnalytics error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

export default new ProjectService();