/**
 * ScrollReveal Component
 * Reveals content with animations when scrolled into view
 */

import React from 'react';
import { AnimationType, STAGGER_CONFIG } from '@/lib/animations/constants';
import { useInView, useStaggerAnimation } from '@/lib/animations/hooks';
import { AnimationWrapper } from './AnimationWrapper';
import { cn } from '@/lib/utils';

interface ScrollRevealProps {
  children: React.ReactNode;
  animation?: AnimationType;
  threshold?: number;
  stagger?: boolean;
  staggerDelay?: keyof typeof STAGGER_CONFIG | number;
  className?: string;
  once?: boolean;
}

export const ScrollReveal: React.FC<ScrollRevealProps> = ({
  children,
  animation = AnimationType.FADE_IN,
  threshold = 0.1,
  stagger = false,
  staggerDelay = 'NORMAL',
  className,
  once = true
}) => {
  const { ref, hasBeenInView, isInView } = useInView({ threshold });
  const { staggerAnimate } = useStaggerAnimation();
  const shouldAnimate = once ? hasBeenInView : isInView;

  // Handle staggered children
  if (stagger && React.Children.count(children) > 1) {
    const delay = typeof staggerDelay === 'string' 
      ? STAGGER_CONFIG[staggerDelay] 
      : staggerDelay;

    return (
      <div ref={ref} className={cn('space-y-4', className)}>
        {React.Children.map(children, (child, index) => (
          <AnimationWrapper
            animation={animation}
            delay={shouldAnimate ? index * delay : 0}
            triggerOnScroll={false}
            className={!shouldAnimate ? 'opacity-0' : ''}
          >
            {child}
          </AnimationWrapper>
        ))}
      </div>
    );
  }

  // Single child animation
  return (
    <div ref={ref} className={className}>
      {shouldAnimate ? (
        <AnimationWrapper animation={animation}>
          {children}
        </AnimationWrapper>
      ) : (
        <div className="opacity-0">{children}</div>
      )}
    </div>
  );
};