'use client';

import React, { useState, useEffect } from 'react';
import { ProtectedRoute } from '@/hooks/useAuth';
import { AuthenticatedLayout } from '@/components/Layout/DashboardLayout';
import {
  ChartBarIcon,
  DocumentTextIcon,
  TrendingUpIcon,
  ClockIcon,
  CalendarIcon,
  FunnelIcon,
  ArrowDownTrayIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

interface AnalyticsData {
  summary: {
    totalContent: number;
    totalWords: number;
    averageWords: number;
    averageSeoScore: number;
    timeframe: string;
  };
  performanceByType: Record<string, {
    count: number;
    averageScore: number;
    averageWords: number;
  }>;
  topPerforming: Array<{
    id: string;
    title: string;
    target_keyword: string;
    seo_score: number;
    word_count: number;
    created_at: string;
    project_name?: string;
  }>;
  filters: {
    industry: string | null;
    project_id: string | null;
    timeframe: string;
  };
  generatedAt: string;
}

export default function AnalyticsPage() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState('30d');
  const [selectedIndustry, setSelectedIndustry] = useState<string>('');
  const [selectedProject, setSelectedProject] = useState<string>('');

  // Load analytics data
  useEffect(() => {
    loadAnalyticsData();
  }, [timeframe, selectedIndustry, selectedProject]);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      
      // Import analytics API client
      const { analyticsAPI } = await import('@/lib/api-client');
      
      // Build query parameters
      const params = new URLSearchParams();
      if (timeframe) params.append('timeframe', timeframe);
      if (selectedIndustry) params.append('industry', selectedIndustry);
      if (selectedProject) params.append('project_id', selectedProject);
      
      // Fetch content metrics
      const response = await analyticsAPI.getContentMetrics();
      
      if (response.success) {
        setAnalyticsData(response.data);
      } else {
        console.error('Failed to fetch analytics data:', response.error);
        setFallbackData();
      }
    } catch (error) {
      console.error('Analytics data fetch error:', error);
      setFallbackData();
    } finally {
      setLoading(false);
    }
  };

  const setFallbackData = () => {
    // Fallback data when API is not available
    setAnalyticsData({
      summary: {
        totalContent: 0,
        totalWords: 0,
        averageWords: 0,
        averageSeoScore: 0,
        timeframe: timeframe
      },
      performanceByType: {},
      topPerforming: [],
      filters: {
        industry: selectedIndustry || null,
        project_id: selectedProject || null,
        timeframe: timeframe
      },
      generatedAt: new Date().toISOString()
    });
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <ProtectedRoute>
      <AuthenticatedLayout>
        <div className="space-y-6">
          {/* Page Header */}
          <div className="border-b border-gray-200 pb-5">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Analytics & Reports</h1>
                <p className="mt-1 text-sm text-gray-500">
                  Track your content performance and SEO metrics
                </p>
              </div>
              
              <div className="flex space-x-3">
                <button className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                  <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                  Export
                </button>
                <button className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                  <FunnelIcon className="h-4 w-4 mr-2" />
                  Filters
                </button>
              </div>
            </div>

            {/* Filters */}
            <div className="mt-4 flex space-x-4">
              <select
                value={timeframe}
                onChange={(e) => setTimeframe(e.target.value)}
                className="block w-40 rounded-md border-gray-300 text-sm focus:border-blue-500 focus:ring-blue-500"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
                <option value="1y">Last year</option>
              </select>
              
              <select
                value={selectedIndustry}
                onChange={(e) => setSelectedIndustry(e.target.value)}
                className="block w-40 rounded-md border-gray-300 text-sm focus:border-blue-500 focus:ring-blue-500"
              >
                <option value="">All Industries</option>
                <option value="technology">Technology</option>
                <option value="healthcare">Healthcare</option>
                <option value="finance">Finance</option>
                <option value="education">Education</option>
                <option value="ecommerce">E-commerce</option>
              </select>
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <>
              {/* Summary Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <DocumentTextIcon className="h-6 w-6 text-gray-400" />
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 truncate">
                            Total Content
                          </dt>
                          <dd className="text-lg font-medium text-gray-900">
                            {analyticsData?.summary.totalContent || 0}
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <ChartBarIcon className="h-6 w-6 text-gray-400" />
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 truncate">
                            Total Words
                          </dt>
                          <dd className="text-lg font-medium text-gray-900">
                            {formatNumber(analyticsData?.summary.totalWords || 0)}
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <TrendingUpIcon className="h-6 w-6 text-gray-400" />
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 truncate">
                            Avg SEO Score
                          </dt>
                          <dd className="text-lg font-medium text-gray-900">
                            {analyticsData?.summary.averageSeoScore || 0}
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <ClockIcon className="h-6 w-6 text-gray-400" />
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 truncate">
                            Avg Words/Content
                          </dt>
                          <dd className="text-lg font-medium text-gray-900">
                            {analyticsData?.summary.averageWords || 0}
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Performance by Content Type */}
              {analyticsData?.performanceByType && Object.keys(analyticsData.performanceByType).length > 0 && (
                <div className="bg-white shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                      Performance by Content Type
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {Object.entries(analyticsData.performanceByType).map(([type, data]) => (
                        <div key={type} className="border rounded-lg p-4">
                          <h4 className="font-medium text-gray-900 capitalize">
                            {type.replace('_', ' ')}
                          </h4>
                          <div className="mt-2 space-y-1">
                            <div className="text-sm text-gray-500">
                              Count: <span className="font-medium text-gray-900">{data.count}</span>
                            </div>
                            <div className="text-sm text-gray-500">
                              Avg Score: <span className="font-medium text-gray-900">{data.averageScore}</span>
                            </div>
                            <div className="text-sm text-gray-500">
                              Avg Words: <span className="font-medium text-gray-900">{data.averageWords}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Top Performing Content */}
              {analyticsData?.topPerforming && analyticsData.topPerforming.length > 0 && (
                <div className="bg-white shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                      Top Performing Content
                    </h3>
                    <div className="overflow-hidden">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Content
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              SEO Score
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Words
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Created
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Actions
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {analyticsData.topPerforming.map((content) => (
                            <tr key={content.id}>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div>
                                  <div className="text-sm font-medium text-gray-900">
                                    {content.title}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    {content.target_keyword}
                                  </div>
                                  {content.project_name && (
                                    <div className="text-xs text-gray-400">
                                      Project: {content.project_name}
                                    </div>
                                  )}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  content.seo_score >= 90 
                                    ? 'bg-green-100 text-green-800'
                                    : content.seo_score >= 70
                                    ? 'bg-yellow-100 text-yellow-800'
                                    : 'bg-red-100 text-red-800'
                                }`}>
                                  {content.seo_score}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {formatNumber(content.word_count)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {formatDate(content.created_at)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button className="text-blue-600 hover:text-blue-900">
                                  <EyeIcon className="h-4 w-4" />
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              )}

              {/* Empty State */}
              {analyticsData?.summary.totalContent === 0 && (
                <div className="text-center py-12">
                  <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No content data</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Start creating content to see analytics and insights.
                  </p>
                  <div className="mt-6">
                    <button
                      type="button"
                      className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                    >
                      Create Content
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </AuthenticatedLayout>
    </ProtectedRoute>
  );
}