import validator from 'validator';
import DOMPurify from 'isomorphic-dompurify';

/**
 * Comprehensive Input Validation and Sanitization System
 * 
 * Protects against injection attacks, validates data types,
 * and ensures all user inputs are properly sanitized.
 */

// Common validation patterns
const VALIDATION_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  keyword: /^[a-zA-Z0-9\s\-_,.!?'"\[\]()&]{1,100}$/,
  domain: /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/,
  projectName: /^[a-zA-Z0-9\s\-_]{1,50}$/,
  slug: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
  uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  phone: /^[\+]?[1-9][\d]{0,15}$/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  safeString: /^[a-zA-Z0-9\s\-_.,!?'"()]+$/
};

// Dangerous keywords that indicate potential attacks
const DANGEROUS_KEYWORDS = [
  'script', 'javascript', 'vbscript', 'onload', 'onerror', 'onclick',
  'eval', 'exec', 'expression', 'document.cookie', 'window.location',
  'alert', 'confirm', 'prompt', 'innerHTML', 'outerHTML',
  'union', 'select', 'insert', 'update', 'delete', 'drop', 'create',
  'alter', 'grant', 'revoke', 'truncate', 'declare', 'exec',
  '<script', '</script>', '<iframe', '<object', '<embed', '<applet',
  'data:', 'javascript:', 'vbscript:', 'file:', 'ftp:'
];

// Demo data patterns to reject
const DEMO_PATTERNS = [
  /test.*data/i,
  /demo.*content/i,
  /sample.*text/i,
  /placeholder/i,
  /example\.com/i,
  /lorem.*ipsum/i,
  /fake.*data/i,
  /mock.*content/i,
  /dummy.*text/i,
  /\[.*placeholder.*\]/i,
  /\{.*example.*\}/i,
  /your.*keyword.*here/i,
  /replace.*with.*actual/i
];

class InputValidator {
  constructor() {
    this.validationCache = new Map();
    this.sanitizationCache = new Map();
  }

  /**
   * Validate and sanitize input data
   */
  validateAndSanitize(data, schema) {
    const errors = [];
    const sanitized = {};

    for (const [key, rules] of Object.entries(schema)) {
      const value = data[key];
      
      try {
        // Check if field is required
        if (rules.required && (value === undefined || value === null || value === '')) {
          errors.push(`${key} is required`);
          continue;
        }

        // Skip validation for optional empty fields
        if (!rules.required && (value === undefined || value === null || value === '')) {
          sanitized[key] = value;
          continue;
        }

        // Type validation
        if (rules.type && !this.validateType(value, rules.type)) {
          errors.push(`${key} must be of type ${rules.type}`);
          continue;
        }

        // Length validation
        if (rules.minLength && value.length < rules.minLength) {
          errors.push(`${key} must be at least ${rules.minLength} characters long`);
          continue;
        }

        if (rules.maxLength && value.length > rules.maxLength) {
          errors.push(`${key} cannot exceed ${rules.maxLength} characters`);
          continue;
        }

        // Pattern validation
        if (rules.pattern && !this.validatePattern(value, rules.pattern)) {
          errors.push(`${key} has invalid format`);
          continue;
        }

        // Custom validation
        if (rules.validator && !rules.validator(value)) {
          errors.push(`${key} failed custom validation`);
          continue;
        }

        // Sanitize the value
        sanitized[key] = this.sanitizeValue(value, rules);

      } catch (error) {
        errors.push(`${key} validation error: ${error.message}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      data: sanitized
    };
  }

  /**
   * Validate data type
   */
  validateType(value, type) {
    switch (type) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      case 'email':
        return typeof value === 'string' && validator.isEmail(value);
      case 'url':
        return typeof value === 'string' && validator.isURL(value);
      case 'uuid':
        return typeof value === 'string' && validator.isUUID(value);
      case 'date':
        return value instanceof Date || !isNaN(Date.parse(value));
      case 'integer':
        return Number.isInteger(value);
      case 'float':
        return typeof value === 'number' && !isNaN(value);
      default:
        return true;
    }
  }

  /**
   * Validate against patterns
   */
  validatePattern(value, pattern) {
    if (typeof pattern === 'string') {
      pattern = VALIDATION_PATTERNS[pattern] || new RegExp(pattern);
    }
    
    return pattern.test(value);
  }

  /**
   * Sanitize input value
   */
  sanitizeValue(value, rules) {
    let sanitized = value;

    // HTML sanitization
    if (rules.allowHTML) {
      sanitized = DOMPurify.sanitize(sanitized, {
        ALLOWED_TAGS: rules.allowedTags || ['b', 'i', 'u', 'strong', 'em'],
        ALLOWED_ATTR: rules.allowedAttributes || []
      });
    } else {
      sanitized = this.stripHTML(sanitized);
    }

    // SQL injection prevention
    if (typeof sanitized === 'string') {
      sanitized = this.preventSQLInjection(sanitized);
    }

    // XSS prevention
    if (typeof sanitized === 'string') {
      sanitized = this.preventXSS(sanitized);
    }

    // Trim whitespace
    if (rules.trim !== false && typeof sanitized === 'string') {
      sanitized = sanitized.trim();
    }

    // Convert case
    if (rules.case === 'lower' && typeof sanitized === 'string') {
      sanitized = sanitized.toLowerCase();
    } else if (rules.case === 'upper' && typeof sanitized === 'string') {
      sanitized = sanitized.toUpperCase();
    }

    // Remove dangerous keywords
    if (rules.removeDangerous !== false && typeof sanitized === 'string') {
      sanitized = this.removeDangerousKeywords(sanitized);
    }

    return sanitized;
  }

  /**
   * Strip HTML tags
   */
  stripHTML(input) {
    if (typeof input !== 'string') return input;
    return input.replace(/<[^>]*>/g, '');
  }

  /**
   * Prevent SQL injection
   */
  preventSQLInjection(input) {
    if (typeof input !== 'string') return input;
    
    // Escape single quotes
    let sanitized = input.replace(/'/g, "''");
    
    // Remove or escape dangerous SQL keywords
    const sqlKeywords = ['union', 'select', 'insert', 'update', 'delete', 'drop', 'create', 'alter'];
    sqlKeywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
      sanitized = sanitized.replace(regex, '');
    });
    
    return sanitized;
  }

  /**
   * Prevent XSS attacks
   */
  preventXSS(input) {
    if (typeof input !== 'string') return input;
    
    // Encode dangerous characters
    let sanitized = input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
    
    // Remove javascript: and data: protocols
    sanitized = sanitized.replace(/javascript:/gi, '');
    sanitized = sanitized.replace(/data:/gi, '');
    sanitized = sanitized.replace(/vbscript:/gi, '');
    
    return sanitized;
  }

  /**
   * Remove dangerous keywords
   */
  removeDangerousKeywords(input) {
    if (typeof input !== 'string') return input;
    
    let sanitized = input;
    DANGEROUS_KEYWORDS.forEach(keyword => {
      const regex = new RegExp(keyword, 'gi');
      sanitized = sanitized.replace(regex, '');
    });
    
    return sanitized;
  }

  /**
   * Detect and reject demo data
   */
  detectDemoData(input) {
    if (typeof input !== 'string') return false;
    
    const lowercaseInput = input.toLowerCase();
    
    return DEMO_PATTERNS.some(pattern => pattern.test(lowercaseInput));
  }

  /**
   * Validate content generation input
   */
  validateContentInput(data) {
    const schema = {
      primaryKeyword: {
        required: true,
        type: 'string',
        minLength: 2,
        maxLength: 100,
        pattern: 'keyword',
        validator: (value) => !this.detectDemoData(value)
      },
      targetKeywords: {
        required: false,
        type: 'array',
        validator: (value) => {
          if (!Array.isArray(value)) return false;
          return value.every(keyword => 
            typeof keyword === 'string' && 
            keyword.length >= 2 && 
            keyword.length <= 100 &&
            !this.detectDemoData(keyword)
          );
        }
      },
      contentType: {
        required: true,
        type: 'string',
        validator: (value) => {
          const allowedTypes = ['blog_post', 'product_description', 'landing_page', 'social_media', 'email', 'meta_description'];
          return allowedTypes.includes(value);
        }
      },
      targetAudience: {
        required: false,
        type: 'string',
        maxLength: 200,
        validator: (value) => !this.detectDemoData(value)
      },
      contentLength: {
        required: false,
        type: 'integer',
        validator: (value) => value >= 100 && value <= 10000
      },
      tone: {
        required: false,
        type: 'string',
        validator: (value) => {
          const allowedTones = ['professional', 'casual', 'friendly', 'authoritative', 'conversational'];
          return allowedTones.includes(value);
        }
      },
      includeSchema: {
        required: false,
        type: 'boolean'
      },
      competitorUrls: {
        required: false,
        type: 'array',
        validator: (value) => {
          if (!Array.isArray(value)) return false;
          return value.every(url => 
            typeof url === 'string' && 
            validator.isURL(url) &&
            !this.detectDemoData(url)
          );
        }
      }
    };

    return this.validateAndSanitize(data, schema);
  }

  /**
   * Validate project input
   */
  validateProjectInput(data) {
    const schema = {
      name: {
        required: true,
        type: 'string',
        minLength: 1,
        maxLength: 50,
        pattern: 'projectName',
        validator: (value) => !this.detectDemoData(value)
      },
      description: {
        required: false,
        type: 'string',
        maxLength: 500,
        validator: (value) => !this.detectDemoData(value)
      },
      website: {
        required: false,
        type: 'url',
        validator: (value) => !this.detectDemoData(value)
      },
      targetKeywords: {
        required: false,
        type: 'array',
        validator: (value) => {
          if (!Array.isArray(value)) return false;
          return value.every(keyword => 
            typeof keyword === 'string' && 
            keyword.length >= 2 && 
            keyword.length <= 100 &&
            !this.detectDemoData(keyword)
          );
        }
      },
      industry: {
        required: false,
        type: 'string',
        maxLength: 50,
        validator: (value) => !this.detectDemoData(value)
      },
      targetLocation: {
        required: false,
        type: 'string',
        maxLength: 100,
        validator: (value) => !this.detectDemoData(value)
      }
    };

    return this.validateAndSanitize(data, schema);
  }

  /**
   * Validate user profile input
   */
  validateUserInput(data) {
    const schema = {
      email: {
        required: true,
        type: 'email',
        maxLength: 100
      },
      fullName: {
        required: false,
        type: 'string',
        maxLength: 100,
        pattern: 'safeString'
      },
      company: {
        required: false,
        type: 'string',
        maxLength: 100,
        pattern: 'safeString'
      },
      website: {
        required: false,
        type: 'url'
      },
      phone: {
        required: false,
        type: 'string',
        pattern: 'phone'
      }
    };

    return this.validateAndSanitize(data, schema);
  }

  /**
   * Rate limit validation requests
   */
  isRateLimited(ip) {
    const key = `validation:${ip}`;
    const now = Date.now();
    const window = 60000; // 1 minute
    const limit = 100; // 100 validations per minute

    if (this.validationCache.has(key)) {
      const data = this.validationCache.get(key);
      if (now - data.timestamp < window) {
        if (data.count >= limit) {
          return true;
        }
        data.count++;
      } else {
        this.validationCache.set(key, { timestamp: now, count: 1 });
      }
    } else {
      this.validationCache.set(key, { timestamp: now, count: 1 });
    }

    return false;
  }

  /**
   * Clear old cache entries
   */
  clearOldCache() {
    const now = Date.now();
    const maxAge = 60000; // 1 minute

    for (const [key, data] of this.validationCache.entries()) {
      if (now - data.timestamp > maxAge) {
        this.validationCache.delete(key);
      }
    }

    for (const [key, data] of this.sanitizationCache.entries()) {
      if (now - data.timestamp > maxAge) {
        this.sanitizationCache.delete(key);
      }
    }
  }
}

// Create singleton instance
const inputValidator = new InputValidator();

// Clean cache periodically
setInterval(() => {
  inputValidator.clearOldCache();
}, 60000); // Every minute

/**
 * Express middleware for input validation
 */
export const validateInput = (validationType = 'general') => {
  return (req, res, next) => {
    try {
      // Rate limiting
      if (inputValidator.isRateLimited(req.ip)) {
        return res.status(429).json({
          success: false,
          error: 'Too many validation requests',
          message: 'Please slow down your requests'
        });
      }

      let validation;
      
      switch (validationType) {
        case 'content':
          validation = inputValidator.validateContentInput(req.body);
          break;
        case 'project':
          validation = inputValidator.validateProjectInput(req.body);
          break;
        case 'user':
          validation = inputValidator.validateUserInput(req.body);
          break;
        default:
          // General validation - just sanitize
          validation = { isValid: true, data: req.body, errors: [] };
      }

      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          error: 'Input validation failed',
          details: validation.errors
        });
      }

      // Replace request body with sanitized data
      req.body = validation.data;
      next();

    } catch (error) {
      console.error('Input validation error:', error);
      return res.status(500).json({
        success: false,
        error: 'Validation system error',
        message: 'Please try again'
      });
    }
  };
};

/**
 * Validate single field
 */
export const validateField = (value, fieldType) => {
  const schema = {
    [fieldType]: {
      required: true,
      type: 'string',
      validator: (val) => !inputValidator.detectDemoData(val)
    }
  };

  return inputValidator.validateAndSanitize({ [fieldType]: value }, schema);
};

/**
 * Sanitize HTML content
 */
export const sanitizeHTML = (html, options = {}) => {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: options.allowedTags || ['b', 'i', 'u', 'strong', 'em', 'p', 'br'],
    ALLOWED_ATTR: options.allowedAttributes || []
  });
};

/**
 * Check for demo data
 */
export const isDemoData = (input) => {
  return inputValidator.detectDemoData(input);
};

export { inputValidator };
export default validateInput;