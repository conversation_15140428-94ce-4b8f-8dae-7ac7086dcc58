import express from 'express';
import { createClient } from '@supabase/supabase-js';
import { body, param, validationResult } from 'express-validator';
import dotenv from 'dotenv';

dotenv.config();

const router = express.Router();

// Initialize Supabase client with service role for server-side operations
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

/**
 * Authentication middleware to verify JWT token
 */
const authenticateUser = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'Missing or invalid authorization header',
        code: 'UNAUTHORIZED'
      });
    }

    const token = authHeader.substring(7);
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired token',
        code: 'UNAUTHORIZED'
      });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({
      success: false,
      error: 'Authentication failed',
      code: 'AUTH_ERROR'
    });
  }
};

/**
 * GET /api/projects
 * Get all projects for the authenticated user
 */
router.get('/', authenticateUser, async (req, res) => {
  try {
    const { data: projects, error } = await supabase
      .from('projects')
      .select('*')
      .eq('user_id', req.user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Database error:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch projects',
        code: 'DATABASE_ERROR'
      });
    }

    res.json({
      success: true,
      data: projects,
      count: projects.length,
      message: 'Projects retrieved successfully'
    });
  } catch (error) {
    console.error('Get projects error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

/**
 * GET /api/projects/:id
 * Get a specific project by ID
 */
router.get('/:id', 
  authenticateUser,
  param('id').isUUID().withMessage('Invalid project ID'),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
          code: 'VALIDATION_ERROR'
        });
      }

      const { data: project, error } = await supabase
        .from('projects')
        .select('*')
        .eq('id', req.params.id)
        .eq('user_id', req.user.id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return res.status(404).json({
            success: false,
            error: 'Project not found',
            code: 'PROJECT_NOT_FOUND'
          });
        }
        console.error('Database error:', error);
        return res.status(500).json({
          success: false,
          error: 'Failed to fetch project',
          code: 'DATABASE_ERROR'
        });
      }

      res.json({
        success: true,
        data: project,
        message: 'Project retrieved successfully'
      });
    } catch (error) {
      console.error('Get project error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

/**
 * POST /api/projects
 * Create a new project
 */
router.post('/',
  authenticateUser,
  [
    body('name')
      .trim()
      .isLength({ min: 1, max: 255 })
      .withMessage('Project name is required and must be less than 255 characters'),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 1000 })
      .withMessage('Description must be less than 1000 characters'),
    body('website_url')
      .optional()
      .isURL()
      .withMessage('Website URL must be a valid URL'),
    body('target_keywords')
      .optional()
      .isArray()
      .withMessage('Target keywords must be an array'),
    body('location')
      .optional()
      .trim()
      .isLength({ max: 255 })
      .withMessage('Location must be less than 255 characters'),
    body('industry')
      .optional()
      .trim()
      .isLength({ max: 100 })
      .withMessage('Industry must be less than 100 characters')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
          code: 'VALIDATION_ERROR'
        });
      }

      const projectData = {
        user_id: req.user.id,
        name: req.body.name,
        description: req.body.description || null,
        website_url: req.body.website_url || null,
        target_keywords: req.body.target_keywords || [],
        location: req.body.location || null,
        industry: req.body.industry || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: project, error } = await supabase
        .from('projects')
        .insert(projectData)
        .select()
        .single();

      if (error) {
        console.error('Database error:', error);
        return res.status(500).json({
          success: false,
          error: 'Failed to create project',
          code: 'DATABASE_ERROR'
        });
      }

      res.status(201).json({
        success: true,
        data: project,
        message: 'Project created successfully'
      });
    } catch (error) {
      console.error('Create project error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

/**
 * PUT /api/projects/:id
 * Update an existing project
 */
router.put('/:id',
  authenticateUser,
  [
    param('id').isUUID().withMessage('Invalid project ID'),
    body('name')
      .optional()
      .trim()
      .isLength({ min: 1, max: 255 })
      .withMessage('Project name must be less than 255 characters'),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 1000 })
      .withMessage('Description must be less than 1000 characters'),
    body('website_url')
      .optional()
      .isURL()
      .withMessage('Website URL must be a valid URL'),
    body('target_keywords')
      .optional()
      .isArray()
      .withMessage('Target keywords must be an array'),
    body('location')
      .optional()
      .trim()
      .isLength({ max: 255 })
      .withMessage('Location must be less than 255 characters'),
    body('industry')
      .optional()
      .trim()
      .isLength({ max: 100 })
      .withMessage('Industry must be less than 100 characters')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
          code: 'VALIDATION_ERROR'
        });
      }

      // First check if project exists and belongs to user
      const { data: existingProject, error: fetchError } = await supabase
        .from('projects')
        .select('id')
        .eq('id', req.params.id)
        .eq('user_id', req.user.id)
        .single();

      if (fetchError) {
        if (fetchError.code === 'PGRST116') {
          return res.status(404).json({
            success: false,
            error: 'Project not found',
            code: 'PROJECT_NOT_FOUND'
          });
        }
        console.error('Database error:', fetchError);
        return res.status(500).json({
          success: false,
          error: 'Failed to fetch project',
          code: 'DATABASE_ERROR'
        });
      }

      const updateData = {
        ...req.body,
        updated_at: new Date().toISOString()
      };

      // Remove undefined values
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      const { data: project, error } = await supabase
        .from('projects')
        .update(updateData)
        .eq('id', req.params.id)
        .eq('user_id', req.user.id)
        .select()
        .single();

      if (error) {
        console.error('Database error:', error);
        return res.status(500).json({
          success: false,
          error: 'Failed to update project',
          code: 'DATABASE_ERROR'
        });
      }

      res.json({
        success: true,
        data: project,
        message: 'Project updated successfully'
      });
    } catch (error) {
      console.error('Update project error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

/**
 * DELETE /api/projects/:id
 * Delete a project
 */
router.delete('/:id',
  authenticateUser,
  param('id').isUUID().withMessage('Invalid project ID'),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
          code: 'VALIDATION_ERROR'
        });
      }

      // Check if project exists and belongs to user
      const { data: existingProject, error: fetchError } = await supabase
        .from('projects')
        .select('id')
        .eq('id', req.params.id)
        .eq('user_id', req.user.id)
        .single();

      if (fetchError) {
        if (fetchError.code === 'PGRST116') {
          return res.status(404).json({
            success: false,
            error: 'Project not found',
            code: 'PROJECT_NOT_FOUND'
          });
        }
        console.error('Database error:', fetchError);
        return res.status(500).json({
          success: false,
          error: 'Failed to fetch project',
          code: 'DATABASE_ERROR'
        });
      }

      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', req.params.id)
        .eq('user_id', req.user.id);

      if (error) {
        console.error('Database error:', error);
        return res.status(500).json({
          success: false,
          error: 'Failed to delete project',
          code: 'DATABASE_ERROR'
        });
      }

      res.json({
        success: true,
        message: 'Project deleted successfully'
      });
    } catch (error) {
      console.error('Delete project error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

/**
 * GET /api/projects/:id/content
 * Get all generated content for a specific project
 */
router.get('/:id/content',
  authenticateUser,
  param('id').isUUID().withMessage('Invalid project ID'),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
          code: 'VALIDATION_ERROR'
        });
      }

      // Verify project belongs to user
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('id')
        .eq('id', req.params.id)
        .eq('user_id', req.user.id)
        .single();

      if (projectError) {
        if (projectError.code === 'PGRST116') {
          return res.status(404).json({
            success: false,
            error: 'Project not found',
            code: 'PROJECT_NOT_FOUND'
          });
        }
        console.error('Database error:', projectError);
        return res.status(500).json({
          success: false,
          error: 'Failed to fetch project',
          code: 'DATABASE_ERROR'
        });
      }

      // Get content for this project
      const { data: content, error } = await supabase
        .from('generated_content')
        .select('*')
        .eq('project_id', req.params.id)
        .eq('user_id', req.user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Database error:', error);
        return res.status(500).json({
          success: false,
          error: 'Failed to fetch content',
          code: 'DATABASE_ERROR'
        });
      }

      res.json({
        success: true,
        data: content,
        count: content.length,
        message: 'Project content retrieved successfully'
      });
    } catch (error) {
      console.error('Get project content error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

/**
 * GET /api/projects/:id/metrics
 * Get metrics and analytics for a specific project
 */
router.get('/:id/metrics',
  authenticateUser,
  param('id').isUUID().withMessage('Invalid project ID'),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
          code: 'VALIDATION_ERROR'
        });
      }

      // Verify project belongs to user
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('id, name, created_at')
        .eq('id', req.params.id)
        .eq('user_id', req.user.id)
        .single();

      if (projectError) {
        if (projectError.code === 'PGRST116') {
          return res.status(404).json({
            success: false,
            error: 'Project not found',
            code: 'PROJECT_NOT_FOUND'
          });
        }
        console.error('Database error:', projectError);
        return res.status(500).json({
          success: false,
          error: 'Failed to fetch project',
          code: 'DATABASE_ERROR'
        });
      }

      // Get content metrics
      const { data: contentMetrics, error: contentError } = await supabase
        .from('generated_content')
        .select('id, content_type, word_count, created_at, seo_score')
        .eq('project_id', req.params.id)
        .eq('user_id', req.user.id);

      if (contentError) {
        console.error('Content metrics error:', contentError);
      }

      // Calculate metrics
      const totalContent = contentMetrics?.length || 0;
      const avgWordCount = contentMetrics?.length > 0 
        ? Math.round(contentMetrics.reduce((sum, content) => sum + (content.word_count || 0), 0) / contentMetrics.length)
        : 0;
      const avgSeoScore = contentMetrics?.length > 0 
        ? Math.round(contentMetrics.reduce((sum, content) => sum + (content.seo_score || 0), 0) / contentMetrics.length)
        : 0;

      // Content by type
      const contentByType = contentMetrics?.reduce((acc, content) => {
        acc[content.content_type] = (acc[content.content_type] || 0) + 1;
        return acc;
      }, {}) || {};

      // Recent activity (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const recentContent = contentMetrics?.filter(content => 
        new Date(content.created_at) > thirtyDaysAgo
      ) || [];

      res.json({
        success: true,
        data: {
          project: {
            id: project.id,
            name: project.name,
            created_at: project.created_at
          },
          metrics: {
            totalContent,
            avgWordCount,
            avgSeoScore,
            contentByType,
            recentActivity: recentContent.length
          },
          timeline: {
            last30Days: recentContent.length,
            totalDays: Math.ceil((new Date() - new Date(project.created_at)) / (1000 * 60 * 60 * 24))
          }
        },
        message: 'Project metrics retrieved successfully'
      });
    } catch (error) {
      console.error('Get project metrics error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

/**
 * POST /api/projects/:id/generate-content
 * Generate content for a specific project
 */
router.post('/:id/generate-content',
  authenticateUser,
  [
    param('id').isUUID().withMessage('Invalid project ID'),
    body('content_type')
      .isIn(['blog_post', 'page_content', 'product_description', 'meta_description', 'social_media'])
      .withMessage('Invalid content type'),
    body('target_keyword')
      .trim()
      .isLength({ min: 1, max: 255 })
      .withMessage('Target keyword is required'),
    body('word_count')
      .optional()
      .isInt({ min: 100, max: 5000 })
      .withMessage('Word count must be between 100 and 5000'),
    body('tone')
      .optional()
      .isIn(['professional', 'casual', 'friendly', 'authoritative', 'conversational'])
      .withMessage('Invalid tone')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
          code: 'VALIDATION_ERROR'
        });
      }

      // Verify project belongs to user
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('*')
        .eq('id', req.params.id)
        .eq('user_id', req.user.id)
        .single();

      if (projectError) {
        if (projectError.code === 'PGRST116') {
          return res.status(404).json({
            success: false,
            error: 'Project not found',
            code: 'PROJECT_NOT_FOUND'
          });
        }
        console.error('Database error:', projectError);
        return res.status(500).json({
          success: false,
          error: 'Failed to fetch project',
          code: 'DATABASE_ERROR'
        });
      }

      // Create content generation request
      const contentRequest = {
        project_id: req.params.id,
        user_id: req.user.id,
        content_type: req.body.content_type,
        target_keyword: req.body.target_keyword,
        word_count: req.body.word_count || 500,
        tone: req.body.tone || 'professional',
        status: 'processing',
        created_at: new Date().toISOString()
      };

      // Store the request
      const { data: contentRecord, error: insertError } = await supabase
        .from('generated_content')
        .insert(contentRequest)
        .select()
        .single();

      if (insertError) {
        console.error('Database error:', insertError);
        return res.status(500).json({
          success: false,
          error: 'Failed to create content request',
          code: 'DATABASE_ERROR'
        });
      }

      // TODO: Implement actual content generation logic here
      // For now, return the request ID for tracking
      res.status(202).json({
        success: true,
        data: {
          id: contentRecord.id,
          status: 'processing',
          estimated_completion: new Date(Date.now() + 60000).toISOString(), // 1 minute
          project_id: req.params.id
        },
        message: 'Content generation started successfully'
      });
    } catch (error) {
      console.error('Generate content error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

/**
 * GET /api/projects/:id/keywords
 * Get keyword analysis for a project
 */
router.get('/:id/keywords',
  authenticateUser,
  param('id').isUUID().withMessage('Invalid project ID'),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
          code: 'VALIDATION_ERROR'
        });
      }

      // Verify project belongs to user
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('target_keywords, industry, location')
        .eq('id', req.params.id)
        .eq('user_id', req.user.id)
        .single();

      if (projectError) {
        if (projectError.code === 'PGRST116') {
          return res.status(404).json({
            success: false,
            error: 'Project not found',
            code: 'PROJECT_NOT_FOUND'
          });
        }
        console.error('Database error:', projectError);
        return res.status(500).json({
          success: false,
          error: 'Failed to fetch project',
          code: 'DATABASE_ERROR'
        });
      }

      // Get keyword usage from generated content
      const { data: contentKeywords, error: keywordError } = await supabase
        .from('generated_content')
        .select('target_keyword, seo_score, created_at')
        .eq('project_id', req.params.id)
        .eq('user_id', req.user.id);

      if (keywordError) {
        console.error('Keyword analysis error:', keywordError);
      }

      // Analyze keywords
      const keywordStats = {};
      const targetKeywords = project.target_keywords || [];
      
      // Initialize stats for target keywords
      targetKeywords.forEach(keyword => {
        keywordStats[keyword] = {
          keyword,
          type: 'target',
          usage_count: 0,
          avg_seo_score: 0,
          last_used: null
        };
      });

      // Process content keywords
      (contentKeywords || []).forEach(content => {
        const keyword = content.target_keyword;
        if (!keywordStats[keyword]) {
          keywordStats[keyword] = {
            keyword,
            type: 'content',
            usage_count: 0,
            avg_seo_score: 0,
            last_used: null
          };
        }
        
        keywordStats[keyword].usage_count++;
        keywordStats[keyword].avg_seo_score = 
          (keywordStats[keyword].avg_seo_score + (content.seo_score || 0)) / 2;
        keywordStats[keyword].last_used = content.created_at;
      });

      const keywords = Object.values(keywordStats);

      res.json({
        success: true,
        data: {
          project: {
            id: req.params.id,
            industry: project.industry,
            location: project.location
          },
          keywords,
          summary: {
            total_keywords: keywords.length,
            target_keywords: targetKeywords.length,
            content_keywords: keywords.filter(k => k.type === 'content').length,
            avg_seo_score: Math.round(
              keywords.reduce((sum, k) => sum + k.avg_seo_score, 0) / keywords.length
            ) || 0
          }
        },
        message: 'Keyword analysis retrieved successfully'
      });
    } catch (error) {
      console.error('Get project keywords error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

export default router;