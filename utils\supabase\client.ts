import { createBrowserClient } from '@supabase/ssr';
import { createClient } from '@supabase/supabase-js';

export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          avatar_url: string | null;
          subscription_tier: 'free' | 'pro' | 'enterprise';
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          full_name?: string | null;
          avatar_url?: string | null;
          subscription_tier?: 'free' | 'pro' | 'enterprise';
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          full_name?: string | null;
          avatar_url?: string | null;
          subscription_tier?: 'free' | 'pro' | 'enterprise';
          created_at?: string;
          updated_at?: string;
        };
      };
      projects: {
        Row: {
          id: string;
          user_id: string;
          name: string;
          description: string | null;
          website_url: string | null;
          target_keywords: string[];
          location: string | null;
          industry: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          name: string;
          description?: string | null;
          website_url?: string | null;
          target_keywords?: string[];
          location?: string | null;
          industry?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          name?: string;
          description?: string | null;
          website_url?: string | null;
          target_keywords?: string[];
          location?: string | null;
          industry?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      generated_content: {
        Row: {
          id: string;
          project_id: string;
          user_id: string;
          title: string;
          content: string;
          meta_description: string | null;
          target_keyword: string;
          word_count: number;
          seo_score: number | null;
          competitor_urls: string[];
          generation_settings: Record<string, any>;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          project_id: string;
          user_id: string;
          title: string;
          content: string;
          meta_description?: string | null;
          target_keyword: string;
          word_count: number;
          seo_score?: number | null;
          competitor_urls?: string[];
          generation_settings?: Record<string, any>;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          project_id?: string;
          user_id?: string;
          title?: string;
          content?: string;
          meta_description?: string | null;
          target_keyword?: string;
          word_count?: number;
          seo_score?: number | null;
          competitor_urls?: string[];
          generation_settings?: Record<string, any>;
          created_at?: string;
          updated_at?: string;
        };
      };
      seo_analysis: {
        Row: {
          id: string;
          content_id: string;
          keyword_density: Record<string, number>;
          readability_score: number;
          meta_tags: Record<string, string>;
          internal_links: number;
          external_links: number;
          headings_structure: Record<string, number>;
          recommendations: string[];
          created_at: string;
        };
        Insert: {
          id?: string;
          content_id: string;
          keyword_density: Record<string, number>;
          readability_score: number;
          meta_tags: Record<string, string>;
          internal_links: number;
          external_links: number;
          headings_structure: Record<string, number>;
          recommendations: string[];
          created_at?: string;
        };
        Update: {
          id?: string;
          content_id?: string;
          keyword_density?: Record<string, number>;
          readability_score?: number;
          meta_tags?: Record<string, string>;
          internal_links?: number;
          external_links?: number;
          headings_structure?: Record<string, number>;
          recommendations?: string[];
          created_at?: string;
        };
      };
      usage_tracking: {
        Row: {
          id: string;
          user_id: string;
          action: string;
          resource_type: string;
          resource_id: string | null;
          metadata: Record<string, any>;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          action: string;
          resource_type: string;
          resource_id?: string | null;
          metadata?: Record<string, any>;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          action?: string;
          resource_type?: string;
          resource_id?: string | null;
          metadata?: Record<string, any>;
          created_at?: string;
        };
      };
    };
  };
};

// Client-side Supabase client
export const createClientSupabase = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key';
  
  return createBrowserClient<Database>(supabaseUrl, supabaseAnonKey);
};

// Server-side Supabase client with service role
export const createServerSupabase = () => {
  return createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  );
};

// Default client for browser usage
export const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co',
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'
);