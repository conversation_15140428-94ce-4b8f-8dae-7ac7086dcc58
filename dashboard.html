<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="SEO SAAS Dashboard - Manage your AI-powered content generation projects and analytics.">
    <title>Dashboard - SEO SAAS</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/data-visualization.css">
    <link rel="stylesheet" href="css/animations.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
</head>
<body class="dashboard-layout">
    <!-- Dashboard App Container -->
    <div class="dashboard-app">
        <!-- Global Top Navigation -->
        <header class="top-navigation">
            <div class="nav-left">
                <button class="sidebar-toggle" aria-label="Toggle sidebar" onclick="toggleSidebar()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
                <div class="brand-section">
                    <svg class="brand-logo w-8 h-8 text-primary-600" viewBox="0 0 40 40" fill="currentColor">
                        <path d="M20 5L5 15v10l15 10 15-10V15L20 5z"/>
                        <path d="M20 15l-10 6v8l10 6 10-6v-8l-10-6z" fill="white" opacity="0.5"/>
                    </svg>
                    <span class="brand-name">SEO SAAS</span>
                    <span class="environment-badge">Pro</span>
                </div>
            </div>
            
            <div class="nav-center">
                <div class="global-search">
                    <svg class="search-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <input type="search" placeholder="Search projects, keywords, content..." class="search-input">
                </div>
            </div>
            
            <div class="nav-right">
                <button class="quick-action-btn" title="New Project" onclick="createNewProject()">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    <span>New</span>
                </button>
                
                <div class="notifications-dropdown">
                    <button class="notification-btn" title="Notifications" onclick="toggleNotifications()">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5-5V9.5a6.5 6.5 0 10-13 0V12l-5 5h5"></path>
                        </svg>
                        <span class="notification-badge">3</span>
                    </button>
                </div>
                
                <div class="help-dropdown">
                    <button class="help-btn" title="Help & Support">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </button>
                </div>
                
                <div class="user-menu-dropdown">
                    <button class="user-menu-btn" onclick="toggleUserMenu()">
                        <div class="user-avatar">
                            <span>JD</span>
                        </div>
                        <div class="user-info">
                            <span class="user-name">John Doe</span>
                            <span class="user-plan">Professional</span>
                        </div>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <aside class="sidebar-navigation" id="sidebarNav">
            <nav class="nav-menu">
                <!-- Dashboard Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Overview</h3>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item active">
                            <a href="dashboard.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z"></path>
                                </svg>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="analytics.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                <span>Analytics</span>
                                <span class="nav-badge">New</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Content Creation Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Content Creation</h3>
                        <button class="section-toggle" aria-label="Toggle section" onclick="toggleSection(this)">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="content-creator.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                <span>Create Content</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="content-optimizer.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                <span>Optimize Content</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="bulk-generator.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                <span>Bulk Generator</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="content-editor.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                                </svg>
                                <span>Content Editor</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Research & Analysis Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Research & Analysis</h3>
                        <button class="section-toggle" aria-label="Toggle section" onclick="toggleSection(this)">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="keyword-research.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-3.586l4.293-4.293A6 6 0 0119 9z"></path>
                                </svg>
                                <span>Keyword Research</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="competitor-analysis.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                <span>Competitor Analysis</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="serp-analyzer.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                <span>SERP Analyzer</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="content-gaps.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                                </svg>
                                <span>Content Gaps</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Projects Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Projects</h3>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="projects.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                <span>All Projects</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="templates.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <span>Templates</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Settings Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Settings</h3>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="account-settings.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <span>Account</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="billing.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                </svg>
                                <span>Billing</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="api-settings.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <span>API Settings</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Sidebar Footer -->
            <div class="sidebar-footer">
                <div class="usage-indicator">
                    <div class="usage-label">API Usage</div>
                    <div class="usage-bar">
                        <div class="usage-fill" style="width: 65%"></div>
                    </div>
                    <div class="usage-text">325 / 500 requests</div>
                </div>
                
                <button class="upgrade-btn" onclick="window.location.href='pricing.html'">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    <span>Upgrade Plan</span>
                </button>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="page-title">Dashboard</h1>
                        <p class="page-subtitle">Welcome back! Here's what's happening with your content generation.</p>
                    </div>
                    <div class="header-right">
                        <div class="time-range-selector">
                            <select class="form-select" onchange="updateTimeRange(this.value)">
                                <option value="7d">Last 7 days</option>
                                <option value="30d" selected>Last 30 days</option>
                                <option value="90d">Last 90 days</option>
                                <option value="1y">Last year</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Metrics Overview -->
            <div class="metrics-section">
                <div class="metrics-grid">
                    <div class="metric-card animate-fade-in-up">
                        <div class="metric-header">
                            <div class="metric-title">
                                <h3>Content Generated</h3>
                                <svg class="metric-icon w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="metric-value" data-count="247">247</div>
                        <div class="metric-change positive">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                            <span>+23% from last month</span>
                        </div>
                    </div>
                    
                    <div class="metric-card animate-fade-in-up" style="animation-delay: 0.1s">
                        <div class="metric-header">
                            <div class="metric-title">
                                <h3>Average SEO Score</h3>
                                <svg class="metric-icon w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="metric-value" data-count="94.2">94.2</div>
                        <div class="metric-change positive">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                            <span>+5.3 points</span>
                        </div>
                    </div>
                    
                    <div class="metric-card animate-fade-in-up" style="animation-delay: 0.2s">
                        <div class="metric-header">
                            <div class="metric-title">
                                <h3>Keywords Analyzed</h3>
                                <svg class="metric-icon w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-3.586l4.293-4.293A6 6 0 0119 9z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="metric-value" data-count="1891">1,891</div>
                        <div class="metric-change positive">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                            <span>+18% from last month</span>
                        </div>
                    </div>
                    
                    <div class="metric-card animate-fade-in-up" style="animation-delay: 0.3s">
                        <div class="metric-header">
                            <div class="metric-title">
                                <h3>Competitors Analyzed</h3>
                                <svg class="metric-icon w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="metric-value" data-count="7342">7,342</div>
                        <div class="metric-change positive">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                            <span>+31% from last month</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dashboard Content Grid -->
            <div class="dashboard-content-grid">
                <!-- Recent Projects -->
                <div class="dashboard-card projects-card">
                    <div class="card-header">
                        <div class="card-title">
                            <h2>Recent Projects</h2>
                            <span class="card-subtitle">Your latest content generation projects</span>
                        </div>
                        <a href="projects.html" class="btn btn-secondary btn-sm">View All</a>
                    </div>
                    
                    <div class="card-content">
                        <div class="projects-list">
                            <div class="project-item">
                                <div class="project-info">
                                    <div class="project-name">Digital Marketing Agency</div>
                                    <div class="project-meta">
                                        <span class="project-keyword">digital marketing services</span>
                                        <span class="project-industry">Marketing</span>
                                    </div>
                                </div>
                                <div class="project-score">
                                    <div class="score-badge score-excellent">96</div>
                                </div>
                                <div class="project-actions">
                                    <button class="btn-icon" title="Edit">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="project-item">
                                <div class="project-info">
                                    <div class="project-name">E-commerce Store</div>
                                    <div class="project-meta">
                                        <span class="project-keyword">buy running shoes online</span>
                                        <span class="project-industry">E-commerce</span>
                                    </div>
                                </div>
                                <div class="project-score">
                                    <div class="score-badge score-good">88</div>
                                </div>
                                <div class="project-actions">
                                    <button class="btn-icon" title="Edit">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="project-item">
                                <div class="project-info">
                                    <div class="project-name">Law Firm Website</div>
                                    <div class="project-meta">
                                        <span class="project-keyword">personal injury lawyer</span>
                                        <span class="project-industry">Legal</span>
                                    </div>
                                </div>
                                <div class="project-score">
                                    <div class="score-badge score-excellent">92</div>
                                </div>
                                <div class="project-actions">
                                    <button class="btn-icon" title="Edit">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="dashboard-card quick-actions-card">
                    <div class="card-header">
                        <div class="card-title">
                            <h2>Quick Actions</h2>
                            <span class="card-subtitle">Get started with content generation</span>
                        </div>
                    </div>
                    
                    <div class="card-content">
                        <div class="quick-actions-grid">
                            <a href="content-creator.html" class="quick-action-item">
                                <div class="action-icon">
                                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </div>
                                <div class="action-content">
                                    <h3>Create Content</h3>
                                    <p>Generate new SEO content for any niche</p>
                                </div>
                            </a>
                            
                            <a href="bulk-generator.html" class="quick-action-item">
                                <div class="action-icon">
                                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                </div>
                                <div class="action-content">
                                    <h3>Bulk Generate</h3>
                                    <p>Create multiple content pieces at once</p>
                                </div>
                            </a>
                            
                            <a href="keyword-research.html" class="quick-action-item">
                                <div class="action-icon">
                                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-3.586l4.293-4.293A6 6 0 0119 9z"></path>
                                    </svg>
                                </div>
                                <div class="action-content">
                                    <h3>Research Keywords</h3>
                                    <p>Find profitable keywords for your niche</p>
                                </div>
                            </a>
                            
                            <a href="competitor-analysis.html" class="quick-action-item">
                                <div class="action-icon">
                                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                                <div class="action-content">
                                    <h3>Analyze Competitors</h3>
                                    <p>Study top-ranking competitor content</p>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Performance Chart -->
                <div class="dashboard-card chart-card">
                    <div class="card-header">
                        <div class="card-title">
                            <h2>Content Performance</h2>
                            <span class="card-subtitle">SEO scores over time</span>
                        </div>
                        <div class="chart-legend">
                            <div class="legend-item">
                                <div class="legend-color" style="background-color: #3b82f6;"></div>
                                <span>SEO Score</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-content">
                        <div class="chart-container">
                            <canvas id="performanceChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script>
        // Dashboard JavaScript functionality
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
            animateMetrics();
            initializeChart();
        });

        function initializeDashboard() {
            // Initialize sidebar state
            const sidebar = document.getElementById('sidebarNav');
            const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (isCollapsed) {
                sidebar.classList.add('collapsed');
            }
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebarNav');
            sidebar.classList.toggle('collapsed');
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }

        function toggleSection(button) {
            const section = button.closest('.nav-section');
            const items = section.querySelector('.nav-items');
            const icon = button.querySelector('svg');
            
            section.classList.toggle('collapsed');
            
            if (section.classList.contains('collapsed')) {
                items.style.display = 'none';
                icon.style.transform = 'rotate(-90deg)';
            } else {
                items.style.display = 'block';
                icon.style.transform = 'rotate(0deg)';
            }
        }

        function animateMetrics() {
            const metricValues = document.querySelectorAll('.metric-value[data-count]');
            
            metricValues.forEach(element => {
                const target = parseFloat(element.getAttribute('data-count'));
                const duration = 2000;
                const start = performance.now();
                
                function updateValue(currentTime) {
                    const elapsed = currentTime - start;
                    const progress = Math.min(elapsed / duration, 1);
                    
                    const current = target * easeOutCubic(progress);
                    
                    if (target % 1 === 0) {
                        element.textContent = Math.floor(current).toLocaleString();
                    } else {
                        element.textContent = current.toFixed(1);
                    }
                    
                    if (progress < 1) {
                        requestAnimationFrame(updateValue);
                    }
                }
                
                requestAnimationFrame(updateValue);
            });
        }

        function easeOutCubic(t) {
            return 1 - Math.pow(1 - t, 3);
        }

        function initializeChart() {
            const canvas = document.getElementById('performanceChart');
            const ctx = canvas.getContext('2d');
            
            // Sample data
            const data = [85, 88, 92, 89, 94, 91, 96, 93, 97, 94, 98, 94];
            const labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            
            // Chart dimensions
            const padding = 40;
            const chartWidth = canvas.width - 2 * padding;
            const chartHeight = canvas.height - 2 * padding;
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Draw grid lines
            ctx.strokeStyle = '#e5e7eb';
            ctx.lineWidth = 1;
            
            // Horizontal grid lines
            for (let i = 0; i <= 4; i++) {
                const y = padding + (chartHeight / 4) * i;
                ctx.beginPath();
                ctx.moveTo(padding, y);
                ctx.lineTo(padding + chartWidth, y);
                ctx.stroke();
            }
            
            // Draw chart line
            ctx.strokeStyle = '#3b82f6';
            ctx.lineWidth = 3;
            ctx.beginPath();
            
            data.forEach((value, index) => {
                const x = padding + (chartWidth / (data.length - 1)) * index;
                const y = padding + chartHeight - ((value - 80) / 20) * chartHeight;
                
                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });
            
            ctx.stroke();
            
            // Draw data points
            ctx.fillStyle = '#3b82f6';
            data.forEach((value, index) => {
                const x = padding + (chartWidth / (data.length - 1)) * index;
                const y = padding + chartHeight - ((value - 80) / 20) * chartHeight;
                
                ctx.beginPath();
                ctx.arc(x, y, 4, 0, 2 * Math.PI);
                ctx.fill();
            });
        }

        function toggleNotifications() {
            // Placeholder for notifications functionality
            console.log('Toggle notifications');
        }

        function toggleUserMenu() {
            // Placeholder for user menu functionality
            console.log('Toggle user menu');
        }

        function createNewProject() {
            window.location.href = 'content-creator.html';
        }

        function updateTimeRange(range) {
            // Placeholder for time range update
            console.log('Update time range:', range);
            // In a real app, this would update the metrics and charts
        }
    </script>
</body>
</html>