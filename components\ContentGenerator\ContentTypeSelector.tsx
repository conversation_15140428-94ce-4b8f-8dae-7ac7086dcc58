/**
 * ContentTypeSelector Component
 * Enterprise SEO SAAS - Content type selection with optimization features
 */

import { ContentType } from '@/types/project'
import {
  DocumentTextIcon,
  TagIcon,
  RocketLaunchIcon,
  ShareIcon,
  EnvelopeIcon,
  CheckIcon
} from '@heroicons/react/24/outline'

interface ContentTypeSelectorProps {
  formData: {
    contentType: ContentType
    wordCount: number
    tone: string
  }
  onUpdate: (updates: any) => void
}

export default function ContentTypeSelector({ formData, onUpdate }: ContentTypeSelectorProps) {
  const contentTypes = [
    {
      type: 'blog_post' as ContentType,
      title: 'Blog Post',
      icon: DocumentTextIcon,
      description: 'Long-form SEO articles and guides',
      wordRange: '1500-4000 words',
      defaultWords: 2500,
      features: ['In-depth content', 'Multiple H2/H3 sections', 'FAQ sections', 'Internal linking'],
      seoStrength: 'High',
      color: 'border-blue-500 bg-blue-50'
    },
    {
      type: 'product_description' as ContentType,
      title: 'Product Description',
      icon: TagIcon,
      description: 'E-commerce product copy with conversion focus',
      wordRange: '150-500 words',
      defaultWords: 300,
      features: ['Benefit-focused', 'Call-to-action', 'Feature bullets', 'Schema markup'],
      seoStrength: 'Medium',
      color: 'border-green-500 bg-green-50'
    },
    {
      type: 'landing_page' as ContentType,
      title: 'Landing Page',
      icon: RocketLaunchIcon,
      description: 'Conversion-optimized landing pages',
      wordRange: '800-2000 words',
      defaultWords: 1200,
      features: ['Hero section', 'Benefits & features', 'Social proof', 'Strong CTA'],
      seoStrength: 'High',
      color: 'border-purple-500 bg-purple-50'
    },
    {
      type: 'meta_description' as ContentType,
      title: 'Meta Description',
      icon: TagIcon,
      description: 'SERP snippet optimization',
      wordRange: '150-160 characters',
      defaultWords: 155,
      features: ['Click-through optimization', 'Keyword inclusion', 'Action-oriented', 'SERP preview'],
      seoStrength: 'Critical',
      color: 'border-orange-500 bg-orange-50'
    },
    {
      type: 'social_media' as ContentType,
      title: 'Social Media Post',
      icon: ShareIcon,
      description: 'Platform-specific social content',
      wordRange: '50-300 characters',
      defaultWords: 150,
      features: ['Platform optimization', 'Hashtag strategy', 'Engagement hooks', 'Visual suggestions'],
      seoStrength: 'Low',
      color: 'border-pink-500 bg-pink-50'
    },
    {
      type: 'email_marketing' as ContentType,
      title: 'Email Marketing',
      icon: EnvelopeIcon,
      description: 'Email campaigns and newsletters',
      wordRange: '200-1000 words',
      defaultWords: 500,
      features: ['Subject line optimization', 'Personalization', 'CTA placement', 'Mobile responsive'],
      seoStrength: 'Low',
      color: 'border-indigo-500 bg-indigo-50'
    }
  ]

  const toneOptions = [
    { value: 'professional', label: 'Professional', description: 'Formal, authoritative tone' },
    { value: 'casual', label: 'Casual', description: 'Conversational, friendly tone' },
    { value: 'technical', label: 'Technical', description: 'Expert-level, detailed explanations' },
    { value: 'friendly', label: 'Friendly', description: 'Warm, approachable tone' },
    { value: 'persuasive', label: 'Persuasive', description: 'Sales-focused, compelling tone' }
  ]

  const handleContentTypeSelect = (type: ContentType) => {
    const selectedType = contentTypes.find(ct => ct.type === type)
    onUpdate({
      contentType: type,
      wordCount: selectedType?.defaultWords || 1000
    })
  }

  const handleWordCountChange = (count: number) => {
    onUpdate({ wordCount: count })
  }

  const handleToneChange = (tone: string) => {
    onUpdate({ tone })
  }

  const getSeoStrengthColor = (strength: string) => {
    switch (strength) {
      case 'Critical': return 'text-red-600 bg-red-100'
      case 'High': return 'text-green-600 bg-green-100'
      case 'Medium': return 'text-yellow-600 bg-yellow-100'
      case 'Low': return 'text-gray-600 bg-gray-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="p-8 space-y-8">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Choose Content Type</h2>
        <p className="text-gray-600">
          Select the type of content you want to generate. Each type is optimized for specific SEO goals and user intent.
        </p>
      </div>

      {/* Content Type Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {contentTypes.map((type) => {
          const Icon = type.icon
          const isSelected = formData.contentType === type.type
          
          return (
            <div
              key={type.type}
              onClick={() => handleContentTypeSelect(type.type)}
              className={`
                relative p-6 rounded-lg border-2 cursor-pointer transition-all
                ${isSelected 
                  ? `${type.color} border-current shadow-lg` 
                  : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-md'
                }
              `}
            >
              {/* Selection Indicator */}
              {isSelected && (
                <div className="absolute top-4 right-4">
                  <div className="w-6 h-6 bg-current rounded-full flex items-center justify-center">
                    <CheckIcon className="h-4 w-4 text-white" />
                  </div>
                </div>
              )}

              {/* Content Type Header */}
              <div className="flex items-center gap-3 mb-4">
                <div className={`p-2 rounded-lg ${isSelected ? 'bg-white bg-opacity-50' : 'bg-gray-100'}`}>
                  <Icon className={`h-6 w-6 ${isSelected ? 'text-current' : 'text-gray-600'}`} />
                </div>
                <div>
                  <h3 className={`font-semibold ${isSelected ? 'text-current' : 'text-gray-900'}`}>
                    {type.title}
                  </h3>
                  <span className={`
                    inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                    ${getSeoStrengthColor(type.seoStrength)}
                  `}>
                    {type.seoStrength} SEO Impact
                  </span>
                </div>
              </div>

              {/* Description */}
              <p className={`text-sm mb-4 ${isSelected ? 'text-current opacity-80' : 'text-gray-600'}`}>
                {type.description}
              </p>

              {/* Word Range */}
              <div className="mb-4">
                <span className={`text-sm font-medium ${isSelected ? 'text-current' : 'text-gray-700'}`}>
                  {type.wordRange}
                </span>
              </div>

              {/* Features */}
              <div className="space-y-2">
                {type.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className={`w-1.5 h-1.5 rounded-full ${isSelected ? 'bg-current' : 'bg-gray-400'}`} />
                    <span className={`text-sm ${isSelected ? 'text-current opacity-75' : 'text-gray-600'}`}>
                      {feature}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )
        })}
      </div>

      {/* Configuration Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Word Count */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Target Word Count
          </label>
          <div className="space-y-3">
            <input
              type="range"
              min="100"
              max="5000"
              step="100"
              value={formData.wordCount}
              onChange={(e) => handleWordCountChange(parseInt(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex items-center justify-between text-sm text-gray-500">
              <span>100 words</span>
              <div className="text-center">
                <div className="text-lg font-semibold text-gray-900">
                  {formData.wordCount.toLocaleString()} words
                </div>
                <div className="text-xs">
                  ~{Math.ceil(formData.wordCount / 250)} min read
                </div>
              </div>
              <span>5,000 words</span>
            </div>
          </div>
        </div>

        {/* Tone Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Content Tone
          </label>
          <div className="space-y-2">
            {toneOptions.map((tone) => (
              <label key={tone.value} className="flex items-center cursor-pointer">
                <input
                  type="radio"
                  name="tone"
                  value={tone.value}
                  checked={formData.tone === tone.value}
                  onChange={(e) => handleToneChange(e.target.value)}
                  className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                />
                <div className="ml-3">
                  <div className="text-sm font-medium text-gray-900">{tone.label}</div>
                  <div className="text-xs text-gray-500">{tone.description}</div>
                </div>
              </label>
            ))}
          </div>
        </div>
      </div>

      {/* Content Type Info */}
      {formData.contentType && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 mb-3">
            {contentTypes.find(ct => ct.type === formData.contentType)?.title} Optimization
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-blue-800 mb-2">What we'll include:</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• SEO-optimized structure and headings</li>
                <li>• Strategic keyword placement and density</li>
                <li>• Meta tags and schema markup</li>
                <li>• Internal and external linking strategy</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-blue-800 mb-2">Best practices:</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• E-E-A-T compliance and authority signals</li>
                <li>• User intent optimization</li>
                <li>• Readability and engagement optimization</li>
                <li>• Conversion-focused calls-to-action</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}