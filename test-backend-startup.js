#!/usr/bin/env node

/**
 * Backend Startup Test
 * Tests if the backend can start without Supabase errors
 */

import { spawn } from 'child_process';
import fetch from 'node-fetch';

const BACKEND_URL = 'http://localhost:5000';
const STARTUP_TIMEOUT = 15000; // 15 seconds

function testBackendStartup() {
  return new Promise((resolve, reject) => {
    console.log('🧪 Testing Backend Startup...');
    
    // Start the backend server
    const backendProcess = spawn('npm', ['run', 'dev'], {
      cwd: 'F:\\Claude-Code-Setup\\SEO SAAS APP\\new seo saas app\\backend',
      stdio: ['ignore', 'pipe', 'pipe'],
      shell: true
    });

    let output = '';
    let hasStarted = false;
    let hasSupabaseError = false;

    // Capture stdout and stderr
    backendProcess.stdout.on('data', (data) => {
      output += data.toString();
      console.log('📝 Backend Output:', data.toString().trim());
      
      // Look for startup success indicators
      if (data.toString().includes('SEO SAAS Backend running on port')) {
        hasStarted = true;
        console.log('✅ Backend started successfully!');
      }
      
      // Look for Supabase warnings (should be present but not errors)
      if (data.toString().includes('Supabase not configured')) {
        console.log('⚠️  Expected: Supabase warning detected (this is normal for dev mode)');
      }
    });

    backendProcess.stderr.on('data', (data) => {
      output += data.toString();
      console.error('❌ Backend Error:', data.toString().trim());
      
      // Look for Supabase errors (should not happen now)
      if (data.toString().includes('supabaseUrl is required')) {
        hasSupabaseError = true;
        console.error('❌ FAILED: Supabase error still occurring');
      }
    });

    // Set timeout for startup
    const timeout = setTimeout(() => {
      backendProcess.kill();
      
      if (hasSupabaseError) {
        reject(new Error('Backend failed with Supabase errors'));
      } else if (!hasStarted) {
        reject(new Error('Backend failed to start within timeout'));
      } else {
        resolve('Backend started successfully without Supabase errors');
      }
    }, STARTUP_TIMEOUT);

    // Handle process exit
    backendProcess.on('exit', (code) => {
      clearTimeout(timeout);
      
      if (code !== 0 && !hasStarted) {
        reject(new Error(`Backend exited with code ${code}`));
      }
    });

    // If backend starts successfully, test health endpoint
    if (hasStarted) {
      setTimeout(async () => {
        try {
          const response = await fetch(`${BACKEND_URL}/api/health`);
          if (response.ok) {
            console.log('✅ Health check passed');
            backendProcess.kill();
            clearTimeout(timeout);
            resolve('Backend startup test completed successfully');
          }
        } catch (error) {
          console.log('⚠️  Health check failed, but startup was successful');
          backendProcess.kill();
          clearTimeout(timeout);
          resolve('Backend started but health check failed (normal if port in use)');
        }
      }, 3000);
    }
  });
}

async function runStartupTest() {
  console.log('🔧 SEO SAAS Backend Startup Test');
  console.log('='.repeat(40));
  
  try {
    const result = await testBackendStartup();
    console.log('🎉 SUCCESS:', result);
    
    console.log('\n💡 Next Steps:');
    console.log('   1. The backend should now start without Supabase errors');
    console.log('   2. Use the Windows PowerShell commands below to start normally:');
    console.log('   3. cd "F:\\Claude-Code-Setup\\SEO SAAS APP\\new seo saas app\\backend"');
    console.log('   4. npm run dev');
    
  } catch (error) {
    console.error('❌ FAILED:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Check if port 5000 is already in use');
    console.log('   2. Verify the backend directory path is correct');
    console.log('   3. Ensure npm dependencies are installed');
  }
}

// Run the test
runStartupTest();