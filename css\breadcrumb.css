/* Breadcrumb Navigation Styles - Professional navigation trail system */

/* ===========================
   BREADCRUMB CONTAINER
   =========================== */

.breadcrumb-container {
    background: rgba(249, 250, 251, 0.8);
    backdrop-filter: blur(8px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.6);
    padding: 12px 24px;
    margin: 0;
    position: sticky;
    top: 0;
    z-index: 100;
    transition: all 0.2s ease;
}

.breadcrumb-container:empty {
    display: none;
}

/* ===========================
   BREADCRUMB LIST
   =========================== */

.breadcrumb-list {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    padding: 0;
    list-style: none;
    flex-wrap: wrap;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    line-height: 1.5;
}

.breadcrumb-item:last-child .breadcrumb-separator {
    display: none;
}

/* ===========================
   BREADCRUMB LINKS
   =========================== */

.breadcrumb-link {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 10px;
    border-radius: 6px;
    text-decoration: none;
    color: #6b7280;
    font-weight: 500;
    transition: all 0.2s ease;
    position: relative;
    min-height: 32px;
}

.breadcrumb-link:hover {
    background-color: rgba(59, 130, 246, 0.08);
    color: #3b82f6;
    transform: translateY(-1px);
}

.breadcrumb-link:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
    background-color: rgba(59, 130, 246, 0.1);
}

.breadcrumb-link:active {
    transform: translateY(0);
}

.breadcrumb-hover {
    background-color: rgba(59, 130, 246, 0.06);
}

/* ===========================
   BREADCRUMB CURRENT PAGE
   =========================== */

.breadcrumb-current {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 10px;
    border-radius: 6px;
    color: #111827;
    font-weight: 600;
    background-color: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    min-height: 32px;
}

/* ===========================
   BREADCRUMB ICONS
   =========================== */

.breadcrumb-icon {
    flex-shrink: 0;
    color: currentColor;
    transition: transform 0.2s ease;
}

.breadcrumb-link:hover .breadcrumb-icon {
    transform: scale(1.1);
}

.breadcrumb-current .breadcrumb-icon {
    color: #3b82f6;
}

/* ===========================
   BREADCRUMB TEXT
   =========================== */

.breadcrumb-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
    transition: max-width 0.2s ease;
}

.breadcrumb-link:hover .breadcrumb-text {
    max-width: 200px;
}

/* ===========================
   BREADCRUMB SEPARATOR
   =========================== */

.breadcrumb-separator {
    color: #d1d5db;
    flex-shrink: 0;
    margin: 0 4px;
    transition: color 0.2s ease;
}

.breadcrumb-item:hover + .breadcrumb-item .breadcrumb-separator,
.breadcrumb-item:hover .breadcrumb-separator {
    color: #9ca3af;
}

/* ===========================
   MOBILE RESPONSIVE DESIGN
   =========================== */

@media (max-width: 768px) {
    .breadcrumb-container {
        padding: 8px 16px;
        background: rgba(255, 255, 255, 0.95);
    }
    
    .breadcrumb-list {
        gap: 6px;
        font-size: 13px;
    }
    
    .breadcrumb-text {
        max-width: 100px;
        font-size: 13px;
    }
    
    .breadcrumb-link:hover .breadcrumb-text {
        max-width: 120px;
    }
    
    .breadcrumb-link,
    .breadcrumb-current {
        padding: 4px 8px;
        min-height: 28px;
    }
    
    .breadcrumb-icon {
        width: 14px;
        height: 14px;
    }
    
    .breadcrumb-separator {
        width: 14px;
        height: 14px;
        margin: 0 2px;
    }
}

@media (max-width: 480px) {
    .breadcrumb-container {
        padding: 6px 12px;
    }
    
    .breadcrumb-text {
        max-width: 80px;
        font-size: 12px;
    }
    
    .breadcrumb-link:hover .breadcrumb-text {
        max-width: 100px;
    }
    
    /* Hide middle breadcrumbs on very small screens */
    .breadcrumb-item:not(:first-child):not(:last-child):not(:nth-last-child(2)) {
        display: none;
    }
    
    /* Show ellipsis indicator */
    .breadcrumb-list::before {
        content: '...';
        color: #9ca3af;
        margin: 0 4px;
        order: 1;
    }
    
    .breadcrumb-item:first-child {
        order: 0;
    }
    
    .breadcrumb-item:last-child {
        order: 2;
    }
}

/* ===========================
   BREADCRUMB ANIMATIONS
   =========================== */

.breadcrumb-container {
    animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.breadcrumb-item {
    animation: fadeInUp 0.3s ease-out;
    animation-fill-mode: both;
}

.breadcrumb-item:nth-child(1) { animation-delay: 0.1s; }
.breadcrumb-item:nth-child(2) { animation-delay: 0.15s; }
.breadcrumb-item:nth-child(3) { animation-delay: 0.2s; }
.breadcrumb-item:nth-child(4) { animation-delay: 0.25s; }
.breadcrumb-item:nth-child(5) { animation-delay: 0.3s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hover animation for breadcrumb trail */
.breadcrumb-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    border-radius: 1px;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.breadcrumb-link:hover::before {
    width: 80%;
}

/* ===========================
   BREADCRUMB VARIANTS
   =========================== */

/* Compact variant */
.breadcrumb-container.compact {
    padding: 6px 16px;
}

.breadcrumb-container.compact .breadcrumb-link,
.breadcrumb-container.compact .breadcrumb-current {
    padding: 4px 6px;
    font-size: 12px;
}

.breadcrumb-container.compact .breadcrumb-icon {
    width: 12px;
    height: 12px;
}

/* Minimal variant */
.breadcrumb-container.minimal {
    background: transparent;
    border-bottom: none;
    padding: 8px 0;
}

.breadcrumb-container.minimal .breadcrumb-link {
    padding: 4px 8px;
    background: none;
}

.breadcrumb-container.minimal .breadcrumb-current {
    background: none;
    border: none;
    font-weight: 700;
}

/* ===========================
   DARK MODE SUPPORT
   =========================== */

@media (prefers-color-scheme: dark) {
    .breadcrumb-container {
        background: rgba(17, 24, 39, 0.8);
        border-bottom-color: rgba(55, 65, 81, 0.6);
    }
    
    .breadcrumb-link {
        color: #d1d5db;
    }
    
    .breadcrumb-link:hover {
        background-color: rgba(59, 130, 246, 0.15);
        color: #60a5fa;
    }
    
    .breadcrumb-current {
        color: #f9fafb;
        background-color: rgba(59, 130, 246, 0.2);
        border-color: rgba(59, 130, 246, 0.3);
    }
    
    .breadcrumb-separator {
        color: #4b5563;
    }
    
    .breadcrumb-container.minimal {
        background: transparent;
    }
}

/* ===========================
   HIGH CONTRAST MODE
   =========================== */

@media (prefers-contrast: high) {
    .breadcrumb-link:focus {
        outline: 3px solid #000;
        outline-offset: 2px;
    }
    
    .breadcrumb-current {
        background-color: #000;
        color: #fff;
        border-color: #000;
    }
    
    .breadcrumb-separator {
        color: #000;
    }
}

/* ===========================
   REDUCED MOTION SUPPORT
   =========================== */

@media (prefers-reduced-motion: reduce) {
    .breadcrumb-container,
    .breadcrumb-item,
    .breadcrumb-link,
    .breadcrumb-icon,
    .breadcrumb-text,
    .breadcrumb-link::before {
        animation: none;
        transition: none;
    }
    
    .breadcrumb-link:hover {
        transform: none;
    }
    
    .breadcrumb-link:active {
        transform: none;
    }
}

/* ===========================
   PRINT STYLES
   =========================== */

@media print {
    .breadcrumb-container {
        background: none;
        border-bottom: 1px solid #ccc;
        padding: 8px 0;
    }
    
    .breadcrumb-link {
        color: #000;
        text-decoration: underline;
    }
    
    .breadcrumb-current {
        background: none;
        border: none;
        font-weight: bold;
    }
    
    .breadcrumb-icon {
        display: none;
    }
}

/* ===========================
   ACCESSIBILITY ENHANCEMENTS
   =========================== */

/* Screen reader only text for better context */
.breadcrumb-container::before {
    content: 'You are here: ';
    position: absolute;
    left: -10000px;
    top: auto;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

/* Focus management */
.breadcrumb-link:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Improved touch targets for mobile */
@media (pointer: coarse) {
    .breadcrumb-link,
    .breadcrumb-current {
        min-height: 44px;
        padding: 8px 12px;
    }
}