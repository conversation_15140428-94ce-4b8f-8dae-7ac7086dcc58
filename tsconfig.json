{"compilerOptions": {"target": "es2017", "lib": ["dom", "dom.iterable", "es2017", "es2019", "es2020"], "allowJs": true, "skipLibCheck": true, "strict": false, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "downlevelIteration": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./components/*"], "@/utils/*": ["./utils/*"], "@/hooks/*": ["./hooks/*"], "@/styles/*": ["./styles/*"], "@/types/*": ["./types/*"]}, "strictNullChecks": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}