import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import DashboardPage from '@/app/dashboard/page';

// Mock the API client
jest.mock('@/lib/api-client', () => ({
  analyticsAPI: {
    getDashboardData: jest.fn(),
  },
}));

// Mock the auth hook
jest.mock('@/hooks/useAuth', () => ({
  ProtectedRoute: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

// Mock the dashboard layout components
jest.mock('@/components/Layout/DashboardLayout', () => ({
  AuthenticatedLayout: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="authenticated-layout">{children}</div>
  ),
  PageHeader: ({ title, description }: { title: string; description: string }) => (
    <div data-testid="page-header">
      <h1>{title}</h1>
      <p>{description}</p>
    </div>
  ),
  DashboardGrid: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="dashboard-grid">{children}</div>
  ),
}));

// Mock the lazy loading components
jest.mock('@/components/UI/LazyLoad', () => ({
  MetricCardSkeleton: () => <div data-testid="metric-card-skeleton">Loading...</div>,
  ViewportLazy: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="viewport-lazy">{children}</div>
  ),
}));

// Mock dynamic imports
jest.mock('next/dynamic', () => {
  return (importFunc: () => Promise<any>) => {
    const Component = React.lazy(importFunc);
    return (props: any) => (
      <React.Suspense fallback={<div>Loading...</div>}>
        <Component {...props} />
      </React.Suspense>
    );
  };
});

// Mock MetricCard component
jest.mock('@/components/UI/MetricCard', () => {
  return function MockMetricCard({ title, value, loading }: any) {
    if (loading) {
      return <div data-testid="metric-card-skeleton">Loading...</div>;
    }
    return (
      <div data-testid="metric-card">
        <h3>{title}</h3>
        <div>{value}</div>
      </div>
    );
  };
});

// Mock RecentActivity component
jest.mock('@/components/UI/RecentActivity', () => {
  return function MockRecentActivity({ activities, loading }: any) {
    if (loading) {
      return <div data-testid="recent-activity-loading">Loading activities...</div>;
    }
    return (
      <div data-testid="recent-activity">
        <h3>Recent Activity</h3>
        {activities.map((activity: any) => (
          <div key={activity.id} data-testid="activity-item">
            {activity.title}
          </div>
        ))}
      </div>
    );
  };
});

const mockDashboardData = {
  success: true,
  data: {
    totalContent: 247,
    monthlyGeneration: [
      { month: '2024-01', count: 20 },
      { month: '2024-02', count: 32 },
    ],
    averageSeoScore: 94.2,
    topKeywords: ['seo', 'content', 'marketing'],
    totalProjects: 12,
    totalWords: 125000,
  },
};

describe('Dashboard Integration Tests', () => {
  let mockGetDashboardData: jest.Mock;

  beforeEach(() => {
    const { analyticsAPI } = require('@/lib/api-client');
    mockGetDashboardData = analyticsAPI.getDashboardData;
    mockGetDashboardData.mockClear();
  });

  it('renders dashboard with successful data fetch', async () => {
    mockGetDashboardData.mockResolvedValue(mockDashboardData);

    render(<DashboardPage />);

    // Check if layout components are rendered
    expect(screen.getByTestId('authenticated-layout')).toBeInTheDocument();
    expect(screen.getByTestId('page-header')).toBeInTheDocument();
    expect(screen.getByTestId('dashboard-grid')).toBeInTheDocument();

    // Check if page header content is correct
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText("Welcome back! Here's what's happening with your SEO campaigns.")).toBeInTheDocument();

    // Wait for API call to complete and data to be displayed
    await waitFor(() => {
      expect(mockGetDashboardData).toHaveBeenCalledTimes(1);
    });

    // Check if metric cards are rendered with actual data
    await waitFor(() => {
      const metricCards = screen.getAllByTestId('metric-card');
      expect(metricCards).toHaveLength(6); // 6 metric cards expected
    });

    // Check if specific metrics are displayed
    expect(screen.getByText('Content Generated')).toBeInTheDocument();
    expect(screen.getByText('Average SEO Score')).toBeInTheDocument();
    expect(screen.getByText('Keywords Tracked')).toBeInTheDocument();
    expect(screen.getByText('Active Projects')).toBeInTheDocument();
    expect(screen.getByText('Words Generated')).toBeInTheDocument();
    expect(screen.getByText('Avg. Ranking')).toBeInTheDocument();
  });

  it('handles API error gracefully by falling back to mock data', async () => {
    mockGetDashboardData.mockRejectedValue(new Error('API Error'));

    render(<DashboardPage />);

    // Wait for error handling and fallback to mock data
    await waitFor(() => {
      expect(mockGetDashboardData).toHaveBeenCalledTimes(1);
    });

    // Should still render metric cards with fallback data
    await waitFor(() => {
      const metricCards = screen.getAllByTestId('metric-card');
      expect(metricCards).toHaveLength(6);
    });

    // Check that console.error was called (mocked in jest.setup.js)
    expect(console.error).toHaveBeenCalled();
  });

  it('shows loading states initially', () => {
    mockGetDashboardData.mockImplementation(() => new Promise(() => {})); // Never resolves

    render(<DashboardPage />);

    // Check if loading skeletons are shown
    const loadingElements = screen.getAllByTestId('metric-card-skeleton');
    expect(loadingElements.length).toBeGreaterThan(0);
  });

  it('renders quick actions section', async () => {
    mockGetDashboardData.mockResolvedValue(mockDashboardData);

    render(<DashboardPage />);

    // Check if quick actions are rendered
    expect(screen.getByText('Quick Actions')).toBeInTheDocument();
    expect(screen.getByText('Create Content')).toBeInTheDocument();
    expect(screen.getByText('Analyze Competitors')).toBeInTheDocument();
    expect(screen.getByText('Bulk Generator')).toBeInTheDocument();
    expect(screen.getByText('New Project')).toBeInTheDocument();
  });

  it('renders recent activity section', async () => {
    mockGetDashboardData.mockResolvedValue(mockDashboardData);

    render(<DashboardPage />);

    // Check if recent activity section is rendered
    expect(screen.getByTestId('viewport-lazy')).toBeInTheDocument();
    
    // The RecentActivity component should be rendered within ViewportLazy
    await waitFor(() => {
      expect(screen.getByTestId('recent-activity')).toBeInTheDocument();
    });
  });

  it('renders pro tip section', async () => {
    mockGetDashboardData.mockResolvedValue(mockDashboardData);

    render(<DashboardPage />);

    // Check if pro tip section is rendered
    expect(screen.getByText('Pro Tip: Maximize Your SEO Impact')).toBeInTheDocument();
    expect(screen.getByText(/Start by analyzing your top competitors/)).toBeInTheDocument();
    expect(screen.getByText('Analyze Competitors')).toBeInTheDocument();
    expect(screen.getByText('View Tutorial')).toBeInTheDocument();
  });

  it('calculates monthly change correctly', async () => {
    const dataWithGrowth = {
      ...mockDashboardData,
      data: {
        ...mockDashboardData.data,
        monthlyGeneration: [
          { month: '2024-01', count: 20 },
          { month: '2024-02', count: 30 }, // 50% increase
        ],
      },
    };

    mockGetDashboardData.mockResolvedValue(dataWithGrowth);

    render(<DashboardPage />);

    await waitFor(() => {
      expect(mockGetDashboardData).toHaveBeenCalledTimes(1);
    });

    // The component should calculate the monthly change correctly
    // This would be visible in the MetricCard component for content generated
  });

  it('handles empty monthly generation data', async () => {
    const dataWithEmptyGeneration = {
      ...mockDashboardData,
      data: {
        ...mockDashboardData.data,
        monthlyGeneration: [],
      },
    };

    mockGetDashboardData.mockResolvedValue(dataWithEmptyGeneration);

    render(<DashboardPage />);

    await waitFor(() => {
      expect(mockGetDashboardData).toHaveBeenCalledTimes(1);
    });

    // Should handle empty data gracefully without crashing
    const metricCards = screen.getAllByTestId('metric-card');
    expect(metricCards).toHaveLength(6);
  });

  it('renders with correct accessibility attributes', async () => {
    mockGetDashboardData.mockResolvedValue(mockDashboardData);

    render(<DashboardPage />);

    await waitFor(() => {
      expect(mockGetDashboardData).toHaveBeenCalledTimes(1);
    });

    // Check that the main content area has proper structure
    const mainContent = screen.getByTestId('authenticated-layout');
    expect(mainContent).toBeInTheDocument();

    // Check that header has proper heading structure
    const heading = screen.getByRole('heading', { level: 1 });
    expect(heading).toHaveTextContent('Dashboard');
  });
});