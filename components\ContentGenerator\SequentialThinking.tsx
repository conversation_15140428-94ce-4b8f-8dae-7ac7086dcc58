/**
 * SequentialThinking Component
 * Enterprise SEO SAAS - Sequential AI reasoning visualization during content generation
 */

import { useState, useEffect } from 'react'
import {
  CpuChipIcon,
  LightBulbIcon,
  MagnifyingGlassIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  SparklesIcon,
  EyeIcon,
  BrainIcon,
  TargetIcon,
  ChartBarIcon,
  LinkIcon,
  BookOpenIcon,
  PuzzlePieceIcon,
  BeakerIcon
} from '@heroicons/react/24/outline'

interface SequentialThinkingProps {
  formData: {
    primaryKeyword: string
    secondaryKeywords: string[]
    competitors: string[]
    contentType: string
    wordCount: number
    tone: string
    targetLocation: string
  }
  isGenerating: boolean
}

interface ThinkingStep {
  id: string
  phase: 'input' | 'analysis' | 'planning' | 'generation' | 'optimization' | 'validation'
  title: string
  description: string
  status: 'pending' | 'processing' | 'completed' | 'error'
  progress: number
  details: string[]
  insights: string[]
  duration: number
  icon: any
  subSteps?: ThinkingSubStep[]
}

interface ThinkingSubStep {
  id: string
  title: string
  status: 'pending' | 'processing' | 'completed'
  result?: string
  confidence?: number
}

export default function SequentialThinking({ formData, isGenerating }: SequentialThinkingProps) {
  const [currentPhase, setCurrentPhase] = useState<string>('')
  const [thinkingSteps, setThinkingSteps] = useState<ThinkingStep[]>([])
  const [overallProgress, setOverallProgress] = useState(0)
  const [activeStepId, setActiveStepId] = useState<string>('')
  const [startTime, setStartTime] = useState<number>(0)
  const [currentThought, setCurrentThought] = useState<string>('')

  useEffect(() => {
    if (isGenerating) {
      initializeThinkingProcess()
    }
  }, [isGenerating])

  const initializeThinkingProcess = () => {
    setStartTime(Date.now())
    setOverallProgress(0)
    setCurrentPhase('Initializing AI reasoning...')
    
    const steps: ThinkingStep[] = [
      {
        id: 'input-validation',
        phase: 'input',
        title: 'Input Validation & Context Analysis',
        description: 'Analyzing user inputs and building context understanding',
        status: 'pending',
        progress: 0,
        details: [],
        insights: [],
        duration: 0,
        icon: MagnifyingGlassIcon,
        subSteps: [
          { id: 'keyword-analysis', title: 'Primary keyword analysis', status: 'pending' },
          { id: 'secondary-keywords', title: 'Secondary keywords processing', status: 'pending' },
          { id: 'competitor-validation', title: 'Competitor URLs validation', status: 'pending' },
          { id: 'content-specs', title: 'Content specifications review', status: 'pending' }
        ]
      },
      {
        id: 'competitor-intelligence',
        phase: 'analysis',
        title: 'Competitor Intelligence Gathering',
        description: 'Deep analysis of competitor content strategies and patterns',
        status: 'pending',
        progress: 0,
        details: [],
        insights: [],
        duration: 0,
        icon: EyeIcon,
        subSteps: [
          { id: 'content-extraction', title: 'Content structure extraction', status: 'pending' },
          { id: 'keyword-patterns', title: 'Keyword usage patterns', status: 'pending' },
          { id: 'content-gaps', title: 'Content gap identification', status: 'pending' },
          { id: 'ranking-factors', title: 'Ranking factor analysis', status: 'pending' }
        ]
      },
      {
        id: 'strategic-planning',
        phase: 'planning',
        title: 'Strategic Content Planning',
        description: 'Developing content strategy based on competitive intelligence',
        status: 'pending',
        progress: 0,
        details: [],
        insights: [],
        duration: 0,
        icon: TargetIcon,
        subSteps: [
          { id: 'outline-creation', title: 'Content outline development', status: 'pending' },
          { id: 'keyword-placement', title: 'Strategic keyword placement', status: 'pending' },
          { id: 'link-strategy', title: 'Internal linking strategy', status: 'pending' },
          { id: 'authority-sources', title: 'Authority source identification', status: 'pending' }
        ]
      },
      {
        id: 'content-synthesis',
        phase: 'generation',
        title: 'AI Content Synthesis',
        description: 'Generating high-quality, SEO-optimized content',
        status: 'pending',
        progress: 0,
        details: [],
        insights: [],
        duration: 0,
        icon: SparklesIcon,
        subSteps: [
          { id: 'introduction', title: 'Introduction section', status: 'pending' },
          { id: 'main-content', title: 'Main content sections', status: 'pending' },
          { id: 'faq-generation', title: 'FAQ section creation', status: 'pending' },
          { id: 'conclusion', title: 'Conclusion and CTAs', status: 'pending' }
        ]
      },
      {
        id: 'seo-optimization',
        phase: 'optimization',
        title: 'SEO Enhancement & Optimization',
        description: 'Optimizing content for search engines and user experience',
        status: 'pending',
        progress: 0,
        details: [],
        insights: [],
        duration: 0,
        icon: ChartBarIcon,
        subSteps: [
          { id: 'keyword-density', title: 'Keyword density optimization', status: 'pending' },
          { id: 'meta-elements', title: 'Meta title and description', status: 'pending' },
          { id: 'schema-markup', title: 'Schema markup implementation', status: 'pending' },
          { id: 'readability', title: 'Readability enhancement', status: 'pending' }
        ]
      },
      {
        id: 'quality-validation',
        phase: 'validation',
        title: 'Quality Assurance & Validation',
        description: 'Final validation and quality checks',
        status: 'pending',
        progress: 0,
        details: [],
        insights: [],
        duration: 0,
        icon: CheckCircleIcon,
        subSteps: [
          { id: 'content-quality', title: 'Content quality assessment', status: 'pending' },
          { id: 'seo-compliance', title: 'SEO compliance check', status: 'pending' },
          { id: 'plagiarism-check', title: 'Originality verification', status: 'pending' },
          { id: 'final-review', title: 'Final human-like review', status: 'pending' }
        ]
      }
    ]

    setThinkingSteps(steps)
    startSequentialProcessing(steps)
  }

  const startSequentialProcessing = async (steps: ThinkingStep[]) => {
    for (let i = 0; i < steps.length; i++) {
      const step = steps[i]
      setActiveStepId(step.id)
      setCurrentPhase(`Phase ${i + 1}: ${step.title}`)
      
      await processThinkingStep(step, i)
      
      setOverallProgress(((i + 1) / steps.length) * 100)
    }
  }

  const processThinkingStep = async (step: ThinkingStep, stepIndex: number) => {
    const stepStartTime = Date.now()
    
    // Update step status to processing
    setThinkingSteps(prev => prev.map(s => 
      s.id === step.id ? { ...s, status: 'processing' as const } : s
    ))

    // Process sub-steps
    if (step.subSteps) {
      for (let i = 0; i < step.subSteps.length; i++) {
        const subStep = step.subSteps[i]
        
        // Update sub-step to processing
        setThinkingSteps(prev => prev.map(s => 
          s.id === step.id 
            ? {
                ...s,
                subSteps: s.subSteps?.map(ss => 
                  ss.id === subStep.id ? { ...ss, status: 'processing' as const } : ss
                )
              }
            : s
        ))

        // Simulate processing time and generate insights
        await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 1200))
        
        const subStepResult = generateSubStepResult(step.phase, subStep.title, stepIndex)
        
        // Complete sub-step
        setThinkingSteps(prev => prev.map(s => 
          s.id === step.id 
            ? {
                ...s,
                subSteps: s.subSteps?.map(ss => 
                  ss.id === subStep.id 
                    ? { 
                        ...ss, 
                        status: 'completed' as const,
                        result: subStepResult.result,
                        confidence: subStepResult.confidence
                      } 
                    : ss
                ),
                progress: ((i + 1) / step.subSteps.length) * 100
              }
            : s
        ))

        // Update current thought
        setCurrentThought(subStepResult.thought)
      }
    }

    // Generate step insights and details
    const stepResult = generateStepResult(step.phase, formData)
    
    // Complete the step
    setThinkingSteps(prev => prev.map(s => 
      s.id === step.id 
        ? { 
            ...s, 
            status: 'completed' as const,
            progress: 100,
            details: stepResult.details,
            insights: stepResult.insights,
            duration: Date.now() - stepStartTime
          } 
        : s
    ))
  }

  const generateSubStepResult = (phase: string, title: string, stepIndex: number) => {
    const results = {
      input: {
        'Primary keyword analysis': {
          result: `"${formData.primaryKeyword}" - Commercial intent detected, search volume estimated 10K-50K/month`,
          confidence: 92,
          thought: `Analyzing "${formData.primaryKeyword}" for search intent and competition level...`
        },
        'Secondary keywords processing': {
          result: `${formData.secondaryKeywords.length} secondary keywords validated for semantic relevance`,
          confidence: 88,
          thought: 'Building semantic keyword cluster for comprehensive topic coverage...'
        },
        'Competitor URLs validation': {
          result: `${formData.competitors.length} competitor websites analyzed and validated`,
          confidence: 95,
          thought: 'Validating competitor domains and checking accessibility for analysis...'
        },
        'Content specifications review': {
          result: `${formData.contentType} format, ${formData.wordCount} words, ${formData.tone} tone confirmed`,
          confidence: 100,
          thought: 'Reviewing content specifications to ensure optimal output format...'
        }
      },
      analysis: {
        'Content structure extraction': {
          result: 'Average competitor uses 7-section structure with H2-H4 hierarchy',
          confidence: 87,
          thought: 'Extracting content structures from top-ranking pages to identify patterns...'
        },
        'Keyword usage patterns': {
          result: 'Primary keyword appears 12-18 times, LSI keywords heavily used',
          confidence: 91,
          thought: 'Analyzing keyword density and placement patterns across competitors...'
        },
        'Content gap identification': {
          result: '4 major content gaps identified with high-impact potential',
          confidence: 89,
          thought: 'Identifying opportunities where competitors lack comprehensive coverage...'
        },
        'Ranking factor analysis': {
          result: 'Content length, internal links, and E-A-T signals are key factors',
          confidence: 84,
          thought: 'Analyzing what makes top competitors rank well for target keywords...'
        }
      },
      planning: {
        'Content outline development': {
          result: '8-section outline created based on competitor analysis and user intent',
          confidence: 93,
          thought: 'Creating comprehensive outline that surpasses competitor content depth...'
        },
        'Strategic keyword placement': {
          result: 'Keywords strategically placed in H1, H2s, intro, and throughout content',
          confidence: 96,
          thought: 'Planning optimal keyword placement for maximum SEO impact...'
        },
        'Internal linking strategy': {
          result: '12-15 internal links planned with relevant anchor text variations',
          confidence: 85,
          thought: 'Developing internal linking strategy based on site architecture...'
        },
        'Authority source identification': {
          result: '5 high-authority sources identified for external linking',
          confidence: 91,
          thought: 'Identifying authoritative sources to boost content credibility...'
        }
      },
      generation: {
        'Introduction section': {
          result: 'Compelling 200-word introduction with hook and value proposition',
          confidence: 94,
          thought: 'Crafting engaging introduction that captures attention and previews value...'
        },
        'Main content sections': {
          result: `${Math.floor(formData.wordCount * 0.7)} words of in-depth, actionable content generated`,
          confidence: 92,
          thought: 'Generating comprehensive main content with practical insights and examples...'
        },
        'FAQ section creation': {
          result: '8 frequently asked questions with detailed answers created',
          confidence: 89,
          thought: 'Creating FAQ section optimized for voice search and featured snippets...'
        },
        'Conclusion and CTAs': {
          result: 'Persuasive conclusion with clear next steps and call-to-action',
          confidence: 95,
          thought: 'Crafting conclusion that summarizes value and guides user action...'
        }
      },
      optimization: {
        'Keyword density optimization': {
          result: `Primary keyword density optimized to 1.5%, LSI keywords naturally integrated`,
          confidence: 97,
          thought: 'Fine-tuning keyword density for optimal SEO without over-optimization...'
        },
        'Meta title and description': {
          result: 'SEO-optimized meta title (58 chars) and description (155 chars) created',
          confidence: 98,
          thought: 'Creating compelling meta elements that improve click-through rates...'
        },
        'Schema markup implementation': {
          result: 'Article schema and FAQ schema markup added for rich snippets',
          confidence: 93,
          thought: 'Implementing structured data for enhanced search result appearance...'
        },
        'Readability enhancement': {
          result: 'Flesch Reading Score: 65 (easy to read), optimized for target audience',
          confidence: 91,
          thought: 'Optimizing content readability for better user engagement...'
        }
      },
      validation: {
        'Content quality assessment': {
          result: 'Quality score: 94/100 - Comprehensive, actionable, and well-structured',
          confidence: 96,
          thought: 'Assessing content quality against industry best practices...'
        },
        'SEO compliance check': {
          result: 'All SEO best practices implemented - Technical score: 98/100',
          confidence: 99,
          thought: 'Validating SEO compliance across all ranking factors...'
        },
        'Originality verification': {
          result: '100% original content - No plagiarism detected',
          confidence: 100,
          thought: 'Verifying content originality and uniqueness...'
        },
        'Final human-like review': {
          result: 'Content flows naturally, provides value, and meets user intent',
          confidence: 95,
          thought: 'Conducting final review for natural flow and user value...'
        }
      }
    }

    const phaseResults = results[phase as keyof typeof results] || {}
    const result = phaseResults[title as keyof typeof phaseResults] || {
      result: 'Processing completed successfully',
      confidence: 90,
      thought: 'Processing step...'
    }

    return result
  }

  const generateStepResult = (phase: string, formData: any) => {
    const stepResults = {
      input: {
        details: [
          `Primary keyword: "${formData.primaryKeyword}" validated for search intent`,
          `${formData.secondaryKeywords.length} secondary keywords processed`,
          `${formData.competitors.length} competitor URLs validated and accessible`,
          `Content type: ${formData.contentType}, Target length: ${formData.wordCount} words`
        ],
        insights: [
          'Commercial search intent detected - focus on solution-oriented content',
          'Semantic keyword opportunities identified for comprehensive coverage',
          'Competitor landscape analysis reveals strong competition requiring differentiation',
          'Content specifications align with industry best practices'
        ]
      },
      analysis: {
        details: [
          'Extracted content structures from top 10 ranking pages',
          'Identified keyword usage patterns and density optimization opportunities',
          'Mapped content gaps where competitors provide insufficient coverage',
          'Analyzed ranking factors contributing to competitor success'
        ],
        insights: [
          'Competitors average 2,100 words - opportunity for 3,000+ word comprehensive guide',
          'FAQ sections missing in 60% of competitor content',
          'Internal linking patterns show 8-15 links per article average',
          'Authority signals strongly correlated with higher rankings'
        ]
      },
      planning: {
        details: [
          'Created 8-section content outline exceeding competitor depth',
          'Planned strategic keyword placement across headers and content',
          'Developed internal linking strategy with 12-15 contextual links',
          'Identified 5 high-authority external sources for credibility'
        ],
        insights: [
          'Comprehensive outline addresses all user intent signals',
          'Keyword placement strategy balances SEO and readability',
          'Internal linking will distribute page authority effectively',
          'External authority links will boost E-A-T signals'
        ]
      },
      generation: {
        details: [
          'Generated compelling introduction with clear value proposition',
          `Created ${Math.floor(formData.wordCount * 0.7)} words of main content`,
          'Developed 8-question FAQ section for voice search optimization',
          'Crafted persuasive conclusion with clear call-to-action'
        ],
        insights: [
          'Content structure follows proven high-ranking patterns',
          'Practical examples and actionable advice throughout',
          'FAQ section targets featured snippet opportunities',
          'Natural keyword integration maintains readability'
        ]
      },
      optimization: {
        details: [
          `Optimized keyword density to 1.5% for primary keyword`,
          'Created SEO-optimized meta title and description',
          'Implemented Article and FAQ schema markup',
          'Achieved optimal readability score for target audience'
        ],
        insights: [
          'Keyword optimization balances SEO impact with natural flow',
          'Meta elements optimized for click-through rate improvement',
          'Schema markup enhances rich snippet eligibility',
          'Readability optimization improves user engagement metrics'
        ]
      },
      validation: {
        details: [
          'Content quality assessment: 94/100 score achieved',
          'SEO compliance check: All best practices implemented',
          'Originality verification: 100% unique content confirmed',
          'Final review: Natural flow and user value validated'
        ],
        insights: [
          'Content exceeds industry quality standards',
          'Technical SEO implementation is comprehensive',
          'Originality ensures no duplicate content penalties',
          'Human-like quality enhances user experience'
        ]
      }
    }

    return stepResults[phase as keyof typeof stepResults] || {
      details: ['Processing completed'],
      insights: ['Step completed successfully']
    }
  }

  const getPhaseIcon = (phase: string) => {
    const icons = {
      input: MagnifyingGlassIcon,
      analysis: EyeIcon,
      planning: TargetIcon,
      generation: SparklesIcon,
      optimization: ChartBarIcon,
      validation: CheckCircleIcon
    }
    return icons[phase as keyof typeof icons] || CpuChipIcon
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100 border-green-200'
      case 'processing': return 'text-blue-600 bg-blue-100 border-blue-200'
      case 'error': return 'text-red-600 bg-red-100 border-red-200'
      default: return 'text-gray-600 bg-gray-100 border-gray-200'
    }
  }

  const formatDuration = (ms: number) => {
    return `${(ms / 1000).toFixed(1)}s`
  }

  return (
    <div className="p-8 space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Sequential AI Thinking</h2>
        <p className="text-gray-600">
          Observing the AI's step-by-step reasoning process for optimal content generation
        </p>
      </div>

      {/* Overall Progress */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <BrainIcon className="h-6 w-6 text-blue-600" />
            <span className="font-medium text-blue-900">AI Processing Status</span>
          </div>
          <span className="text-sm text-blue-600 font-medium">{Math.round(overallProgress)}% Complete</span>
        </div>
        
        <div className="w-full bg-blue-200 rounded-full h-3 mb-3">
          <div 
            className="bg-gradient-to-r from-blue-600 to-purple-600 h-3 rounded-full transition-all duration-300"
            style={{ width: `${overallProgress}%` }}
          />
        </div>
        
        {currentPhase && (
          <div className="text-sm text-blue-700">
            {currentPhase}
          </div>
        )}
      </div>

      {/* Current Thought Display */}
      {currentThought && isGenerating && (
        <div className="bg-white border-l-4 border-purple-500 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <LightBulbIcon className="h-5 w-5 text-purple-500 mt-0.5 flex-shrink-0" />
            <div>
              <div className="text-sm font-medium text-purple-900 mb-1">Current Thought</div>
              <div className="text-sm text-purple-700">{currentThought}</div>
            </div>
          </div>
        </div>
      )}

      {/* Thinking Steps */}
      <div className="space-y-6">
        {thinkingSteps.map((step, index) => {
          const Icon = step.icon
          const isActive = activeStepId === step.id
          
          return (
            <div 
              key={step.id}
              className={`
                border-2 rounded-lg transition-all duration-300
                ${isActive 
                  ? 'border-blue-500 bg-blue-50 shadow-lg' 
                  : step.status === 'completed'
                  ? 'border-green-300 bg-green-50'
                  : 'border-gray-200 bg-white'
                }
              `}
            >
              <div className="p-6">
                {/* Step Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-start gap-4">
                    <div className={`
                      w-12 h-12 rounded-lg flex items-center justify-center
                      ${step.status === 'completed' 
                        ? 'bg-green-600 text-white' 
                        : step.status === 'processing'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 text-gray-600'
                      }
                    `}>
                      {step.status === 'processing' ? (
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white" />
                      ) : (
                        <Icon className="h-6 w-6" />
                      )}
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-1">{step.title}</h3>
                      <p className="text-sm text-gray-600">{step.description}</p>
                      
                      {step.status === 'completed' && step.duration > 0 && (
                        <div className="text-xs text-gray-500 mt-1">
                          Completed in {formatDuration(step.duration)}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className={`
                    inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border
                    ${getStatusColor(step.status)}
                  `}>
                    {step.status === 'processing' && <ClockIcon className="h-3 w-3 mr-1" />}
                    {step.status === 'completed' && <CheckCircleIcon className="h-3 w-3 mr-1" />}
                    {step.status.charAt(0).toUpperCase() + step.status.slice(1)}
                  </div>
                </div>

                {/* Progress Bar */}
                {(step.status === 'processing' || step.status === 'completed') && (
                  <div className="mb-4">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full transition-all duration-300 ${
                          step.status === 'completed' ? 'bg-green-500' : 'bg-blue-500'
                        }`}
                        style={{ width: `${step.progress}%` }}
                      />
                    </div>
                  </div>
                )}

                {/* Sub-steps */}
                {step.subSteps && step.subSteps.length > 0 && (
                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-gray-900 mb-3">Processing Steps</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {step.subSteps.map((subStep) => (
                        <div 
                          key={subStep.id}
                          className={`
                            p-3 rounded-lg border text-sm
                            ${subStep.status === 'completed' 
                              ? 'bg-green-50 border-green-200 text-green-800' 
                              : subStep.status === 'processing'
                              ? 'bg-blue-50 border-blue-200 text-blue-800'
                              : 'bg-gray-50 border-gray-200 text-gray-600'
                            }
                          `}
                        >
                          <div className="flex items-center justify-between mb-1">
                            <span className="font-medium">{subStep.title}</span>
                            {subStep.status === 'processing' && (
                              <div className="animate-spin rounded-full h-3 w-3 border-b border-current" />
                            )}
                            {subStep.status === 'completed' && (
                              <CheckCircleIcon className="h-3 w-3" />
                            )}
                          </div>
                          
                          {subStep.result && (
                            <div className="text-xs mb-1">{subStep.result}</div>
                          )}
                          
                          {subStep.confidence && (
                            <div className="text-xs">
                              Confidence: {subStep.confidence}%
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Step Results */}
                {step.status === 'completed' && (step.details.length > 0 || step.insights.length > 0) && (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Details */}
                    {step.details.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center gap-2">
                          <DocumentTextIcon className="h-4 w-4" />
                          Processing Details
                        </h4>
                        <ul className="space-y-2">
                          {step.details.map((detail, index) => (
                            <li key={index} className="text-sm text-gray-600 flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0" />
                              {detail}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Insights */}
                    {step.insights.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center gap-2">
                          <LightBulbIcon className="h-4 w-4" />
                          Key Insights
                        </h4>
                        <ul className="space-y-2">
                          {step.insights.map((insight, index) => (
                            <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                              <SparklesIcon className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                              {insight}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )
        })}
      </div>

      {/* Summary */}
      {!isGenerating && thinkingSteps.length > 0 && thinkingSteps.every(s => s.status === 'completed') && (
        <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-green-900 mb-4 flex items-center gap-2">
            <CheckCircleIcon className="h-5 w-5" />
            AI Processing Complete
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <div className="font-medium text-green-800 mb-1">Total Processing Time</div>
              <div className="text-green-700">
                {formatDuration(thinkingSteps.reduce((sum, step) => sum + step.duration, 0))}
              </div>
            </div>
            
            <div>
              <div className="font-medium text-green-800 mb-1">Steps Completed</div>
              <div className="text-green-700">
                {thinkingSteps.length}/6 phases
              </div>
            </div>
            
            <div>
              <div className="font-medium text-green-800 mb-1">Content Quality</div>
              <div className="text-green-700">
                Enterprise-grade SEO optimization
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}