'use client';

import React from 'react';
import Link from 'next/link';
import {
  DocumentTextIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  FolderIcon,
  TrendingUpIcon
} from '@heroicons/react/24/outline';

export default function SimpleDashboardPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900">
      {/* Header */}
      <header className="bg-white/10 backdrop-blur-md border-b border-white/20">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="text-2xl font-bold text-white">
              SEO SAAS
            </Link>
            <nav className="flex space-x-6">
              <Link href="/content-generator" className="text-white hover:text-blue-300 transition-colors">
                Content Generator
              </Link>
              <Link href="/" className="text-white hover:text-blue-300 transition-colors">
                Home
              </Link>
            </nav>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-12">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-white mb-4">
              ✅ SEO Dashboard - Working!
            </h1>
            <p className="text-xl text-gray-300">
              Your SEO SAAS application is fully operational
            </p>
          </div>

          {/* Status Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
            <div className="bg-green-600/20 backdrop-blur-md rounded-xl p-6 border border-green-500/50">
              <div className="flex items-center">
                <DocumentTextIcon className="h-8 w-8 text-green-400 mr-3" />
                <div>
                  <p className="text-gray-300 text-sm">Backend Status</p>
                  <p className="text-2xl font-bold text-green-400">✅ Online</p>
                </div>
              </div>
            </div>
            
            <div className="bg-green-600/20 backdrop-blur-md rounded-xl p-6 border border-green-500/50">
              <div className="flex items-center">
                <FolderIcon className="h-8 w-8 text-green-400 mr-3" />
                <div>
                  <p className="text-gray-300 text-sm">Frontend Status</p>
                  <p className="text-2xl font-bold text-green-400">✅ Online</p>
                </div>
              </div>
            </div>
            
            <div className="bg-green-600/20 backdrop-blur-md rounded-xl p-6 border border-green-500/50">
              <div className="flex items-center">
                <TrendingUpIcon className="h-8 w-8 text-green-400 mr-3" />
                <div>
                  <p className="text-gray-300 text-sm">AI Generation</p>
                  <p className="text-2xl font-bold text-green-400">✅ Working</p>
                </div>
              </div>
            </div>
            
            <div className="bg-green-600/20 backdrop-blur-md rounded-xl p-6 border border-green-500/50">
              <div className="flex items-center">
                <ChartBarIcon className="h-8 w-8 text-green-400 mr-3" />
                <div>
                  <p className="text-gray-300 text-sm">API Status</p>
                  <p className="text-2xl font-bold text-green-400">✅ Ready</p>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-8 border border-white/20 mb-12">
            <h2 className="text-2xl font-bold text-white mb-6">🚀 Start Creating Content</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Link
                href="/content-generator"
                className="bg-blue-600/30 hover:bg-blue-600/40 border border-blue-500/50 rounded-lg p-6 transition-colors group"
              >
                <div className="flex items-center space-x-4">
                  <DocumentTextIcon className="h-8 w-8 text-blue-400" />
                  <div>
                    <h3 className="text-white font-bold text-lg">Generate SEO Content</h3>
                    <p className="text-gray-300">Create high-quality, SEO-optimized content with AI</p>
                  </div>
                </div>
              </Link>

              <Link
                href="/test-content-generation.html"
                className="bg-green-600/30 hover:bg-green-600/40 border border-green-500/50 rounded-lg p-6 transition-colors group"
              >
                <div className="flex items-center space-x-4">
                  <MagnifyingGlassIcon className="h-8 w-8 text-green-400" />
                  <div>
                    <h3 className="text-white font-bold text-lg">Test API Directly</h3>
                    <p className="text-gray-300">Test content generation API with debugging</p>
                  </div>
                </div>
              </Link>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-8 border border-white/20">
            <h3 className="text-2xl font-bold text-white mb-6">📋 How to Use</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h4 className="text-lg font-semibold text-white mb-4">Content Generator</h4>
                <ol className="text-gray-300 space-y-2">
                  <li>1. Enter your target keyword</li>
                  <li>2. Select target country and content type</li>
                  <li>3. Choose tone and length</li>
                  <li>4. Click "Generate SEO Content"</li>
                  <li>5. Wait 30-60 seconds for AI generation</li>
                  <li>6. Copy or download your content</li>
                </ol>
              </div>
              <div>
                <h4 className="text-lg font-semibold text-white mb-4">API Testing</h4>
                <ol className="text-gray-300 space-y-2">
                  <li>1. Use the test page for debugging</li>
                  <li>2. Check browser console for errors</li>
                  <li>3. Verify backend is running on port 5000</li>
                  <li>4. Test with simple keywords first</li>
                  <li>5. Check network tab for API responses</li>
                </ol>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
