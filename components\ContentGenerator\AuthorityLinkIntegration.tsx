/**
 * AuthorityLinkIntegration Component
 * Enterprise SEO SAAS - Authority link preview and selection for content generation
 */

import { useState, useEffect } from 'react'
import { AuthorityLinkDiscovery, AuthorityLink, LinkDiscoveryRequest } from '@/utils/authorityLinkDiscovery'
import { DomainAuthorityAssessment } from '@/utils/domainAuthorityAssessment'
import {
  BookOpenIcon,
  LinkIcon,
  CheckCircleIcon,
  XMarkIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon,
  StarIcon,
  EyeIcon,
  AcademicCapIcon,
  BuildingOfficeIcon,
  NewspaperIcon,
  ChartBarIcon,
  GlobeAltIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  FunnelIcon,
  AdjustmentsHorizontalIcon,
  ClockIcon,
  ShieldCheckIcon,
  BoltIcon,
  CogIcon
} from '@heroicons/react/24/outline'

interface AuthorityLinkIntegrationProps {
  isVisible: boolean
  onClose: () => void
  topic: string
  keywords: string[]
  authorityLinkSettings: {
    maxLinks: number
    minAuthorityScore: number
    sourceTypes: string[]
    includeWikipedia: boolean
    includeAcademic: boolean
    includeGovernment: boolean
    includeNews: boolean
    includeIndustry: boolean
  }
  onLinksSelected: (links: AuthorityLink[]) => void
}

export default function AuthorityLinkIntegration({
  isVisible,
  onClose,
  topic,
  keywords,
  authorityLinkSettings,
  onLinksSelected
}: AuthorityLinkIntegrationProps) {
  const [discoveredLinks, setDiscoveredLinks] = useState<AuthorityLink[]>([])
  const [selectedLinks, setSelectedLinks] = useState<Set<string>>(new Set())
  const [isDiscovering, setIsDiscovering] = useState(false)
  const [discoveryComplete, setDiscoveryComplete] = useState(false)
  const [filterType, setFilterType] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'authority' | 'relevance' | 'type'>('authority')
  const [previewMode, setPreviewMode] = useState<'grid' | 'list'>('grid')

  const [authorityDiscovery] = useState(() => new AuthorityLinkDiscovery())
  const [domainAssessment] = useState(() => new DomainAuthorityAssessment())

  // Automatically discover links when component becomes visible
  useEffect(() => {
    if (isVisible && topic && !discoveryComplete) {
      discoverAuthorityLinks()
    }
  }, [isVisible, topic])

  const discoverAuthorityLinks = async () => {
    if (!topic) return

    setIsDiscovering(true)
    setDiscoveredLinks([])
    setSelectedLinks(new Set())

    try {
      const discoveryRequest: LinkDiscoveryRequest = {
        topic,
        keywords,
        targetLanguage: 'en',
        maxResults: authorityLinkSettings.maxLinks * 2, // Get more for filtering
        sourceTypes: buildSourceTypes(),
        minAuthorityScore: authorityLinkSettings.minAuthorityScore,
        includeWikipedia: authorityLinkSettings.includeWikipedia,
        includeReferences: true
      }

      const result = await authorityDiscovery.discoverAuthorityLinks(discoveryRequest)
      setDiscoveredLinks(result.discoveredLinks)
      setDiscoveryComplete(true)

      // Auto-select top links based on settings
      const topLinks = result.discoveredLinks
        .sort((a, b) => (b.authorityScore * b.relevanceScore) - (a.authorityScore * a.relevanceScore))
        .slice(0, authorityLinkSettings.maxLinks)
      
      setSelectedLinks(new Set(topLinks.map(link => link.id)))

    } catch (error) {
      console.error('Authority link discovery failed:', error)
    } finally {
      setIsDiscovering(false)
    }
  }

  const buildSourceTypes = (): string[] => {
    const types: string[] = []
    if (authorityLinkSettings.includeWikipedia) types.push('wikipedia')
    if (authorityLinkSettings.includeAcademic) types.push('academic')
    if (authorityLinkSettings.includeGovernment) types.push('government')
    if (authorityLinkSettings.includeNews) types.push('news')
    if (authorityLinkSettings.includeIndustry) types.push('industry')
    return types
  }

  const handleLinkToggle = (linkId: string) => {
    const newSelected = new Set(selectedLinks)
    if (newSelected.has(linkId)) {
      newSelected.delete(linkId)
    } else {
      if (newSelected.size < authorityLinkSettings.maxLinks) {
        newSelected.add(linkId)
      }
    }
    setSelectedLinks(newSelected)
  }

  const handleSelectAll = () => {
    const filteredLinks = getFilteredAndSortedLinks()
    const maxSelectable = Math.min(authorityLinkSettings.maxLinks, filteredLinks.length)
    const topLinks = filteredLinks.slice(0, maxSelectable)
    setSelectedLinks(new Set(topLinks.map(link => link.id)))
  }

  const handleDeselectAll = () => {
    setSelectedLinks(new Set())
  }

  const handleConfirmSelection = () => {
    const selectedLinkObjects = discoveredLinks.filter(link => selectedLinks.has(link.id))
    onLinksSelected(selectedLinkObjects)
    onClose()
  }

  const getFilteredAndSortedLinks = () => {
    let filtered = discoveredLinks

    // Apply type filter
    if (filterType !== 'all') {
      filtered = filtered.filter(link => link.sourceType === filterType)
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'authority':
          return b.authorityScore - a.authorityScore
        case 'relevance':
          return b.relevanceScore - a.relevanceScore
        case 'type':
          return a.sourceType.localeCompare(b.sourceType)
        default:
          return 0
      }
    })

    return filtered
  }

  const getSourceTypeIcon = (sourceType: string) => {
    switch (sourceType) {
      case 'wikipedia': return BookOpenIcon
      case 'academic': return AcademicCapIcon
      case 'government': return BuildingOfficeIcon
      case 'news': return NewspaperIcon
      case 'industry': return ChartBarIcon
      default: return LinkIcon
    }
  }

  const getSourceTypeColor = (sourceType: string) => {
    switch (sourceType) {
      case 'wikipedia': return 'text-blue-600 bg-blue-100'
      case 'academic': return 'text-purple-600 bg-purple-100'
      case 'government': return 'text-green-600 bg-green-100'
      case 'news': return 'text-orange-600 bg-orange-100'
      case 'industry': return 'text-indigo-600 bg-indigo-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getAuthorityScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-100'
    if (score >= 80) return 'text-blue-600 bg-blue-100'
    if (score >= 70) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  const sourceTypes = [
    { value: 'all', label: 'All Sources' },
    { value: 'wikipedia', label: 'Wikipedia' },
    { value: 'academic', label: 'Academic' },
    { value: 'government', label: 'Government' },
    { value: 'news', label: 'News' },
    { value: 'industry', label: 'Industry' }
  ]

  if (!isVisible) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-7xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="bg-white bg-opacity-20 p-3 rounded-lg">
                <BookOpenIcon className="h-8 w-8" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">Authority Link Selection</h2>
                <p className="text-blue-100">
                  Discover and select high-authority links for: "{topic}"
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto" style={{ maxHeight: 'calc(90vh - 200px)' }}>
          {/* Discovery Progress */}
          {isDiscovering && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
              <div className="flex items-center gap-3 mb-4">
                <ArrowPathIcon className="h-6 w-6 text-blue-600 animate-spin" />
                <span className="font-medium text-blue-900">Discovering authority links...</span>
              </div>
              <div className="space-y-2 text-sm text-blue-800">
                <div>• Searching Wikipedia articles and references</div>
                <div>• Analyzing academic and government sources</div>
                <div>• Assessing domain authority and trust scores</div>
                <div>• Validating link quality and relevance</div>
              </div>
            </div>
          )}

          {/* Discovery Results */}
          {discoveryComplete && !isDiscovering && (
            <>
              {/* Stats and Controls */}
              <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-900">{discoveredLinks.length}</div>
                      <div className="text-sm text-gray-500">Links Found</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{selectedLinks.size}</div>
                      <div className="text-sm text-gray-500">Selected</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {Math.round(discoveredLinks.reduce((sum, link) => sum + link.authorityScore, 0) / discoveredLinks.length)}
                      </div>
                      <div className="text-sm text-gray-500">Avg Authority</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <button
                      onClick={handleSelectAll}
                      className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors"
                    >
                      Select Top {Math.min(authorityLinkSettings.maxLinks, discoveredLinks.length)}
                    </button>
                    <button
                      onClick={handleDeselectAll}
                      className="px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded-lg transition-colors"
                    >
                      Deselect All
                    </button>
                  </div>
                </div>

                {/* Filters and Controls */}
                <div className="flex items-center justify-between border-t border-gray-200 pt-4">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <FunnelIcon className="h-4 w-4 text-gray-500" />
                      <select
                        value={filterType}
                        onChange={(e) => setFilterType(e.target.value)}
                        className="text-sm border border-gray-300 rounded px-2 py-1"
                      >
                        {sourceTypes.map(type => (
                          <option key={type.value} value={type.value}>{type.label}</option>
                        ))}
                      </select>
                    </div>

                    <div className="flex items-center gap-2">
                      <AdjustmentsHorizontalIcon className="h-4 w-4 text-gray-500" />
                      <select
                        value={sortBy}
                        onChange={(e) => setSortBy(e.target.value as any)}
                        className="text-sm border border-gray-300 rounded px-2 py-1"
                      >
                        <option value="authority">Authority Score</option>
                        <option value="relevance">Relevance</option>
                        <option value="type">Source Type</option>
                      </select>
                    </div>
                  </div>

                  <div className="flex items-center border border-gray-300 rounded">
                    <button
                      onClick={() => setPreviewMode('grid')}
                      className={`p-2 ${previewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-500'}`}
                    >
                      <CogIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => setPreviewMode('list')}
                      className={`p-2 ${previewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-500'}`}
                    >
                      <LinkIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Links Display */}
              {previewMode === 'grid' ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {getFilteredAndSortedLinks().map((link) => {
                    const Icon = getSourceTypeIcon(link.sourceType)
                    const isSelected = selectedLinks.has(link.id)
                    const canSelect = selectedLinks.size < authorityLinkSettings.maxLinks || isSelected

                    return (
                      <div
                        key={link.id}
                        className={`
                          border-2 rounded-lg p-4 transition-all cursor-pointer
                          ${isSelected
                            ? 'border-blue-500 bg-blue-50'
                            : canSelect 
                              ? 'border-gray-200 hover:border-gray-300 bg-white'
                              : 'border-gray-200 bg-gray-50 opacity-60 cursor-not-allowed'
                          }
                        `}
                        onClick={() => canSelect && handleLinkToggle(link.id)}
                      >
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${getSourceTypeColor(link.sourceType)}`}>
                              <Icon className="h-4 w-4" />
                            </div>
                            <div>
                              <div className="font-medium text-gray-900 text-sm">{link.domain}</div>
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSourceTypeColor(link.sourceType)}`}>
                                {link.sourceType}
                              </span>
                            </div>
                          </div>
                          
                          <div className={`
                            w-5 h-5 rounded-full flex items-center justify-center
                            ${isSelected ? 'bg-blue-600' : 'bg-gray-300'}
                          `}>
                            {isSelected && <CheckCircleIcon className="h-3 w-3 text-white" />}
                          </div>
                        </div>

                        <h3 className="font-medium text-gray-900 mb-2 text-sm line-clamp-2">{link.title}</h3>
                        <p className="text-xs text-gray-600 mb-3 line-clamp-2">{link.description}</p>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getAuthorityScoreColor(link.authorityScore)}`}>
                              {link.authorityScore}/100
                            </span>
                            <span className="text-xs text-gray-500">
                              {Math.round(link.relevanceScore * 100)}%
                            </span>
                          </div>
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              window.open(link.url, '_blank')
                            }}
                            className="p-1 hover:bg-gray-100 rounded"
                          >
                            <EyeIcon className="h-4 w-4 text-gray-500" />
                          </button>
                        </div>
                      </div>
                    )
                  })}
                </div>
              ) : (
                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Select
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Source</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Authority</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Relevance</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {getFilteredAndSortedLinks().map((link) => {
                        const Icon = getSourceTypeIcon(link.sourceType)
                        const isSelected = selectedLinks.has(link.id)
                        const canSelect = selectedLinks.size < authorityLinkSettings.maxLinks || isSelected

                        return (
                          <tr key={link.id} className={`hover:bg-gray-50 ${!canSelect ? 'opacity-60' : ''}`}>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <input
                                type="checkbox"
                                checked={isSelected}
                                onChange={() => canSelect && handleLinkToggle(link.id)}
                                disabled={!canSelect}
                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center gap-2">
                                <Icon className="h-4 w-4 text-gray-500" />
                                <span className="text-sm text-gray-900">{link.domain}</span>
                              </div>
                            </td>
                            <td className="px-6 py-4">
                              <div className="text-sm text-gray-900 line-clamp-2">{link.title}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getAuthorityScoreColor(link.authorityScore)}`}>
                                {link.authorityScore}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className="text-sm text-gray-900">{Math.round(link.relevanceScore * 100)}%</span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <button
                                onClick={() => window.open(link.url, '_blank')}
                                className="text-blue-600 hover:text-blue-800"
                              >
                                <EyeIcon className="h-4 w-4" />
                              </button>
                            </td>
                          </tr>
                        )
                      })}
                    </tbody>
                  </table>
                </div>
              )}

              {/* Selection Limit Warning */}
              {selectedLinks.size >= authorityLinkSettings.maxLinks && (
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mt-4">
                  <div className="flex items-center gap-2">
                    <ExclamationTriangleIcon className="h-5 w-5 text-amber-600" />
                    <span className="text-sm text-amber-800">
                      Maximum selection limit reached ({authorityLinkSettings.maxLinks} links). 
                      Deselect links to choose different ones.
                    </span>
                  </div>
                </div>
              )}
            </>
          )}

          {/* Empty State */}
          {discoveryComplete && !isDiscovering && discoveredLinks.length === 0 && (
            <div className="text-center py-12">
              <BookOpenIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Authority Links Found</h3>
              <p className="text-gray-600">
                No authority links were found for "{topic}". Try adjusting your keywords or authority settings.
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Selected {selectedLinks.size} of {authorityLinkSettings.maxLinks} maximum links
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmSelection}
                disabled={selectedLinks.size === 0}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white rounded-lg transition-colors"
              >
                Use {selectedLinks.size} Selected Links
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}