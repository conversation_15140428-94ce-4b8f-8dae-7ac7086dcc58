import { responseChunker, paginateResponse, timeoutOperation } from '../../lib/response-chunker.js';

/**
 * Response Handler Middleware
 * 
 * Handles large responses, prevents timeouts, and manages response chunking
 * to solve the "response exceed error" issue.
 */

export const responseHandler = () => {
  return (req, res, next) => {
    // Store original json method
    const originalJson = res.json;
    
    // Override json method to handle large responses
    res.json = function(data) {
      try {
        // Check if response is too large
        const serialized = JSON.stringify(data);
        const responseSize = Buffer.byteLength(serialized, 'utf8');
        
        // If response is larger than 1MB, chunk it
        if (responseSize > 1024 * 1024) {
          console.log(`Large response detected: ${responseSize} bytes, chunking...`);
          
          const chunks = responseChunker.chunkObject(data, {
            maxChunkSize: 8192,
            includeProgress: true,
            chunkDelay: 50
          });
          
          // Send chunked response
          res.setHeader('Content-Type', 'application/json');
          res.setHeader('X-Response-Chunked', 'true');
          res.setHeader('X-Total-Chunks', chunks.length.toString());
          
          return originalJson.call(this, {
            success: true,
            chunked: true,
            totalChunks: chunks.length,
            chunks: chunks
          });
        }
        
        // For normal sized responses, use original method
        return originalJson.call(this, data);
      } catch (error) {
        console.error('Response handling error:', error);
        return originalJson.call(this, {
          success: false,
          error: 'Response processing failed',
          message: error.message
        });
      }
    };
    
    // Add pagination helper
    res.paginate = function(data, page = 1, limit = 50) {
      const paginatedResponse = paginateResponse(data, page, limit);
      return this.json({
        success: true,
        ...paginatedResponse
      });
    };
    
    // Add streaming helper
    res.stream = function(data, config = {}) {
      const streamingResponse = responseChunker.createStreamingResponse(data, config);
      
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('X-Response-Streaming', 'true');
      res.setHeader('X-Total-Size', streamingResponse.totalSize.toString());
      res.setHeader('X-Chunk-Count', streamingResponse.chunkCount.toString());
      
      return streamingResponse.stream.pipeTo(new WritableStream({
        write(chunk) {
          res.write(chunk);
        },
        close() {
          res.end();
        }
      }));
    };
    
    // Add timeout wrapper
    res.withTimeout = function(operation, timeoutMs = 30000) {
      return timeoutOperation(operation, timeoutMs, {
        success: false,
        error: 'Operation timed out',
        timeout: true
      });
    };
    
    next();
  };
};

/**
 * Error handler for large responses
 */
export const errorHandler = () => {
  return (error, req, res, next) => {
    console.error('API Error:', error);
    
    // Handle different types of errors
    if (error.name === 'PayloadTooLargeError') {
      return res.status(413).json({
        success: false,
        error: 'Request payload too large',
        message: 'Please reduce the size of your request'
      });
    }
    
    if (error.name === 'TimeoutError') {
      return res.status(408).json({
        success: false,
        error: 'Request timeout',
        message: 'The operation took too long to complete'
      });
    }
    
    if (error.code === 'ECONNRESET') {
      return res.status(503).json({
        success: false,
        error: 'Connection reset',
        message: 'Please try again'
      });
    }
    
    // Default error response
    const statusCode = error.statusCode || error.status || 500;
    res.status(statusCode).json({
      success: false,
      error: error.message || 'Internal server error',
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  };
};

/**
 * Request size limiter
 */
export const requestSizeLimiter = (maxSize = '50mb') => {
  return (req, res, next) => {
    const contentLength = req.headers['content-length'];
    
    if (contentLength) {
      const sizeMB = parseInt(contentLength) / (1024 * 1024);
      const maxSizeMB = parseInt(maxSize.replace('mb', ''));
      
      if (sizeMB > maxSizeMB) {
        return res.status(413).json({
          success: false,
          error: 'Request too large',
          message: `Maximum request size is ${maxSize}`
        });
      }
    }
    
    next();
  };
};

/**
 * Response compression
 */
export const responseCompression = () => {
  return async (req, res, next) => {
    // Check if client accepts compression
    const acceptEncoding = req.headers['accept-encoding'] || '';
    
    if (acceptEncoding.includes('gzip')) {
      // Store original json method
      const originalJson = res.json;
      
      res.json = async function(data) {
        try {
          // Try to compress the response
          const compressed = await responseChunker.compressResponse(data);
          
          res.setHeader('Content-Encoding', 'gzip');
          res.setHeader('Content-Type', 'application/json');
          
          return originalJson.call(this, compressed);
        } catch (error) {
          console.error('Compression failed:', error);
          return originalJson.call(this, data);
        }
      };
    }
    
    next();
  };
};

/**
 * Performance monitoring
 */
export const performanceMonitor = () => {
  return (req, res, next) => {
    const startTime = Date.now();
    
    // Monitor response time
    res.on('finish', () => {
      const duration = Date.now() - startTime;
      
      // Log slow responses
      if (duration > 5000) {
        console.warn(`Slow response detected: ${req.method} ${req.path} took ${duration}ms`);
      }
      
      // Add performance headers
      res.setHeader('X-Response-Time', `${duration}ms`);
      res.setHeader('X-Timestamp', new Date().toISOString());
    });
    
    next();
  };
};

export default responseHandler;