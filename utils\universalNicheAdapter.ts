/**
 * Universal Niche Adaptation System
 * Enterprise SEO SAAS - Handles ANY keyword in ANY industry with real data validation
 */

import { validateKeyword, validateCompetitorUrl } from './projectHelpers'

// Industry detection patterns for universal adaptation
const INDUSTRY_PATTERNS = {
  technology: [
    /software|tech|digital|app|platform|saas|cloud|ai|machine learning|coding|programming/i,
    /web development|mobile app|cybersecurity|blockchain|cryptocurrency|fintech/i
  ],
  healthcare: [
    /health|medical|doctor|hospital|clinic|pharmacy|medicine|treatment|therapy/i,
    /dental|mental health|wellness|fitness|nutrition|supplement/i
  ],
  finance: [
    /finance|banking|investment|loan|credit|mortgage|insurance|trading|wealth/i,
    /accounting|tax|financial planning|retirement|budget/i
  ],
  education: [
    /education|school|university|course|training|learning|teach|study|academic/i,
    /online course|certification|degree|skill|tutorial/i
  ],
  ecommerce: [
    /shop|store|buy|sell|product|retail|marketplace|commerce|order|shipping/i,
    /amazon|ebay|shopify|online store|dropshipping/i
  ],
  real_estate: [
    /real estate|property|home|house|apartment|rent|buy|sell|mortgage|realtor/i,
    /commercial property|residential|investment property/i
  ],
  automotive: [
    /car|auto|vehicle|truck|motorcycle|automotive|repair|maintenance|parts/i,
    /dealership|used car|insurance|mechanic/i
  ],
  food_beverage: [
    /food|restaurant|recipe|cooking|chef|kitchen|dining|catering|delivery/i,
    /wine|beer|coffee|tea|beverage|nutrition/i
  ],
  fashion: [
    /fashion|clothing|apparel|style|design|beauty|cosmetics|jewelry|accessories/i,
    /brand|designer|trend|makeup|skincare/i
  ],
  travel: [
    /travel|vacation|hotel|flight|tourism|destination|booking|trip|cruise/i,
    /adventure|backpacking|luxury travel|business travel/i
  ],
  fitness: [
    /fitness|gym|workout|exercise|training|sports|athletic|health|strength/i,
    /yoga|pilates|running|bodybuilding|nutrition/i
  ],
  marketing: [
    /marketing|advertising|brand|promotion|campaign|seo|social media|content/i,
    /digital marketing|email marketing|ppc|affiliate/i
  ],
  consulting: [
    /consulting|consultant|business|strategy|management|advisory|professional services/i,
    /coaching|mentor|expert|specialist/i
  ],
  legal: [
    /legal|lawyer|attorney|law firm|court|litigation|contract|compliance/i,
    /immigration|personal injury|criminal|family law/i
  ],
  entertainment: [
    /entertainment|movie|music|game|gaming|streaming|media|celebrity|show/i,
    /concert|festival|theater|comedy|art/i
  ]
}

// Search intent patterns for universal keyword analysis
const INTENT_PATTERNS = {
  informational: [
    /what is|how to|why|when|where|guide|tutorial|tips|learn|understand/i,
    /definition|meaning|explanation|overview|benefits|advantages/i
  ],
  commercial: [
    /best|top|review|compare|vs|versus|price|cost|cheap|affordable/i,
    /buy|purchase|shop|deal|discount|coupon|sale/i
  ],
  transactional: [
    /buy now|order|purchase|book|hire|get quote|contact|call now|download/i,
    /free trial|sign up|register|subscribe|apply/i
  ],
  navigational: [
    /login|sign in|account|dashboard|portal|official|website/i,
    /contact us|about us|location|hours|phone number/i
  ]
}

// Content type suggestions based on keyword analysis
const CONTENT_TYPE_MAPPING = {
  'how to': 'blog_post',
  'what is': 'blog_post',
  'best': 'blog_post',
  'review': 'blog_post',
  'buy': 'product_description',
  'service': 'landing_page',
  'company': 'landing_page',
  'guide': 'blog_post',
  'tips': 'blog_post',
  'vs': 'blog_post',
  'compare': 'blog_post'
}

export interface NicheAnalysis {
  keyword: string
  detectedIndustry: string
  industryConfidence: number
  searchIntent: string
  intentConfidence: number
  suggestedContentType: string
  keywordType: 'short-tail' | 'long-tail' | 'branded' | 'geo-targeted'
  competitiveness: 'low' | 'medium' | 'high'
  commercialValue: 'low' | 'medium' | 'high'
  semanticKeywords: string[]
  targetAudience: string
  contentSuggestions: string[]
  validationResult: any
}

export interface CompetitorIntelligence {
  url: string
  domain: string
  title: string
  metaDescription: string
  contentLength: number
  keywordUsage: {
    density: number
    placement: string[]
    variations: string[]
  }
  contentStructure: {
    headings: string[]
    sections: number
    lists: number
    images: number
  }
  linkProfile: {
    internal: number
    external: number
    authorities: string[]
  }
  technicalSEO: {
    schemaMarkup: boolean
    loadSpeed: string
    mobileOptimized: boolean
  }
  contentGaps: string[]
  opportunities: string[]
}

export class UniversalNicheAdapter {
  private demoDataPatterns: RegExp[]

  constructor() {
    this.demoDataPatterns = [
      /example|demo|test|sample|placeholder|lorem ipsum|dummy|mock|fake|template/i,
      /your keyword|insert keyword|keyword here|add keyword|replace this/i,
      /\[keyword\]|\{keyword\}|\<keyword\>/i
    ]
  }

  /**
   * Analyze any keyword for industry, intent, and optimization opportunities
   */
  async analyzeKeyword(keyword: string, location?: string): Promise<NicheAnalysis> {
    // Validate keyword first
    const validation = validateKeyword(keyword)
    if (!validation.isValid) {
      throw new Error(`Invalid keyword: ${validation.errors.join(', ')}`)
    }

    // Detect demo data
    if (this.isDemoData(keyword)) {
      throw new Error('Demo or placeholder keywords are not supported. Please provide a real target keyword.')
    }

    const analysis: NicheAnalysis = {
      keyword: keyword.trim().toLowerCase(),
      detectedIndustry: 'other',
      industryConfidence: 0,
      searchIntent: 'informational',
      intentConfidence: 0,
      suggestedContentType: 'blog_post',
      keywordType: this.analyzeKeywordType(keyword),
      competitiveness: 'medium',
      commercialValue: 'medium',
      semanticKeywords: [],
      targetAudience: '',
      contentSuggestions: [],
      validationResult: validation
    }

    // Detect industry
    const industryResult = this.detectIndustry(keyword)
    analysis.detectedIndustry = industryResult.industry
    analysis.industryConfidence = industryResult.confidence

    // Detect search intent
    const intentResult = this.detectSearchIntent(keyword)
    analysis.searchIntent = intentResult.intent
    analysis.intentConfidence = intentResult.confidence

    // Generate semantic keywords
    analysis.semanticKeywords = this.generateSemanticKeywords(keyword, analysis.detectedIndustry)

    // Determine target audience
    analysis.targetAudience = this.determineTargetAudience(keyword, analysis.detectedIndustry, analysis.searchIntent)

    // Suggest content type
    analysis.suggestedContentType = this.suggestContentType(keyword, analysis.searchIntent)

    // Assess competitiveness and commercial value
    analysis.competitiveness = this.assessCompetitiveness(keyword)
    analysis.commercialValue = this.assessCommercialValue(keyword, analysis.searchIntent)

    // Generate content suggestions
    analysis.contentSuggestions = this.generateContentSuggestions(analysis)

    return analysis
  }

  /**
   * Analyze competitor pages for any niche
   */
  async analyzeCompetitor(url: string, targetKeyword: string): Promise<CompetitorIntelligence> {
    // Validate URL first
    const validation = validateCompetitorUrl(url)
    if (!validation.isValid) {
      throw new Error(`Invalid competitor URL: ${validation.errors.join(', ')}`)
    }

    // Simulate competitor analysis (in real implementation, this would scrape/analyze the actual page)
    const intelligence: CompetitorIntelligence = {
      url: validation.url,
      domain: validation.domain,
      title: `${targetKeyword} Solutions | ${validation.domain}`,
      metaDescription: `Comprehensive ${targetKeyword} services and solutions for businesses.`,
      contentLength: Math.floor(Math.random() * 2000) + 1500,
      keywordUsage: {
        density: Math.random() * 2 + 0.5,
        placement: ['title', 'h1', 'h2', 'first-paragraph'],
        variations: this.generateSemanticKeywords(targetKeyword, 'general').slice(0, 5)
      },
      contentStructure: {
        headings: [`What is ${targetKeyword}?`, `Benefits of ${targetKeyword}`, `How to Choose ${targetKeyword}`],
        sections: Math.floor(Math.random() * 5) + 5,
        lists: Math.floor(Math.random() * 3) + 2,
        images: Math.floor(Math.random() * 8) + 3
      },
      linkProfile: {
        internal: Math.floor(Math.random() * 15) + 5,
        external: Math.floor(Math.random() * 8) + 2,
        authorities: ['wikipedia.org', 'industry-authority.com', 'research-institute.edu']
      },
      technicalSEO: {
        schemaMarkup: Math.random() > 0.3,
        loadSpeed: Math.random() > 0.5 ? 'fast' : 'medium',
        mobileOptimized: Math.random() > 0.2
      },
      contentGaps: [],
      opportunities: []
    }

    // Identify content gaps and opportunities
    intelligence.contentGaps = this.identifyContentGaps(intelligence, targetKeyword)
    intelligence.opportunities = this.identifyOpportunities(intelligence, targetKeyword)

    return intelligence
  }

  /**
   * Generate content strategy for any niche
   */
  generateContentStrategy(analysis: NicheAnalysis, competitors: CompetitorIntelligence[]): any {
    const strategy = {
      primaryStrategy: this.determinePrimaryStrategy(analysis, competitors),
      contentApproach: this.determineContentApproach(analysis),
      keywordStrategy: this.planKeywordStrategy(analysis),
      contentStructure: this.planContentStructure(analysis, competitors),
      linkingStrategy: this.planLinkingStrategy(analysis),
      competitorDifferentiation: this.planDifferentiation(competitors),
      optimizationFocus: this.determineOptimizationFocus(analysis, competitors)
    }

    return strategy
  }

  private isDemoData(input: string): boolean {
    return this.demoDataPatterns.some(pattern => pattern.test(input))
  }

  private detectIndustry(keyword: string): { industry: string; confidence: number } {
    let bestMatch = { industry: 'other', confidence: 0 }

    for (const [industry, patterns] of Object.entries(INDUSTRY_PATTERNS)) {
      for (const pattern of patterns) {
        if (pattern.test(keyword)) {
          const confidence = this.calculateMatchConfidence(keyword, pattern)
          if (confidence > bestMatch.confidence) {
            bestMatch = { industry, confidence }
          }
        }
      }
    }

    return bestMatch
  }

  private detectSearchIntent(keyword: string): { intent: string; confidence: number } {
    let bestMatch = { intent: 'informational', confidence: 50 }

    for (const [intent, patterns] of Object.entries(INTENT_PATTERNS)) {
      for (const pattern of patterns) {
        if (pattern.test(keyword)) {
          const confidence = this.calculateMatchConfidence(keyword, pattern)
          if (confidence > bestMatch.confidence) {
            bestMatch = { intent, confidence }
          }
        }
      }
    }

    return bestMatch
  }

  private analyzeKeywordType(keyword: string): 'short-tail' | 'long-tail' | 'branded' | 'geo-targeted' {
    const words = keyword.trim().split(/\s+/)
    
    if (words.length === 1) return 'short-tail'
    if (words.length >= 4) return 'long-tail'
    if (/\b(near me|in|city|state|location)\b/i.test(keyword)) return 'geo-targeted'
    if (/\b(brand|company|product name)\b/i.test(keyword)) return 'branded'
    
    return words.length <= 3 ? 'short-tail' : 'long-tail'
  }

  private generateSemanticKeywords(keyword: string, industry: string): string[] {
    const baseKeywords = [
      `${keyword} guide`,
      `${keyword} tips`,
      `${keyword} benefits`,
      `${keyword} solutions`,
      `${keyword} services`,
      `best ${keyword}`,
      `${keyword} for beginners`,
      `${keyword} strategy`,
      `${keyword} tools`,
      `${keyword} cost`
    ]

    // Add industry-specific variations
    const industrySpecific = this.getIndustrySpecificKeywords(keyword, industry)
    
    return [...baseKeywords, ...industrySpecific].slice(0, 15)
  }

  private getIndustrySpecificKeywords(keyword: string, industry: string): string[] {
    const industryKeywords: Record<string, string[]> = {
      technology: [`${keyword} software`, `${keyword} app`, `${keyword} platform`, `${keyword} API`],
      healthcare: [`${keyword} treatment`, `${keyword} therapy`, `${keyword} medical`, `${keyword} health`],
      finance: [`${keyword} investment`, `${keyword} financial`, `${keyword} banking`, `${keyword} loan`],
      education: [`${keyword} course`, `${keyword} training`, `${keyword} certification`, `${keyword} learning`],
      ecommerce: [`${keyword} store`, `${keyword} shop`, `${keyword} buy`, `${keyword} online`],
      other: [`${keyword} professional`, `${keyword} expert`, `${keyword} consultant`, `${keyword} business`]
    }

    return industryKeywords[industry] || industryKeywords.other
  }

  private determineTargetAudience(keyword: string, industry: string, intent: string): string {
    const audienceMap: Record<string, string> = {
      'technology-informational': 'Tech professionals and developers seeking implementation guidance',
      'healthcare-commercial': 'Patients and healthcare consumers researching treatment options',
      'finance-transactional': 'Individuals seeking financial services and investment opportunities',
      'education-informational': 'Students and professionals looking to acquire new skills',
      'ecommerce-commercial': 'Consumers researching products before purchase',
      'default': 'Professionals and consumers seeking information and solutions'
    }

    const key = `${industry}-${intent}`
    return audienceMap[key] || audienceMap.default
  }

  private suggestContentType(keyword: string, intent: string): string {
    if (intent === 'transactional') return 'landing_page'
    if (intent === 'commercial') return 'product_description'
    if (keyword.includes('how to') || keyword.includes('guide')) return 'blog_post'
    
    for (const [pattern, type] of Object.entries(CONTENT_TYPE_MAPPING)) {
      if (keyword.includes(pattern)) return type
    }
    
    return 'blog_post'
  }

  private assessCompetitiveness(keyword: string): 'low' | 'medium' | 'high' {
    const words = keyword.split(/\s+/)
    if (words.length >= 4) return 'low'  // Long-tail keywords are less competitive
    if (words.length === 1) return 'high' // Single words are highly competitive
    return 'medium'
  }

  private assessCommercialValue(keyword: string, intent: string): 'low' | 'medium' | 'high' {
    if (intent === 'transactional') return 'high'
    if (intent === 'commercial') return 'high'
    if (/buy|price|cost|purchase|hire|service/i.test(keyword)) return 'high'
    if (/free|what is|how to|guide/i.test(keyword)) return 'low'
    return 'medium'
  }

  private generateContentSuggestions(analysis: NicheAnalysis): string[] {
    const suggestions = []
    
    if (analysis.searchIntent === 'informational') {
      suggestions.push(
        `Create comprehensive guide covering all aspects of ${analysis.keyword}`,
        `Include practical examples and step-by-step instructions`,
        `Add FAQ section for common questions about ${analysis.keyword}`
      )
    }
    
    if (analysis.commercialValue === 'high') {
      suggestions.push(
        `Include comparison tables and buying guides`,
        `Add customer testimonials and case studies`,
        `Create clear call-to-action sections`
      )
    }
    
    suggestions.push(
      `Target semantic keywords: ${analysis.semanticKeywords.slice(0, 3).join(', ')}`,
      `Optimize for ${analysis.targetAudience.toLowerCase()}`,
      `Focus on ${analysis.detectedIndustry} industry expertise`
    )
    
    return suggestions
  }

  private calculateMatchConfidence(keyword: string, pattern: RegExp): number {
    const matches = keyword.match(pattern)
    if (!matches) return 0
    
    // Calculate confidence based on match strength and keyword length
    const matchLength = matches[0].length
    const keywordLength = keyword.length
    return Math.min(95, (matchLength / keywordLength) * 100 + 25)
  }

  private identifyContentGaps(competitor: CompetitorIntelligence, targetKeyword: string): string[] {
    const gaps = []
    
    if (competitor.contentLength < 2000) {
      gaps.push('Content length below optimal range (2000+ words)')
    }
    
    if (competitor.contentStructure.headings.length < 5) {
      gaps.push('Insufficient heading structure for comprehensive coverage')
    }
    
    if (competitor.linkProfile.external < 3) {
      gaps.push('Limited external authority links')
    }
    
    if (!competitor.technicalSEO.schemaMarkup) {
      gaps.push('Missing schema markup implementation')
    }
    
    return gaps
  }

  private identifyOpportunities(competitor: CompetitorIntelligence, targetKeyword: string): string[] {
    const opportunities = []
    
    opportunities.push(
      `Exceed competitor content length by 500+ words`,
      `Include more comprehensive ${targetKeyword} examples`,
      `Add interactive elements and multimedia content`,
      `Improve internal linking structure`,
      `Implement advanced schema markup`
    )
    
    return opportunities
  }

  private determinePrimaryStrategy(analysis: NicheAnalysis, competitors: CompetitorIntelligence[]): string {
    if (analysis.competitiveness === 'high') {
      return 'Focus on long-tail variations and niche-specific angles'
    }
    if (analysis.commercialValue === 'high') {
      return 'Create conversion-optimized content with clear value propositions'
    }
    return 'Develop comprehensive, authoritative content that exceeds competitor standards'
  }

  private determineContentApproach(analysis: NicheAnalysis): string {
    if (analysis.searchIntent === 'informational') {
      return 'Educational and comprehensive approach with practical insights'
    }
    if (analysis.searchIntent === 'commercial') {
      return 'Comparison-focused approach highlighting benefits and features'
    }
    return 'Solution-oriented approach addressing specific user needs'
  }

  private planKeywordStrategy(analysis: NicheAnalysis): any {
    return {
      primary: analysis.keyword,
      secondary: analysis.semanticKeywords.slice(0, 5),
      density: analysis.competitiveness === 'high' ? '1.5-2%' : '1-1.5%',
      placement: ['H1', 'first 100 words', 'H2 headers', 'conclusion']
    }
  }

  private planContentStructure(analysis: NicheAnalysis, competitors: CompetitorIntelligence[]): any {
    const avgCompetitorSections = competitors.length > 0 
      ? Math.round(competitors.reduce((sum, c) => sum + c.contentStructure.sections, 0) / competitors.length)
      : 6

    return {
      sections: Math.max(8, avgCompetitorSections + 2),
      wordCount: analysis.competitiveness === 'high' ? '3000+' : '2500+',
      structure: [
        'Introduction with hook and value proposition',
        `Comprehensive ${analysis.keyword} overview`,
        'Key benefits and features',
        'Step-by-step implementation guide',
        'Best practices and expert tips',
        'Common mistakes to avoid',
        'FAQ section',
        'Conclusion with clear next steps'
      ]
    }
  }

  private planLinkingStrategy(analysis: NicheAnalysis): any {
    return {
      internal: '10-15 contextual links to related content',
      external: '3-5 high-authority sources',
      anchor: 'Natural, keyword-rich anchor text variations'
    }
  }

  private planDifferentiation(competitors: CompetitorIntelligence[]): string[] {
    return [
      'Provide more comprehensive coverage than competitors',
      'Include unique insights and expert perspectives',
      'Add practical examples and case studies',
      'Implement better user experience and readability',
      'Offer actionable takeaways and implementation guides'
    ]
  }

  private determineOptimizationFocus(analysis: NicheAnalysis, competitors: CompetitorIntelligence[]): string[] {
    const focus = [
      'Keyword optimization and semantic coverage',
      'Content structure and readability',
      'Internal and external linking',
      'Technical SEO implementation'
    ]
    
    if (analysis.commercialValue === 'high') {
      focus.push('Conversion optimization and CTAs')
    }
    
    return focus
  }
}