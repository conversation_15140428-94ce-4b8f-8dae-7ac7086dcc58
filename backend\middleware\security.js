import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import slowDown from 'express-slow-down';
import { createHash } from 'crypto';

/**
 * Enterprise Security Middleware Collection
 * Comprehensive security protection against all known attack vectors
 */

/**
 * Enhanced Helmet Configuration
 * Provides robust security headers protection
 */
export const securityHeaders = helmet({
  // Content Security Policy
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: [
        "'self'",
        "'unsafe-inline'", // Allow inline styles for Tailwind
        "https://fonts.googleapis.com",
        "https://cdn.jsdelivr.net"
      ],
      scriptSrc: [
        "'self'",
        "'unsafe-inline'", // Needed for Next.js in development
        "https://cdn.jsdelivr.net",
        "https://www.googletagmanager.com"
      ],
      imgSrc: [
        "'self'",
        "data:",
        "https:",
        "blob:"
      ],
      connectSrc: [
        "'self'",
        "https://api.openai.com",
        "https://api.groq.com",
        "https://serper.dev",
        "https://*.supabase.co"
      ],
      fontSrc: [
        "'self'",
        "https://fonts.gstatic.com",
        "data:"
      ],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
      upgradeInsecureRequests: []
    },
    reportOnly: false
  },
  
  // Cross-Origin policies
  crossOriginEmbedderPolicy: { policy: "credentialless" },
  crossOriginOpenerPolicy: { policy: "same-origin" },
  crossOriginResourcePolicy: { policy: "cross-origin" },
  
  // Additional security headers
  dnsPrefetchControl: { allow: false },
  frameguard: { action: 'deny' },
  hidePoweredBy: true,
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  },
  ieNoOpen: true,
  noSniff: true,
  originAgentCluster: true,
  permittedCrossDomainPolicies: false,
  referrerPolicy: { policy: "strict-origin-when-cross-origin" },
  xssFilter: true
});

/**
 * Advanced Rate Limiting Configuration
 * Multi-tier protection against abuse
 */
export const createAdvancedRateLimit = () => {
  // Aggressive rate limiting for authentication endpoints
  const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // limit each IP to 5 auth requests per windowMs
    message: {
      error: 'Too many authentication attempts',
      code: 'AUTH_RATE_LIMIT_EXCEEDED',
      retryAfter: 15 * 60 // 15 minutes
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
      // Use combination of IP and user agent for better tracking
      return createHash('sha256')
        .update(req.ip + req.get('User-Agent'))
        .digest('hex');
    }
  });

  // Standard rate limiting for API endpoints
  const apiLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: {
      error: 'Too many API requests',
      code: 'API_RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // Skip rate limiting for health checks and options requests
      return req.path === '/api/health' || req.method === 'OPTIONS';
    }
  });

  // Content generation specific rate limiting
  const contentLimiter = rateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 50, // limit each user to 50 content generations per hour
    message: {
      error: 'Content generation rate limit exceeded',
      code: 'CONTENT_RATE_LIMIT_EXCEEDED'
    },
    keyGenerator: (req) => {
      // Use user ID if authenticated, otherwise IP
      return req.user?.id || req.ip;
    }
  });

  return {
    auth: authLimiter,
    api: apiLimiter,
    content: contentLimiter
  };
};

/**
 * Request Slowdown Middleware
 * Gradually slows down requests from repeat offenders
 */
export const requestSlowdown = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 50, // allow 50 requests per windowMs without delay
  delayMs: 500, // add 500ms delay per request after delayAfter
  maxDelayMs: 5000, // maximum delay of 5 seconds
  skipFailedRequests: false,
  skipSuccessfulRequests: false,
  keyGenerator: (req) => {
    return req.user?.id || req.ip;
  }
});

/**
 * IP Blocking Middleware
 * Blocks known malicious IPs and bot networks
 */
export const ipBlocking = (req, res, next) => {
  const clientIP = req.ip;
  const userAgent = req.get('User-Agent') || '';
  
  // Block known malicious IP ranges
  const blockedIPRanges = [
    // Add known malicious IP ranges here
    '***********/24', // Example - should be replaced with actual threat intelligence
  ];
  
  // Block suspicious user agents
  const suspiciousAgents = [
    /curl/i,
    /wget/i,
    /python-requests/i,
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i
  ];
  
  // Check for suspicious user agents (except for legitimate monitoring)
  const isSuspiciousAgent = suspiciousAgents.some(pattern => 
    pattern.test(userAgent) && !userAgent.includes('monitoring')
  );
  
  if (isSuspiciousAgent) {
    return res.status(403).json({
      error: 'Access denied',
      code: 'SUSPICIOUS_USER_AGENT'
    });
  }
  
  // Check for missing or suspicious headers
  if (!req.get('Accept') || !req.get('Accept-Language')) {
    return res.status(403).json({
      error: 'Invalid request headers',
      code: 'INVALID_HEADERS'
    });
  }
  
  next();
};

/**
 * Request Size Limiting
 * Prevents large payload attacks
 */
export const requestSizeLimit = (req, res, next) => {
  const maxSize = 10 * 1024 * 1024; // 10MB
  
  if (req.get('Content-Length') && parseInt(req.get('Content-Length')) > maxSize) {
    return res.status(413).json({
      error: 'Request payload too large',
      code: 'PAYLOAD_TOO_LARGE',
      maxSize: maxSize
    });
  }
  
  next();
};

/**
 * SQL Injection Protection
 * Additional layer of protection against SQL injection
 */
export const sqlInjectionProtection = (req, res, next) => {
  const sqlPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC)\b)/gi,
    /(UNION|HAVING|ORDER\s+BY|GROUP\s+BY)/gi,
    /('|(\\')|(;)|(\\)|(--)|(\s|%20)+(OR|AND)\s*(\w)*\s*(=|LIKE)/gi,
    /(script|javascript|vbscript|onload|onerror|onclick)/gi
  ];
  
  const checkForSQLInjection = (obj, path = '') => {
    for (const [key, value] of Object.entries(obj)) {
      const currentPath = path ? `${path}.${key}` : key;
      
      if (typeof value === 'string') {
        for (const pattern of sqlPatterns) {
          if (pattern.test(value)) {
            return {
              detected: true,
              path: currentPath,
              value: value,
              pattern: pattern.toString()
            };
          }
        }
      } else if (typeof value === 'object' && value !== null) {
        const result = checkForSQLInjection(value, currentPath);
        if (result.detected) return result;
      }
    }
    return { detected: false };
  };
  
  // Check request body
  if (req.body) {
    const result = checkForSQLInjection(req.body);
    if (result.detected) {
      console.error('SQL injection attempt detected:', result);
      return res.status(400).json({
        error: 'Malicious input detected',
        code: 'SQL_INJECTION_DETECTED'
      });
    }
  }
  
  // Check query parameters
  if (req.query) {
    const result = checkForSQLInjection(req.query);
    if (result.detected) {
      console.error('SQL injection attempt detected in query:', result);
      return res.status(400).json({
        error: 'Malicious query parameters detected',
        code: 'SQL_INJECTION_DETECTED'
      });
    }
  }
  
  next();
};

/**
 * CSRF Protection Middleware
 * Protects against Cross-Site Request Forgery attacks
 */
export const csrfProtection = (req, res, next) => {
  // Skip CSRF for GET, HEAD, OPTIONS requests
  if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    return next();
  }
  
  // Skip CSRF for API key authenticated requests
  if (req.headers['x-api-key']) {
    return next();
  }
  
  const token = req.headers['x-csrf-token'] || req.body._csrf;
  const sessionToken = req.session?.csrfToken;
  
  if (!token || !sessionToken || token !== sessionToken) {
    return res.status(403).json({
      error: 'Invalid CSRF token',
      code: 'CSRF_TOKEN_MISMATCH'
    });
  }
  
  next();
};

/**
 * Request Logging Middleware
 * Logs all requests for security monitoring
 */
export const securityLogging = (req, res, next) => {
  const startTime = Date.now();
  
  // Log request details
  const requestLog = {
    timestamp: new Date().toISOString(),
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    referer: req.get('Referer'),
    userId: req.user?.id || null,
    sessionId: req.sessionID || null
  };
  
  // Log response details when request completes
  res.on('finish', () => {
    const responseTime = Date.now() - startTime;
    const responseLog = {
      ...requestLog,
      statusCode: res.statusCode,
      responseTime,
      contentLength: res.get('Content-Length')
    };
    
    // Log suspicious requests
    if (res.statusCode >= 400 || responseTime > 5000) {
      console.warn('Suspicious request:', responseLog);
    }
    
    // TODO: Send to centralized logging service
    // logToSecurityService(responseLog);
  });
  
  next();
};

/**
 * Honeypot Protection
 * Detects and blocks automated attacks
 */
export const honeypotProtection = (req, res, next) => {
  // Check for honeypot fields in forms
  if (req.body && req.body.honeypot) {
    console.warn('Honeypot triggered:', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      body: req.body
    });
    
    // Delay response to waste attacker's time
    setTimeout(() => {
      res.status(400).json({
        error: 'Form submission failed',
        code: 'FORM_ERROR'
      });
    }, 3000);
    
    return;
  }
  
  next();
};

export default {
  securityHeaders,
  createAdvancedRateLimit,
  requestSlowdown,
  ipBlocking,
  requestSizeLimit,
  sqlInjectionProtection,
  csrfProtection,
  securityLogging,
  honeypotProtection
};