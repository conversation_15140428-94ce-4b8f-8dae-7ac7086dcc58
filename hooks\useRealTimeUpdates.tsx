'use client';

import { useState, useEffect, useRef, useCallback } from 'react';

interface RealTimeUpdateConfig {
  endpoint: string;
  interval?: number;
  enabled?: boolean;
  onUpdate?: (data: any) => void;
  onError?: (error: Error) => void;
}

interface WebSocketMessage {
  type: 'update' | 'error' | 'connected' | 'disconnected';
  data?: any;
  timestamp: number;
}

// Custom hook for real-time updates using polling
export function useRealTimeUpdates<T>(config: RealTimeUpdateConfig) {
  const [data, setData] = useState<T | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const { endpoint, interval = 30000, enabled = true, onUpdate, onError } = config;

  const fetchData = useCallback(async () => {
    try {
      const response = await fetch(endpoint);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const newData = await response.json();
      setData(newData);
      setLastUpdate(new Date());
      setError(null);
      
      if (onUpdate) {
        onUpdate(newData);
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error);
      if (onError) {
        onError(error);
      }
    }
  }, [endpoint, onUpdate, onError]);

  useEffect(() => {
    if (!enabled) {
      setIsConnected(false);
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    setIsConnected(true);
    
    // Initial fetch
    fetchData();

    // Set up polling interval
    intervalRef.current = setInterval(fetchData, interval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      setIsConnected(false);
    };
  }, [enabled, interval, fetchData]);

  const refresh = useCallback(() => {
    if (enabled) {
      fetchData();
    }
  }, [enabled, fetchData]);

  return {
    data,
    isConnected,
    error,
    lastUpdate,
    refresh
  };
}

// Custom hook for WebSocket-based real-time updates
export function useWebSocketUpdates<T>(config: {
  url: string;
  enabled?: boolean;
  onMessage?: (data: T) => void;
  onError?: (error: Error) => void;
}) {
  const [data, setData] = useState<T | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const { url, enabled = true, onMessage, onError } = config;

  const connect = useCallback(() => {
    if (!enabled || wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      wsRef.current = new WebSocket(url);

      wsRef.current.onopen = () => {
        setIsConnected(true);
        setError(null);
        console.log('WebSocket connected');
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          
          if (message.type === 'update' && message.data) {
            setData(message.data);
            setLastUpdate(new Date());
            
            if (onMessage) {
              onMessage(message.data);
            }
          }
        } catch (err) {
          console.error('WebSocket message parse error:', err);
        }
      };

      wsRef.current.onclose = () => {
        setIsConnected(false);
        console.log('WebSocket disconnected');
        
        // Attempt to reconnect after 5 seconds
        if (enabled) {
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, 5000);
        }
      };

      wsRef.current.onerror = (event) => {
        const error = new Error('WebSocket error');
        setError(error);
        if (onError) {
          onError(error);
        }
      };
    } catch (err) {
      const error = err instanceof Error ? err : new Error('WebSocket connection failed');
      setError(error);
      if (onError) {
        onError(error);
      }
    }
  }, [url, enabled, onMessage, onError]);

  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    setIsConnected(false);
  }, []);

  useEffect(() => {
    if (enabled) {
      connect();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [enabled, connect, disconnect]);

  const send = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    }
  }, []);

  return {
    data,
    isConnected,
    error,
    lastUpdate,
    send,
    connect,
    disconnect
  };
}

// Custom hook for activity feed updates
export function useActivityFeed(userId?: string) {
  const [activities, setActivities] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const { data, isConnected, error, lastUpdate, refresh } = useRealTimeUpdates({
    endpoint: `/api/activities${userId ? `?userId=${userId}` : ''}`,
    interval: 15000, // Update every 15 seconds
    enabled: true,
    onUpdate: (newData) => {
      if (newData?.activities) {
        setActivities(newData.activities);
      }
    },
    onError: (error) => {
      console.error('Activity feed error:', error);
    }
  });

  const addActivity = useCallback((activity: any) => {
    setActivities(prev => [activity, ...prev].slice(0, 50)); // Keep only latest 50
  }, []);

  return {
    activities,
    isLoading,
    isConnected,
    error,
    lastUpdate,
    refresh,
    addActivity
  };
}

// Custom hook for metrics updates
export function useMetricsUpdates(projectId?: string) {
  const [metrics, setMetrics] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  const { data, isConnected, error, lastUpdate, refresh } = useRealTimeUpdates({
    endpoint: `/api/metrics${projectId ? `?projectId=${projectId}` : ''}`,
    interval: 60000, // Update every minute
    enabled: true,
    onUpdate: (newData) => {
      if (newData?.metrics) {
        setMetrics(newData.metrics);
      }
    },
    onError: (error) => {
      console.error('Metrics update error:', error);
    }
  });

  return {
    metrics,
    isLoading,
    isConnected,
    error,
    lastUpdate,
    refresh
  };
}

// Custom hook for notification updates
export function useNotificationUpdates(userId?: string) {
  const [notifications, setNotifications] = useState<any[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  
  const { data, isConnected, error, lastUpdate, refresh } = useRealTimeUpdates({
    endpoint: `/api/notifications${userId ? `?userId=${userId}` : ''}`,
    interval: 30000, // Update every 30 seconds
    enabled: true,
    onUpdate: (newData) => {
      if (newData?.notifications) {
        setNotifications(newData.notifications);
        setUnreadCount(newData.unreadCount || 0);
      }
    },
    onError: (error) => {
      console.error('Notification update error:', error);
    }
  });

  const markAsRead = useCallback((notificationId: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, read: true }
          : notification
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  }, []);

  const addNotification = useCallback((notification: any) => {
    setNotifications(prev => [notification, ...prev]);
    if (!notification.read) {
      setUnreadCount(prev => prev + 1);
    }
  }, []);

  return {
    notifications,
    unreadCount,
    isConnected,
    error,
    lastUpdate,
    refresh,
    markAsRead,
    addNotification
  };
}

// Real-time connection indicator component
export function RealTimeIndicator({ 
  isConnected, 
  lastUpdate, 
  className = '' 
}: { 
  isConnected: boolean; 
  lastUpdate: Date | null; 
  className?: string;
}) {
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
      <span className="text-xs text-gray-500">
        {isConnected ? 'Live' : 'Disconnected'}
        {lastUpdate && (
          <span className="ml-1">
            • Updated {lastUpdate.toLocaleTimeString()}
          </span>
        )}
      </span>
    </div>
  );
}