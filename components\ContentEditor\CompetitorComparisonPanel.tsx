/**
 * Competitor Comparison Panel Component
 * Side-by-side comparison with top-ranking competitors
 */

'use client'

import React, { useState } from 'react'
import Card from '@/components/UI/Card'
import Button from '@/components/UI/Button'
import Badge from '@/components/UI/Badge'
import {
  XMarkIcon,
  ArrowTopRightOnSquareIcon,
  ChartBarIcon,
  DocumentTextIcon,
  MagnifyingGlassIcon,
  LinkIcon,
  ClockIcon,
  EyeIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowTrendingUpIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline'

interface EditorContent {
  title: string
  content: string
  metaTitle: string
  metaDescription: string
  keyword: string
  contentType: string
  status: 'draft' | 'published' | 'scheduled'
}

interface CompetitorData {
  keyword: string
  topCompetitors: Array<{
    url: string
    title: string
    wordCount: number
    seoScore: number
    keywordDensity: number
    headings: string[]
    insights: string[]
  }>
}

interface CompetitorComparisonPanelProps {
  competitorData: CompetitorData
  currentContent: EditorContent
  onClose: () => void
}

export default function CompetitorComparisonPanel({
  competitorData,
  currentContent,
  onClose
}: CompetitorComparisonPanelProps) {
  const [selectedCompetitor, setSelectedCompetitor] = useState(0)
  const [activeTab, setActiveTab] = useState<'overview' | 'content' | 'insights'>('overview')

  const currentStats = {
    wordCount: currentContent.content.split(/\s+/).filter(w => w.length > 0).length,
    keywordDensity: currentContent.keyword && currentContent.content 
      ? (currentContent.content.toLowerCase().match(new RegExp(currentContent.keyword.toLowerCase(), 'g')) || []).length / 
        currentContent.content.split(/\s+/).filter(w => w.length > 0).length * 100
      : 0,
    headingCount: (currentContent.content.match(/^#{1,6}\s/gm) || []).length,
    seoScore: 75 // This would come from actual analysis
  }

  const competitor = competitorData.topCompetitors[selectedCompetitor]

  const getComparisonStatus = (current: number, competitor: number, higherIsBetter = true) => {
    const diff = higherIsBetter ? current - competitor : competitor - current
    if (diff > 0) return { status: 'better', color: 'text-green-600 dark:text-green-400', icon: CheckCircleIcon }
    if (diff < -10) return { status: 'worse', color: 'text-red-600 dark:text-red-400', icon: ExclamationTriangleIcon }
    return { status: 'similar', color: 'text-yellow-600 dark:text-yellow-400', icon: ExclamationTriangleIcon }
  }

  const generateRecommendations = () => {
    const recommendations = []
    
    if (currentStats.wordCount < competitor.wordCount) {
      recommendations.push(`Increase content length to at least ${competitor.wordCount} words to match top competitors`)
    }
    
    if (currentStats.keywordDensity < competitor.keywordDensity - 0.5) {
      recommendations.push(`Increase keyword density to around ${competitor.keywordDensity.toFixed(1)}% for better optimization`)
    } else if (currentStats.keywordDensity > competitor.keywordDensity + 1) {
      recommendations.push('Consider reducing keyword density to avoid over-optimization')
    }
    
    if (currentStats.headingCount < 3) {
      recommendations.push('Add more headings to improve content structure and readability')
    }

    if (!currentContent.title.toLowerCase().includes(currentContent.keyword.toLowerCase())) {
      recommendations.push('Include your target keyword in the title')
    }

    return recommendations
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        {/* Backdrop */}
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={onClose}
        />

        {/* Panel */}
        <div className="relative w-full max-w-6xl bg-white dark:bg-gray-900 rounded-xl shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Competitor Analysis
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Compare your content with top-ranking competitors for "{competitorData.keyword}"
              </p>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <XMarkIcon className="h-5 w-5" />
            </Button>
          </div>

          <div className="flex h-96">
            {/* Competitor List */}
            <div className="w-1/3 border-r border-gray-200 dark:border-gray-700 p-4">
              <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">
                Top Competitors
              </h3>
              <div className="space-y-2">
                {competitorData.topCompetitors.map((comp, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedCompetitor(index)}
                    className={`w-full text-left p-3 rounded-lg border transition-colors ${
                      selectedCompetitor === index
                        ? 'border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                        #{index + 1}
                      </span>
                      <Badge variant={comp.seoScore >= 85 ? 'success' : comp.seoScore >= 70 ? 'warning' : 'error'} size="sm">
                        {comp.seoScore}%
                      </Badge>
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400 mb-1 truncate">
                      {comp.title}
                    </div>
                    <div className="text-xs text-blue-600 dark:text-blue-400 truncate">
                      {comp.url}
                    </div>
                    <div className="flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400">
                      <span>{comp.wordCount} words</span>
                      <span>{comp.keywordDensity.toFixed(1)}% density</span>
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Comparison Content */}
            <div className="flex-1 p-6">
              {/* Tab Navigation */}
              <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1 mb-6">
                <button
                  onClick={() => setActiveTab('overview')}
                  className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'overview'
                      ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
                  }`}
                >
                  Overview
                </button>
                <button
                  onClick={() => setActiveTab('content')}
                  className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'content'
                      ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
                  }`}
                >
                  Content
                </button>
                <button
                  onClick={() => setActiveTab('insights')}
                  className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'insights'
                      ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
                  }`}
                >
                  Insights
                </button>
              </div>

              {/* Tab Content */}
              {activeTab === 'overview' && (
                <div className="space-y-6">
                  {/* Competitor Header */}
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                        {competitor.title}
                      </h4>
                      <a
                        href={`https://${competitor.url}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center mt-1"
                      >
                        {competitor.url}
                        <ArrowTopRightOnSquareIcon className="h-3 w-3 ml-1" />
                      </a>
                    </div>
                    <Badge variant={competitor.seoScore >= 85 ? 'success' : competitor.seoScore >= 70 ? 'warning' : 'error'}>
                      SEO Score: {competitor.seoScore}%
                    </Badge>
                  </div>

                  {/* Comparison Metrics */}
                  <div className="grid grid-cols-2 gap-6">
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">Your Content</h5>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">Word Count</span>
                          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {currentStats.wordCount.toLocaleString()}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">Keyword Density</span>
                          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {currentStats.keywordDensity.toFixed(1)}%
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">Headings</span>
                          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {currentStats.headingCount}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">SEO Score</span>
                          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {currentStats.seoScore}%
                          </span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">Competitor</h5>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">Word Count</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {competitor.wordCount.toLocaleString()}
                            </span>
                            {React.createElement(getComparisonStatus(currentStats.wordCount, competitor.wordCount).icon, {
                              className: `h-4 w-4 ${getComparisonStatus(currentStats.wordCount, competitor.wordCount).color}`
                            })}
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">Keyword Density</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {competitor.keywordDensity.toFixed(1)}%
                            </span>
                            {React.createElement(getComparisonStatus(currentStats.keywordDensity, competitor.keywordDensity, false).icon, {
                              className: `h-4 w-4 ${getComparisonStatus(currentStats.keywordDensity, competitor.keywordDensity, false).color}`
                            })}
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">Headings</span>
                          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {competitor.headings.length}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">SEO Score</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {competitor.seoScore}%
                            </span>
                            {React.createElement(getComparisonStatus(currentStats.seoScore, competitor.seoScore).icon, {
                              className: `h-4 w-4 ${getComparisonStatus(currentStats.seoScore, competitor.seoScore).color}`
                            })}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'content' && (
                <div className="space-y-4">
                  <div>
                    <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      Content Structure
                    </h5>
                    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                      <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        Heading Structure:
                      </div>
                      <div className="space-y-1">
                        {competitor.headings.map((heading, index) => (
                          <div key={index} className="text-sm text-gray-900 dark:text-gray-100">
                            {heading}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      Key Metrics Comparison
                    </h5>
                    <div className="grid grid-cols-1 gap-3">
                      <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Average sentence length</span>
                        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">16.2 words</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Paragraph count</span>
                        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">12</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Reading time</span>
                        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">8 min</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'insights' && (
                <div className="space-y-6">
                  {/* Competitor Insights */}
                  <div>
                    <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      What Makes This Content Successful
                    </h5>
                    <div className="space-y-2">
                      {competitor.insights.map((insight, index) => (
                        <div key={index} className="flex items-start space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                          <CheckCircleIcon className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-green-700 dark:text-green-300">
                            {insight}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Recommendations */}
                  <div>
                    <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      Recommendations for Your Content
                    </h5>
                    <div className="space-y-2">
                      {generateRecommendations().map((recommendation, index) => (
                        <div key={index} className="flex items-start space-x-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                          <LightBulbIcon className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-blue-700 dark:text-blue-300">
                            {recommendation}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Analysis based on current SERP data for "{competitorData.keyword}"
            </div>
            <div className="flex items-center space-x-3">
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
              <Button 
                onClick={() => window.open(`https://${competitor.url}`, '_blank')}
                className="inline-flex items-center"
              >
                <ArrowTopRightOnSquareIcon className="h-4 w-4 mr-2" />
                View Competitor
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}