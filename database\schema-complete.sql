-- ================================================================
-- SEO SAAS COMPREHENSIVE DATABASE SCHEMA
-- Enterprise-Grade Database Design for AI-Powered SEO Platform
-- ================================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "vector";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- ================================================================
-- USER MANAGEMENT & AUTHENTICATION
-- ================================================================

-- Users table with comprehensive profile management
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    encrypted_password VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    company_name VARCHAR(255),
    industry VARCHAR(100),
    website_url VARCHAR(500),
    phone VARCHAR(50),
    country VARCHAR(100),
    timezone VARCHAR(100) DEFAULT 'UTC',
    avatar_url TEXT,
    subscription_tier VARCHAR(50) DEFAULT 'free',
    subscription_status VARCHAR(50) DEFAULT 'active',
    billing_email VARCHAR(255),
    api_quota_used INTEGER DEFAULT 0,
    api_quota_limit INTEGER DEFAULT 100,
    last_login_at TIMESTAMP WITH TIME ZONE,
    email_verified_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- User sessions for security tracking
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(500) UNIQUE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    location VARCHAR(255),
    device_type VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User API keys management
CREATE TABLE user_api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    key_name VARCHAR(100) NOT NULL,
    api_key_hash VARCHAR(255) UNIQUE NOT NULL,
    permissions JSONB DEFAULT '{}',
    usage_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ================================================================
-- PROJECT MANAGEMENT SYSTEM
-- ================================================================

-- Projects with comprehensive metadata
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    website_url VARCHAR(500),
    target_audience TEXT,
    industry VARCHAR(100),
    primary_keywords TEXT[],
    target_locations VARCHAR(100)[],
    project_type VARCHAR(100) DEFAULT 'website',
    status VARCHAR(50) DEFAULT 'active',
    goals JSONB DEFAULT '{}',
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    archived_at TIMESTAMP WITH TIME ZONE
);

-- Project collaborators
CREATE TABLE project_collaborators (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role VARCHAR(50) DEFAULT 'viewer',
    permissions JSONB DEFAULT '{}',
    invited_by UUID REFERENCES users(id),
    invited_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    accepted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(project_id, user_id)
);

-- ================================================================
-- UNIVERSAL NICHE ADAPTATION SYSTEM
-- ================================================================

-- Niche configurations cache
CREATE TABLE niche_configurations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    keyword VARCHAR(500) NOT NULL,
    industry_category VARCHAR(100),
    subcategory VARCHAR(100),
    configuration JSONB NOT NULL,
    confidence_score INTEGER,
    usage_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Industry entities and terminology
CREATE TABLE industry_entities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    industry VARCHAR(100) NOT NULL,
    subcategory VARCHAR(100),
    entity_name VARCHAR(255) NOT NULL,
    entity_type VARCHAR(100),
    definition TEXT,
    importance_level VARCHAR(50),
    usage_context TEXT,
    synonyms TEXT[],
    related_entities TEXT[],
    verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Authority domains database
CREATE TABLE authority_domains (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain VARCHAR(255) UNIQUE NOT NULL,
    domain_authority INTEGER,
    industry VARCHAR(100),
    domain_type VARCHAR(100),
    trust_score INTEGER,
    verification_status VARCHAR(50),
    last_verified_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ================================================================
-- CONTENT GENERATION & MANAGEMENT
-- ================================================================

-- Generated content with full metadata
CREATE TABLE generated_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    target_keyword VARCHAR(500) NOT NULL,
    content_type VARCHAR(100),
    content_intent VARCHAR(100),
    target_location VARCHAR(100),
    industry VARCHAR(100),
    
    -- Content data
    title VARCHAR(500),
    content TEXT NOT NULL,
    meta_description TEXT,
    headings JSONB DEFAULT '{}',
    word_count INTEGER,
    
    -- SEO metrics
    seo_score INTEGER,
    keyword_density JSONB DEFAULT '{}',
    readability_score INTEGER,
    
    -- AI generation metadata
    generation_method VARCHAR(100) DEFAULT 'sequential_ai_thinking',
    thinking_chain_id VARCHAR(255),
    processing_time INTEGER,
    quality_score INTEGER,
    
    -- Competitor analysis
    competitor_data JSONB DEFAULT '{}',
    niche_config_id UUID REFERENCES niche_configurations(id),
    
    -- Authority links
    authority_links JSONB DEFAULT '[]',
    internal_links JSONB DEFAULT '[]',
    
    -- Version control
    version INTEGER DEFAULT 1,
    parent_content_id UUID REFERENCES generated_content(id),
    
    -- Status and workflow
    status VARCHAR(50) DEFAULT 'draft',
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content revisions for version control
CREATE TABLE content_revisions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_id UUID REFERENCES generated_content(id) ON DELETE CASCADE,
    revision_number INTEGER NOT NULL,
    title VARCHAR(500),
    content TEXT NOT NULL,
    changes_summary TEXT,
    changed_by UUID REFERENCES users(id),
    change_reason VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content templates for reuse
CREATE TABLE content_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    template_type VARCHAR(100),
    industry VARCHAR(100),
    content_structure JSONB NOT NULL,
    variables JSONB DEFAULT '{}',
    usage_count INTEGER DEFAULT 0,
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ================================================================
-- COMPETITOR ANALYSIS SYSTEM
-- ================================================================

-- Competitor tracking and analysis
CREATE TABLE competitors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    domain VARCHAR(255) NOT NULL,
    company_name VARCHAR(255),
    description TEXT,
    industry VARCHAR(100),
    analysis_status VARCHAR(50) DEFAULT 'pending',
    last_analyzed_at TIMESTAMP WITH TIME ZONE,
    
    -- SEO metrics
    domain_authority INTEGER,
    page_authority INTEGER,
    spam_score INTEGER,
    organic_keywords INTEGER,
    organic_traffic INTEGER,
    backlinks_count INTEGER,
    
    -- Content metrics
    content_strategy JSONB DEFAULT '{}',
    top_keywords TEXT[],
    content_gaps JSONB DEFAULT '{}',
    
    -- Competitive intelligence
    strengths TEXT[],
    weaknesses TEXT[],
    opportunities TEXT[],
    threats TEXT[],
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Competitor content analysis
CREATE TABLE competitor_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    competitor_id UUID REFERENCES competitors(id) ON DELETE CASCADE,
    url VARCHAR(1000) NOT NULL,
    title VARCHAR(500),
    meta_description TEXT,
    content_type VARCHAR(100),
    word_count INTEGER,
    heading_count INTEGER,
    
    -- Content analysis
    topics TEXT[],
    keywords JSONB DEFAULT '{}',
    sentiment_score DECIMAL(3,2),
    readability_score INTEGER,
    
    -- SEO analysis
    page_authority INTEGER,
    social_shares INTEGER,
    backlinks_count INTEGER,
    estimated_traffic INTEGER,
    
    -- Content structure
    headings JSONB DEFAULT '{}',
    internal_links INTEGER,
    external_links INTEGER,
    images_count INTEGER,
    
    analyzed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ================================================================
-- AUTHORITY LINK DISCOVERY & VALIDATION
-- ================================================================

-- Authority links database
CREATE TABLE authority_links (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    content_id UUID REFERENCES generated_content(id) ON DELETE CASCADE,
    
    -- Link data
    url VARCHAR(1000) NOT NULL,
    title VARCHAR(500),
    domain VARCHAR(255),
    anchor_text VARCHAR(255),
    context TEXT,
    
    -- Authority metrics
    domain_authority INTEGER,
    page_authority INTEGER,
    trust_score INTEGER,
    relevance_score INTEGER,
    
    -- Link validation
    validation_status VARCHAR(50) DEFAULT 'pending',
    is_accessible BOOLEAN,
    response_time INTEGER,
    content_quality_score INTEGER,
    
    -- Link classification
    link_type VARCHAR(100),
    industry_relevance VARCHAR(100),
    content_type VARCHAR(100),
    
    -- E-E-A-T scoring
    expertise_score INTEGER,
    authoritativeness_score INTEGER,
    trustworthiness_score INTEGER,
    experience_score INTEGER,
    
    -- Discovery metadata
    discovery_method VARCHAR(100),
    discovery_source VARCHAR(255),
    discovered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Usage tracking
    usage_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Authority link validation history
CREATE TABLE authority_link_validations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    authority_link_id UUID REFERENCES authority_links(id) ON DELETE CASCADE,
    validation_type VARCHAR(100),
    validation_result JSONB NOT NULL,
    issues_found TEXT[],
    recommendations TEXT[],
    validated_by VARCHAR(100),
    validated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ================================================================
-- SEQUENTIAL AI THINKING SYSTEM
-- ================================================================

-- AI thinking chains tracking
CREATE TABLE ai_thinking_chains (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    chain_id VARCHAR(255) UNIQUE NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    content_id UUID REFERENCES generated_content(id) ON DELETE CASCADE,
    
    -- Chain metadata
    keyword VARCHAR(500),
    industry VARCHAR(100),
    content_intent VARCHAR(100),
    
    -- Processing data
    stages_count INTEGER,
    reasoning_depth INTEGER,
    processing_time INTEGER,
    
    -- Quality metrics
    coherence_score INTEGER,
    expertise_score INTEGER,
    completeness_score INTEGER,
    accuracy_score INTEGER,
    engagement_score INTEGER,
    overall_quality_score INTEGER,
    
    -- Chain data
    chain_data JSONB NOT NULL,
    
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI thinking stages for detailed analysis
CREATE TABLE ai_thinking_stages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    thinking_chain_id UUID REFERENCES ai_thinking_chains(id) ON DELETE CASCADE,
    stage_number INTEGER NOT NULL,
    stage_name VARCHAR(255) NOT NULL,
    stage_output JSONB NOT NULL,
    processing_time INTEGER,
    quality_contribution DECIMAL(3,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ================================================================
-- KEYWORD RESEARCH & ANALYSIS
-- ================================================================

-- Keyword research data
CREATE TABLE keywords (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    keyword VARCHAR(500) NOT NULL,
    keyword_type VARCHAR(100),
    search_intent VARCHAR(100),
    
    -- Search metrics
    search_volume INTEGER,
    keyword_difficulty INTEGER,
    competition_level VARCHAR(50),
    cost_per_click DECIMAL(10,2),
    
    -- Trend data
    trend_data JSONB DEFAULT '{}',
    seasonal_trends JSONB DEFAULT '{}',
    
    -- Semantic analysis
    related_keywords TEXT[],
    lsi_keywords TEXT[],
    semantic_clusters JSONB DEFAULT '{}',
    
    -- Competitive data
    ranking_difficulty INTEGER,
    top_competitors TEXT[],
    content_gaps JSONB DEFAULT '{}',
    
    -- Tracking
    current_ranking INTEGER,
    target_ranking INTEGER,
    tracking_status VARCHAR(50) DEFAULT 'active',
    
    last_updated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Keyword performance tracking
CREATE TABLE keyword_rankings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    keyword_id UUID REFERENCES keywords(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    
    -- Ranking data
    ranking_position INTEGER,
    page_url VARCHAR(1000),
    search_engine VARCHAR(50) DEFAULT 'google',
    location VARCHAR(100),
    device_type VARCHAR(50) DEFAULT 'desktop',
    
    -- Performance metrics
    impressions INTEGER,
    clicks INTEGER,
    ctr DECIMAL(5,4),
    average_position DECIMAL(4,2),
    
    -- Change tracking
    position_change INTEGER,
    change_percentage DECIMAL(5,2),
    
    tracked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ================================================================
-- ANALYTICS & PERFORMANCE TRACKING
-- ================================================================

-- Content performance analytics
CREATE TABLE content_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_id UUID REFERENCES generated_content(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    
    -- Traffic metrics
    page_views INTEGER DEFAULT 0,
    unique_visitors INTEGER DEFAULT 0,
    bounce_rate DECIMAL(5,4),
    avg_time_on_page INTEGER,
    
    -- SEO metrics
    organic_traffic INTEGER DEFAULT 0,
    keyword_rankings JSONB DEFAULT '{}',
    backlinks_gained INTEGER DEFAULT 0,
    social_shares INTEGER DEFAULT 0,
    
    -- Engagement metrics
    scroll_depth DECIMAL(5,4),
    click_through_rate DECIMAL(5,4),
    conversion_rate DECIMAL(5,4),
    
    -- Performance scores
    page_speed_score INTEGER,
    core_web_vitals JSONB DEFAULT '{}',
    
    measurement_date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User activity tracking
CREATE TABLE user_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    
    -- Activity data
    activity_type VARCHAR(100) NOT NULL,
    activity_description TEXT,
    resource_type VARCHAR(100),
    resource_id UUID,
    
    -- Context data
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ================================================================
-- REAL DATA VALIDATION SYSTEM
-- ================================================================

-- Demo data detection and blocking
CREATE TABLE demo_data_attempts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    
    -- Attempt data
    input_data TEXT NOT NULL,
    input_type VARCHAR(100),
    detection_method VARCHAR(100),
    confidence_score INTEGER,
    
    -- Validation results
    is_demo_data BOOLEAN NOT NULL,
    blocked_reason TEXT,
    suggested_alternatives TEXT[],
    
    -- Context
    ip_address INET,
    user_agent TEXT,
    endpoint_accessed VARCHAR(255),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Real data validation rules
CREATE TABLE validation_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    rule_name VARCHAR(255) NOT NULL,
    rule_type VARCHAR(100),
    rule_pattern TEXT,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    severity_level VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ================================================================
-- ENTERPRISE SECURITY SYSTEM
-- ================================================================

-- Security events and monitoring
CREATE TABLE security_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    
    -- Event data
    event_type VARCHAR(100) NOT NULL,
    event_description TEXT,
    severity_level VARCHAR(50),
    
    -- Technical details
    ip_address INET,
    user_agent TEXT,
    endpoint VARCHAR(255),
    request_method VARCHAR(10),
    
    -- Response data
    was_blocked BOOLEAN DEFAULT false,
    response_action VARCHAR(100),
    
    -- Context
    session_id VARCHAR(255),
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Rate limiting tracking
CREATE TABLE rate_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    identifier VARCHAR(255) NOT NULL, -- IP or user ID
    limit_type VARCHAR(100) NOT NULL,
    
    -- Limit data
    requests_count INTEGER DEFAULT 1,
    window_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    window_duration INTEGER, -- in seconds
    max_requests INTEGER,
    
    -- Status
    is_exceeded BOOLEAN DEFAULT false,
    reset_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ================================================================
-- INTELLIGENT CONTENT EDITOR
-- ================================================================

-- Content refinement history
CREATE TABLE content_refinements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_id UUID REFERENCES generated_content(id) ON DELETE CASCADE,
    
    -- Refinement data
    refinement_type VARCHAR(100),
    original_content TEXT NOT NULL,
    refined_content TEXT NOT NULL,
    refinement_prompt TEXT,
    
    -- Quality metrics
    quality_before INTEGER,
    quality_after INTEGER,
    improvement_score INTEGER,
    
    -- Refinement metadata
    issues_addressed TEXT[],
    techniques_applied TEXT[],
    processing_time INTEGER,
    
    -- AI model data
    model_used VARCHAR(100),
    model_version VARCHAR(50),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- E-E-A-T enhancement tracking
CREATE TABLE eeat_enhancements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_id UUID REFERENCES generated_content(id) ON DELETE CASCADE,
    
    -- E-E-A-T scores
    experience_score_before INTEGER,
    experience_score_after INTEGER,
    expertise_score_before INTEGER,
    expertise_score_after INTEGER,
    authoritativeness_score_before INTEGER,
    authoritativeness_score_after INTEGER,
    trustworthiness_score_before INTEGER,
    trustworthiness_score_after INTEGER,
    
    -- Enhancement details
    enhancements_applied JSONB NOT NULL,
    signals_added TEXT[],
    authority_sources_added TEXT[],
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ================================================================
-- BILLING & SUBSCRIPTION MANAGEMENT
-- ================================================================

-- Subscription plans
CREATE TABLE subscription_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    plan_name VARCHAR(100) UNIQUE NOT NULL,
    plan_type VARCHAR(50),
    price_monthly DECIMAL(10,2),
    price_yearly DECIMAL(10,2),
    
    -- Feature limits
    content_generation_limit INTEGER,
    api_requests_limit INTEGER,
    projects_limit INTEGER,
    collaborators_limit INTEGER,
    
    -- Features
    features JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User subscriptions
CREATE TABLE user_subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    plan_id UUID REFERENCES subscription_plans(id),
    
    -- Subscription data
    status VARCHAR(50) DEFAULT 'active',
    billing_cycle VARCHAR(50),
    current_period_start TIMESTAMP WITH TIME ZONE,
    current_period_end TIMESTAMP WITH TIME ZONE,
    
    -- Billing
    stripe_subscription_id VARCHAR(255),
    stripe_customer_id VARCHAR(255),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Usage tracking for billing
CREATE TABLE usage_tracking (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    
    -- Usage data
    feature_type VARCHAR(100) NOT NULL,
    usage_count INTEGER DEFAULT 1,
    billing_period DATE,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ================================================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- ================================================================

-- User indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_subscription_tier ON users(subscription_tier);
CREATE INDEX idx_users_created_at ON users(created_at);

-- Project indexes
CREATE INDEX idx_projects_user_id ON projects(user_id);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_industry ON projects(industry);

-- Content indexes
CREATE INDEX idx_generated_content_user_id ON generated_content(user_id);
CREATE INDEX idx_generated_content_project_id ON generated_content(project_id);
CREATE INDEX idx_generated_content_keyword ON generated_content(target_keyword);
CREATE INDEX idx_generated_content_status ON generated_content(status);
CREATE INDEX idx_generated_content_created_at ON generated_content(created_at);

-- Niche configuration indexes
CREATE INDEX idx_niche_configurations_keyword ON niche_configurations(keyword);
CREATE INDEX idx_niche_configurations_industry ON niche_configurations(industry_category);

-- Authority links indexes
CREATE INDEX idx_authority_links_project_id ON authority_links(project_id);
CREATE INDEX idx_authority_links_domain ON authority_links(domain);
CREATE INDEX idx_authority_links_validation_status ON authority_links(validation_status);

-- Keyword indexes
CREATE INDEX idx_keywords_project_id ON keywords(project_id);
CREATE INDEX idx_keywords_keyword ON keywords(keyword);
CREATE INDEX idx_keywords_search_volume ON keywords(search_volume);

-- Analytics indexes
CREATE INDEX idx_content_analytics_content_id ON content_analytics(content_id);
CREATE INDEX idx_content_analytics_measurement_date ON content_analytics(measurement_date);

-- Security indexes
CREATE INDEX idx_security_events_user_id ON security_events(user_id);
CREATE INDEX idx_security_events_event_type ON security_events(event_type);
CREATE INDEX idx_security_events_created_at ON security_events(created_at);

-- Full-text search indexes
CREATE INDEX idx_generated_content_search ON generated_content USING gin(to_tsvector('english', title || ' ' || content));
CREATE INDEX idx_keywords_search ON keywords USING gin(to_tsvector('english', keyword));

-- ================================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- ================================================================

-- Enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE generated_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE keywords ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_analytics ENABLE ROW LEVEL SECURITY;

-- User access policies
CREATE POLICY users_own_data ON users
    FOR ALL USING (auth.uid() = id);

-- Project access policies  
CREATE POLICY projects_owner_access ON projects
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY projects_collaborator_access ON projects
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM project_collaborators 
            WHERE project_id = projects.id 
            AND user_id = auth.uid()
        )
    );

-- Content access policies
CREATE POLICY content_owner_access ON generated_content
    FOR ALL USING (auth.uid() = user_id);

-- Keyword access policies
CREATE POLICY keywords_project_access ON keywords
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM projects 
            WHERE id = keywords.project_id 
            AND user_id = auth.uid()
        )
    );

-- ================================================================
-- TRIGGERS FOR AUTOMATED MAINTENANCE
-- ================================================================

-- Update timestamps trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply timestamp triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_generated_content_updated_at BEFORE UPDATE ON generated_content
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Auto-archive old demo data attempts
CREATE OR REPLACE FUNCTION cleanup_old_demo_attempts()
RETURNS void AS $$
BEGIN
    DELETE FROM demo_data_attempts 
    WHERE created_at < NOW() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- ================================================================
-- INITIAL DATA SEEDING
-- ================================================================

-- Insert default subscription plans
INSERT INTO subscription_plans (plan_name, plan_type, price_monthly, price_yearly, content_generation_limit, api_requests_limit, projects_limit, collaborators_limit, features) VALUES
('Free', 'free', 0.00, 0.00, 10, 100, 1, 0, '{"basic_generation": true, "keyword_research": false, "competitor_analysis": false, "authority_links": false}'),
('Professional', 'paid', 29.99, 299.99, 500, 5000, 10, 5, '{"basic_generation": true, "keyword_research": true, "competitor_analysis": true, "authority_links": true, "advanced_analytics": false}'),
('Enterprise', 'paid', 99.99, 999.99, 2000, 20000, 50, 25, '{"basic_generation": true, "keyword_research": true, "competitor_analysis": true, "authority_links": true, "advanced_analytics": true, "api_access": true, "custom_integrations": true}');

-- Insert validation rules for demo data detection
INSERT INTO validation_rules (rule_name, rule_type, rule_pattern, description, severity_level) VALUES
('Demo Website URLs', 'url_pattern', '(example\\.com|test\\.com|demo\\.com|placeholder\\.com)', 'Detect demo website URLs', 'high'),
('Placeholder Text', 'text_pattern', '(lorem ipsum|placeholder|demo text|sample text)', 'Detect placeholder content', 'high'),
('Test Email Addresses', 'email_pattern', '(test@|demo@|example@|placeholder@)', 'Detect test email addresses', 'medium'),
('Generic Company Names', 'text_pattern', '(acme corp|demo company|test inc|sample business)', 'Detect generic company names', 'medium');

-- ================================================================
-- FUNCTIONS FOR BUSINESS LOGIC
-- ================================================================

-- Function to calculate content SEO score
CREATE OR REPLACE FUNCTION calculate_seo_score(
    p_content_id UUID
) RETURNS INTEGER AS $$
DECLARE
    v_score INTEGER := 0;
    v_content generated_content;
    v_keyword_density DECIMAL;
    v_heading_ratio DECIMAL;
BEGIN
    SELECT * INTO v_content FROM generated_content WHERE id = p_content_id;
    
    -- Base score
    v_score := 60;
    
    -- Word count scoring
    IF v_content.word_count > 1500 THEN v_score := v_score + 10; END IF;
    IF v_content.word_count > 2500 THEN v_score := v_score + 10; END IF;
    
    -- Heading structure scoring
    IF (v_content.headings->>'h2')::INTEGER > 3 THEN v_score := v_score + 5; END IF;
    IF (v_content.headings->>'h3')::INTEGER > 5 THEN v_score := v_score + 5; END IF;
    
    -- Meta description scoring
    IF LENGTH(v_content.meta_description) BETWEEN 150 AND 160 THEN v_score := v_score + 10; END IF;
    
    RETURN LEAST(v_score, 100);
END;
$$ LANGUAGE plpgsql;

-- Function to track API usage
CREATE OR REPLACE FUNCTION track_api_usage(
    p_user_id UUID,
    p_feature_type VARCHAR(100)
) RETURNS BOOLEAN AS $$
DECLARE
    v_current_usage INTEGER;
    v_limit INTEGER;
BEGIN
    -- Get current usage for this billing period
    SELECT COALESCE(SUM(usage_count), 0) INTO v_current_usage
    FROM usage_tracking 
    WHERE user_id = p_user_id 
    AND feature_type = p_feature_type 
    AND billing_period = DATE_TRUNC('month', NOW());
    
    -- Get user's limit
    SELECT 
        CASE 
            WHEN p_feature_type = 'content_generation' THEN sp.content_generation_limit
            WHEN p_feature_type = 'api_requests' THEN sp.api_requests_limit
            ELSE 1000
        END INTO v_limit
    FROM users u
    JOIN user_subscriptions us ON u.id = us.user_id
    JOIN subscription_plans sp ON us.plan_id = sp.id
    WHERE u.id = p_user_id AND us.status = 'active';
    
    -- Check if limit exceeded
    IF v_current_usage >= v_limit THEN
        RETURN FALSE;
    END IF;
    
    -- Record usage
    INSERT INTO usage_tracking (user_id, feature_type, billing_period)
    VALUES (p_user_id, p_feature_type, DATE_TRUNC('month', NOW()));
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- ================================================================
-- VIEWS FOR COMMON QUERIES
-- ================================================================

-- User dashboard summary view
CREATE VIEW user_dashboard_summary AS
SELECT 
    u.id as user_id,
    u.email,
    u.subscription_tier,
    COUNT(DISTINCT p.id) as project_count,
    COUNT(DISTINCT gc.id) as content_count,
    COUNT(DISTINCT k.id) as keyword_count,
    COALESCE(SUM(ut.usage_count), 0) as monthly_api_usage
FROM users u
LEFT JOIN projects p ON u.id = p.user_id AND p.archived_at IS NULL
LEFT JOIN generated_content gc ON u.id = gc.user_id 
LEFT JOIN keywords k ON p.id = k.project_id
LEFT JOIN usage_tracking ut ON u.id = ut.user_id 
    AND ut.billing_period = DATE_TRUNC('month', NOW())
GROUP BY u.id, u.email, u.subscription_tier;

-- Project performance summary view  
CREATE VIEW project_performance_summary AS
SELECT 
    p.id as project_id,
    p.name,
    p.user_id,
    COUNT(DISTINCT gc.id) as content_count,
    COUNT(DISTINCT k.id) as keyword_count,
    AVG(gc.seo_score) as avg_seo_score,
    SUM(ca.organic_traffic) as total_organic_traffic,
    MAX(ca.measurement_date) as last_analytics_update
FROM projects p
LEFT JOIN generated_content gc ON p.id = gc.project_id
LEFT JOIN keywords k ON p.id = k.project_id  
LEFT JOIN content_analytics ca ON gc.id = ca.content_id
WHERE p.archived_at IS NULL
GROUP BY p.id, p.name, p.user_id;

-- Content performance view
CREATE VIEW content_performance_view AS
SELECT 
    gc.id,
    gc.title,
    gc.target_keyword,
    gc.seo_score,
    gc.word_count,
    gc.quality_score,
    ca.organic_traffic,
    ca.page_views,
    ca.bounce_rate,
    COUNT(al.id) as authority_links_count
FROM generated_content gc
LEFT JOIN content_analytics ca ON gc.id = ca.content_id
LEFT JOIN authority_links al ON gc.id = al.content_id
GROUP BY gc.id, gc.title, gc.target_keyword, gc.seo_score, 
         gc.word_count, gc.quality_score, ca.organic_traffic, 
         ca.page_views, ca.bounce_rate;

-- ================================================================
-- COMPLETION MESSAGE
-- ================================================================

-- Insert completion marker
INSERT INTO validation_rules (rule_name, rule_type, description, severity_level) 
VALUES ('Schema Installation Complete', 'system', 'Marks successful completion of schema installation', 'info');