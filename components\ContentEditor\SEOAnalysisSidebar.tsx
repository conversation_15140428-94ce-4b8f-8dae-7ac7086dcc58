/**
 * SEO Analysis Sidebar Component
 * Real-time SEO analysis and recommendations panel
 */

'use client'

import React, { useState } from 'react'
import Card from '@/components/UI/Card'
import But<PERSON> from '@/components/UI/Button'
import Badge from '@/components/UI/Badge'
import LoadingSpinner from '@/components/UI/LoadingSpinner'
import {
  ChartBarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  LightBulbIcon,
  MagnifyingGlassIcon,
  DocumentTextIcon,
  EyeIcon,
  ClockIcon,
  ArrowTrendingUpIcon,
  ChevronDownIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline'

interface SEOAnalysis {
  overallScore: number
  titleScore: number
  metaScore: number
  keywordDensity: number
  readabilityScore: number
  wordCount: number
  recommendations: string[]
  keywordUsage: {
    inTitle: boolean
    inMeta: boolean
    inFirstParagraph: boolean
    density: number
    frequency: number
  }
}

interface SEOAnalysisSidebarProps {
  analysis: SEOAnalysis | null
  isAnalyzing: boolean
  keyword: string
  onKeywordChange: (keyword: string) => void
}

export default function SEOAnalysisSidebar({
  analysis,
  isAnalyzing,
  keyword,
  onKeywordChange
}: SEOAnalysisSidebarProps) {
  const [expandedSections, setExpandedSections] = useState<string[]>(['overview', 'recommendations'])

  const toggleSection = (section: string) => {
    setExpandedSections(prev => 
      prev.includes(section)
        ? prev.filter(s => s !== section)
        : [...prev, section]
    )
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 dark:text-green-400'
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400'
    return 'text-red-600 dark:text-red-400'
  }

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return 'success'
    if (score >= 60) return 'warning'
    return 'error'
  }

  const getDensityStatus = (density: number) => {
    if (density >= 1 && density <= 3) return { status: 'good', color: 'text-green-600 dark:text-green-400', icon: CheckCircleIcon }
    if (density >= 0.5 && density <= 5) return { status: 'warning', color: 'text-yellow-600 dark:text-yellow-400', icon: ExclamationTriangleIcon }
    return { status: 'poor', color: 'text-red-600 dark:text-red-400', icon: XCircleIcon }
  }

  const getReadabilityStatus = (score: number) => {
    if (score >= 80) return { status: 'excellent', color: 'text-green-600 dark:text-green-400' }
    if (score >= 60) return { status: 'good', color: 'text-yellow-600 dark:text-yellow-400' }
    return { status: 'needs improvement', color: 'text-red-600 dark:text-red-400' }
  }

  const SectionHeader = ({ title, isExpanded, onToggle }: { title: string, isExpanded: boolean, onToggle: () => void }) => (
    <button
      onClick={onToggle}
      className="w-full flex items-center justify-between p-3 text-left font-medium text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
    >
      <span>{title}</span>
      {isExpanded ? (
        <ChevronDownIcon className="h-4 w-4" />
      ) : (
        <ChevronRightIcon className="h-4 w-4" />
      )}
    </button>
  )

  return (
    <div className="space-y-4">
      {/* Quick Keyword Input */}
      <Card className="p-4">
        <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
          <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
          Target Keyword
        </h3>
        <input
          type="text"
          value={keyword}
          onChange={(e) => onKeywordChange(e.target.value)}
          placeholder="Enter target keyword..."
          className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
        />
      </Card>

      {isAnalyzing && (
        <Card className="p-6 text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-sm text-gray-600 dark:text-gray-400">
            Analyzing SEO performance...
          </p>
        </Card>
      )}

      {analysis && (
        <div className="space-y-4">
          {/* Overall Score */}
          <Card className="p-0 overflow-hidden">
            <SectionHeader 
              title="SEO Overview" 
              isExpanded={expandedSections.includes('overview')}
              onToggle={() => toggleSection('overview')}
            />
            {expandedSections.includes('overview') && (
              <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                <div className="text-center mb-4">
                  <div className={`text-4xl font-bold ${getScoreColor(analysis.overallScore)}`}>
                    {analysis.overallScore}%
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Overall SEO Score
                  </div>
                  <Badge variant={getScoreBadgeVariant(analysis.overallScore)} className="mt-2">
                    {analysis.overallScore >= 80 ? 'Excellent' : 
                     analysis.overallScore >= 60 ? 'Good' : 'Needs Work'}
                  </Badge>
                </div>

                <div className="space-y-3">
                  {/* Title Optimization */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <DocumentTextIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-700 dark:text-gray-300">Title SEO</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`text-sm font-medium ${getScoreColor(analysis.titleScore)}`}>
                        {analysis.titleScore}%
                      </span>
                    </div>
                  </div>

                  {/* Meta Description */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <EyeIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-700 dark:text-gray-300">Meta Description</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`text-sm font-medium ${getScoreColor(analysis.metaScore)}`}>
                        {analysis.metaScore}%
                      </span>
                    </div>
                  </div>

                  {/* Readability */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <DocumentTextIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-700 dark:text-gray-300">Readability</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`text-sm font-medium ${getReadabilityStatus(analysis.readabilityScore).color}`}>
                        {analysis.readabilityScore}%
                      </span>
                    </div>
                  </div>

                  {/* Word Count */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <ChartBarIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-700 dark:text-gray-300">Word Count</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {analysis.wordCount.toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </Card>

          {/* Keyword Analysis */}
          {keyword && (
            <Card className="p-0 overflow-hidden">
              <SectionHeader 
                title="Keyword Analysis" 
                isExpanded={expandedSections.includes('keyword')}
                onToggle={() => toggleSection('keyword')}
              />
              {expandedSections.includes('keyword') && (
                <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="space-y-4">
                    {/* Keyword Density */}
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Keyword Density
                        </span>
                        <div className="flex items-center space-x-2">
                          {React.createElement(getDensityStatus(analysis.keywordDensity).icon, {
                            className: `h-4 w-4 ${getDensityStatus(analysis.keywordDensity).color}`
                          })}
                          <span className={`text-sm font-medium ${getDensityStatus(analysis.keywordDensity).color}`}>
                            {analysis.keywordDensity.toFixed(1)}%
                          </span>
                        </div>
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                        Optimal range: 1-3%
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full transition-all duration-300 ${
                            analysis.keywordDensity >= 1 && analysis.keywordDensity <= 3
                              ? 'bg-green-500'
                              : analysis.keywordDensity >= 0.5 && analysis.keywordDensity <= 5
                              ? 'bg-yellow-500'
                              : 'bg-red-500'
                          }`}
                          style={{ width: `${Math.min(analysis.keywordDensity * 20, 100)}%` }}
                        />
                      </div>
                    </div>

                    {/* Keyword Usage Checklist */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                        Keyword Placement
                      </h4>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">In Title</span>
                          <div className="flex items-center">
                            {analysis.keywordUsage.inTitle ? (
                              <CheckCircleIcon className="h-4 w-4 text-green-500" />
                            ) : (
                              <XCircleIcon className="h-4 w-4 text-red-500" />
                            )}
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">In Meta Description</span>
                          <div className="flex items-center">
                            {analysis.keywordUsage.inMeta ? (
                              <CheckCircleIcon className="h-4 w-4 text-green-500" />
                            ) : (
                              <XCircleIcon className="h-4 w-4 text-red-500" />
                            )}
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">In First Paragraph</span>
                          <div className="flex items-center">
                            {analysis.keywordUsage.inFirstParagraph ? (
                              <CheckCircleIcon className="h-4 w-4 text-green-500" />
                            ) : (
                              <XCircleIcon className="h-4 w-4 text-red-500" />
                            )}
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">Total Mentions</span>
                          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {analysis.keywordUsage.frequency}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </Card>
          )}

          {/* Recommendations */}
          <Card className="p-0 overflow-hidden">
            <SectionHeader 
              title="SEO Recommendations" 
              isExpanded={expandedSections.includes('recommendations')}
              onToggle={() => toggleSection('recommendations')}
            />
            {expandedSections.includes('recommendations') && (
              <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                {analysis.recommendations.length > 0 ? (
                  <div className="space-y-3">
                    {analysis.recommendations.map((recommendation, index) => (
                      <div key={index} className="flex items-start space-x-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <LightBulbIcon className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-blue-700 dark:text-blue-300">
                          {recommendation}
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6">
                    <CheckCircleIcon className="h-8 w-8 text-green-500 mx-auto mb-2" />
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Great job! No immediate recommendations.
                    </p>
                  </div>
                )}
              </div>
            )}
          </Card>

          {/* Content Metrics */}
          <Card className="p-0 overflow-hidden">
            <SectionHeader 
              title="Content Metrics" 
              isExpanded={expandedSections.includes('metrics')}
              onToggle={() => toggleSection('metrics')}
            />
            {expandedSections.includes('metrics') && (
              <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      {analysis.wordCount}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Words
                    </div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      {analysis.readabilityScore}%
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Readability
                    </div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      {analysis.keywordUsage.frequency}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Keywords
                    </div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className={`text-lg font-semibold ${getScoreColor(analysis.overallScore)}`}>
                      {analysis.overallScore}%
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      SEO Score
                    </div>
                  </div>
                </div>
              </div>
            )}
          </Card>
        </div>
      )}

      {!analysis && !isAnalyzing && (
        <Card className="p-6 text-center">
          <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            Start Writing to See Analysis
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Add content and a target keyword to get real-time SEO analysis and recommendations.
          </p>
        </Card>
      )}
    </div>
  )
}