/* 
 * SEO SAAS Main CSS
 * Enterprise-grade design system foundation
 * Version: 2.0.0
 */

@import url('components.css');
@import url('layout.css');
@import url('dashboard.css');
@import url('forms.css');
@import url('data-visualization.css');
@import url('animations.css');
@import url('global-search.css');
@import url('sidebar-enhancements.css');
@import url('breadcrumb.css');

/* Import design system variables */
@import '../styles/design-system.css';

/* ===========================
   GLOBAL SEARCH INTEGRATION
   =========================== */

/* Global Search Navigation Styles */
.global-search {
    position: relative;
    display: flex;
    align-items: center;
    gap: 12px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 8px 12px;
    min-width: 320px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.global-search:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
}

.global-search:focus-within {
    background: white;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.global-search .search-icon {
    color: #64748b;
    flex-shrink: 0;
}

.global-search .search-input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-size: 14px;
    color: #334155;
    cursor: pointer;
}

.global-search .search-input::placeholder {
    color: #94a3b8;
}

.search-shortcut {
    display: flex;
    align-items: center;
}

.shortcut-hint {
    background: #e2e8f0;
    color: #64748b;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    border: 1px solid #cbd5e1;
}

/* Navigation Layout Adjustments */
.nav-center {
    display: flex;
    align-items: center;
    gap: 24px;
    flex: 1;
    justify-content: center;
}

/* Analytics Controls */
.analytics-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.analytics-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    background: white;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
}

.analytics-btn:hover {
    background: #f9fafb;
    border-color: #d1d5db;
}

.analytics-btn-primary {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

.analytics-btn-primary:hover {
    background: #2563eb;
    border-color: #2563eb;
}

.date-range-selector select {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    color: #374151;
    background: white;
    cursor: pointer;
}

/* ===========================
   GLOBAL ENTERPRISE STYLES
   =========================== */

*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-family: var(--font-primary);
  font-size: 16px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-family: var(--font-primary);
  min-height: 100vh;
  overflow-x: hidden;
}

/* Remove focus outline for mouse users but keep for keyboard users */
.js-focus-visible :focus:not(.focus-visible) {
  outline: none;
}

/* ===========================
   ENHANCED ACCESSIBILITY
   =========================== */

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --gray-100: #000000;
    --gray-200: #000000;
    --gray-300: #000000;
    --gray-800: #ffffff;
    --gray-900: #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===========================
   ENTERPRISE TYPOGRAPHY ENHANCEMENTS
   =========================== */

/* Professional text selection */
::selection {
  background-color: var(--primary-200);
  color: var(--primary-900);
}

::-moz-selection {
  background-color: var(--primary-200);
  color: var(--primary-900);
}

/* Enhanced link styles */
a {
  color: var(--primary-600);
  text-decoration: none;
  transition: color 0.2s ease-in-out;
}

a:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Professional code styling */
code {
  font-family: var(--font-mono);
  font-size: 0.875em;
  background-color: var(--gray-100);
  color: var(--gray-800);
  padding: 0.125rem 0.25rem;
  border-radius: var(--radius-sm);
}

pre {
  font-family: var(--font-mono);
  background-color: var(--gray-900);
  color: var(--gray-100);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  overflow-x: auto;
  margin: var(--space-4) 0;
}

pre code {
  background: none;
  color: inherit;
  padding: 0;
}

/* ===========================
   ENTERPRISE UTILITY CLASSES
   =========================== */

/* Spacing utilities */
.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-5 { margin: var(--space-5); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }
.m-10 { margin: var(--space-10); }
.m-12 { margin: var(--space-12); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }

.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-5 { padding: var(--space-5); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

/* Display utilities */
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.hidden { display: none; }

/* Flexbox utilities */
.flex-row { flex-direction: row; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-none { flex: none; }

/* Gap utilities */
.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

/* Width utilities */
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-fit { width: fit-content; }
.w-screen { width: 100vw; }

.max-w-xs { max-width: 20rem; }
.max-w-sm { max-width: 24rem; }
.max-w-md { max-width: 28rem; }
.max-w-lg { max-width: 32rem; }
.max-w-xl { max-width: 36rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-6xl { max-width: 72rem; }
.max-w-7xl { max-width: 80rem; }
.max-w-full { max-width: 100%; }
.max-w-screen-xl { max-width: 1280px; }

/* Height utilities */
.h-full { height: 100%; }
.h-screen { height: 100vh; }
.min-h-screen { min-height: 100vh; }

/* Position utilities */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* Overflow utilities */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }

/* Border utilities */
.border { border: 1px solid var(--gray-200); }
.border-t { border-top: 1px solid var(--gray-200); }
.border-b { border-bottom: 1px solid var(--gray-200); }
.border-l { border-left: 1px solid var(--gray-200); }
.border-r { border-right: 1px solid var(--gray-200); }

.border-gray-100 { border-color: var(--gray-100); }
.border-gray-200 { border-color: var(--gray-200); }
.border-gray-300 { border-color: var(--gray-300); }

.rounded { border-radius: var(--radius-md); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

/* Shadow utilities */
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* Background utilities */
.bg-white { background-color: white; }
.bg-gray-50 { background-color: var(--gray-50); }
.bg-gray-100 { background-color: var(--gray-100); }
.bg-gray-200 { background-color: var(--gray-200); }
.bg-gray-900 { background-color: var(--gray-900); }

.bg-primary-50 { background-color: var(--primary-50); }
.bg-primary-100 { background-color: var(--primary-100); }
.bg-primary-500 { background-color: var(--primary-500); }
.bg-primary-600 { background-color: var(--primary-600); }

.bg-success-50 { background-color: var(--success-50); }
.bg-success-500 { background-color: var(--success-500); }
.bg-warning-50 { background-color: var(--warning-50); }
.bg-warning-500 { background-color: var(--warning-500); }
.bg-error-50 { background-color: var(--error-50); }
.bg-error-500 { background-color: var(--error-500); }

/* Text color utilities */
.text-white { color: white; }
.text-gray-500 { color: var(--gray-500); }
.text-gray-600 { color: var(--gray-600); }
.text-gray-700 { color: var(--gray-700); }
.text-gray-800 { color: var(--gray-800); }
.text-gray-900 { color: var(--gray-900); }

.text-primary-600 { color: var(--primary-600); }
.text-primary-700 { color: var(--primary-700); }
.text-success-600 { color: var(--success-600); }
.text-warning-600 { color: var(--warning-600); }
.text-error-600 { color: var(--error-600); }

/* Text alignment */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

/* Cursor utilities */
.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }
.cursor-default { cursor: default; }

/* Z-index utilities */
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* ===========================
   PROFESSIONAL SCROLLBARS
   =========================== */

/* Webkit browsers */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: var(--gray-100);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background-color: var(--gray-300);
  border-radius: var(--radius-full);
  transition: background-color 0.2s ease-in-out;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--gray-400);
}

/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--gray-300) var(--gray-100);
}

/* ===========================
   PRINT STYLES
   =========================== */

@media print {
  *,
  *::before,
  *::after {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  a[href]::after {
    content: " (" attr(href) ")";
  }

  thead {
    display: table-header-group;
  }

  tr,
  img {
    page-break-inside: avoid;
  }

  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }

  h2,
  h3 {
    page-break-after: avoid;
  }
}