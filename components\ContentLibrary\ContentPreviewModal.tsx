/**
 * Content Preview Modal Component
 * Full-screen preview of content with metadata and actions
 */

'use client'

import React, { useState } from 'react'
import { ContentItem } from '@/lib/api/types'
import Button from '@/components/UI/Button'
import Badge from '@/components/UI/Badge'
import {
  XMarkIcon,
  PencilIcon,
  DocumentDuplicateIcon,
  ShareIcon,
  ArrowDownTrayIcon,
  EyeIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  TabletIcon,
  ClockIcon,
  TagIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'

interface ContentPreviewModalProps {
  content: ContentItem
  onClose: () => void
  onEdit: () => void
}

type ViewMode = 'formatted' | 'raw' | 'preview'
type DeviceView = 'desktop' | 'tablet' | 'mobile'

export default function ContentPreviewModal({
  content,
  onClose,
  onEdit
}: ContentPreviewModalProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('formatted')
  const [deviceView, setDeviceView] = useState<DeviceView>('desktop')

  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('en-US', {
      dateStyle: 'long',
      timeStyle: 'short'
    }).format(new Date(dateString))
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'success'
      case 'draft':
        return 'warning'
      case 'scheduled':
        return 'info'
      default:
        return 'secondary'
    }
  }

  const getSeoScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 dark:text-green-400'
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400'
    return 'text-red-600 dark:text-red-400'
  }

  const getDeviceClasses = () => {
    switch (deviceView) {
      case 'mobile':
        return 'max-w-sm mx-auto'
      case 'tablet':
        return 'max-w-2xl mx-auto'
      default:
        return 'max-w-full'
    }
  }

  const formatContent = (text: string) => {
    return text
      .replace(/^# (.*$)/gim, '<h1 class="text-3xl font-bold mb-4 text-gray-900 dark:text-gray-100">$1</h1>')
      .replace(/^## (.*$)/gim, '<h2 class="text-2xl font-semibold mb-3 text-gray-900 dark:text-gray-100">$1</h2>')
      .replace(/^### (.*$)/gim, '<h3 class="text-xl font-medium mb-2 text-gray-900 dark:text-gray-100">$1</h3>')
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
      .replace(/\n\n/g, '</p><p class="mb-4 text-gray-700 dark:text-gray-300">')
      .replace(/^(.+)$/gm, '<p class="mb-4 text-gray-700 dark:text-gray-300">$1</p>')
  }

  const highlightKeyword = (text: string) => {
    const regex = new RegExp(`(${content.keyword})`, 'gi')
    return text.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">$1</mark>')
  }

  const handleDownload = () => {
    const blob = new Blob([content.content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${content.title.replace(/[^a-z0-9]/gi, '-').toLowerCase()}.txt`
    a.click()
    URL.revokeObjectURL(url)
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: content.title,
          text: content.content.substring(0, 200) + '...',
          url: window.location.href
        })
      } catch (error) {
        console.log('Share canceled')
      }
    } else {
      // Fallback to copying URL
      await navigator.clipboard.writeText(window.location.href)
      // You would show a notification here
    }
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        {/* Backdrop */}
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={onClose}
        />

        {/* Modal */}
        <div className="relative w-full max-w-7xl bg-white dark:bg-gray-900 rounded-xl shadow-xl">
          {/* Header */}
          <div className="sticky top-0 z-10 flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 rounded-t-xl">
            <div className="flex-1 min-w-0">
              <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100 truncate">
                {content.title}
              </h1>
              <div className="flex items-center space-x-4 mt-2">
                <Badge variant={getStatusColor(content.status)} size="sm">
                  {content.status.charAt(0).toUpperCase() + content.status.slice(1)}
                </Badge>
                <span className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                  <TagIcon className="h-4 w-4 mr-1" />
                  {content.keyword}
                </span>
                <span className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                  <ClockIcon className="h-4 w-4 mr-1" />
                  {formatDate(content.created_at)}
                </span>
                <span className={`text-sm font-medium flex items-center ${getSeoScoreColor(content.seo_score || 0)}`}>
                  <ChartBarIcon className="h-4 w-4 mr-1" />
                  SEO: {content.seo_score || 0}%
                </span>
              </div>
            </div>

            <div className="flex items-center space-x-2 ml-4">
              {/* View Mode Toggles */}
              <div className="flex items-center space-x-1 mr-4">
                <Button
                  variant={viewMode === 'formatted' ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('formatted')}
                >
                  <EyeIcon className="h-4 w-4 mr-1" />
                  Formatted
                </Button>
                <Button
                  variant={viewMode === 'raw' ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('raw')}
                >
                  Raw
                </Button>
                <Button
                  variant={viewMode === 'preview' ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('preview')}
                >
                  Preview
                </Button>
              </div>

              {/* Device View Toggles */}
              <div className="flex items-center space-x-1 mr-4">
                <Button
                  variant={deviceView === 'desktop' ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => setDeviceView('desktop')}
                >
                  <ComputerDesktopIcon className="h-4 w-4" />
                </Button>
                <Button
                  variant={deviceView === 'tablet' ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => setDeviceView('tablet')}
                >
                  <TabletIcon className="h-4 w-4" />
                </Button>
                <Button
                  variant={deviceView === 'mobile' ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => setDeviceView('mobile')}
                >
                  <DevicePhoneMobileIcon className="h-4 w-4" />
                </Button>
              </div>

              {/* Action Buttons */}
              <Button variant="outline" size="sm" onClick={onEdit}>
                <PencilIcon className="h-4 w-4 mr-1" />
                Edit
              </Button>
              <Button variant="outline" size="sm" onClick={handleDownload}>
                <ArrowDownTrayIcon className="h-4 w-4 mr-1" />
                Download
              </Button>
              <Button variant="outline" size="sm" onClick={handleShare}>
                <ShareIcon className="h-4 w-4 mr-1" />
                Share
              </Button>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <XMarkIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            <div className={`transition-all duration-300 ${getDeviceClasses()}`}>
              {/* Meta Information */}
              {content.meta_description && (
                <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="text-sm font-medium text-blue-700 dark:text-blue-300 mb-1">
                    Meta Description ({content.meta_description.length} chars)
                  </div>
                  <div className="text-blue-600 dark:text-blue-400">
                    {content.meta_description}
                  </div>
                </div>
              )}

              {/* Main Content */}
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 min-h-96">
                {viewMode === 'formatted' && (
                  <div 
                    className="prose prose-gray dark:prose-invert max-w-none"
                    dangerouslySetInnerHTML={{ 
                      __html: highlightKeyword(formatContent(content.content))
                    }}
                  />
                )}

                {viewMode === 'raw' && (
                  <div className="font-mono text-sm">
                    <pre className="whitespace-pre-wrap text-gray-800 dark:text-gray-200">
                      {content.content}
                    </pre>
                  </div>
                )}

                {viewMode === 'preview' && (
                  <div className="space-y-4">
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                      {content.title}
                    </h1>
                    <div 
                      className="prose prose-gray dark:prose-invert max-w-none"
                      dangerouslySetInnerHTML={{ 
                        __html: highlightKeyword(formatContent(content.content))
                      }}
                    />
                  </div>
                )}
              </div>

              {/* Content Stats */}
              <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Words
                  </div>
                  <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {content.word_count.toLocaleString()}
                  </div>
                </div>
                
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Content Type
                  </div>
                  <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {content.content_type.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </div>
                </div>
                
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    SEO Score
                  </div>
                  <div className={`text-lg font-semibold ${getSeoScoreColor(content.seo_score || 0)}`}>
                    {content.seo_score || 0}%
                  </div>
                </div>

                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                    Status
                  </div>
                  <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {content.status.charAt(0).toUpperCase() + content.status.slice(1)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}