/**
 * CompetitorPanel Component
 * Enterprise SEO SAAS - Competitor analysis and tracking interface
 */

import { useState } from 'react'
import { Project, CompetitorAnalysis } from '@/types/project'
import { validateCompetitorUrl } from '@/utils/projectHelpers'
import {
  MagnifyingGlassIcon,
  PlusIcon,
  GlobeAltIcon,
  ChartBarIcon,
  EyeIcon,
  ArrowPathIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'

interface CompetitorPanelProps {
  project: Project
  onProjectUpdate: (project: Project) => void
}

export default function CompetitorPanel({ project, onProjectUpdate }: CompetitorPanelProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [showAddCompetitor, setShowAddCompetitor] = useState(false)
  const [newCompetitorUrl, setNewCompetitorUrl] = useState('')
  const [analyzingCompetitor, setAnalyzingCompetitor] = useState<string | null>(null)

  const filteredCompetitors = project.competitors.filter(competitor =>
    competitor.url.toLowerCase().includes(searchTerm.toLowerCase()) ||
    competitor.domain.toLowerCase().includes(searchTerm.toLowerCase()) ||
    competitor.title?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleAddCompetitor = async () => {
    if (!newCompetitorUrl.trim()) return

    // Validate competitor URL using existing validation
    const validation = validateCompetitorUrl(newCompetitorUrl.trim())
    if (!validation.isValid) {
      alert(`Invalid competitor URL: ${validation.errors[0]}`)
      return
    }

    const competitor: CompetitorAnalysis = {
      id: `comp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      url: newCompetitorUrl.trim(),
      domain: validation.domain,
      lastAnalyzed: new Date().toISOString(),
      isValid: true,
      // Mock analysis data (in real app, would come from SERP analysis API)
      title: `Analysis pending for ${validation.domain}`,
      metaDescription: 'Analysis pending...',
      wordCount: 0,
      domainAuthority: Math.floor(Math.random() * 100) + 1,
      backlinks: Math.floor(Math.random() * 100000) + 1000,
      contentScore: Math.floor(Math.random() * 100) + 1,
      analysisData: {
        headingStructure: { h1: 0, h2: 0, h3: 0, h4: 0, h5: 0, h6: 0 },
        keywordDensity: {},
        internalLinks: 0,
        externalLinks: 0,
        technicalScore: 0,
        contentDepth: 0
      }
    }

    const updatedProject = {
      ...project,
      competitors: [...project.competitors, competitor],
      updatedAt: new Date().toISOString()
    }

    onProjectUpdate(updatedProject)
    setNewCompetitorUrl('')
    setShowAddCompetitor(false)

    // Trigger analysis
    handleAnalyzeCompetitor(competitor.id)
  }

  const handleAnalyzeCompetitor = async (competitorId: string) => {
    setAnalyzingCompetitor(competitorId)

    try {
      // Simulate analysis process (in real app, would call SERP analysis API)
      await new Promise(resolve => setTimeout(resolve, 3000))

      // Update competitor with analysis results
      const updatedProject = {
        ...project,
        competitors: project.competitors.map(comp => 
          comp.id === competitorId 
            ? {
                ...comp,
                title: `High-Quality Content for ${comp.domain}`,
                metaDescription: 'Comprehensive SEO-optimized content with strong user engagement signals.',
                wordCount: Math.floor(Math.random() * 3000) + 1000,
                lastAnalyzed: new Date().toISOString(),
                analysisData: {
                  headingStructure: {
                    h1: Math.floor(Math.random() * 3) + 1,
                    h2: Math.floor(Math.random() * 8) + 3,
                    h3: Math.floor(Math.random() * 12) + 5,
                    h4: Math.floor(Math.random() * 6) + 2,
                    h5: Math.floor(Math.random() * 3) + 1,
                    h6: Math.floor(Math.random() * 2) + 1
                  },
                  keywordDensity: {
                    'primary keyword': Math.random() * 3 + 1,
                    'secondary keyword': Math.random() * 2 + 0.5,
                    'long tail keyword': Math.random() * 1.5 + 0.3
                  },
                  internalLinks: Math.floor(Math.random() * 20) + 5,
                  externalLinks: Math.floor(Math.random() * 10) + 2,
                  technicalScore: Math.floor(Math.random() * 30) + 70,
                  contentDepth: Math.floor(Math.random() * 30) + 70
                }
              }
            : comp
        ),
        updatedAt: new Date().toISOString()
      }

      onProjectUpdate(updatedProject)
    } catch (error) {
      console.error('Error analyzing competitor:', error)
    } finally {
      setAnalyzingCompetitor(null)
    }
  }

  const handleDeleteCompetitor = (competitorId: string) => {
    if (confirm('Are you sure you want to remove this competitor?')) {
      const updatedProject = {
        ...project,
        competitors: project.competitors.filter(c => c.id !== competitorId),
        updatedAt: new Date().toISOString()
      }
      onProjectUpdate(updatedProject)
    }
  }

  const getAnalysisStatus = (competitor: CompetitorAnalysis) => {
    const isOld = new Date(competitor.lastAnalyzed) < new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days old
    const hasData = competitor.wordCount && competitor.wordCount > 0

    if (analyzingCompetitor === competitor.id) {
      return { status: 'analyzing', color: 'text-blue-600', icon: ArrowPathIcon, label: 'Analyzing...' }
    }
    if (!hasData) {
      return { status: 'pending', color: 'text-yellow-600', icon: ClockIcon, label: 'Analysis Pending' }
    }
    if (isOld) {
      return { status: 'outdated', color: 'text-orange-600', icon: ExclamationTriangleIcon, label: 'Needs Update' }
    }
    return { status: 'current', color: 'text-green-600', icon: CheckCircleIcon, label: 'Up to Date' }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  return (
    <div className="space-y-6">
      {/* Header with Search and Add Button */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="relative flex-1 max-w-md">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search competitors..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-white text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <button
          onClick={() => setShowAddCompetitor(true)}
          className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Competitor
        </button>
      </div>

      {/* Add Competitor Form */}
      {showAddCompetitor && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Add New Competitor</h3>
          <div className="flex gap-3">
            <input
              type="url"
              placeholder="https://competitor-website.com (no demo sites)"
              value={newCompetitorUrl}
              onChange={(e) => setNewCompetitorUrl(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              onKeyPress={(e) => e.key === 'Enter' && handleAddCompetitor()}
            />
            <button
              onClick={handleAddCompetitor}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg"
            >
              Add & Analyze
            </button>
            <button
              onClick={() => setShowAddCompetitor(false)}
              className="px-3 py-2 text-gray-600 hover:text-gray-800"
            >
              <XMarkIcon className="h-4 w-4" />
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Enter real competitor websites. Demo sites like example.com are not allowed.
          </p>
        </div>
      )}

      {/* Competitors Grid */}
      {filteredCompetitors.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredCompetitors.map((competitor) => {
            const status = getAnalysisStatus(competitor)
            const StatusIcon = status.icon

            return (
              <div key={competitor.id} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className="p-2 bg-blue-50 rounded-lg flex-shrink-0">
                      <GlobeAltIcon className="h-5 w-5 text-blue-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-gray-900 truncate">{competitor.domain}</h3>
                      <div className={`flex items-center gap-1 mt-1 ${status.color}`}>
                        <StatusIcon className={`h-4 w-4 ${analyzingCompetitor === competitor.id ? 'animate-spin' : ''}`} />
                        <span className="text-sm">{status.label}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-1 flex-shrink-0">
                    {status.status !== 'analyzing' && (
                      <button
                        onClick={() => handleAnalyzeCompetitor(competitor.id)}
                        className="p-2 text-gray-400 hover:text-blue-600 rounded-lg hover:bg-gray-50"
                        title="Refresh Analysis"
                      >
                        <ArrowPathIcon className="h-4 w-4" />
                      </button>
                    )}
                    <button
                      onClick={() => handleDeleteCompetitor(competitor.id)}
                      className="p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-gray-50"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                {/* Content Info */}
                {competitor.title && competitor.wordCount && competitor.wordCount > 0 ? (
                  <>
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-900 mb-1 line-clamp-2">
                        {competitor.title}
                      </h4>
                      {competitor.metaDescription && (
                        <p className="text-sm text-gray-600 line-clamp-2">
                          {competitor.metaDescription}
                        </p>
                      )}
                    </div>

                    {/* Metrics Grid */}
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="text-center p-3 bg-gray-50 rounded-lg">
                        <div className="text-lg font-semibold text-gray-900">{competitor.wordCount?.toLocaleString()}</div>
                        <div className="text-xs text-gray-500">Words</div>
                      </div>
                      <div className="text-center p-3 bg-gray-50 rounded-lg">
                        <div className="text-lg font-semibold text-gray-900">{competitor.domainAuthority}</div>
                        <div className="text-xs text-gray-500">DA Score</div>
                      </div>
                      <div className="text-center p-3 bg-gray-50 rounded-lg">
                        <div className="text-lg font-semibold text-gray-900">{competitor.backlinks?.toLocaleString()}</div>
                        <div className="text-xs text-gray-500">Backlinks</div>
                      </div>
                      <div className="text-center p-3 bg-gray-50 rounded-lg">
                        <div className="text-lg font-semibold text-gray-900">{competitor.contentScore}/100</div>
                        <div className="text-xs text-gray-500">Content Score</div>
                      </div>
                    </div>

                    {/* Analysis Details */}
                    {competitor.analysisData && (
                      <div className="space-y-3">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-500">Heading Structure</span>
                          <span className="font-medium text-gray-900">
                            H1: {competitor.analysisData.headingStructure.h1}, 
                            H2: {competitor.analysisData.headingStructure.h2}, 
                            H3: {competitor.analysisData.headingStructure.h3}
                          </span>
                        </div>
                        
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-500">Links</span>
                          <span className="font-medium text-gray-900">
                            {competitor.analysisData.internalLinks} internal, {competitor.analysisData.externalLinks} external
                          </span>
                        </div>

                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-500">Technical Score</span>
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-gray-900">{competitor.analysisData.technicalScore}/100</span>
                            <div className="w-16 bg-gray-200 rounded-full h-2">
                              <div 
                                className={`h-2 rounded-full ${
                                  competitor.analysisData.technicalScore >= 80 ? 'bg-green-500' :
                                  competitor.analysisData.technicalScore >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                                }`}
                                style={{ width: `${competitor.analysisData.technicalScore}%` }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="text-center py-8">
                    <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-sm text-gray-500">
                      {analyzingCompetitor === competitor.id 
                        ? 'Analyzing competitor content...'
                        : 'Click refresh to analyze this competitor'
                      }
                    </p>
                  </div>
                )}

                {/* Footer */}
                <div className="mt-4 pt-4 border-t border-gray-100 flex items-center justify-between text-xs text-gray-500">
                  <a 
                    href={competitor.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="hover:text-blue-600 truncate flex-1"
                  >
                    {competitor.url}
                  </a>
                  <span className="ml-2 flex-shrink-0">
                    Analyzed {formatDate(competitor.lastAnalyzed)}
                  </span>
                </div>
              </div>
            )
          })}
        </div>
      ) : (
        // Empty state
        <div className="text-center py-12">
          <GlobeAltIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No competitors found</h3>
          <p className="text-gray-600 mb-6">
            {searchTerm
              ? 'Try adjusting your search term'
              : 'Add competitor websites to analyze their content and SEO strategies'
            }
          </p>
          <button
            onClick={() => setShowAddCompetitor(true)}
            className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Your First Competitor
          </button>
        </div>
      )}

      {/* Analysis Summary */}
      {filteredCompetitors.length > 0 && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
            <EyeIcon className="h-5 w-5 text-blue-600" />
            Competitive Insights
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Average Metrics</h4>
              <div className="space-y-1 text-sm text-gray-600">
                <div>Word Count: {Math.round(filteredCompetitors.reduce((sum, c) => sum + (c.wordCount || 0), 0) / filteredCompetitors.length).toLocaleString()}</div>
                <div>Domain Authority: {Math.round(filteredCompetitors.reduce((sum, c) => sum + (c.domainAuthority || 0), 0) / filteredCompetitors.length)}</div>
                <div>Content Score: {Math.round(filteredCompetitors.reduce((sum, c) => sum + (c.contentScore || 0), 0) / filteredCompetitors.length)}/100</div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Content Opportunities</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• {Math.floor(Math.random() * 5) + 3} content gaps identified</li>
                <li>• {Math.floor(Math.random() * 8) + 5} keyword opportunities</li>
                <li>• {Math.floor(Math.random() * 3) + 2} link building prospects</li>
              </ul>
            </div>
            
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Next Actions</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Analyze top-performing content</li>
                <li>• Identify keyword targeting patterns</li>
                <li>• Study content structure strategies</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}