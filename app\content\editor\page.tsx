'use client';

import React, { useState } from 'react';
import { ProtectedRoute } from '@/hooks/useAuth';
import { AuthenticatedLayout, PageHeader } from '@/components/Layout/DashboardLayout';
import IntelligentContentEditor from '@/components/ContentEditor/IntelligentContentEditor';
import { DocumentTextIcon, SparklesIcon, CheckCircleIcon } from '@heroicons/react/24/outline';

interface ContentMetadata {
  wordCount: number;
  characterCount: number;
  keywordDensity: number;
  readabilityScore: number;
  seoScore: number;
  headingStructure: any[];
  keywords: any[];
  suggestions: any[];
}

export default function ContentEditorPage() {
  const [savedContent, setSavedContent] = useState<string>('');
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');

  const handleSave = async (content: string, metadata: ContentMetadata) => {
    setSaveStatus('saving');
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, this would save to your API
      localStorage.setItem('editorContent', content);
      localStorage.setItem('editorMetadata', JSON.stringify(metadata));
      
      setSavedContent(content);
      setSaveStatus('saved');
      
      // Reset status after 2 seconds
      setTimeout(() => setSaveStatus('idle'), 2000);
    } catch (error) {
      console.error('Save failed:', error);
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 2000);
    }
  };

  const handleExport = (content: string, format: 'markdown' | 'html' | 'docx') => {
    let mimeType: string;
    let fileExtension: string;
    let processedContent: string;

    switch (format) {
      case 'markdown':
        mimeType = 'text/markdown';
        fileExtension = 'md';
        processedContent = content;
        break;
      case 'html':
        mimeType = 'text/html';
        fileExtension = 'html';
        processedContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exported Content</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1, h2, h3 { color: #333; }
        h1 { border-bottom: 2px solid #eee; padding-bottom: 10px; }
        h2 { border-bottom: 1px solid #eee; padding-bottom: 5px; }
        code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }
        blockquote { border-left: 4px solid #ddd; margin: 0; padding-left: 20px; color: #666; }
    </style>
</head>
<body>
${content
  .replace(/^# (.*$)/gm, '<h1>$1</h1>')
  .replace(/^## (.*$)/gm, '<h2>$1</h2>')
  .replace(/^### (.*$)/gm, '<h3>$1</h3>')
  .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
  .replace(/\*(.*?)\*/g, '<em>$1</em>')
  .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
  .replace(/\n/g, '<br>')
}
</body>
</html>`;
        break;
      case 'docx':
        mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        fileExtension = 'docx';
        processedContent = content; // In a real app, you'd use a library like docx or mammoth
        break;
    }

    const blob = new Blob([processedContent], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `content-${Date.now()}.${fileExtension}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const sampleContent = `# SEO Content Creation: A Complete Guide

## Introduction

Creating high-quality SEO content is essential for any successful digital marketing strategy. This comprehensive guide will walk you through the process of creating content that both search engines and users will love.

## Understanding SEO Content

SEO content is content created with the primary goal of attracting search engine traffic. It involves:

- **Keyword research and optimization**
- **User intent understanding**
- **Quality content creation**
- **Technical optimization**

### Key Components of SEO Content

1. **Primary Keywords**: The main terms you want to rank for
2. **Secondary Keywords**: Related terms that support your primary keywords
3. **Content Structure**: Headers, subheaders, and logical flow
4. **Meta Elements**: Title tags, meta descriptions, and schema markup

## Best Practices for SEO Content

### Keyword Optimization
- Use primary keywords naturally throughout your content
- Include keywords in headings and subheadings
- Maintain a keyword density of 1-3%
- Use semantic keywords and variations

### Content Structure
- Start with a compelling introduction
- Use clear headings and subheadings
- Include bullet points and numbered lists
- End with a strong conclusion

### Technical Considerations
- Optimize page loading speed
- Ensure mobile responsiveness
- Use proper internal linking
- Implement schema markup

## Advanced SEO Techniques

### Content Depth and Expertise
Modern SEO values content that demonstrates expertise, authority, and trustworthiness (E-A-T). This means:

- **In-depth coverage** of topics
- **Expert insights** and analysis
- **Authoritative sources** and citations
- **Trustworthy information** and accuracy

### User Experience Optimization
- Write for your audience first, search engines second
- Use clear, conversational language
- Include relevant examples and case studies
- Provide actionable insights and takeaways

## Conclusion

Creating effective SEO content requires a balance of technical knowledge, creativity, and strategic thinking. By following these guidelines and continuously optimizing your approach, you can create content that performs well in search results while providing genuine value to your audience.

Remember to regularly analyze your content performance, update your strategies based on results, and stay informed about the latest SEO best practices and algorithm updates.`;

  return (
    <ProtectedRoute>
      <AuthenticatedLayout>
        <PageHeader
          title="Content Editor"
          description="Create and optimize SEO-friendly content with real-time analysis"
          actions={
            <div className="flex items-center space-x-2">
              {saveStatus === 'saving' && (
                <div className="flex items-center text-sm text-gray-500">
                  <SparklesIcon className="h-4 w-4 mr-1 animate-pulse" />
                  Saving...
                </div>
              )}
              {saveStatus === 'saved' && (
                <div className="flex items-center text-sm text-green-600">
                  <CheckCircleIcon className="h-4 w-4 mr-1" />
                  Saved
                </div>
              )}
              {saveStatus === 'error' && (
                <div className="flex items-center text-sm text-red-600">
                  <DocumentTextIcon className="h-4 w-4 mr-1" />
                  Save failed
                </div>
              )}
            </div>
          }
        />
        
        <div className="max-w-6xl mx-auto">
          <IntelligentContentEditor
            initialContent={sampleContent}
            onSave={handleSave}
            onExport={handleExport}
            primaryKeyword="SEO content"
            targetKeywords={['content optimization', 'search engine optimization', 'keyword research']}
            contentType="blog_post"
          />
        </div>
      </AuthenticatedLayout>
    </ProtectedRoute>
  );
}