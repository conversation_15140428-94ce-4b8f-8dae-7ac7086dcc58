/**
 * Content Editor Page
 * Rich text editor with real-time SEO analysis and competitor comparison
 */

'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSearchParams } from 'next/navigation'
import { AuthenticatedLayout, PageHeader } from '@/components/Layout/DashboardLayout'
import { useNotifications } from '@/components/Notifications'
import { contentService } from '@/lib/api'
import { ContentItem } from '@/lib/api/types'
import RichTextEditor from '@/components/ContentEditor/RichTextEditor'
import SEOAnalysisSidebar from '@/components/ContentEditor/SEOAnalysisSidebar'
import CompetitorComparisonPanel from '@/components/ContentEditor/CompetitorComparisonPanel'
import ContentStructureAnalyzer from '@/components/ContentEditor/ContentStructureAnalyzer'
import EditorToolbar from '@/components/ContentEditor/EditorToolbar'
import Button from '@/components/UI/Button'
import LoadingSpinner from '@/components/UI/LoadingSpinner'
import { 
  DocumentTextIcon,
  EyeIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  UserGroupIcon,
  BookmarkIcon,
  ShareIcon
} from '@heroicons/react/24/outline'

interface EditorContent {
  title: string
  content: string
  metaTitle: string
  metaDescription: string
  keyword: string
  contentType: string
  status: 'draft' | 'published' | 'scheduled'
}

interface SEOAnalysis {
  overallScore: number
  titleScore: number
  metaScore: number
  keywordDensity: number
  readabilityScore: number
  wordCount: number
  recommendations: string[]
  keywordUsage: {
    inTitle: boolean
    inMeta: boolean
    inFirstParagraph: boolean
    density: number
    frequency: number
  }
}

export default function ContentEditorPage() {
  const searchParams = useSearchParams()
  const contentId = searchParams.get('id')
  
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [showSEOSidebar, setShowSEOSidebar] = useState(true)
  const [showCompetitorPanel, setShowCompetitorPanel] = useState(false)
  const [showStructureAnalyzer, setShowStructureAnalyzer] = useState(false)
  
  const [editorContent, setEditorContent] = useState<EditorContent>({
    title: '',
    content: '',
    metaTitle: '',
    metaDescription: '',
    keyword: '',
    contentType: 'blog-post',
    status: 'draft'
  })
  
  const [originalContent, setOriginalContent] = useState<ContentItem | null>(null)
  const [seoAnalysis, setSeoAnalysis] = useState<SEOAnalysis | null>(null)
  const [competitorData, setCompetitorData] = useState<any>(null)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  const { error: notifyError, success: notifySuccess, info: notifyInfo } = useNotifications()

  // Initialize with sample content if no ID provided
  useEffect(() => {
    if (contentId) {
      loadContent(contentId)
    } else {
      // Initialize with sample content for demo
      setEditorContent({
        title: 'SEO Content Creation: A Complete Guide',
        content: `# SEO Content Creation: A Complete Guide

## Introduction

Creating high-quality SEO content is essential for any successful digital marketing strategy. This comprehensive guide will walk you through the process of creating content that both search engines and users will love.

## Understanding SEO Content

SEO content is content created with the primary goal of attracting search engine traffic. It involves:

- **Keyword research and optimization**
- **User intent understanding**
- **Quality content creation**
- **Technical optimization**

### Key Components of SEO Content

1. **Primary Keywords**: The main terms you want to rank for
2. **Secondary Keywords**: Related terms that support your primary keywords
3. **Content Structure**: Headers, subheaders, and logical flow
4. **Meta Elements**: Title tags, meta descriptions, and schema markup

## Best Practices for SEO Content

### Keyword Optimization
- Use primary keywords naturally throughout your content
- Include keywords in headings and subheadings
- Maintain a keyword density of 1-3%
- Use semantic keywords and variations

### Content Structure
- Start with a compelling introduction
- Use clear headings and subheadings
- Include bullet points and numbered lists
- End with a strong conclusion

## Conclusion

Creating effective SEO content requires a balance of technical knowledge, creativity, and strategic thinking. By following these guidelines, you can create content that performs well in search results.`,
        metaTitle: 'SEO Content Creation: Complete Guide for 2024',
        metaDescription: 'Learn how to create high-quality SEO content that ranks well in search engines. Complete guide with best practices, keyword optimization tips, and content structure.',
        keyword: 'SEO content',
        contentType: 'blog-post',
        status: 'draft'
      })
      setIsLoading(false)
    }
  }, [contentId])

  // Track unsaved changes
  useEffect(() => {
    if (originalContent) {
      const hasChanges = 
        editorContent.title !== originalContent.title ||
        editorContent.content !== originalContent.content ||
        editorContent.metaDescription !== (originalContent.meta_description || '') ||
        editorContent.keyword !== originalContent.keyword
      
      setHasUnsavedChanges(hasChanges)
    } else if (editorContent.title || editorContent.content) {
      setHasUnsavedChanges(true)
    }
  }, [editorContent, originalContent])

  // Auto-save functionality
  useEffect(() => {
    if (!hasUnsavedChanges || !contentId) return

    const autoSaveTimer = setTimeout(() => {
      saveContent(true) // Auto-save
    }, 30000) // Auto-save every 30 seconds

    return () => clearTimeout(autoSaveTimer)
  }, [editorContent, hasUnsavedChanges])

  // Real-time SEO analysis
  useEffect(() => {
    const analysisTimer = setTimeout(() => {
      if (editorContent.content || editorContent.title) {
        performSEOAnalysis()
      }
    }, 1000) // Debounce analysis

    return () => clearTimeout(analysisTimer)
  }, [editorContent])

  const loadContent = async (id: string) => {
    setIsLoading(true)
    try {
      const response = await contentService.getContentItem(id)
      if (response.success && response.data) {
        const content = response.data
        setOriginalContent(content)
        setEditorContent({
          title: content.title,
          content: content.content,
          metaTitle: content.meta_title || content.title,
          metaDescription: content.meta_description || '',
          keyword: content.keyword,
          contentType: content.content_type,
          status: content.status
        })
        
        // Load competitor data if available
        if (content.keyword) {
          loadCompetitorData(content.keyword)
        }
      }
    } catch (error) {
      notifyError('Failed to load content')
    } finally {
      setIsLoading(false)
    }
  }

  const loadCompetitorData = async (keyword: string) => {
    try {
      // Simulate competitor data for demo
      setCompetitorData({
        keyword,
        topCompetitors: [
          {
            url: 'example.com/seo-guide',
            title: 'Ultimate SEO Guide 2024',
            wordCount: 2500,
            seoScore: 92,
            keywordDensity: 2.1,
            headings: ['H1: 1', 'H2: 8', 'H3: 15'],
            insights: ['Uses target keyword in title', 'Strong internal linking', 'Good content structure']
          },
          {
            url: 'competitor.com/content-marketing',
            title: 'Content Marketing SEO Strategy',
            wordCount: 1800,
            seoScore: 87,
            keywordDensity: 1.8,
            headings: ['H1: 1', 'H2: 6', 'H3: 12'],
            insights: ['Excellent keyword distribution', 'High-quality backlinks', 'Mobile optimized']
          }
        ]
      })
    } catch (error) {
      console.error('Failed to load competitor data:', error)
    }
  }

  const performSEOAnalysis = useCallback(async () => {
    if (!editorContent.keyword) return

    setIsAnalyzing(true)
    try {
      // Calculate SEO metrics
      const wordCount = editorContent.content.split(/\s+/).filter(word => word.length > 0).length
      const keywordCount = (editorContent.content.toLowerCase().match(new RegExp(editorContent.keyword.toLowerCase(), 'g')) || []).length
      const keywordDensity = wordCount > 0 ? (keywordCount / wordCount) * 100 : 0
      
      const analysis: SEOAnalysis = {
        overallScore: calculateOverallScore(),
        titleScore: analyzeTitleOptimization(),
        metaScore: analyzeMetaOptimization(),
        keywordDensity,
        readabilityScore: calculateReadabilityScore(),
        wordCount,
        recommendations: generateRecommendations(),
        keywordUsage: {
          inTitle: editorContent.title.toLowerCase().includes(editorContent.keyword.toLowerCase()),
          inMeta: editorContent.metaDescription.toLowerCase().includes(editorContent.keyword.toLowerCase()),
          inFirstParagraph: getFirstParagraph().toLowerCase().includes(editorContent.keyword.toLowerCase()),
          density: keywordDensity,
          frequency: keywordCount
        }
      }

      setSeoAnalysis(analysis)
    } catch (error) {
      console.error('SEO analysis failed:', error)
    } finally {
      setIsAnalyzing(false)
    }
  }, [editorContent])

  const calculateOverallScore = () => {
    let score = 0
    let factors = 0

    // Title optimization (20%)
    if (editorContent.title) {
      score += analyzeTitleOptimization() * 0.2
      factors++
    }

    // Meta description (15%)
    if (editorContent.metaDescription) {
      score += analyzeMetaOptimization() * 0.15
      factors++
    }

    // Content length (20%)
    const wordCount = editorContent.content.split(/\s+/).length
    const lengthScore = Math.min(100, (wordCount / 1000) * 100)
    score += lengthScore * 0.2
    factors++

    // Keyword optimization (25%)
    if (editorContent.keyword) {
      score += analyzeKeywordOptimization() * 0.25
      factors++
    }

    // Readability (20%)
    score += calculateReadabilityScore() * 0.2
    factors++

    return Math.round(score / factors)
  }

  const analyzeTitleOptimization = () => {
    if (!editorContent.title) return 0
    
    let score = 50 // Base score
    
    // Length check (50-60 characters is optimal)
    const titleLength = editorContent.title.length
    if (titleLength >= 50 && titleLength <= 60) {
      score += 30
    } else if (titleLength >= 40 && titleLength <= 70) {
      score += 20
    }
    
    // Keyword in title
    if (editorContent.keyword && editorContent.title.toLowerCase().includes(editorContent.keyword.toLowerCase())) {
      score += 20
    }
    
    return Math.min(100, score)
  }

  const analyzeMetaOptimization = () => {
    if (!editorContent.metaDescription) return 0
    
    let score = 40 // Base score
    
    // Length check (150-160 characters is optimal)
    const metaLength = editorContent.metaDescription.length
    if (metaLength >= 150 && metaLength <= 160) {
      score += 40
    } else if (metaLength >= 120 && metaLength <= 180) {
      score += 30
    }
    
    // Keyword in meta description
    if (editorContent.keyword && editorContent.metaDescription.toLowerCase().includes(editorContent.keyword.toLowerCase())) {
      score += 20
    }
    
    return Math.min(100, score)
  }

  const analyzeKeywordOptimization = () => {
    if (!editorContent.keyword || !editorContent.content) return 0
    
    const keyword = editorContent.keyword.toLowerCase()
    const content = editorContent.content.toLowerCase()
    const wordCount = editorContent.content.split(/\s+/).length
    const keywordCount = (content.match(new RegExp(keyword, 'g')) || []).length
    const density = (keywordCount / wordCount) * 100
    
    let score = 0
    
    // Keyword density (1-3% is optimal)
    if (density >= 1 && density <= 3) {
      score += 40
    } else if (density >= 0.5 && density <= 5) {
      score += 25
    }
    
    // Keyword in first paragraph
    const firstParagraph = getFirstParagraph().toLowerCase()
    if (firstParagraph.includes(keyword)) {
      score += 30
    }
    
    // Keyword distribution
    const paragraphs = editorContent.content.split('\n\n')
    const paragraphsWithKeyword = paragraphs.filter(p => p.toLowerCase().includes(keyword)).length
    const distribution = paragraphsWithKeyword / paragraphs.length
    if (distribution >= 0.3) {
      score += 30
    }
    
    return Math.min(100, score)
  }

  const calculateReadabilityScore = () => {
    if (!editorContent.content) return 0
    
    const sentences = editorContent.content.split(/[.!?]+/).filter(s => s.trim().length > 0)
    const words = editorContent.content.split(/\s+/).filter(w => w.length > 0)
    const avgWordsPerSentence = words.length / sentences.length
    
    // Simplified readability calculation
    let score = 100
    
    // Penalize very long sentences
    if (avgWordsPerSentence > 20) {
      score -= 20
    } else if (avgWordsPerSentence > 15) {
      score -= 10
    }
    
    // Check for complex words (more than 2 syllables)
    const complexWords = words.filter(word => estimateSyllables(word) > 2)
    const complexWordRatio = complexWords.length / words.length
    if (complexWordRatio > 0.15) {
      score -= 20
    }
    
    return Math.max(0, Math.min(100, score))
  }

  const estimateSyllables = (word: string) => {
    return word.toLowerCase().replace(/[^aeiouy]/g, '').length || 1
  }

  const getFirstParagraph = () => {
    const paragraphs = editorContent.content.split('\n\n')
    return paragraphs[0] || ''
  }

  const generateRecommendations = () => {
    const recommendations: string[] = []
    
    if (!editorContent.title) {
      recommendations.push('Add a compelling title to your content')
    } else if (editorContent.title.length < 40) {
      recommendations.push('Consider making your title longer (40-60 characters)')
    } else if (editorContent.title.length > 70) {
      recommendations.push('Consider shortening your title (40-60 characters is optimal)')
    }
    
    if (!editorContent.metaDescription) {
      recommendations.push('Add a meta description to improve search appearance')
    } else if (editorContent.metaDescription.length < 120) {
      recommendations.push('Consider expanding your meta description (150-160 characters is optimal)')
    }
    
    if (editorContent.keyword) {
      const keywordInTitle = editorContent.title.toLowerCase().includes(editorContent.keyword.toLowerCase())
      if (!keywordInTitle) {
        recommendations.push(`Include your target keyword "${editorContent.keyword}" in the title`)
      }
      
      const keywordInMeta = editorContent.metaDescription.toLowerCase().includes(editorContent.keyword.toLowerCase())
      if (!keywordInMeta) {
        recommendations.push(`Include your target keyword "${editorContent.keyword}" in the meta description`)
      }
      
      const wordCount = editorContent.content.split(/\s+/).length
      const keywordCount = (editorContent.content.toLowerCase().match(new RegExp(editorContent.keyword.toLowerCase(), 'g')) || []).length
      const density = (keywordCount / wordCount) * 100
      
      if (density < 0.5) {
        recommendations.push('Increase keyword density (aim for 1-3%)')
      } else if (density > 5) {
        recommendations.push('Reduce keyword density to avoid over-optimization')
      }
    }
    
    const wordCount = editorContent.content.split(/\s+/).length
    if (wordCount < 300) {
      recommendations.push('Consider adding more content (aim for at least 300 words)')
    }
    
    return recommendations
  }

  const saveContent = async (isAutoSave = false) => {
    if (!isAutoSave) setIsSaving(true)
    
    try {
      const contentData = {
        title: editorContent.title,
        content: editorContent.content,
        meta_title: editorContent.metaTitle,
        meta_description: editorContent.metaDescription,
        keyword: editorContent.keyword,
        content_type: editorContent.contentType,
        status: editorContent.status,
        word_count: editorContent.content.split(/\s+/).filter(w => w.length > 0).length,
        seo_score: seoAnalysis?.overallScore || 0
      }

      let response
      if (contentId) {
        response = await contentService.updateContent(contentId, contentData)
      } else {
        response = await contentService.saveContent(contentData)
      }

      if (response.success) {
        if (!isAutoSave) {
          notifySuccess('Content saved successfully')
        }
        setHasUnsavedChanges(false)
        
        if (!contentId && response.data) {
          // Redirect to editor with new ID
          window.history.replaceState(null, '', `/content/editor?id=${response.data.id}`)
        }
      } else {
        throw new Error(response.error || 'Failed to save content')
      }
    } catch (error) {
      if (!isAutoSave) {
        notifyError('Failed to save content')
      }
    } finally {
      if (!isAutoSave) setIsSaving(false)
    }
  }

  const publishContent = async () => {
    await saveContent()
    // Additional publish logic would go here
    notifySuccess('Content published successfully')
  }

  if (isLoading) {
    return (
      <AuthenticatedLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <LoadingSpinner size="lg" />
            <p className="mt-4 text-gray-600 dark:text-gray-400">Loading content editor...</p>
          </div>
        </div>
      </AuthenticatedLayout>
    )
  }

  return (
    <AuthenticatedLayout>
      <PageHeader 
        title={contentId ? 'Edit Content' : 'Create Content'}
        description="Rich text editor with real-time SEO analysis"
        actions={
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowPreview(!showPreview)}
              className="inline-flex items-center"
            >
              <EyeIcon className="h-4 w-4 mr-2" />
              {showPreview ? 'Edit' : 'Preview'}
            </Button>
            <Button
              variant="outline"
              onClick={() => saveContent()}
              disabled={isSaving}
              className="inline-flex items-center"
            >
              <BookmarkIcon className="h-4 w-4 mr-2" />
              {isSaving ? 'Saving...' : 'Save Draft'}
            </Button>
            <Button
              onClick={publishContent}
              className="inline-flex items-center"
            >
              <ShareIcon className="h-4 w-4 mr-2" />
              Publish
            </Button>
          </div>
        }
      />

      {/* Editor Toolbar */}
      <EditorToolbar
        showSEOSidebar={showSEOSidebar}
        showCompetitorPanel={showCompetitorPanel}
        showStructureAnalyzer={showStructureAnalyzer}
        onToggleSEOSidebar={() => setShowSEOSidebar(!showSEOSidebar)}
        onToggleCompetitorPanel={() => setShowCompetitorPanel(!showCompetitorPanel)}
        onToggleStructureAnalyzer={() => setShowStructureAnalyzer(!showStructureAnalyzer)}
        hasUnsavedChanges={hasUnsavedChanges}
        seoScore={seoAnalysis?.overallScore || 0}
      />

      <div className="flex gap-6">
        {/* Main Editor */}
        <div className={`flex-1 transition-all duration-300 ${
          showSEOSidebar ? 'max-w-none' : 'max-w-full'
        }`}>
          <RichTextEditor
            content={editorContent}
            onChange={setEditorContent}
            showPreview={showPreview}
            isAnalyzing={isAnalyzing}
            seoAnalysis={seoAnalysis}
          />
        </div>

        {/* SEO Analysis Sidebar */}
        {showSEOSidebar && (
          <div className="w-80 flex-shrink-0">
            <SEOAnalysisSidebar
              analysis={seoAnalysis}
              isAnalyzing={isAnalyzing}
              keyword={editorContent.keyword}
              onKeywordChange={(keyword) => setEditorContent({...editorContent, keyword})}
            />
          </div>
        )}
      </div>

      {/* Competitor Comparison Panel */}
      {showCompetitorPanel && competitorData && (
        <CompetitorComparisonPanel
          competitorData={competitorData}
          currentContent={editorContent}
          onClose={() => setShowCompetitorPanel(false)}
        />
      )}

      {/* Content Structure Analyzer */}
      {showStructureAnalyzer && (
        <ContentStructureAnalyzer
          content={editorContent.content}
          onClose={() => setShowStructureAnalyzer(false)}
        />
      )}
    </AuthenticatedLayout>
  )
}