/**
 * API Test Setup
 * 
 * Setup file for API tests including database initialization,
 * authentication helpers, and test utilities.
 */

const { createClient } = require('@supabase/supabase-js');
const jwt = require('jsonwebtoken');

// Environment variables for testing
process.env.NODE_ENV = 'test';
process.env.NEXT_PUBLIC_SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL_TEST || process.env.NEXT_PUBLIC_SUPABASE_URL;
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_TEST || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
process.env.SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY_TEST || process.env.SUPABASE_SERVICE_KEY;
process.env.JWT_SECRET = process.env.JWT_SECRET_TEST || process.env.JWT_SECRET || 'test-secret';
process.env.OPENAI_API_KEY = process.env.OPENAI_API_KEY_TEST || process.env.OPENAI_API_KEY;

// Initialize Supabase client for testing
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Test users
const testUsers = {
  user1: {
    id: 'test-user-1',
    email: '<EMAIL>',
    password: 'testpassword123',
    role: 'user'
  },
  admin: {
    id: 'test-admin-1',
    email: '<EMAIL>',
    password: 'adminpassword123',
    role: 'admin'
  }
};

// Test data
const testData = {
  projects: [
    {
      id: 'test-project-1',
      name: 'Test Project 1',
      description: 'A test project',
      niche: 'Technology',
      user_id: testUsers.user1.id
    }
  ],
  content: [
    {
      id: 'test-content-1',
      title: 'Test Article',
      content: 'This is test content',
      project_id: 'test-project-1',
      user_id: testUsers.user1.id
    }
  ]
};

// Authentication helpers
const createAuthToken = (user) => {
  return jwt.sign(
    {
      sub: user.id,
      email: user.email,
      role: user.role,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour
    },
    process.env.JWT_SECRET
  );
};

const getAuthHeaders = (user) => {
  const token = createAuthToken(user);
  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };
};

// Database helpers
const cleanupDatabase = async () => {
  try {
    // Clean up test data in reverse order of dependencies
    await supabase.from('analytics_events').delete().like('user_id', 'test-%');
    await supabase.from('reports').delete().like('user_id', 'test-%');
    await supabase.from('content').delete().like('user_id', 'test-%');
    await supabase.from('projects').delete().like('user_id', 'test-%');
    
    console.log('Database cleanup completed');
  } catch (error) {
    console.error('Database cleanup failed:', error);
  }
};

const seedDatabase = async () => {
  try {
    // Seed test projects
    for (const project of testData.projects) {
      await supabase.from('projects').upsert(project);
    }
    
    // Seed test content
    for (const content of testData.content) {
      await supabase.from('content').upsert(content);
    }
    
    console.log('Database seeding completed');
  } catch (error) {
    console.error('Database seeding failed:', error);
  }
};

// API helpers
const createMockRequest = (method, path, data = {}, headers = {}) => {
  return {
    method,
    url: path,
    headers: {
      'content-type': 'application/json',
      ...headers
    },
    body: JSON.stringify(data),
    query: {},
    ...data
  };
};

const createMockResponse = () => {
  const res = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
    end: jest.fn().mockReturnThis(),
    setHeader: jest.fn().mockReturnThis(),
    getHeader: jest.fn(),
    removeHeader: jest.fn(),
    writeHead: jest.fn(),
    write: jest.fn(),
    statusCode: 200,
    headersSent: false,
    locals: {}
  };
  
  return res;
};

// Test utilities
const expectValidationError = (response, field) => {
  expect(response.status).toBe(400);
  expect(response.body).toHaveProperty('error');
  expect(response.body.error).toContain(field);
};

const expectAuthError = (response) => {
  expect(response.status).toBe(401);
  expect(response.body).toHaveProperty('error');
  expect(response.body.error).toContain('Unauthorized');
};

const expectNotFoundError = (response) => {
  expect(response.status).toBe(404);
  expect(response.body).toHaveProperty('error');
  expect(response.body.error).toContain('Not found');
};

const expectRateLimitError = (response) => {
  expect(response.status).toBe(429);
  expect(response.body).toHaveProperty('error');
  expect(response.body.error).toContain('Rate limit');
};

// Mock external APIs
const mockOpenAI = () => {
  const originalFetch = global.fetch;
  
  global.fetch = jest.fn((url, options) => {
    if (url.includes('api.openai.com')) {
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: 'This is a test response from OpenAI mock'
            }
          }],
          usage: {
            prompt_tokens: 10,
            completion_tokens: 20,
            total_tokens: 30
          }
        })
      });
    }
    
    return originalFetch(url, options);
  });
  
  return () => {
    global.fetch = originalFetch;
  };
};

// Export helpers
global.testHelpers = {
  // Data
  testUsers,
  testData,
  
  // Authentication
  createAuthToken,
  getAuthHeaders,
  
  // Database
  supabase,
  cleanupDatabase,
  seedDatabase,
  
  // API
  createMockRequest,
  createMockResponse,
  
  // Assertions
  expectValidationError,
  expectAuthError,
  expectNotFoundError,
  expectRateLimitError,
  
  // Mocks
  mockOpenAI
};

// Global test setup
beforeAll(async () => {
  // Clean up any existing test data
  await cleanupDatabase();
  
  // Seed fresh test data
  await seedDatabase();
});

// Global test teardown
afterAll(async () => {
  // Clean up test data
  await cleanupDatabase();
});

// Per-test setup
beforeEach(() => {
  // Reset any mocks
  jest.clearAllMocks();
  
  // Set up fresh environment
  process.env.NODE_ENV = 'test';
});

// Per-test teardown
afterEach(() => {
  // Clean up any per-test resources
  jest.restoreAllMocks();
});

// Increase timeout for database operations
jest.setTimeout(30000);