'use client';

import React from 'react';

interface ChangeIndicator {
  value: number;
  type: 'increase' | 'decrease';
  period?: string;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: ChangeIndicator;
  icon?: React.ComponentType<{ className?: string }>;
  loading?: boolean;
  className?: string;
}

export default function MetricCard({
  title,
  value,
  change,
  icon: Icon,
  loading = false,
  className = ''
}: MetricCardProps) {
  if (loading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 animate-pulse ${className}`}>
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded w-24"></div>
            <div className="h-8 bg-gray-200 rounded w-16"></div>
          </div>
          <div className="h-12 w-12 bg-gray-200 rounded-full"></div>
        </div>
        {change && (
          <div className="mt-4 h-4 bg-gray-200 rounded w-32"></div>
        )}
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow ${className}`}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-3xl font-bold text-gray-900 mt-2">{value}</p>
        </div>
        {Icon && (
          <div className="p-3 bg-blue-50 rounded-full">
            <Icon className="h-6 w-6 text-blue-600" />
          </div>
        )}
      </div>
      
      {change && (
        <div className="mt-4 flex items-center">
          <span
            className={`inline-flex items-center text-sm font-medium ${
              change.type === 'increase'
                ? 'text-green-600'
                : 'text-red-600'
            }`}
          >
            {change.type === 'increase' ? '↗' : '↘'}
            {change.value}%
            {change.period && (
              <span className="ml-1 text-gray-500 font-normal">
                {change.period}
              </span>
            )}
          </span>
        </div>
      )}
    </div>
  );
}