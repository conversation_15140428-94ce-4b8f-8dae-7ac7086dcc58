/**
 * Global Test Teardown
 * 
 * Handles global test environment cleanup including database cleanup,
 * session cleanup, and test artifact management.
 */

import { FullConfig } from '@playwright/test';
import { createClient } from '@supabase/supabase-js';
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Running global test teardown...');
  
  try {
    // Initialize Supabase client for test environment
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL_TEST || process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY_TEST || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    
    if (supabaseUrl && supabaseKey) {
      const supabase = createClient(supabaseUrl, supabaseKey);
      
      // Clean up test data
      await cleanupTestData(supabase);
      
      // Clean up test users (optional - depends on your test strategy)
      if (process.env.CLEANUP_TEST_USERS === 'true') {
        await cleanupTestUsers(supabase);
      }
    }
    
    // Clean up authentication files
    cleanupAuthFiles();
    
    // Clean up temporary files
    cleanupTempFiles();
    
    // Generate test summary report
    generateTestSummary();
    
    console.log('✅ Global test teardown complete');
    
  } catch (error) {
    console.error('❌ Global test teardown failed:', error);
    // Don't throw here - teardown failures shouldn't fail the test run
  }
}

/**
 * Clean up test data from database
 */
async function cleanupTestData(supabase: any) {
  console.log('🗑️ Cleaning up test data...');
  
  try {
    // Clean up test content
    const { error: contentError } = await supabase
      .from('content')
      .delete()
      .like('title', '%test%');
    
    // Clean up test projects
    const { error: projectError } = await supabase
      .from('projects')
      .delete()
      .like('name', '%test%');
    
    // Clean up test analytics events
    const { error: analyticsError } = await supabase
      .from('analytics_events')
      .delete()
      .like('category', '%test%');
    
    // Clean up test reports
    const { error: reportError } = await supabase
      .from('reports')
      .delete()
      .like('title', '%test%');
    
    if (contentError || projectError || analyticsError || reportError) {
      console.warn('Some test data cleanup operations failed');
    }
    
    console.log('✅ Test data cleanup complete');
    
  } catch (error) {
    console.error('❌ Test data cleanup failed:', error);
  }
}

/**
 * Clean up test users (optional)
 */
async function cleanupTestUsers(supabase: any) {
  console.log('👥 Cleaning up test users...');
  
  try {
    // Note: Supabase doesn't allow deleting users via client API
    // This would need to be done via Admin API or manually
    console.log('⚠️ Test user cleanup requires admin privileges');
    
  } catch (error) {
    console.error('❌ Test user cleanup failed:', error);
  }
}

/**
 * Clean up authentication files
 */
function cleanupAuthFiles() {
  console.log('🔐 Cleaning up authentication files...');
  
  try {
    const authDir = path.join(__dirname, '../auth');
    if (fs.existsSync(authDir)) {
      fs.rmSync(authDir, { recursive: true, force: true });
      console.log('✅ Authentication files cleaned up');
    }
  } catch (error) {
    console.error('❌ Authentication files cleanup failed:', error);
  }
}

/**
 * Clean up temporary files
 */
function cleanupTempFiles() {
  console.log('🗂️ Cleaning up temporary files...');
  
  try {
    const tempDirs = [
      './test-results/temp',
      './playwright-report/temp',
      './screenshots/temp',
      './videos/temp'
    ];
    
    tempDirs.forEach(dir => {
      if (fs.existsSync(dir)) {
        fs.rmSync(dir, { recursive: true, force: true });
      }
    });
    
    console.log('✅ Temporary files cleaned up');
    
  } catch (error) {
    console.error('❌ Temporary files cleanup failed:', error);
  }
}

/**
 * Generate test summary report
 */
function generateTestSummary() {
  console.log('📊 Generating test summary report...');
  
  try {
    const resultsPath = './test-results/results.json';
    
    if (fs.existsSync(resultsPath)) {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      
      const summary = {
        timestamp: new Date().toISOString(),
        total: results.stats?.total || 0,
        passed: results.stats?.passed || 0,
        failed: results.stats?.failed || 0,
        skipped: results.stats?.skipped || 0,
        duration: results.stats?.duration || 0,
        browsers: results.config?.projects?.map((p: any) => p.name) || [],
        environment: {
          nodeVersion: process.version,
          platform: process.platform,
          ci: !!process.env.CI
        }
      };
      
      fs.writeFileSync(
        './test-results/summary.json',
        JSON.stringify(summary, null, 2)
      );
      
      console.log('✅ Test summary report generated');
      console.log(`📈 Tests: ${summary.total} total, ${summary.passed} passed, ${summary.failed} failed`);
      
    } else {
      console.log('⚠️ No test results found for summary generation');
    }
    
  } catch (error) {
    console.error('❌ Test summary generation failed:', error);
  }
}

export default globalTeardown;