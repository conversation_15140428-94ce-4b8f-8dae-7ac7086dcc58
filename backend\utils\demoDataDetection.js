// Temporary stub for demo data detection
export class DemoDataValidator {
  static validate(data) {
    // Simple validation - just return true for now
    return { isValid: true, confidence: 100 };
  }
  
  static detectDemoPatterns(content) {
    // Simple demo pattern detection
    const demoKeywords = ['lorem', 'ipsum', 'placeholder', 'example', 'sample'];
    const lowerContent = content.toLowerCase();
    const foundPatterns = demoKeywords.filter(keyword => lowerContent.includes(keyword));
    return foundPatterns.length > 0;
  }
}