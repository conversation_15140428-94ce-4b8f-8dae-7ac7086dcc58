/**
 * TargetConfig Component
 * Enterprise SEO SAAS - Target configuration with keyword and location targeting
 */

import { useState, useEffect } from 'react'
import { Project } from '@/types/project'
import { validateKeyword } from '@/utils/projectHelpers'
import { DemoDataValidator, formatErrors } from '@/utils/demoDataDetection'
import {
  TagIcon,
  MapPinIcon,
  GlobeAltIcon,
  PlusIcon,
  XMarkIcon,
  MagnifyingGlassIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  LightBulbIcon,
  ChevronDownIcon,
  ChevronUpIcon
} from '@heroicons/react/24/outline'

interface TargetConfigProps {
  formData: {
    primaryKeyword: string
    secondaryKeywords: string[]
    targetLocation: string
    targetLanguage: string
  }
  onUpdate: (updates: any) => void
  project: Project | null
}

export default function TargetConfig({ formData, onUpdate, project }: TargetConfigProps) {
  const [keywordValidation, setKeywordValidation] = useState<any>(null)
  const [newSecondaryKeyword, setNewSecondaryKeyword] = useState('')
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [searchIntent, setSearchIntent] = useState('informational')
  const [audienceType, setAudienceType] = useState('b2c')
  const [locationValidation, setLocationValidation] = useState<{ isValid: boolean; error?: string; isDemo?: boolean }>({ isValid: true })
  const [secondaryKeywordValidation, setSecondaryKeywordValidation] = useState<{ isValid: boolean; error?: string; isDemo?: boolean }>({ isValid: true })
  const [validationWarnings, setValidationWarnings] = useState<string[]>([])

  const languageOptions = [
    { value: 'en', label: 'English', flag: '🇺🇸', regions: ['US', 'UK', 'CA', 'AU'] },
    { value: 'es', label: 'Spanish', flag: '🇪🇸', regions: ['ES', 'MX', 'AR', 'CO'] },
    { value: 'fr', label: 'French', flag: '🇫🇷', regions: ['FR', 'CA', 'BE', 'CH'] },
    { value: 'de', label: 'German', flag: '🇩🇪', regions: ['DE', 'AT', 'CH'] },
    { value: 'it', label: 'Italian', flag: '🇮🇹', regions: ['IT', 'CH'] },
    { value: 'pt', label: 'Portuguese', flag: '🇵🇹', regions: ['PT', 'BR'] },
    { value: 'ru', label: 'Russian', flag: '🇷🇺', regions: ['RU', 'BY', 'KZ'] },
    { value: 'ja', label: 'Japanese', flag: '🇯🇵', regions: ['JP'] },
    { value: 'ko', label: 'Korean', flag: '🇰🇷', regions: ['KR'] },
    { value: 'zh', label: 'Chinese', flag: '🇨🇳', regions: ['CN', 'TW', 'HK'] }
  ]

  const searchIntentOptions = [
    { value: 'informational', label: 'Informational', description: 'Users seeking information or answers' },
    { value: 'commercial', label: 'Commercial', description: 'Users researching products or services' },
    { value: 'navigational', label: 'Navigational', description: 'Users looking for specific websites' },
    { value: 'transactional', label: 'Transactional', description: 'Users ready to make a purchase' }
  ]

  const audienceOptions = [
    { value: 'b2c', label: 'B2C', description: 'Business to Consumer' },
    { value: 'b2b', label: 'B2B', description: 'Business to Business' }
  ]

  // Location suggestions (mock data - in real app would come from geo API)
  const locationSuggestions = [
    'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany',
    'France', 'Spain', 'Italy', 'Japan', 'South Korea', 'Global'
  ]

  useEffect(() => {
    if (formData.primaryKeyword) {
      // Use enhanced validation with DemoDataValidator
      const basicValidation = validateKeyword(formData.primaryKeyword)
      const demoValidation = DemoDataValidator.validate(formData.primaryKeyword, {
        strict: true,
        field: 'Primary keyword'
      })
      
      setKeywordValidation({
        ...basicValidation,
        isDemo: demoValidation.isDemo,
        demoErrors: demoValidation.errors,
        demoWarnings: demoValidation.warnings
      })
    } else {
      setKeywordValidation(null)
    }
  }, [formData.primaryKeyword])

  const handlePrimaryKeywordChange = (keyword: string) => {
    onUpdate({ primaryKeyword: keyword })
  }

  const handleAddSecondaryKeyword = () => {
    if (newSecondaryKeyword.trim() && formData.secondaryKeywords.length < 10) {
      const validation = validateKeyword(newSecondaryKeyword.trim())
      const demoValidation = DemoDataValidator.validate(newSecondaryKeyword.trim(), {
        strict: true,
        field: 'Secondary keyword'
      })
      
      if (validation.isValid && !demoValidation.isDemo) {
        onUpdate({
          secondaryKeywords: [...formData.secondaryKeywords, newSecondaryKeyword.trim()]
        })
        setNewSecondaryKeyword('')
        setSecondaryKeywordValidation({ isValid: true })
        setValidationWarnings([])
      } else {
        const errors = [...validation.errors, ...demoValidation.errors]
        setSecondaryKeywordValidation({
          isValid: false,
          error: errors[0] || 'Invalid keyword',
          isDemo: demoValidation.isDemo
        })
      }
    }
  }

  const handleRemoveSecondaryKeyword = (index: number) => {
    const updatedKeywords = formData.secondaryKeywords.filter((_, i) => i !== index)
    onUpdate({ secondaryKeywords: updatedKeywords })
  }

  const handleLocationChange = (location: string) => {
    // Use DemoDataValidator for comprehensive location validation
    const demoValidation = DemoDataValidator.validate(location, {
      strict: true,
      field: 'Target location',
      customPatterns: [
        /example\s*location/i, /test\s*location/i, /demo\s*location/i,
        /sample\s*location/i, /your\s*location/i, /some\s*city/i,
        /anywhere/i, /everywhere/i
      ]
    })
    
    if (demoValidation.isValid && !demoValidation.isDemo) {
      setLocationValidation({ isValid: true })
      onUpdate({ targetLocation: location })
    } else {
      setLocationValidation({
        isValid: false,
        error: demoValidation.errors[0] || 'Invalid location',
        isDemo: demoValidation.isDemo
      })
      onUpdate({ targetLocation: location }) // Still update to show the value
    }
  }

  const getValidationIcon = () => {
    if (!keywordValidation) return null
    
    if (keywordValidation.isDemo) {
      return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
    } else if (keywordValidation.isValid) {
      return <CheckCircleIcon className="h-5 w-5 text-green-500" />
    } else {
      return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
    }
  }

  const getKeywordStrength = (keyword: string) => {
    // Mock keyword strength calculation
    const length = keyword.length
    const wordCount = keyword.split(' ').length
    
    if (length < 10 || wordCount > 5) return { score: 30, label: 'Low', color: 'bg-red-500' }
    if (length < 20 || wordCount > 3) return { score: 60, label: 'Medium', color: 'bg-yellow-500' }
    return { score: 85, label: 'High', color: 'bg-green-500' }
  }

  return (
    <div className="p-8 space-y-8">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Target Configuration</h2>
        <p className="text-gray-600">
          Configure your target keywords, location, and audience for optimized content generation.
        </p>
      </div>

      {/* Primary Keyword */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Primary Keyword *
          </label>
          <div className="relative">
            <input
              type="text"
              value={formData.primaryKeyword}
              onChange={(e) => handlePrimaryKeywordChange(e.target.value)}
              placeholder="Enter your main target keyword (no demo data)"
              className={`
                w-full px-4 py-3 pr-12 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent
                ${keywordValidation?.isDemo 
                  ? 'border-yellow-300 focus:ring-yellow-500'
                  : keywordValidation?.isValid === false 
                  ? 'border-red-300 focus:ring-red-500' 
                  : keywordValidation?.isValid === true
                  ? 'border-green-300 focus:ring-green-500'
                  : 'border-gray-300 focus:ring-blue-500'
                }
              `}
            />
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              {getValidationIcon()}
            </div>
          </div>
          
          {/* Keyword Validation Feedback */}
          {keywordValidation && (
            <div className="mt-2 space-y-2">
              {/* Demo Detection */}
              {keywordValidation.isDemo && (
                <div className="flex items-start gap-2 text-sm text-yellow-600">
                  <ExclamationTriangleIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium">Demo keyword detected</div>
                    <div>Please use real search terms that your audience would actually use.</div>
                    {keywordValidation.demoErrors.map((error: string, index: number) => (
                      <div key={index} className="text-xs mt-1">{error}</div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Standard Validation Errors */}
              {keywordValidation.errors.length > 0 && !keywordValidation.isDemo && (
                <div className="flex items-start gap-2 text-sm text-red-600">
                  <ExclamationTriangleIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <div>
                    {keywordValidation.errors.map((error: string, index: number) => (
                      <div key={index}>{error}</div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Warnings */}
              {(keywordValidation.warnings.length > 0 || keywordValidation.demoWarnings?.length > 0) && (
                <div className="flex items-start gap-2 text-sm text-yellow-600">
                  <ExclamationTriangleIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <div>
                    {keywordValidation.warnings.map((warning: string, index: number) => (
                      <div key={index}>{warning}</div>
                    ))}
                    {keywordValidation.demoWarnings?.map((warning: string, index: number) => (
                      <div key={`demo-${index}`}>{warning}</div>
                    ))}
                  </div>
                </div>
              )}
              
              {keywordValidation.suggestions.length > 0 && (
                <div className="flex items-start gap-2 text-sm text-blue-600">
                  <LightBulbIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <div>
                    {keywordValidation.suggestions.map((suggestion: string, index: number) => (
                      <div key={index}>{suggestion}</div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Keyword Strength Indicator */}
          {formData.primaryKeyword && keywordValidation?.isValid && (
            <div className="mt-3 p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Keyword Strength</span>
                <span className="text-sm text-gray-600">
                  {getKeywordStrength(formData.primaryKeyword).label}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${getKeywordStrength(formData.primaryKeyword).color}`}
                  style={{ width: `${getKeywordStrength(formData.primaryKeyword).score}%` }}
                />
              </div>
              <div className="mt-2 grid grid-cols-3 gap-4 text-xs text-gray-600">
                <div>Search Volume: High</div>
                <div>Competition: Medium</div>
                <div>CPC: $2.40</div>
              </div>
            </div>
          )}
        </div>

        {/* Load from Project */}
        {project && project.keywords.length > 0 && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="text-sm font-medium text-blue-900 mb-2">Use from Project</h4>
            <div className="flex flex-wrap gap-2">
              {project.keywords.slice(0, 5).map((kw) => (
                <button
                  key={kw.id}
                  onClick={() => handlePrimaryKeywordChange(kw.keyword)}
                  className="px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-800 text-sm rounded-full transition-colors"
                >
                  {kw.keyword}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Secondary Keywords */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Secondary Keywords
          </label>
          <p className="text-sm text-gray-500 mb-3">
            Add up to 10 related keywords to improve content relevance and ranking potential.
          </p>
          
          {/* Add Secondary Keyword */}
          <div className="flex gap-2 mb-4">
            <input
              type="text"
              value={newSecondaryKeyword}
              onChange={(e) => {
                setNewSecondaryKeyword(e.target.value)
                // Reset validation when typing
                if (e.target.value.trim() === '') {
                  setSecondaryKeywordValidation({ isValid: true })
                }
              }}
              onKeyPress={(e) => e.key === 'Enter' && handleAddSecondaryKeyword()}
              placeholder="Add secondary keyword (no demo data)"
              className={`flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent ${
                secondaryKeywordValidation.isValid === false
                  ? 'border-red-300 focus:ring-red-500'
                  : secondaryKeywordValidation.isDemo
                  ? 'border-yellow-300 focus:ring-yellow-500'
                  : 'border-gray-300 focus:ring-blue-500'
              }`}
              disabled={formData.secondaryKeywords.length >= 10}
            />
            <button
              onClick={handleAddSecondaryKeyword}
              disabled={!newSecondaryKeyword.trim() || formData.secondaryKeywords.length >= 10}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white rounded-lg transition-colors"
            >
              <PlusIcon className="h-4 w-4" />
            </button>
          </div>
          
          {/* Secondary Keyword Validation Feedback */}
          {secondaryKeywordValidation.error && (
            <div className="mb-3 flex items-center text-sm text-red-600">
              <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
              {secondaryKeywordValidation.error}
            </div>
          )}
          {secondaryKeywordValidation.isDemo && (
            <div className="mb-3 flex items-center text-sm text-yellow-600">
              <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
              Demo keyword detected. Please use real search terms.
            </div>
          )}

          {/* Secondary Keywords List */}
          {formData.secondaryKeywords.length > 0 && (
            <div className="space-y-2">
              <div className="flex flex-wrap gap-2">
                {formData.secondaryKeywords.map((keyword, index) => (
                  <div
                    key={index}
                    className="inline-flex items-center gap-2 px-3 py-1 bg-gray-100 rounded-full text-sm"
                  >
                    <TagIcon className="h-3 w-3 text-gray-500" />
                    <span>{keyword}</span>
                    <button
                      onClick={() => handleRemoveSecondaryKeyword(index)}
                      className="text-gray-400 hover:text-red-500 transition-colors"
                    >
                      <XMarkIcon className="h-3 w-3" />
                    </button>
                  </div>
                ))}
              </div>
              <div className="text-xs text-gray-500">
                {formData.secondaryKeywords.length}/10 keywords added
              </div>
            </div>
          )}

          {/* LSI Keyword Suggestions */}
          {formData.primaryKeyword && keywordValidation?.isValid && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h4 className="text-sm font-medium text-green-900 mb-2">Suggested Related Keywords</h4>
              <div className="flex flex-wrap gap-2">
                {[
                  `${formData.primaryKeyword} guide`,
                  `${formData.primaryKeyword} tips`,
                  `best ${formData.primaryKeyword}`,
                  `${formData.primaryKeyword} strategy`,
                  `how to ${formData.primaryKeyword}`
                ].map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => setNewSecondaryKeyword(suggestion)}
                    className="px-3 py-1 bg-green-100 hover:bg-green-200 text-green-800 text-sm rounded-full transition-colors"
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Target Location */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Target Location
          </label>
          <div className="relative">
            <input
              type="text"
              value={formData.targetLocation}
              onChange={(e) => handleLocationChange(e.target.value)}
              placeholder="e.g., United States, Global"
              list="location-suggestions"
              className={`w-full px-4 py-3 pl-10 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent ${
                locationValidation.isValid === false
                  ? 'border-red-300 focus:ring-red-500'
                  : locationValidation.isDemo
                  ? 'border-yellow-300 focus:ring-yellow-500'
                  : 'border-gray-300 focus:ring-blue-500'
              }`}
            />
            <MapPinIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <datalist id="location-suggestions">
              {locationSuggestions.map((location) => (
                <option key={location} value={location} />
              ))}
            </datalist>
          </div>
          
          {/* Location Validation Feedback */}
          {locationValidation.error && (
            <div className="mt-1 flex items-center text-sm text-red-600">
              <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
              {locationValidation.error}
            </div>
          )}
          {locationValidation.isDemo && (
            <div className="mt-1 flex items-center text-sm text-yellow-600">
              <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
              Demo location detected. Please specify a real target location.
            </div>
          )}
          
          <p className="text-xs text-gray-500 mt-1">
            Leave blank for global targeting
          </p>
        </div>

        {/* Target Language */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Target Language
          </label>
          <select
            value={formData.targetLanguage}
            onChange={(e) => onUpdate({ targetLanguage: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {languageOptions.map((lang) => (
              <option key={lang.value} value={lang.value}>
                {lang.flag} {lang.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Advanced Options */}
      <div className="border-t border-gray-200 pt-6">
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
        >
          {showAdvanced ? (
            <ChevronUpIcon className="h-4 w-4" />
          ) : (
            <ChevronDownIcon className="h-4 w-4" />
          )}
          Advanced Targeting Options
        </button>

        {showAdvanced && (
          <div className="mt-6 space-y-6">
            {/* Search Intent */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Search Intent
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {searchIntentOptions.map((intent) => (
                  <label key={intent.value} className="flex items-center cursor-pointer">
                    <input
                      type="radio"
                      name="searchIntent"
                      value={intent.value}
                      checked={searchIntent === intent.value}
                      onChange={(e) => setSearchIntent(e.target.value)}
                      className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <div className="ml-3">
                      <div className="text-sm font-medium text-gray-900">{intent.label}</div>
                      <div className="text-xs text-gray-500">{intent.description}</div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Audience Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Target Audience
              </label>
              <div className="flex gap-4">
                {audienceOptions.map((audience) => (
                  <label key={audience.value} className="flex items-center cursor-pointer">
                    <input
                      type="radio"
                      name="audienceType"
                      value={audience.value}
                      checked={audienceType === audience.value}
                      onChange={(e) => setAudienceType(e.target.value)}
                      className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <div className="ml-3">
                      <div className="text-sm font-medium text-gray-900">{audience.label}</div>
                      <div className="text-xs text-gray-500">{audience.description}</div>
                    </div>
                  </label>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Validation Warning Panel */}
      {(keywordValidation?.isDemo || locationValidation.isDemo || secondaryKeywordValidation.isDemo) && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-yellow-800 mb-2">
                ⚠️ Demo Content Detected
              </h4>
              <p className="text-sm text-yellow-700 mb-3">
                Please replace demo content with real targeting data to ensure accurate SEO content generation.
              </p>
              <div className="space-y-1 text-xs text-yellow-600">
                {keywordValidation?.isDemo && (
                  <div>• Primary keyword contains demo content</div>
                )}
                {locationValidation.isDemo && (
                  <div>• Target location contains demo content</div>
                )}
                {secondaryKeywordValidation.isDemo && (
                  <div>• Secondary keyword input contains demo content</div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Configuration Summary */}
      {formData.primaryKeyword && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 mb-4">Target Summary</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-blue-800 mb-2">Primary Focus</h4>
              <div className="text-sm text-blue-700 space-y-1">
                <div>Keyword: "{formData.primaryKeyword}"</div>
                <div>Location: {formData.targetLocation || 'Global'}</div>
                <div>Language: {languageOptions.find(l => l.value === formData.targetLanguage)?.label}</div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-blue-800 mb-2">Content Strategy</h4>
              <div className="text-sm text-blue-700 space-y-1">
                <div>Secondary keywords: {formData.secondaryKeywords.length}</div>
                <div>Search intent: {searchIntent}</div>
                <div>Audience: {audienceType.toUpperCase()}</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}