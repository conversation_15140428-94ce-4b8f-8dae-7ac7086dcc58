/* 
 * Critical CSS - Above the fold styles
 * Load immediately for better performance
 */

/* CSS Custom Properties (Design System) */
:root {
  /* Colors */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  --success-600: #16a34a;
  --error-500: #ef4444;
  --error-600: #dc2626;
  
  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  
  /* Border Radius */
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  
  /* Background Colors */
  --bg-primary: white;
  --bg-secondary: #f9fafb;
  
  /* Text Colors */
  --text-primary: #111827;
  --text-secondary: #6b7280;
}

/* Base styles for critical elements */
* {
  box-sizing: border-box;
}

html {
  font-family: var(--font-primary);
  -webkit-text-size-adjust: 100%;
  tab-size: 4;
}

body {
  margin: 0;
  padding: 0;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Critical layout styles */
.min-h-screen {
  min-height: 100vh;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.bg-gray-50 {
  background-color: var(--bg-secondary);
}

/* Loading spinner for critical loading states */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.rounded-full {
  border-radius: var(--radius-full);
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-blue-600 {
  border-color: var(--primary-600);
}

.h-8 {
  height: 2rem;
}

.w-8 {
  width: 2rem;
}

.h-12 {
  height: 3rem;
}

.w-12 {
  width: 3rem;
}

.text-gray-600 {
  color: var(--gray-600);
}

/* Critical button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-lg);
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  border: 1px solid transparent;
}

.btn-primary {
  background-color: var(--primary-600);
  color: white;
  border-color: var(--primary-600);
}

.btn-primary:hover {
  background-color: var(--primary-700);
  border-color: var(--primary-700);
}

.btn-secondary {
  background-color: white;
  color: var(--gray-700);
  border-color: var(--gray-300);
}

.btn-secondary:hover {
  background-color: var(--gray-50);
  border-color: var(--gray-400);
}

.btn-sm {
  padding: var(--space-1) var(--space-3);
  font-size: 0.875rem;
}

/* Critical layout containers */
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

@media (min-width: 640px) {
  .container {
    padding: 0 var(--space-6);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--space-8);
  }
}

@media (min-width: 1280px) {
  .container {
    padding: 0 var(--space-12);
  }
}

/* Critical grid system */
.grid {
  display: grid;
}

.gap-4 {
  gap: var(--space-4);
}

.gap-6 {
  gap: var(--space-6);
}

/* Critical responsive classes */
@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  
  .lg\:gap-6 {
    gap: var(--space-6);
  }
}

@media (min-width: 1280px) {
  .xl\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
}

/* Critical card styles */
.card {
  background-color: white;
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
}

.card-body {
  padding: var(--space-6);
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.card-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0;
}

/* Critical sidebar layout */
.sidebar {
  width: 280px;
  background-color: white;
  border-right: 1px solid var(--gray-200);
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  z-index: 50;
  transform: translateX(-100%);
  transition: transform 0.3s ease-in-out;
}

.sidebar.open {
  transform: translateX(0);
}

@media (min-width: 1024px) {
  .sidebar {
    position: relative;
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 280px;
  }
}

/* Critical header styles */
.header {
  position: sticky;
  top: 0;
  z-index: 40;
  background-color: white;
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4);
}

/* Critical text utilities */
.text-sm {
  font-size: 0.875rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.text-3xl {
  font-size: 1.875rem;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

/* Critical spacing utilities */
.p-4 {
  padding: var(--space-4);
}

.p-6 {
  padding: var(--space-6);
}

.py-2 {
  padding-top: var(--space-2);
  padding-bottom: var(--space-2);
}

.py-4 {
  padding-top: var(--space-4);
  padding-bottom: var(--space-4);
}

.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}

.px-4 {
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

.px-6 {
  padding-left: var(--space-6);
  padding-right: var(--space-6);
}

.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}

.mb-2 {
  margin-bottom: var(--space-2);
}

.mb-4 {
  margin-bottom: var(--space-4);
}

.mb-6 {
  margin-bottom: var(--space-6);
}

.mb-8 {
  margin-bottom: var(--space-8);
}

.mr-2 {
  margin-right: var(--space-2);
}

.mr-4 {
  margin-right: var(--space-4);
}

/* Critical visibility utilities */
.hidden {
  display: none;
}

@media (min-width: 1024px) {
  .lg\:block {
    display: block;
  }
  
  .lg\:hidden {
    display: none;
  }
}

/* Critical border utilities */
.border {
  border-width: 1px;
}

.border-gray-200 {
  border-color: var(--gray-200);
}

.border-gray-300 {
  border-color: var(--gray-300);
}

.rounded-lg {
  border-radius: var(--radius-lg);
}

.rounded-xl {
  border-radius: var(--radius-xl);
}