#!/usr/bin/env node

/**
 * Database Setup Script for SEO SAAS
 * Deploys schema to Supabase and configures authentication
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Supabase configuration
const SUPABASE_URL = 'https://xpcbyzcaidfukddqniny.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwY2J5emNhaWRmdWtkZHFuaW55Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0Njc5MDUzNywiZXhwIjoyMDYyMzY2NTM3fQ.rcH_G_p6zeqz1LPhGvJIDDnKwu7bXjY7qqBFMw9ZTC4';

// Initialize Supabase admin client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function setupDatabase() {
  console.log('🚀 Starting SEO SAAS Database Setup...');
  
  try {
    // Test connection
    console.log('🔗 Testing Supabase connection...');
    const { data, error } = await supabase.rpc('version');
    console.log('✅ Supabase connection successful!');

    // Read and execute schema
    console.log('📋 Reading database schema...');
    const schemaPath = join(__dirname, 'database', 'schema.sql');
    const schema = readFileSync(schemaPath, 'utf-8');
    
    console.log('🏗️  Creating database schema...');
    console.log('📝 Schema will be executed in statements...');

    // Enable RLS
    console.log('🔒 Enabling Row Level Security...');
    const rlsQueries = [
      'ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;',
      'ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;',
      'ALTER TABLE public.generated_content ENABLE ROW LEVEL SECURITY;',
      'ALTER TABLE public.seo_analysis ENABLE ROW LEVEL SECURITY;'
    ];

    for (const query of rlsQueries) {
      await supabase.rpc('exec_sql', { sql: query });
    }

    console.log('✅ Database schema setup completed!');
    
    // Test basic operations
    console.log('🧪 Testing database operations...');
    
    // Test users table
    const { data: usersTest, error: usersError } = await supabase
      .from('users')
      .select('id')
      .limit(1);
    
    if (usersError) {
      console.log('⚠️  Users table test:', usersError.message);
    } else {
      console.log('✅ Users table operational');
    }

    // Test projects table
    const { data: projectsTest, error: projectsError } = await supabase
      .from('projects')
      .select('id')
      .limit(1);
    
    if (projectsError) {
      console.log('⚠️  Projects table test:', projectsError.message);
    } else {
      console.log('✅ Projects table operational');
    }

    console.log('🎉 Database setup completed successfully!');
    console.log('📝 Next steps:');
    console.log('   1. Configure authentication providers in Supabase dashboard');
    console.log('   2. Set up OAuth apps for Google and GitHub');
    console.log('   3. Update auth callback URLs');
    console.log('   4. Test user registration and login');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    process.exit(1);
  }
}

// Alternative method using direct SQL execution
async function executeSchemaDirectly() {
  console.log('🔄 Attempting direct schema execution...');
  
  try {
    const schemaPath = join(__dirname, 'database', 'schema.sql');
    const schema = readFileSync(schemaPath, 'utf-8');
    
    // Split schema into individual statements
    const statements = schema
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📋 Executing ${statements.length} SQL statements...`);
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.length > 10) { // Skip empty statements
        console.log(`   ${i + 1}/${statements.length}: ${statement.substring(0, 50)}...`);
        
        const { error } = await supabase.rpc('exec_sql', {
          sql: statement + ';'
        });
        
        if (error && !error.message.includes('already exists')) {
          console.log(`   ⚠️  Statement ${i + 1} warning:`, error.message);
        }
      }
    }
    
    console.log('✅ Schema execution completed!');
    
  } catch (error) {
    console.error('❌ Direct schema execution failed:', error.message);
  }
}

// Run setup
console.log('🔧 SEO SAAS Database Configuration');
console.log('================================');
setupDatabase()
  .then(() => executeSchemaDirectly())
  .then(() => {
    console.log('🚀 Setup complete! Your SEO SAAS database is ready.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Setup failed:', error);
    process.exit(1);
  });