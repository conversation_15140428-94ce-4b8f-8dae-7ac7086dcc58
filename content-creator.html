<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Create professional SEO content for any niche using AI-powered competitor analysis and real-time optimization.">
    <title>Content Creator - SEO SAAS</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/forms.css">
    <link rel="stylesheet" href="css/data-visualization.css">
    <link rel="stylesheet" href="css/animations.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
</head>
<body class="dashboard-layout">
    <!-- Dashboard App Container -->
    <div class="dashboard-app">
        <!-- Global Top Navigation -->
        <header class="top-navigation">
            <div class="nav-left">
                <button class="sidebar-toggle" aria-label="Toggle sidebar" onclick="toggleSidebar()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
                <div class="brand-section">
                    <svg class="brand-logo w-8 h-8 text-primary-600" viewBox="0 0 40 40" fill="currentColor">
                        <path d="M20 5L5 15v10l15 10 15-10V15L20 5z"/>
                        <path d="M20 15l-10 6v8l10 6 10-6v-8l-10-6z" fill="white" opacity="0.5"/>
                    </svg>
                    <span class="brand-name">SEO SAAS</span>
                    <span class="environment-badge">Pro</span>
                </div>
            </div>
            
            <div class="nav-center">
                <div class="global-search">
                    <svg class="search-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <input type="search" placeholder="Search projects, keywords, content..." class="search-input">
                </div>
            </div>
            
            <div class="nav-right">
                <button class="quick-action-btn" title="Save Draft" onclick="saveDraft()">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <span>Save Draft</span>
                </button>
                
                <div class="notifications-dropdown">
                    <button class="notification-btn" title="Notifications">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5-5V9.5a6.5 6.5 0 10-13 0V12l-5 5h5"></path>
                        </svg>
                        <span class="notification-badge">3</span>
                    </button>
                </div>
                
                <div class="help-dropdown">
                    <button class="help-btn" title="Help & Support">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </button>
                </div>
                
                <div class="user-menu-dropdown">
                    <button class="user-menu-btn">
                        <div class="user-avatar">
                            <span>JD</span>
                        </div>
                        <div class="user-info">
                            <span class="user-name">John Doe</span>
                            <span class="user-plan">Professional</span>
                        </div>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <aside class="sidebar-navigation" id="sidebarNav">
            <nav class="nav-menu">
                <!-- Dashboard Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Overview</h3>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="dashboard.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z"></path>
                                </svg>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="analytics.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                <span>Analytics</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Content Creation Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Content Creation</h3>
                        <button class="section-toggle" aria-label="Toggle section" onclick="toggleSection(this)">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item active">
                            <a href="content-creator.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                <span>Create Content</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="content-optimizer.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                <span>Optimize Content</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="bulk-generator.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                <span>Bulk Generator</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="content-editor.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                                </svg>
                                <span>Content Editor</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Projects Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Projects</h3>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="projects.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                <span>All Projects</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="templates.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <span>Templates</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Settings Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Settings</h3>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="account-settings.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <span>Account</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="billing.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                </svg>
                                <span>Billing</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Sidebar Footer -->
            <div class="sidebar-footer">
                <div class="usage-indicator">
                    <div class="usage-label">API Usage</div>
                    <div class="usage-bar">
                        <div class="usage-fill" style="width: 65%"></div>
                    </div>
                    <div class="usage-text">325 / 500 requests</div>
                </div>
                
                <button class="upgrade-btn" onclick="window.location.href='pricing.html'">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    <span>Upgrade Plan</span>
                </button>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="page-title">Create New Content</h1>
                        <p class="page-subtitle">Generate professional SEO content for any niche using AI-powered competitor analysis</p>
                    </div>
                    <div class="header-right">
                        <div class="generation-status" id="generationStatus">
                            <span class="status-indicator status-ready"></span>
                            <span class="status-text">Ready to generate</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Creator Layout -->
            <div class="content-creator-layout">
                <!-- Input Panel -->
                <div class="input-panel">
                    <div class="panel-header">
                        <h2>Content Configuration</h2>
                        <p>Provide your target information for competitor analysis and content generation</p>
                    </div>
                    
                    <form class="content-form" id="contentCreatorForm">
                        <!-- Basic Information Section -->
                        <div class="form-section">
                            <h3 class="form-section-title">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Basic Information
                            </h3>
                            
                            <div class="form-group">
                                <label for="targetKeyword" class="form-label">
                                    Target Keyword *
                                    <span class="label-help" title="Enter the main keyword you want to rank for">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </span>
                                </label>
                                <div class="input-with-icon">
                                    <svg class="input-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-3.586l4.293-4.293A6 6 0 0119 9z"></path>
                                    </svg>
                                    <input 
                                        type="text" 
                                        id="targetKeyword" 
                                        name="targetKeyword"
                                        class="form-input" 
                                        placeholder="e.g., digital marketing services"
                                        required
                                        onchange="validateKeyword(this)"
                                    >
                                </div>
                                <div class="form-help">Enter the main keyword you want to rank for (no demo/fake keywords)</div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="targetLocation" class="form-label">Target Location *</label>
                                    <select id="targetLocation" name="targetLocation" class="form-select" required onchange="updateCompetitorAnalysis()">
                                        <option value="">Select location</option>
                                        <option value="us">United States</option>
                                        <option value="uk">United Kingdom</option>
                                        <option value="ca">Canada</option>
                                        <option value="au">Australia</option>
                                        <option value="de">Germany</option>
                                        <option value="fr">France</option>
                                        <option value="es">Spain</option>
                                        <option value="it">Italy</option>
                                        <option value="ae">United Arab Emirates</option>
                                        <option value="sg">Singapore</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="contentIntent" class="form-label">Content Type *</label>
                                    <select id="contentIntent" name="contentIntent" class="form-select" required onchange="updateContentSettings()">
                                        <option value="">Select content type</option>
                                        <option value="service-page">Service Page</option>
                                        <option value="blog-post">Blog Post</option>
                                        <option value="product-page">Product Page</option>
                                        <option value="landing-page">Landing Page</option>
                                        <option value="comparison">Comparison Page</option>
                                        <option value="how-to-guide">How-to Guide</option>
                                        <option value="industry-analysis">Industry Analysis</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="websiteUrl" class="form-label">
                                    Your Website URL *
                                    <span class="label-help" title="Enter your real website URL for internal linking">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </span>
                                </label>
                                <div class="input-with-icon">
                                    <svg class="input-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 01-9 9m9-9H3m9 9v9m9-9a9 9 0 01-9-9m9 9c0 5-4 9-9 9m0-9a9 9 0 01-9-9m9 9a9 9 0 019-9m-9 9c0-5-4-9-9-9"></path>
                                    </svg>
                                    <input 
                                        type="url" 
                                        id="websiteUrl" 
                                        name="websiteUrl"
                                        class="form-input" 
                                        placeholder="https://yourwebsite.com"
                                        required
                                        onchange="validateWebsiteUrl(this)"
                                    >
                                </div>
                                <div class="form-help">Your real website URL for intelligent internal linking</div>
                            </div>
                            
                            <div class="form-group">
                                <label for="sitemapUrl" class="form-label">Sitemap URL (Optional)</label>
                                <div class="input-with-icon">
                                    <svg class="input-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                                    </svg>
                                    <input 
                                        type="url" 
                                        id="sitemapUrl" 
                                        name="sitemapUrl"
                                        class="form-input" 
                                        placeholder="https://yourwebsite.com/sitemap.xml"
                                        onchange="analyzeSitemap(this.value)"
                                    >
                                </div>
                                <div class="form-help">For intelligent internal linking (auto-detected if not provided)</div>
                            </div>
                        </div>
                        
                        <!-- Advanced Configuration Section -->
                        <div class="form-section">
                            <h3 class="form-section-title">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                Advanced Configuration
                            </h3>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="targetWordCount" class="form-label">Target Word Count</label>
                                    <select id="targetWordCount" name="targetWordCount" class="form-select">
                                        <option value="auto">Auto (based on competitors)</option>
                                        <option value="500-800">500-800 words</option>
                                        <option value="800-1200">800-1,200 words</option>
                                        <option value="1200-2000">1,200-2,000 words</option>
                                        <option value="2000+">2,000+ words</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="writingTone" class="form-label">Writing Tone</label>
                                    <select id="writingTone" name="writingTone" class="form-select">
                                        <option value="professional">Professional</option>
                                        <option value="conversational">Conversational</option>
                                        <option value="authoritative">Authoritative</option>
                                        <option value="friendly">Friendly</option>
                                        <option value="technical">Technical</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="targetAudience" class="form-label">Target Audience</label>
                                <input 
                                    type="text" 
                                    id="targetAudience" 
                                    name="targetAudience"
                                    class="form-input" 
                                    placeholder="e.g., small business owners, marketing professionals"
                                >
                            </div>
                            
                            <div class="form-group">
                                <label for="additionalKeywords" class="form-label">Additional Keywords (Optional)</label>
                                <textarea 
                                    id="additionalKeywords" 
                                    name="additionalKeywords"
                                    class="form-textarea"
                                    rows="3"
                                    placeholder="Enter related keywords separated by commas"
                                ></textarea>
                                <div class="form-help">Add LSI and semantic keywords to improve content relevance</div>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="form-actions">
                            <button type="button" class="btn btn-secondary" onclick="saveDraft()">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                Save Draft
                            </button>
                            <button type="button" class="btn btn-primary" onclick="analyzeCompetitors()" id="analyzeBtn">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                Analyze Competitors
                            </button>
                            <button type="submit" class="btn btn-primary btn-lg" disabled id="generateBtn">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                Generate Content
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Analysis Panel -->
                <div class="analysis-panel">
                    <div class="panel-header">
                        <h2>Real-Time Analysis</h2>
                        <p>Competitor insights and sequential AI thinking process</p>
                    </div>
                    
                    <div class="analysis-content" id="analysisContent">
                        <!-- Default State -->
                        <div class="analysis-placeholder" id="analysisPlaceholder">
                            <div class="placeholder-icon">
                                <svg class="w-16 h-16 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </div>
                            <h3>Ready for Analysis</h3>
                            <p>Enter your target keyword and location to start real-time competitor analysis and sequential AI thinking</p>
                        </div>
                        
                        <!-- Analysis Results (Hidden by default) -->
                        <div class="analysis-results hidden" id="analysisResults">
                            <!-- Sequential AI Thinking -->
                            <div class="thinking-section">
                                <h3 class="section-title">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                    </svg>
                                    Sequential AI Thinking
                                </h3>
                                <div class="thinking-stages" id="thinkingStages">
                                    <!-- Thinking stages will be populated here -->
                                </div>
                            </div>
                            
                            <!-- Competitor Analysis -->
                            <div class="competitors-section">
                                <h3 class="section-title">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                    Top Competitors
                                </h3>
                                <div class="competitors-list" id="competitorsList">
                                    <!-- Competitor data will be populated here -->
                                </div>
                            </div>
                            
                            <!-- Authority Links -->
                            <div class="authority-section">
                                <h3 class="section-title">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                    </svg>
                                    Authority Links
                                </h3>
                                <div class="authority-links" id="authorityLinks">
                                    <!-- Authority links will be populated here -->
                                </div>
                            </div>
                        </div>
                        
                        <!-- Generation Progress (Hidden by default) -->
                        <div class="generation-progress hidden" id="generationProgress">
                            <div class="progress-header">
                                <h3>Generating Content</h3>
                                <p>Please wait while we create your optimized content...</p>
                            </div>
                            <div class="progress-steps">
                                <div class="progress-step active">
                                    <div class="step-icon">1</div>
                                    <div class="step-label">Analyzing Competitors</div>
                                </div>
                                <div class="progress-step">
                                    <div class="step-icon">2</div>
                                    <div class="step-label">Sequential AI Thinking</div>
                                </div>
                                <div class="progress-step">
                                    <div class="step-icon">3</div>
                                    <div class="step-label">Content Generation</div>
                                </div>
                                <div class="progress-step">
                                    <div class="step-icon">4</div>
                                    <div class="step-label">Quality Optimization</div>
                                </div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 25%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script>
        // Content Creator JavaScript functionality
        document.addEventListener('DOMContentLoaded', function() {
            initializeContentCreator();
            setupFormValidation();
            checkUrlParameters();
        });

        function initializeContentCreator() {
            // Initialize sidebar state
            const sidebar = document.getElementById('sidebarNav');
            const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (isCollapsed) {
                sidebar.classList.add('collapsed');
            }
            
            // Load saved draft if exists
            loadSavedDraft();
        }

        function setupFormValidation() {
            const form = document.getElementById('contentCreatorForm');
            const inputs = form.querySelectorAll('input[required], select[required]');
            
            inputs.forEach(input => {
                input.addEventListener('change', validateForm);
                input.addEventListener('input', validateForm);
            });
            
            // Form submission
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                generateContent();
            });
        }

        function validateForm() {
            const form = document.getElementById('contentCreatorForm');
            const requiredInputs = form.querySelectorAll('input[required], select[required]');
            const generateBtn = document.getElementById('generateBtn');
            const analyzeBtn = document.getElementById('analyzeBtn');
            
            let allValid = true;
            requiredInputs.forEach(input => {
                if (!input.value.trim()) {
                    allValid = false;
                }
            });
            
            // Enable analyze button if keyword and location are provided
            const keyword = document.getElementById('targetKeyword').value;
            const location = document.getElementById('targetLocation').value;
            analyzeBtn.disabled = !(keyword && location);
            
            // Enable generate button only after analysis is complete
            generateBtn.disabled = !allValid || !window.analysisComplete;
        }

        function validateKeyword(input) {
            const keyword = input.value.toLowerCase();
            const demoKeywords = ['example', 'demo', 'test', 'sample', 'placeholder'];
            
            if (demoKeywords.some(demo => keyword.includes(demo))) {
                showValidationError(input, 'Please enter a real keyword, not a demo/example keyword');
                return false;
            }
            
            clearValidationError(input);
            return true;
        }

        function validateWebsiteUrl(input) {
            const url = input.value.toLowerCase();
            const demoUrls = ['example.com', 'demo.com', 'test.com', 'sample.com'];
            
            if (demoUrls.some(demo => url.includes(demo))) {
                showValidationError(input, 'Please enter your real website URL, not a demo URL');
                return false;
            }
            
            clearValidationError(input);
            return true;
        }

        function showValidationError(input, message) {
            clearValidationError(input);
            
            input.classList.add('error');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'form-error';
            errorDiv.textContent = message;
            input.parentNode.parentNode.appendChild(errorDiv);
        }

        function clearValidationError(input) {
            input.classList.remove('error');
            const existingError = input.parentNode.parentNode.querySelector('.form-error');
            if (existingError) {
                existingError.remove();
            }
        }

        function analyzeCompetitors() {
            const keyword = document.getElementById('targetKeyword').value;
            const location = document.getElementById('targetLocation').value;
            
            if (!keyword || !location) {
                alert('Please enter a keyword and select a location first.');
                return;
            }
            
            // Show analysis results section
            document.getElementById('analysisPlaceholder').classList.add('hidden');
            document.getElementById('analysisResults').classList.remove('hidden');
            
            // Update status
            updateGenerationStatus('analyzing', 'Analyzing competitors...');
            
            // Simulate competitor analysis
            setTimeout(() => {
                displaySequentialThinking();
                setTimeout(() => {
                    displayCompetitorResults();
                    setTimeout(() => {
                        displayAuthorityLinks();
                        updateGenerationStatus('ready', 'Analysis complete - Ready to generate');
                        window.analysisComplete = true;
                        validateForm();
                    }, 1500);
                }, 2000);
            }, 1000);
        }

        function displaySequentialThinking() {
            const stages = [
                {
                    stage: 1,
                    title: "Strategic Analysis",
                    description: "Analyzing search intent and user behavior patterns",
                    status: "completed"
                },
                {
                    stage: 2,
                    title: "Competitive Intelligence",
                    description: "Studying top-ranking competitor content strategies",
                    status: "active"
                },
                {
                    stage: 3,
                    title: "Content Architecture",
                    description: "Designing optimal content structure and flow",
                    status: "pending"
                },
                {
                    stage: 4,
                    title: "E-E-A-T Optimization",
                    description: "Enhancing expertise, experience, authoritativeness, and trust",
                    status: "pending"
                }
            ];
            
            const container = document.getElementById('thinkingStages');
            container.innerHTML = stages.map(stage => `
                <div class="thinking-stage ${stage.status}">
                    <div class="stage-number">${stage.stage}</div>
                    <div class="stage-content">
                        <div class="stage-title">${stage.title}</div>
                        <div class="stage-description">${stage.description}</div>
                    </div>
                    <div class="stage-status">
                        ${stage.status === 'completed' ? 
                            '<svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>' :
                            stage.status === 'active' ?
                            '<div class="spinner"></div>' :
                            '<div class="stage-pending"></div>'
                        }
                    </div>
                </div>
            `).join('');
        }

        function displayCompetitorResults() {
            const competitors = [
                {
                    url: "hubspot.com",
                    title: "Digital Marketing Software & Solutions",
                    wordCount: 2847,
                    headings: 18,
                    score: 94
                },
                {
                    url: "salesforce.com",
                    title: "Digital Marketing Platform & Tools",
                    wordCount: 3156,
                    headings: 22,
                    score: 92
                },
                {
                    url: "marketo.com",
                    title: "Marketing Automation & Digital Solutions",
                    wordCount: 2634,
                    headings: 16,
                    score: 89
                }
            ];
            
            const container = document.getElementById('competitorsList');
            container.innerHTML = competitors.map(comp => `
                <div class="competitor-item">
                    <div class="competitor-info">
                        <div class="competitor-url">${comp.url}</div>
                        <div class="competitor-title">${comp.title}</div>
                        <div class="competitor-meta">
                            ${comp.wordCount} words • ${comp.headings} headings
                        </div>
                    </div>
                    <div class="competitor-score">
                        <div class="score-badge ${comp.score >= 90 ? 'score-excellent' : 'score-good'}">${comp.score}</div>
                    </div>
                </div>
            `).join('');
        }

        function displayAuthorityLinks() {
            const links = [
                {
                    domain: "wikipedia.org",
                    title: "Digital Marketing - Wikipedia",
                    score: 98
                },
                {
                    domain: "google.com",
                    title: "Think with Google - Marketing Insights",
                    score: 99
                },
                {
                    domain: "moz.com",
                    title: "Digital Marketing Guide by Moz",
                    score: 96
                }
            ];
            
            const container = document.getElementById('authorityLinks');
            container.innerHTML = links.map(link => `
                <div class="authority-link-item">
                    <div class="link-info">
                        <div class="link-domain">${link.domain}</div>
                        <div class="link-title">${link.title}</div>
                    </div>
                    <div class="link-score">
                        <div class="score-badge score-excellent">${link.score}</div>
                    </div>
                </div>
            `).join('');
        }

        function generateContent() {
            // Show generation progress
            document.getElementById('analysisResults').classList.add('hidden');
            document.getElementById('generationProgress').classList.remove('hidden');
            
            updateGenerationStatus('generating', 'Generating content...');
            
            // Simulate content generation process
            const steps = document.querySelectorAll('.progress-step');
            const progressFill = document.querySelector('.progress-fill');
            
            let currentStep = 0;
            const totalSteps = steps.length;
            
            const progressInterval = setInterval(() => {
                if (currentStep > 0) {
                    steps[currentStep - 1].classList.remove('active');
                    steps[currentStep - 1].classList.add('completed');
                }
                
                if (currentStep < totalSteps) {
                    steps[currentStep].classList.add('active');
                    progressFill.style.width = `${((currentStep + 1) / totalSteps) * 100}%`;
                    currentStep++;
                } else {
                    clearInterval(progressInterval);
                    // Redirect to content editor or show results
                    setTimeout(() => {
                        window.location.href = 'content-editor.html?generated=true';
                    }, 1000);
                }
            }, 2000);
        }

        function updateGenerationStatus(status, text) {
            const statusIndicator = document.querySelector('.status-indicator');
            const statusText = document.querySelector('.status-text');
            
            statusIndicator.className = `status-indicator status-${status}`;
            statusText.textContent = text;
        }

        function saveDraft() {
            const formData = new FormData(document.getElementById('contentCreatorForm'));
            const draftData = Object.fromEntries(formData);
            
            localStorage.setItem('contentCreatorDraft', JSON.stringify(draftData));
            
            // Show success message
            showNotification('Draft saved successfully', 'success');
        }

        function loadSavedDraft() {
            const savedDraft = localStorage.getItem('contentCreatorDraft');
            if (savedDraft) {
                const draftData = JSON.parse(savedDraft);
                const form = document.getElementById('contentCreatorForm');
                
                Object.keys(draftData).forEach(key => {
                    const field = form.querySelector(`[name="${key}"]`);
                    if (field) {
                        field.value = draftData[key];
                    }
                });
            }
        }

        function checkUrlParameters() {
            const urlParams = new URLSearchParams(window.location.search);
            const projectId = urlParams.get('id');
            const editMode = urlParams.get('edit');
            
            if (projectId && editMode) {
                // Load existing project data for editing
                loadProjectForEditing(projectId);
            }
        }

        function loadProjectForEditing(projectId) {
            // Sample project data - would come from API
            const projectData = {
                targetKeyword: "digital marketing services",
                targetLocation: "us",
                contentIntent: "service-page",
                websiteUrl: "https://digitalagency.com",
                targetWordCount: "auto",
                writingTone: "professional"
            };
            
            // Populate form with project data
            Object.keys(projectData).forEach(key => {
                const field = document.querySelector(`[name="${key}"]`);
                if (field) {
                    field.value = projectData[key];
                }
            });
            
            // Update page title
            document.querySelector('.page-title').textContent = 'Edit Project Content';
        }

        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            
            // Add to page
            document.body.appendChild(notification);
            
            // Auto remove after 3 seconds
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Navigation functions
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebarNav');
            sidebar.classList.toggle('collapsed');
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }

        function toggleSection(button) {
            const section = button.closest('.nav-section');
            const items = section.querySelector('.nav-items');
            const icon = button.querySelector('svg');
            
            section.classList.toggle('collapsed');
            
            if (section.classList.contains('collapsed')) {
                items.style.display = 'none';
                icon.style.transform = 'rotate(-90deg)';
            } else {
                items.style.display = 'block';
                icon.style.transform = 'rotate(0deg)';
            }
        }

        // Additional helper functions
        function updateCompetitorAnalysis() {
            // Trigger analysis update when location changes
            if (window.analysisComplete) {
                analyzeCompetitors();
            }
        }

        function updateContentSettings() {
            // Update form based on content type selection
            const contentType = document.getElementById('contentIntent').value;
            
            // Adjust word count recommendations based on content type
            const wordCountSelect = document.getElementById('targetWordCount');
            if (contentType === 'blog-post') {
                wordCountSelect.innerHTML = `
                    <option value="auto">Auto (based on competitors)</option>
                    <option value="800-1200">800-1,200 words</option>
                    <option value="1200-2000">1,200-2,000 words</option>
                    <option value="2000+">2,000+ words</option>
                `;
            } else if (contentType === 'service-page') {
                wordCountSelect.innerHTML = `
                    <option value="auto">Auto (based on competitors)</option>
                    <option value="500-800">500-800 words</option>
                    <option value="800-1200">800-1,200 words</option>
                    <option value="1200-2000">1,200-2,000 words</option>
                `;
            }
        }

        function analyzeSitemap(url) {
            if (url) {
                // Simulate sitemap analysis
                console.log('Analyzing sitemap:', url);
                showNotification('Sitemap analyzed successfully', 'success');
            }
        }
    </script>
</body>
</html>