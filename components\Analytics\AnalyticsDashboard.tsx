/**
 * Analytics Dashboard Component
 * Comprehensive SEO analytics with performance metrics, competitor tracking, and insights
 */

'use client'

import React, { useState, useEffect } from 'react'
import { useNotifications } from '@/components/Notifications'
import Card from '@/components/UI/Card'
import Button from '@/components/UI/Button'
import Badge from '@/components/UI/Badge'
import LoadingSpinner from '@/components/UI/LoadingSpinner'
import {
  ChartBarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  EyeIcon,
  CursorArrowRaysIcon,
  UserGroupIcon,
  ClockIcon,
  GlobeAltIcon,
  MagnifyingGlassIcon,
  ArrowTopRightOnSquareIcon,
  CalendarIcon,
  FunnelIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'

interface AnalyticsDashboardProps {
  timeRange?: '7d' | '30d' | '90d' | '1y'
  autoRefresh?: boolean
}

interface MetricData {
  current: number
  previous: number
  change: number
  trend: 'up' | 'down' | 'neutral'
  formatted: string
}

interface TrafficSource {
  source: string
  visitors: number
  percentage: number
  change: number
  color: string
}

interface TopKeyword {
  keyword: string
  position: number
  volume: number
  traffic: number
  difficulty: number
  change: number
  url: string
}

interface CompetitorMetric {
  competitor: string
  domain: string
  visibility: number
  keywords: number
  traffic: number
  change: number
  logo?: string
}

interface AlertItem {
  id: string
  type: 'warning' | 'info' | 'success' | 'error'
  title: string
  message: string
  timestamp: string
  action?: {
    label: string
    handler: () => void
  }
}

export default function AnalyticsDashboard({
  timeRange = '30d',
  autoRefresh = true
}: AnalyticsDashboardProps) {
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange)
  const [isLoading, setIsLoading] = useState(false)
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())
  const [activeTab, setActiveTab] = useState<'overview' | 'keywords' | 'competitors' | 'traffic'>('overview')
  
  // Analytics data states
  const [metrics, setMetrics] = useState<Record<string, MetricData>>({})
  const [trafficSources, setTrafficSources] = useState<TrafficSource[]>([])
  const [topKeywords, setTopKeywords] = useState<TopKeyword[]>([])
  const [competitors, setCompetitors] = useState<CompetitorMetric[]>([])
  const [alerts, setAlerts] = useState<AlertItem[]>([])
  
  const { success: notifySuccess, error: notifyError } = useNotifications()

  // Mock data - in real app this would come from API
  useEffect(() => {
    loadAnalyticsData()
  }, [selectedTimeRange])

  // Auto-refresh data
  useEffect(() => {
    if (!autoRefresh) return
    
    const interval = setInterval(() => {
      loadAnalyticsData(true)
    }, 300000) // Refresh every 5 minutes
    
    return () => clearInterval(interval)
  }, [autoRefresh, selectedTimeRange])

  const loadAnalyticsData = async (silent = false) => {
    if (!silent) setIsLoading(true)
    
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, silent ? 500 : 1500))
      
      // Mock metrics data
      setMetrics({
        totalTraffic: {
          current: 145820,
          previous: 128450,
          change: 13.5,
          trend: 'up',
          formatted: '145.8K'
        },
        organicTraffic: {
          current: 98760,
          previous: 89230,
          change: 10.7,
          trend: 'up', 
          formatted: '98.7K'
        },
        avgPosition: {
          current: 12.4,
          previous: 15.2,
          change: -18.4,
          trend: 'up',
          formatted: '12.4'
        },
        totalKeywords: {
          current: 2847,
          previous: 2654,
          change: 7.3,
          trend: 'up',
          formatted: '2,847'
        },
        clickThroughRate: {
          current: 3.2,
          previous: 2.8,
          change: 14.3,
          trend: 'up',
          formatted: '3.2%'
        },
        bounceRate: {
          current: 42.1,
          previous: 45.8,
          change: -8.1,
          trend: 'up',
          formatted: '42.1%'
        }
      })
      
      // Mock traffic sources
      setTrafficSources([
        { source: 'Organic Search', visitors: 98760, percentage: 67.7, change: 10.7, color: 'bg-green-500' },
        { source: 'Direct', visitors: 23480, percentage: 16.1, change: 5.2, color: 'bg-blue-500' },
        { source: 'Social Media', visitors: 12890, percentage: 8.8, change: 22.1, color: 'bg-purple-500' },
        { source: 'Referral', visitors: 7450, percentage: 5.1, change: -3.4, color: 'bg-orange-500' },
        { source: 'Email', visitors: 3240, percentage: 2.3, change: 8.9, color: 'bg-pink-500' }
      ])
      
      // Mock top keywords
      setTopKeywords([
        {
          keyword: 'digital marketing',
          position: 3,
          volume: 49500,
          traffic: 15420,
          difficulty: 78,
          change: -1,
          url: '/services/digital-marketing'
        },
        {
          keyword: 'seo services',
          position: 5,
          volume: 33100,
          traffic: 8930,
          difficulty: 82,
          change: 2,
          url: '/services/seo'
        },
        {
          keyword: 'content marketing',
          position: 7,
          volume: 27300,
          traffic: 6420,
          difficulty: 65,
          change: -3,
          url: '/services/content-marketing'
        },
        {
          keyword: 'ppc advertising',
          position: 4,
          volume: 22100,
          traffic: 5830,
          difficulty: 71,
          change: 1,
          url: '/services/ppc'
        },
        {
          keyword: 'social media marketing',
          position: 8,
          volume: 18900,
          traffic: 4220,
          difficulty: 58,
          change: -2,
          url: '/services/social-media'
        }
      ])
      
      // Mock competitor data
      setCompetitors([
        {
          competitor: 'CompetitorA',
          domain: 'competitora.com',
          visibility: 82.4,
          keywords: 3240,
          traffic: 185000,
          change: 5.2
        },
        {
          competitor: 'MarketLeader',
          domain: 'marketleader.com',
          visibility: 95.8,
          keywords: 4850,
          traffic: 320000,
          change: -2.1
        },
        {
          competitor: 'DigitalPro',
          domain: 'digitalpro.com',
          visibility: 67.3,
          keywords: 2890,
          traffic: 145000,
          change: 8.7
        },
        {
          competitor: 'SEOMaster',
          domain: 'seomaster.com',
          visibility: 78.9,
          keywords: 3560,
          traffic: 210000,
          change: 3.4
        }
      ])
      
      // Mock alerts
      setAlerts([
        {
          id: 'alert1',
          type: 'warning',
          title: 'Ranking Drop Alert',
          message: 'Your main keyword "digital marketing" dropped 2 positions in the last 7 days',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 'alert2',
          type: 'success',
          title: 'Traffic Milestone',
          message: 'Congratulations! You\'ve reached 100K monthly organic visitors',
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 'alert3',
          type: 'info',
          title: 'New Competitor Detected',
          message: 'A new competitor "newplayer.com" is targeting your keywords',
          timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString()
        }
      ])
      
      setLastUpdated(new Date())
      
      if (silent) {
        notifySuccess('Analytics data refreshed')
      }
    } catch (error) {
      notifyError('Failed to load analytics data')
    } finally {
      if (!silent) setIsLoading(false)
    }
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return num.toString()
  }

  const getChangeIcon = (trend: string, change: number) => {
    if (change > 0) {
      return <TrendingUpIcon className="h-4 w-4 text-green-500" />
    } else if (change < 0) {
      return <TrendingDownIcon className="h-4 w-4 text-red-500" />
    }
    return <div className="h-4 w-4" />
  }

  const getChangeColor = (change: number) => {
    if (change > 0) return 'text-green-600 dark:text-green-400'
    if (change < 0) return 'text-red-600 dark:text-red-400'
    return 'text-gray-600 dark:text-gray-400'
  }

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'warning': return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
      case 'error': return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
      case 'success': return <CheckCircleIcon className="h-5 w-5 text-green-500" />
      case 'info': return <InformationCircleIcon className="h-5 w-5 text-blue-500" />
      default: return <InformationCircleIcon className="h-5 w-5 text-gray-500" />
    }
  }

  const formatTimeAgo = (timestamp: string) => {
    const diff = Date.now() - new Date(timestamp).getTime()
    const hours = Math.floor(diff / (1000 * 60 * 60))
    if (hours < 24) return `${hours}h ago`
    const days = Math.floor(hours / 24)
    return `${days}d ago`
  }

  const MetricCard = ({ title, metric, icon: Icon }: { title: string, metric: MetricData, icon: React.ComponentType<{ className?: string }> }) => (
    <Card className="p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-gray-600 dark:text-gray-400">{title}</p>
          <p className="text-2xl font-bold text-gray-900 dark:text-gray-100 mt-1">
            {metric.formatted}
          </p>
          <div className="flex items-center mt-2">
            {getChangeIcon(metric.trend, metric.change)}
            <span className={`text-sm font-medium ml-1 ${getChangeColor(metric.change)}`}>
              {metric.change > 0 ? '+' : ''}{metric.change.toFixed(1)}%
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
              vs {selectedTimeRange}
            </span>
          </div>
        </div>
        <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
          <Icon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
        </div>
      </div>
    </Card>
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading analytics data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Analytics Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            SEO performance metrics and competitor insights
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => loadAnalyticsData(true)}
          >
            <ArrowPathIcon className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Last Updated */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
          <ClockIcon className="h-4 w-4" />
          <span>Last updated: {lastUpdated.toLocaleTimeString()}</span>
        </div>
        
        {autoRefresh && (
          <Badge variant="success" size="sm">
            Auto-refresh enabled
          </Badge>
        )}
      </div>

      {/* Alerts */}
      {alerts.length > 0 && (
        <Card className="p-4">
          <h3 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-3">
            Recent Alerts
          </h3>
          <div className="space-y-3">
            {alerts.slice(0, 3).map((alert) => (
              <div key={alert.id} className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                {getAlertIcon(alert.type)}
                <div className="flex-1">
                  <div className="font-medium text-gray-900 dark:text-gray-100">
                    {alert.title}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {alert.message}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {formatTimeAgo(alert.timestamp)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <MetricCard
          title="Total Traffic"
          metric={metrics.totalTraffic}
          icon={EyeIcon}
        />
        <MetricCard
          title="Organic Traffic"
          metric={metrics.organicTraffic}
          icon={MagnifyingGlassIcon}
        />
        <MetricCard
          title="Average Position"
          metric={metrics.avgPosition}
          icon={TrendingUpIcon}
        />
        <MetricCard
          title="Total Keywords"
          metric={metrics.totalKeywords}
          icon={ChartBarIcon}
        />
        <MetricCard
          title="Click-Through Rate"
          metric={metrics.clickThroughRate}
          icon={CursorArrowRaysIcon}
        />
        <MetricCard
          title="Bounce Rate"
          metric={metrics.bounceRate}
          icon={UserGroupIcon}
        />
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <div className="flex space-x-8">
          <button
            onClick={() => setActiveTab('overview')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'overview'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <ChartBarIcon className="h-4 w-4 inline mr-2" />
            Overview
          </button>
          <button
            onClick={() => setActiveTab('keywords')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'keywords'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <MagnifyingGlassIcon className="h-4 w-4 inline mr-2" />
            Keywords
          </button>
          <button
            onClick={() => setActiveTab('competitors')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'competitors'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <UserGroupIcon className="h-4 w-4 inline mr-2" />
            Competitors
          </button>
          <button
            onClick={() => setActiveTab('traffic')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'traffic'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <GlobeAltIcon className="h-4 w-4 inline mr-2" />
            Traffic Sources
          </button>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Traffic Sources */}
          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Traffic Sources
            </h3>
            <div className="space-y-4">
              {trafficSources.map((source, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${source.color}`} />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      {source.source}
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {formatNumber(source.visitors)}
                    </div>
                    <div className={`text-xs ${getChangeColor(source.change)}`}>
                      {source.change > 0 ? '+' : ''}{source.change}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          {/* Top Keywords Performance */}
          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Top Keywords
            </h3>
            <div className="space-y-3">
              {topKeywords.slice(0, 5).map((keyword, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div>
                    <div className="font-medium text-gray-900 dark:text-gray-100">
                      {keyword.keyword}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Position #{keyword.position} • {formatNumber(keyword.traffic)} visits
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`text-sm font-medium ${getChangeColor(keyword.change)}`}>
                      {keyword.change > 0 ? '+' : ''}{keyword.change}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {formatNumber(keyword.volume)} vol
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>
      )}

      {activeTab === 'keywords' && (
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Keyword Performance
            </h3>
            <Button size="sm">
              <ArrowTopRightOnSquareIcon className="h-4 w-4 mr-2" />
              View All Keywords
            </Button>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">Keyword</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">Position</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">Volume</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">Traffic</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">Difficulty</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">Change</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">URL</th>
                </tr>
              </thead>
              <tbody>
                {topKeywords.map((keyword, index) => (
                  <tr key={index} className="border-b border-gray-100 dark:border-gray-800">
                    <td className="py-3 px-4">
                      <div className="font-medium text-gray-900 dark:text-gray-100">
                        {keyword.keyword}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <Badge variant={keyword.position <= 3 ? 'success' : keyword.position <= 10 ? 'warning' : 'secondary'}>
                        #{keyword.position}
                      </Badge>
                    </td>
                    <td className="py-3 px-4 text-gray-600 dark:text-gray-400">
                      {formatNumber(keyword.volume)}
                    </td>
                    <td className="py-3 px-4 text-gray-600 dark:text-gray-400">
                      {formatNumber(keyword.traffic)}
                    </td>
                    <td className="py-3 px-4">
                      <Badge variant={keyword.difficulty >= 80 ? 'error' : keyword.difficulty >= 60 ? 'warning' : 'success'}>
                        {keyword.difficulty}%
                      </Badge>
                    </td>
                    <td className="py-3 px-4">
                      <div className={`flex items-center ${getChangeColor(keyword.change)}`}>
                        {getChangeIcon('neutral', keyword.change)}
                        <span className="ml-1">{keyword.change > 0 ? '+' : ''}{keyword.change}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <a
                        href={keyword.url}
                        className="text-blue-600 dark:text-blue-400 hover:underline text-sm"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {keyword.url}
                      </a>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
      )}

      {activeTab === 'competitors' && (
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Competitor Analysis
            </h3>
            <Button size="sm">
              <ArrowTopRightOnSquareIcon className="h-4 w-4 mr-2" />
              Detailed Analysis
            </Button>
          </div>
          
          <div className="space-y-4">
            {competitors.map((competitor, index) => (
              <div key={index} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                      <span className="text-white font-medium text-sm">
                        {competitor.competitor.substring(0, 2).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 dark:text-gray-100">
                        {competitor.competitor}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        {competitor.domain}
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-4 gap-6 text-center">
                    <div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">Visibility</div>
                      <div className="font-medium text-gray-900 dark:text-gray-100">
                        {competitor.visibility}%
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">Keywords</div>
                      <div className="font-medium text-gray-900 dark:text-gray-100">
                        {formatNumber(competitor.keywords)}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">Traffic</div>
                      <div className="font-medium text-gray-900 dark:text-gray-100">
                        {formatNumber(competitor.traffic)}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">Change</div>
                      <div className={`font-medium ${getChangeColor(competitor.change)}`}>
                        {competitor.change > 0 ? '+' : ''}{competitor.change}%
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {activeTab === 'traffic' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Traffic Sources Breakdown
            </h3>
            <div className="space-y-4">
              {trafficSources.map((source, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${source.color}`} />
                      <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {source.source}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {source.percentage}%
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${source.color}`}
                      style={{ width: `${source.percentage}%` }}
                    />
                  </div>
                  <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                    <span>{formatNumber(source.visitors)} visitors</span>
                    <span className={getChangeColor(source.change)}>
                      {source.change > 0 ? '+' : ''}{source.change}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Traffic Trends
            </h3>
            <div className="bg-gray-100 dark:bg-gray-800 rounded-lg h-64 flex items-center justify-center">
              <div className="text-center">
                <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500 dark:text-gray-400">Traffic trend chart will be rendered here</p>
                <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                  Integration with charting library (Chart.js, Recharts, etc.)
                </p>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  )
}