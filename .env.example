# ================================================================
# SEO SAAS ENVIRONMENT CONFIGURATION
# Enterprise-Grade Environment Variables Template
# ================================================================

# ----------------------------------------------------------------
# APPLICATION CONFIGURATION
# ----------------------------------------------------------------
NODE_ENV=development
PORT=5000
APP_NAME="SEO SAAS"
APP_VERSION=1.0.0
APP_URL=http://localhost:3000
API_URL=http://localhost:5000

# ----------------------------------------------------------------
# DATABASE CONFIGURATION - SUPABASE
# ----------------------------------------------------------------
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
DATABASE_URL=postgresql://postgres:password@localhost:5432/seo_saas

# ----------------------------------------------------------------
# AI SERVICES CONFIGURATION
# ----------------------------------------------------------------
# OpenAI GPT-4o for Sequential AI Thinking Engine
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4o
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.3

# ----------------------------------------------------------------
# COMPETITOR INTELLIGENCE & SERP ANALYSIS
# ----------------------------------------------------------------
# Serper.dev - Google SERP API for real-time competitor discovery
SERPER_API_KEY=your_serper_api_key_here
NEXT_PUBLIC_SERPER_API_KEY=your_serper_api_key_here

# Firecrawl - Web scraping API for competitor content extraction
FIRECRAWL_API_KEY=your_firecrawl_api_key_here
NEXT_PUBLIC_FIRECRAWL_API_KEY=your_firecrawl_api_key_here

# ----------------------------------------------------------------
# UNIVERSAL NICHE ADAPTATION CONFIGURATION
# ----------------------------------------------------------------
NICHE_CACHE_TTL=86400
INDUSTRY_DATA_REFRESH_INTERVAL=3600
AUTHORITY_DOMAINS_UPDATE_INTERVAL=43200

# ----------------------------------------------------------------
# SEQUENTIAL AI THINKING ENGINE
# ----------------------------------------------------------------
AI_REASONING_DEPTH=5
AI_QUALITY_THRESHOLD=85
AI_MAX_THINKING_ITERATIONS=6
AI_CHAIN_CACHE_TTL=3600

# ----------------------------------------------------------------
# REAL DATA VALIDATION SYSTEM
# ----------------------------------------------------------------
DEMO_DATA_STRICT_MODE=true
VALIDATION_CONFIDENCE_THRESHOLD=80
DEMO_DATA_BLOCK_DURATION=300
VALIDATION_CACHE_TTL=1800

# ----------------------------------------------------------------
# AUTHORITY LINK DISCOVERY & VALIDATION
# ----------------------------------------------------------------
AUTHORITY_LINK_DISCOVERY_ENABLED=true
DOMAIN_AUTHORITY_MIN_THRESHOLD=30
LINK_VALIDATION_TIMEOUT=10000
AUTHORITY_CACHE_TTL=7200

# ----------------------------------------------------------------
# ENTERPRISE SECURITY CONFIGURATION
# ----------------------------------------------------------------
# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_key_here
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Session Configuration
SESSION_SECRET=your_super_secure_session_secret_key_here
SESSION_TIMEOUT=1800000
SESSION_SECURE=false

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_LOGIN_MAX=5
RATE_LIMIT_LOGIN_WINDOW=900000

# ----------------------------------------------------------------
# BILLING & SUBSCRIPTION MANAGEMENT
# ----------------------------------------------------------------
# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Subscription Configuration
FREE_TIER_CONTENT_LIMIT=10
PRO_TIER_CONTENT_LIMIT=500
ENTERPRISE_TIER_CONTENT_LIMIT=2000