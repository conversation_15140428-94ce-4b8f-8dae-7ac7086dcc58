# 🚀 COMPREHENSIVE FRONTEND DEVELOPMENT PROMPT
# Professional SEO SAAS Frontend with Three.js & Modern Frameworks

## 🎯 **PROJECT OVERVIEW**

Create a cutting-edge, enterprise-grade frontend for a comprehensive SEO SAAS application using Three.js, React, Next.js 14, and modern web technologies. The frontend should rival big tech platforms (Google Analytics, Microsoft Azure, AWS Console) with professional design quality equivalent to 20+ years of design experience.

## 🏗️ **TECHNICAL ARCHITECTURE**

### **Core Technology Stack**
```javascript
const techStack = {
  framework: "Next.js 14 with App Router",
  language: "TypeScript",
  ui: "React 18 with Hooks",
  styling: "Tailwind CSS + Custom Design System",
  animations: "Framer Motion + Three.js",
  3d: "Three.js + React Three Fiber",
  state: "Zustand + React Query",
  auth: "Supabase Auth",
  charts: "Recharts + D3.js",
  forms: "React Hook Form + Zod",
  icons: "Heroicons + Lucide React",
  deployment: "Vercel"
};
```

### **Project Structure**
```
seo-saas-frontend/
├── app/                          # Next.js 14 App Router
│   ├── (auth)/                   # Authentication routes
│   │   ├── login/
│   │   ├── register/
│   │   └── forgot-password/
│   ├── (dashboard)/              # Protected dashboard routes
│   │   ├── dashboard/            # Main dashboard
│   │   ├── content-generator/    # AI content generation
│   │   ├── seo-analysis/         # SEO analysis tools
│   │   ├── competitor-analysis/  # Competitor research
│   │   ├── projects/             # Project management
│   │   ├── analytics/            # Performance analytics
│   │   ├── content-library/      # Content management
│   │   ├── authority-links/      # Link building tools
│   │   ├── bulk-processing/      # Bulk operations
│   │   ├── settings/             # User settings
│   │   └── profile/              # User profile
│   ├── (marketing)/              # Public marketing pages
│   │   ├── features/
│   │   ├── pricing/
│   │   └── demo/
│   ├── api/                      # API routes
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/                   # Reusable components
│   ├── ui/                      # Base UI components
│   ├── dashboard/               # Dashboard-specific components
│   ├── three/                   # Three.js components
│   ├── charts/                  # Data visualization
│   ├── forms/                   # Form components
│   └── layout/                  # Layout components
├── lib/                         # Utilities and configurations
├── hooks/                       # Custom React hooks
├── stores/                      # State management
├── types/                       # TypeScript definitions
└── public/                      # Static assets
```

## 🎨 **DESIGN SYSTEM REQUIREMENTS**

### **Professional Design Standards**
- **Color System**: Semantic color scales (50-900 variations) with dark mode support
- **Typography**: Professional font hierarchy with perfect readability
- **Spacing**: Consistent 8px grid system
- **Components**: Reusable, accessible, and performant
- **Animations**: Smooth, purposeful micro-interactions
- **3D Elements**: Subtle Three.js enhancements for premium feel

### **Enterprise UI Components**
```typescript
// Required UI Component Library
const uiComponents = [
  'Button', 'Input', 'Select', 'Textarea', 'Checkbox', 'Radio',
  'Card', 'Modal', 'Dropdown', 'Tooltip', 'Badge', 'Avatar',
  'Table', 'Pagination', 'Tabs', 'Accordion', 'Progress',
  'Skeleton', 'Loading', 'Toast', 'Alert', 'Breadcrumb',
  'Sidebar', 'Header', 'Footer', 'Navigation', 'Search'
];
```

## 🔐 **AUTHENTICATION SYSTEM**

### **Authentication Flow**
```typescript
// Authentication Requirements
const authSystem = {
  provider: "Supabase Auth",
  features: [
    "Email/Password login",
    "Social login (Google, GitHub)",
    "Password reset",
    "Email verification",
    "Session management",
    "Role-based access control"
  ],
  pages: [
    "/login",
    "/register", 
    "/forgot-password",
    "/verify-email",
    "/auth/callback"
  ],
  protection: "Middleware-based route protection"
};
```

## 🏢 **DASHBOARD ARCHITECTURE**

### **Big Tech Dashboard Design**
Create a professional dashboard that rivals Google Analytics, Microsoft Azure, and AWS Console:

```typescript
const dashboardLayout = {
  topNavigation: {
    logo: "SEO SAAS branding",
    globalSearch: "Intelligent search across all features",
    notifications: "Real-time system notifications",
    userMenu: "Profile, settings, logout"
  },
  
  sidebarNavigation: {
    sections: [
      {
        title: "Overview",
        items: ["Dashboard", "Quick Actions", "Recent Activity"]
      },
      {
        title: "Content Creation",
        items: ["AI Content Generator", "Content Library", "Templates"]
      },
      {
        title: "SEO Analysis",
        items: ["Keyword Research", "Competitor Analysis", "SERP Analysis"]
      },
      {
        title: "Performance",
        items: ["Analytics", "Reports", "Insights"]
      },
      {
        title: "Management",
        items: ["Projects", "Team", "Bulk Operations"]
      },
      {
        title: "Tools",
        items: ["Authority Links", "Sitemap Analysis", "Content Editor"]
      },
      {
        title: "Account",
        items: ["Settings", "Billing", "API Keys"]
      }
    ],
    features: ["Collapsible sections", "Search", "Favorites", "Customization"]
  },
  
  contentArea: {
    breadcrumbs: "Clear navigation path",
    pageHeader: "Title, description, actions",
    mainContent: "Feature-specific content",
    contextualHelp: "Inline help and tooltips"
  }
};
```

## 🎯 **CORE FEATURES TO IMPLEMENT**

### **1. AI Content Generator**
```typescript
const contentGenerator = {
  interface: "Professional form with real-time preview",
  features: [
    "Universal keyword input (ANY industry)",
    "Target country selection",
    "Content type selection (blog, article, guide, etc.)",
    "Tone selection (professional, casual, friendly)",
    "Length selection (short, medium, long)",
    "Real-time generation progress",
    "Content preview with editing",
    "Export options (copy, download, save)"
  ],
  validation: "Real data validation (reject demo/mock data)",
  api: "POST /api/seo/generate-content"
};
```

### **2. SEO Analysis Suite**
```typescript
const seoAnalysis = {
  keywordResearch: "Comprehensive keyword analysis",
  competitorAnalysis: "Real competitor intelligence",
  serpAnalysis: "Search result analysis",
  contentOptimization: "SurferSEO-style optimization",
  features: [
    "Keyword difficulty analysis",
    "Search volume data",
    "Competitor content analysis",
    "Content gap identification",
    "Optimization recommendations"
  ]
};
```

### **3. Project Management**
```typescript
const projectManagement = {
  features: [
    "Create and manage SEO projects",
    "Team collaboration",
    "Content organization",
    "Progress tracking",
    "Performance monitoring"
  ],
  interface: "Kanban boards + list views",
  api: "/api/projects/*"
};
```

### **4. Analytics Dashboard**
```typescript
const analytics = {
  charts: "Interactive charts with Recharts + D3.js",
  metrics: [
    "Content generation statistics",
    "SEO performance metrics",
    "User engagement data",
    "Project progress tracking"
  ],
  realTime: "Live data updates with WebSocket",
  export: "PDF/Excel report generation"
};
```

## 🌟 **THREE.JS INTEGRATION**

### **3D Enhancement Areas**
```typescript
const threeJsFeatures = {
  landingPage: {
    heroSection: "Animated 3D SEO visualization",
    features: "Interactive 3D feature showcase",
    background: "Subtle particle systems"
  },
  
  dashboard: {
    dataVisualization: "3D charts for complex data",
    loading: "Elegant 3D loading animations",
    transitions: "Smooth 3D page transitions"
  },
  
  contentGenerator: {
    progress: "3D progress visualization",
    preview: "3D content structure preview"
  },
  
  implementation: "React Three Fiber + Drei helpers"
};
```

## 📱 **RESPONSIVE DESIGN**

### **Device Support**
- **Desktop**: 1920px+ (primary focus)
- **Laptop**: 1366px - 1919px
- **Tablet**: 768px - 1365px
- **Mobile**: 320px - 767px

### **Responsive Strategy**
```css
/* Mobile-first approach with progressive enhancement */
.responsive-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
```

## ⚡ **PERFORMANCE REQUIREMENTS**

### **Performance Targets**
- **First Contentful Paint**: <1.5s
- **Largest Contentful Paint**: <2.5s
- **Cumulative Layout Shift**: <0.1
- **First Input Delay**: <100ms
- **Time to Interactive**: <3s

### **Optimization Strategies**
```typescript
const optimizations = {
  codesplitting: "Route-based and component-based splitting",
  lazyLoading: "Images, components, and routes",
  caching: "Aggressive caching with SWR/React Query",
  bundleOptimization: "Tree shaking and dead code elimination",
  imageOptimization: "Next.js Image component with WebP",
  fontOptimization: "Font display swap and preloading"
};
```

## 🔒 **SECURITY IMPLEMENTATION**

### **Security Measures**
```typescript
const security = {
  authentication: "Supabase Auth with JWT tokens",
  authorization: "Role-based access control",
  dataValidation: "Zod schema validation",
  xssProtection: "Content Security Policy headers",
  csrfProtection: "CSRF tokens for forms",
  rateLimit: "API rate limiting",
  encryption: "HTTPS everywhere"
};
```

## 🎨 **ANIMATION & INTERACTIONS**

### **Animation Library**
```typescript
const animations = {
  library: "Framer Motion",
  types: [
    "Page transitions",
    "Component enter/exit",
    "Hover effects",
    "Loading states",
    "Micro-interactions",
    "Gesture handling"
  ],
  performance: "GPU-accelerated transforms",
  accessibility: "Respect prefers-reduced-motion"
};
```

## 📊 **DATA VISUALIZATION**

### **Chart Requirements**
```typescript
const dataVisualization = {
  library: "Recharts + D3.js",
  chartTypes: [
    "Line charts (performance trends)",
    "Bar charts (keyword rankings)",
    "Pie charts (content distribution)",
    "Area charts (traffic growth)",
    "Heatmaps (content performance)",
    "Scatter plots (keyword difficulty)"
  ],
  features: [
    "Interactive tooltips",
    "Zoom and pan",
    "Real-time updates",
    "Export functionality",
    "Responsive design"
  ]
};
```

## 🌐 **API INTEGRATION**

### **Backend API Endpoints**
```typescript
const apiEndpoints = {
  auth: {
    "POST /api/auth/login": "User authentication",
    "POST /api/auth/register": "User registration",
    "POST /api/auth/logout": "User logout"
  },
  
  content: {
    "POST /api/seo/generate-content": "AI content generation",
    "GET /api/content/library": "Content library",
    "POST /api/content/save": "Save generated content"
  },
  
  seo: {
    "POST /api/seo/analyze": "SEO analysis",
    "GET /api/seo/keywords": "Keyword research",
    "POST /api/seo/competitors": "Competitor analysis"
  },
  
  projects: {
    "GET /api/projects": "Get all projects",
    "POST /api/projects": "Create project",
    "PUT /api/projects/:id": "Update project",
    "DELETE /api/projects/:id": "Delete project"
  },
  
  analytics: {
    "GET /api/analytics/dashboard": "Dashboard metrics",
    "GET /api/analytics/reports": "Performance reports"
  }
};
```

## 🚀 **DEPLOYMENT & INFRASTRUCTURE**

### **Deployment Strategy**
```typescript
const deployment = {
  platform: "Vercel",
  features: [
    "Automatic deployments from Git",
    "Preview deployments for PRs",
    "Edge functions for API routes",
    "Global CDN distribution",
    "Custom domain support"
  ],
  environment: {
    development: "localhost:3000",
    staging: "staging.seosaas.com",
    production: "app.seosaas.com"
  }
};
```

## 📋 **DEVELOPMENT CHECKLIST**

### **Phase 1: Foundation (Week 1-2)**
- [ ] Project setup with Next.js 14 + TypeScript
- [ ] Design system implementation
- [ ] Authentication system
- [ ] Basic layout and navigation
- [ ] UI component library

### **Phase 2: Core Features (Week 3-4)**
- [ ] Dashboard implementation
- [ ] AI Content Generator
- [ ] SEO Analysis tools
- [ ] Project management
- [ ] Data visualization

### **Phase 3: Advanced Features (Week 5-6)**
- [ ] Three.js integration
- [ ] Advanced animations
- [ ] Performance optimization
- [ ] Mobile responsiveness
- [ ] Testing implementation

### **Phase 4: Polish & Deploy (Week 7-8)**
- [ ] Bug fixes and refinements
- [ ] Security audit
- [ ] Performance testing
- [ ] Documentation
- [ ] Production deployment

## 🎯 **SUCCESS CRITERIA**

### **Quality Standards**
- **Design**: Professional, modern, enterprise-grade
- **Performance**: Lighthouse score >90
- **Accessibility**: WCAG 2.1 AA compliance
- **Security**: Enterprise security standards
- **Functionality**: All features working flawlessly
- **Responsiveness**: Perfect on all devices
- **User Experience**: Intuitive and delightful

## 💻 **DETAILED IMPLEMENTATION GUIDE**

### **1. Project Initialization**
```bash
# Create Next.js 14 project with TypeScript
npx create-next-app@latest seo-saas-frontend --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"

# Install required dependencies
npm install @supabase/supabase-js @supabase/auth-helpers-nextjs
npm install framer-motion three @react-three/fiber @react-three/drei
npm install recharts d3 @types/d3
npm install react-hook-form @hookform/resolvers zod
npm install zustand @tanstack/react-query
npm install @heroicons/react lucide-react
npm install react-hot-toast sonner
npm install @radix-ui/react-dialog @radix-ui/react-dropdown-menu
npm install class-variance-authority clsx tailwind-merge
npm install next-themes

# Development dependencies
npm install -D @types/three @types/node
npm install -D prettier prettier-plugin-tailwindcss
npm install -D eslint-config-prettier
```

### **2. Environment Configuration**
```typescript
// .env.local
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NEXT_PUBLIC_API_URL=http://localhost:5000
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### **3. Core Configuration Files**

#### **tailwind.config.js**
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}
```

#### **app/globals.css**
```css
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* Smooth animations */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}
```

### **4. Authentication Setup**

#### **lib/supabase.ts**
```typescript
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export const createClient = () => createClientComponentClient()

export const createServerClient = () => createServerComponentClient({ cookies })

export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          subscription_tier: 'free' | 'pro' | 'enterprise'
          usage_count: number
          usage_limit: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          subscription_tier?: 'free' | 'pro' | 'enterprise'
          usage_count?: number
          usage_limit?: number
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          subscription_tier?: 'free' | 'pro' | 'enterprise'
          usage_count?: number
          usage_limit?: number
        }
      }
    }
  }
}
```

#### **middleware.ts**
```typescript
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  const supabase = createMiddlewareClient({ req, res })

  const {
    data: { session },
  } = await supabase.auth.getSession()

  // Protect dashboard routes
  if (req.nextUrl.pathname.startsWith('/dashboard') && !session) {
    return NextResponse.redirect(new URL('/login', req.url))
  }

  // Redirect authenticated users away from auth pages
  if ((req.nextUrl.pathname.startsWith('/login') ||
       req.nextUrl.pathname.startsWith('/register')) && session) {
    return NextResponse.redirect(new URL('/dashboard', req.url))
  }

  return res
}

export const config = {
  matcher: ['/dashboard/:path*', '/login', '/register']
}
```

### **5. State Management Setup**

#### **stores/auth-store.ts**
```typescript
import { create } from 'zustand'
import { User } from '@supabase/auth-helpers-nextjs'

interface AuthState {
  user: User | null
  loading: boolean
  setUser: (user: User | null) => void
  setLoading: (loading: boolean) => void
}

export const useAuthStore = create<AuthState>((set) => ({
  user: null,
  loading: true,
  setUser: (user) => set({ user }),
  setLoading: (loading) => set({ loading }),
}))
```

#### **stores/dashboard-store.ts**
```typescript
import { create } from 'zustand'

interface DashboardState {
  sidebarOpen: boolean
  currentPage: string
  notifications: Notification[]
  setSidebarOpen: (open: boolean) => void
  setCurrentPage: (page: string) => void
  addNotification: (notification: Notification) => void
  removeNotification: (id: string) => void
}

interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  timestamp: Date
}

export const useDashboardStore = create<DashboardState>((set, get) => ({
  sidebarOpen: true,
  currentPage: 'dashboard',
  notifications: [],
  setSidebarOpen: (open) => set({ sidebarOpen: open }),
  setCurrentPage: (page) => set({ currentPage: page }),
  addNotification: (notification) =>
    set({ notifications: [...get().notifications, notification] }),
  removeNotification: (id) =>
    set({ notifications: get().notifications.filter(n => n.id !== id) }),
}))
```

### **6. API Client Setup**

#### **lib/api-client.ts**
```typescript
import { createClient } from './supabase'

class APIClient {
  private baseURL: string
  private supabase = createClient()

  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'
  }

  private async getAuthHeaders() {
    const { data: { session } } = await this.supabase.auth.getSession()
    return {
      'Content-Type': 'application/json',
      ...(session?.access_token && {
        'Authorization': `Bearer ${session.access_token}`
      })
    }
  }

  async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const headers = await this.getAuthHeaders()

    const response = await fetch(`${this.baseURL}${endpoint}`, {
      ...options,
      headers: {
        ...headers,
        ...options.headers,
      },
    })

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`)
    }

    return response.json()
  }

  // Content Generation
  async generateContent(data: {
    keyword: string
    target_country: string
    content_type: string
    tone: string
    length: string
  }) {
    return this.request('/api/seo/generate-content', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  // Projects
  async getProjects() {
    return this.request('/api/projects')
  }

  async createProject(data: any) {
    return this.request('/api/projects', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  // Analytics
  async getDashboardMetrics() {
    return this.request('/api/analytics/dashboard')
  }

  // SEO Analysis
  async analyzeSEO(data: any) {
    return this.request('/api/seo/analyze', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }
}

export const apiClient = new APIClient()
```

---

**🎯 IMPLEMENTATION PRIORITY**

1. **Start with authentication and basic layout**
2. **Implement core dashboard structure**
3. **Build content generator as primary feature**
4. **Add SEO analysis tools**
5. **Integrate Three.js enhancements**
6. **Polish with animations and optimizations**

### **7. Core UI Components**

#### **components/ui/button.tsx**
```typescript
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
```

#### **components/dashboard/sidebar.tsx**
```typescript
'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import {
  HomeIcon,
  DocumentTextIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  FolderIcon,
  CogIcon,
  UserIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline'
import { cn } from '@/lib/utils'
import { useDashboardStore } from '@/stores/dashboard-store'

const navigation = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: HomeIcon,
    description: 'Overview and quick actions'
  },
  {
    name: 'Content Generator',
    href: '/dashboard/content-generator',
    icon: DocumentTextIcon,
    description: 'AI-powered content creation'
  },
  {
    name: 'SEO Analysis',
    href: '/dashboard/seo-analysis',
    icon: MagnifyingGlassIcon,
    description: 'Keyword and competitor research'
  },
  {
    name: 'Projects',
    href: '/dashboard/projects',
    icon: FolderIcon,
    description: 'Manage your SEO projects'
  },
  {
    name: 'Analytics',
    href: '/dashboard/analytics',
    icon: ChartBarIcon,
    description: 'Performance metrics and reports'
  },
  {
    name: 'Settings',
    href: '/dashboard/settings',
    icon: CogIcon,
    description: 'Account and preferences'
  },
  {
    name: 'Profile',
    href: '/dashboard/profile',
    icon: UserIcon,
    description: 'User profile and billing'
  },
]

export function Sidebar() {
  const pathname = usePathname()
  const { sidebarOpen, setSidebarOpen } = useDashboardStore()

  return (
    <motion.div
      initial={false}
      animate={{ width: sidebarOpen ? 280 : 80 }}
      transition={{ duration: 0.3, ease: 'easeInOut' }}
      className="relative flex flex-col bg-card border-r border-border h-full"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <AnimatePresence mode="wait">
          {sidebarOpen && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
              className="flex items-center space-x-3"
            >
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-sm">S</span>
              </div>
              <span className="font-semibold text-foreground">SEO SAAS</span>
            </motion.div>
          )}
        </AnimatePresence>

        <button
          onClick={() => setSidebarOpen(!sidebarOpen)}
          className="p-1.5 rounded-md hover:bg-accent transition-colors"
        >
          {sidebarOpen ? (
            <ChevronLeftIcon className="w-4 h-4" />
          ) : (
            <ChevronRightIcon className="w-4 h-4" />
          )}
        </button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navigation.map((item) => {
          const isActive = pathname === item.href

          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'flex items-center space-x-3 px-3 py-2.5 rounded-lg transition-colors relative group',
                isActive
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:text-foreground hover:bg-accent'
              )}
            >
              <item.icon className="w-5 h-5 flex-shrink-0" />

              <AnimatePresence mode="wait">
                {sidebarOpen && (
                  <motion.div
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -10 }}
                    transition={{ duration: 0.2 }}
                    className="flex-1 min-w-0"
                  >
                    <div className="font-medium">{item.name}</div>
                    <div className="text-xs opacity-70 truncate">
                      {item.description}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Tooltip for collapsed state */}
              {!sidebarOpen && (
                <div className="absolute left-full ml-2 px-2 py-1 bg-popover text-popover-foreground text-sm rounded-md shadow-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
                  {item.name}
                </div>
              )}
            </Link>
          )
        })}
      </nav>
    </motion.div>
  )
}
```

### **8. Three.js Integration**

#### **components/three/hero-scene.tsx**
```typescript
'use client'

import { useRef, useMemo } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { Points, PointMaterial, Sphere } from '@react-three/drei'
import * as THREE from 'three'

function ParticleField() {
  const ref = useRef<THREE.Points>(null!)

  const particlesPosition = useMemo(() => {
    const positions = new Float32Array(5000 * 3)

    for (let i = 0; i < 5000; i++) {
      positions[i * 3] = (Math.random() - 0.5) * 10
      positions[i * 3 + 1] = (Math.random() - 0.5) * 10
      positions[i * 3 + 2] = (Math.random() - 0.5) * 10
    }

    return positions
  }, [])

  useFrame((state) => {
    if (ref.current) {
      ref.current.rotation.x = state.clock.elapsedTime * 0.05
      ref.current.rotation.y = state.clock.elapsedTime * 0.075
    }
  })

  return (
    <Points ref={ref} positions={particlesPosition} stride={3} frustumCulled={false}>
      <PointMaterial
        transparent
        color="#3b82f6"
        size={0.005}
        sizeAttenuation={true}
        depthWrite={false}
      />
    </Points>
  )
}

function FloatingOrb({ position }: { position: [number, number, number] }) {
  const ref = useRef<THREE.Mesh>(null!)

  useFrame((state) => {
    if (ref.current) {
      ref.current.position.y = position[1] + Math.sin(state.clock.elapsedTime) * 0.1
      ref.current.rotation.x += 0.01
      ref.current.rotation.y += 0.01
    }
  })

  return (
    <Sphere ref={ref} position={position} args={[0.1, 32, 32]}>
      <meshStandardMaterial color="#8b5cf6" emissive="#8b5cf6" emissiveIntensity={0.2} />
    </Sphere>
  )
}

export function HeroScene() {
  return (
    <div className="absolute inset-0 -z-10">
      <Canvas
        camera={{ position: [0, 0, 5], fov: 75 }}
        style={{ background: 'transparent' }}
      >
        <ambientLight intensity={0.5} />
        <pointLight position={[10, 10, 10]} />

        <ParticleField />

        <FloatingOrb position={[-2, 1, 0]} />
        <FloatingOrb position={[2, -1, 0]} />
        <FloatingOrb position={[0, 2, -1]} />
      </Canvas>
    </div>
  )
}
```

#### **components/three/data-visualization.tsx**
```typescript
'use client'

import { useRef, useMemo } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import { Text, Box } from '@react-three/drei'
import * as THREE from 'three'

interface DataPoint {
  value: number
  label: string
  color: string
}

interface Data3DChartProps {
  data: DataPoint[]
  title: string
}

function BarChart3D({ data }: { data: DataPoint[] }) {
  const groupRef = useRef<THREE.Group>(null!)

  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1
    }
  })

  const maxValue = Math.max(...data.map(d => d.value))

  return (
    <group ref={groupRef}>
      {data.map((item, index) => {
        const height = (item.value / maxValue) * 3
        const x = (index - data.length / 2) * 0.8

        return (
          <group key={index} position={[x, height / 2, 0]}>
            <Box args={[0.6, height, 0.6]}>
              <meshStandardMaterial color={item.color} />
            </Box>

            <Text
              position={[0, -height / 2 - 0.3, 0]}
              fontSize={0.2}
              color="#666"
              anchorX="center"
              anchorY="middle"
            >
              {item.label}
            </Text>

            <Text
              position={[0, height / 2 + 0.2, 0]}
              fontSize={0.15}
              color="#333"
              anchorX="center"
              anchorY="middle"
            >
              {item.value}
            </Text>
          </group>
        )
      })}
    </group>
  )
}

export function Data3DChart({ data, title }: Data3DChartProps) {
  return (
    <div className="w-full h-96 bg-card rounded-lg border border-border overflow-hidden">
      <div className="p-4 border-b border-border">
        <h3 className="text-lg font-semibold">{title}</h3>
      </div>

      <Canvas
        camera={{ position: [0, 2, 5], fov: 50 }}
        style={{ background: 'transparent' }}
      >
        <ambientLight intensity={0.6} />
        <directionalLight position={[5, 5, 5]} intensity={0.8} />

        <BarChart3D data={data} />
      </Canvas>
    </div>
  )
}
```

### **9. Content Generator Implementation**

#### **app/dashboard/content-generator/page.tsx**
```typescript
'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardHeader, CardContent } from '@/components/ui/card'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { apiClient } from '@/lib/api-client'
import { toast } from 'sonner'

const contentSchema = z.object({
  keyword: z.string().min(1, 'Keyword is required'),
  target_country: z.string().min(1, 'Target country is required'),
  content_type: z.string().min(1, 'Content type is required'),
  tone: z.string().min(1, 'Tone is required'),
  length: z.string().min(1, 'Length is required'),
})

type ContentFormData = z.infer<typeof contentSchema>

export default function ContentGeneratorPage() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedContent, setGeneratedContent] = useState<string>('')
  const [seoAnalysis, setSeoAnalysis] = useState<any>(null)

  const form = useForm<ContentFormData>({
    resolver: zodResolver(contentSchema),
    defaultValues: {
      keyword: '',
      target_country: 'United States',
      content_type: 'blog-post',
      tone: 'professional',
      length: 'medium',
    },
  })

  const onSubmit = async (data: ContentFormData) => {
    setIsGenerating(true)
    setGeneratedContent('')
    setSeoAnalysis(null)

    try {
      const result = await apiClient.generateContent(data)

      if (result.success) {
        setGeneratedContent(result.content.body)
        setSeoAnalysis(result.seo_analysis)
        toast.success('Content generated successfully!')
      } else {
        throw new Error(result.error || 'Failed to generate content')
      }
    } catch (error) {
      console.error('Content generation error:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to generate content')
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1 className="text-3xl font-bold mb-2">AI Content Generator</h1>
        <p className="text-muted-foreground">
          Generate high-quality, SEO-optimized content for any keyword in any industry
        </p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Content Generation Form */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Card>
            <CardHeader>
              <h2 className="text-xl font-semibold">Content Settings</h2>
            </CardHeader>
            <CardContent>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Target Keyword *
                  </label>
                  <Input
                    {...form.register('keyword')}
                    placeholder="e.g., digital marketing strategies"
                    disabled={isGenerating}
                  />
                  {form.formState.errors.keyword && (
                    <p className="text-destructive text-sm mt-1">
                      {form.formState.errors.keyword.message}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Target Country *
                  </label>
                  <Select
                    {...form.register('target_country')}
                    disabled={isGenerating}
                  >
                    <option value="United States">United States</option>
                    <option value="United Kingdom">United Kingdom</option>
                    <option value="Canada">Canada</option>
                    <option value="Australia">Australia</option>
                    <option value="Germany">Germany</option>
                    <option value="France">France</option>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Content Type *
                  </label>
                  <Select
                    {...form.register('content_type')}
                    disabled={isGenerating}
                  >
                    <option value="blog-post">Blog Post</option>
                    <option value="article">Article</option>
                    <option value="guide">Guide</option>
                    <option value="tutorial">Tutorial</option>
                    <option value="review">Review</option>
                    <option value="comparison">Comparison</option>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Tone *
                  </label>
                  <Select
                    {...form.register('tone')}
                    disabled={isGenerating}
                  >
                    <option value="professional">Professional</option>
                    <option value="casual">Casual</option>
                    <option value="friendly">Friendly</option>
                    <option value="authoritative">Authoritative</option>
                    <option value="conversational">Conversational</option>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Content Length *
                  </label>
                  <Select
                    {...form.register('length')}
                    disabled={isGenerating}
                  >
                    <option value="short">Short (800-1200 words)</option>
                    <option value="medium">Medium (1500-2500 words)</option>
                    <option value="long">Long (2500-4000 words)</option>
                  </Select>
                </div>

                <Button
                  type="submit"
                  disabled={isGenerating}
                  className="w-full"
                  size="lg"
                >
                  {isGenerating ? (
                    <>
                      <LoadingSpinner className="mr-2" />
                      Generating Content...
                    </>
                  ) : (
                    'Generate SEO Content'
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </motion.div>

        {/* Generated Content Display */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card className="h-full">
            <CardHeader>
              <h2 className="text-xl font-semibold">Generated Content</h2>
              {seoAnalysis && (
                <div className="flex space-x-4 text-sm text-muted-foreground">
                  <span>Words: {seoAnalysis.actual_word_count}</span>
                  <span>Keyword Density: {seoAnalysis.keyword_density}</span>
                </div>
              )}
            </CardHeader>
            <CardContent>
              {isGenerating ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <LoadingSpinner className="mx-auto mb-4" />
                    <p className="text-muted-foreground">
                      Generating high-quality content...
                    </p>
                    <p className="text-sm text-muted-foreground mt-2">
                      This may take 30-60 seconds
                    </p>
                  </div>
                </div>
              ) : generatedContent ? (
                <div className="space-y-4">
                  <Textarea
                    value={generatedContent}
                    readOnly
                    className="min-h-96 font-mono text-sm"
                  />
                  <div className="flex space-x-2">
                    <Button
                      onClick={() => navigator.clipboard.writeText(generatedContent)}
                      variant="outline"
                    >
                      Copy Content
                    </Button>
                    <Button
                      onClick={() => {
                        const blob = new Blob([generatedContent], { type: 'text/plain' })
                        const url = URL.createObjectURL(blob)
                        const a = document.createElement('a')
                        a.href = url
                        a.download = 'generated-content.txt'
                        a.click()
                      }}
                      variant="outline"
                    >
                      Download
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-64 text-muted-foreground">
                  <p>Generated content will appear here</p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
```

---

**🎯 FINAL IMPLEMENTATION NOTES**

1. **Start with the foundation**: Authentication, layout, and basic components
2. **Focus on the content generator**: This is the primary feature users will use
3. **Add Three.js gradually**: Start with simple effects and enhance over time
4. **Prioritize performance**: Use React.memo, useMemo, and useCallback appropriately
5. **Test thoroughly**: Ensure all features work across different browsers and devices

**🚀 This comprehensive guide provides everything needed to build a world-class SEO SAAS frontend that rivals enterprise applications with cutting-edge technology and professional design standards.**
