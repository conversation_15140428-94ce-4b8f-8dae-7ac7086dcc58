import express from 'express';
import { createClient } from '@supabase/supabase-js';
import { body, validationResult } from 'express-validator';
import dotenv from 'dotenv';

dotenv.config();

const router = express.Router();

// Initialize Supabase client with service role for server-side operations
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

/**
 * Authentication middleware to verify JWT token
 */
const authenticateUser = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'Missing or invalid authorization header',
        code: 'UNAUTHORIZED'
      });
    }

    const token = authHeader.substring(7);
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired token',
        code: 'UNAUTHORIZED'
      });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({
      success: false,
      error: 'Authentication failed',
      code: 'AUTH_ERROR'
    });
  }
};

/**
 * POST /api/usage/track
 * Track user actions and usage patterns
 */
router.post('/track',
  authenticateUser,
  [
    body('action')
      .trim()
      .isLength({ min: 1, max: 100 })
      .withMessage('Action is required and must be less than 100 characters'),
    body('resource_type')
      .trim()
      .isLength({ min: 1, max: 50 })
      .withMessage('Resource type is required and must be less than 50 characters'),
    body('resource_id')
      .optional()
      .trim()
      .isLength({ max: 255 })
      .withMessage('Resource ID must be less than 255 characters'),
    body('metadata')
      .optional()
      .isObject()
      .withMessage('Metadata must be a valid JSON object')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array(),
          code: 'VALIDATION_ERROR'
        });
      }

      const usageData = {
        user_id: req.user.id,
        action: req.body.action,
        resource_type: req.body.resource_type,
        resource_id: req.body.resource_id || null,
        metadata: req.body.metadata || {},
        created_at: new Date().toISOString()
      };

      const { data: usage, error } = await supabase
        .from('usage_tracking')
        .insert(usageData)
        .select()
        .single();

      if (error) {
        console.error('Usage tracking error:', error);
        return res.status(500).json({
          success: false,
          error: 'Failed to track usage',
          code: 'DATABASE_ERROR'
        });
      }

      res.status(201).json({
        success: true,
        data: usage,
        message: 'Usage tracked successfully'
      });

    } catch (error) {
      console.error('Track usage error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      });
    }
  }
);

/**
 * GET /api/usage/stats
 * Get usage statistics for the authenticated user
 */
router.get('/stats', authenticateUser, async (req, res) => {
  try {
    const userId = req.user.id;
    const { timeframe = '30d', action, resource_type } = req.query;

    // Calculate date range based on timeframe
    let startDate = new Date();
    switch (timeframe) {
      case '24h':
        startDate.setHours(startDate.getHours() - 24);
        break;
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(startDate.getDate() - 30);
    }

    // Build query with filters
    let query = supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', userId)
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: false });

    if (action) {
      query = query.eq('action', action);
    }

    if (resource_type) {
      query = query.eq('resource_type', resource_type);
    }

    const { data: usageData, error } = await query;

    if (error) {
      console.error('Usage stats error:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch usage statistics',
        code: 'DATABASE_ERROR'
      });
    }

    // Calculate statistics
    const totalActions = usageData.length;

    // Action breakdown
    const actionCounts = usageData.reduce((acc, item) => {
      acc[item.action] = (acc[item.action] || 0) + 1;
      return acc;
    }, {});

    // Resource type breakdown
    const resourceTypeCounts = usageData.reduce((acc, item) => {
      acc[item.resource_type] = (acc[item.resource_type] || 0) + 1;
      return acc;
    }, {});

    // Daily activity (for timeline)
    const dailyActivity = {};
    usageData.forEach(item => {
      const date = item.created_at.split('T')[0];
      dailyActivity[date] = (dailyActivity[date] || 0) + 1;
    });

    // Convert daily activity to timeline array
    const timeline = [];
    const endDate = new Date();
    const currentDate = new Date(startDate);
    
    while (currentDate <= endDate) {
      const dateKey = currentDate.toISOString().split('T')[0];
      timeline.push({
        date: dateKey,
        count: dailyActivity[dateKey] || 0
      });
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Most used features (top 10)
    const topFeatures = Object.entries(actionCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([action, count]) => ({ action, count }));

    // Peak usage hours (for 24h and 7d timeframes)
    let hourlyActivity = {};
    if (timeframe === '24h' || timeframe === '7d') {
      usageData.forEach(item => {
        const hour = new Date(item.created_at).getHours();
        hourlyActivity[hour] = (hourlyActivity[hour] || 0) + 1;
      });
    }

    // Content generation specific metrics
    const contentGenerationActions = usageData.filter(item => 
      item.action === 'content_generation' || 
      item.resource_type === 'content'
    );

    const averageGenerationTime = contentGenerationActions.length > 0
      ? contentGenerationActions.reduce((sum, item) => {
          const time = item.metadata?.processing_time || 0;
          return sum + time;
        }, 0) / contentGenerationActions.length
      : 0;

    const usageStats = {
      summary: {
        totalActions,
        timeframe,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      },
      breakdown: {
        byAction: actionCounts,
        byResourceType: resourceTypeCounts
      },
      timeline,
      topFeatures,
      ...(Object.keys(hourlyActivity).length > 0 && { hourlyActivity }),
      contentGeneration: {
        totalGenerations: contentGenerationActions.length,
        averageProcessingTime: Math.round(averageGenerationTime)
      },
      filters: {
        action: action || null,
        resource_type: resource_type || null,
        timeframe
      },
      generatedAt: new Date().toISOString()
    };

    res.json({
      success: true,
      data: usageStats,
      message: 'Usage statistics retrieved successfully'
    });

  } catch (error) {
    console.error('Usage stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

/**
 * GET /api/usage/actions
 * Get list of available actions for filtering
 */
router.get('/actions', authenticateUser, async (req, res) => {
  try {
    const userId = req.user.id;

    const { data: actionsData, error } = await supabase
      .from('usage_tracking')
      .select('action, resource_type')
      .eq('user_id', userId);

    if (error) {
      console.error('Actions fetch error:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch available actions',
        code: 'DATABASE_ERROR'
      });
    }

    const uniqueActions = [...new Set(actionsData.map(item => item.action))];
    const uniqueResourceTypes = [...new Set(actionsData.map(item => item.resource_type))];

    res.json({
      success: true,
      data: {
        actions: uniqueActions.sort(),
        resourceTypes: uniqueResourceTypes.sort()
      },
      message: 'Available actions retrieved successfully'
    });

  } catch (error) {
    console.error('Get actions error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

/**
 * DELETE /api/usage/clear
 * Clear usage data (optional retention period)
 */
router.delete('/clear', authenticateUser, async (req, res) => {
  try {
    const userId = req.user.id;
    const { older_than_days = 365 } = req.query;

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - parseInt(older_than_days));

    const { error } = await supabase
      .from('usage_tracking')
      .delete()
      .eq('user_id', userId)
      .lt('created_at', cutoffDate.toISOString());

    if (error) {
      console.error('Clear usage data error:', error);
      return res.status(500).json({
        success: false,
        error: 'Failed to clear usage data',
        code: 'DATABASE_ERROR'
      });
    }

    res.json({
      success: true,
      message: `Usage data older than ${older_than_days} days cleared successfully`
    });

  } catch (error) {
    console.error('Clear usage error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
});

/**
 * Utility function to track usage automatically
 * This can be used as middleware in other routes
 */
export const trackUsage = (action, resourceType) => {
  return async (req, res, next) => {
    try {
      if (req.user) {
        const usageData = {
          user_id: req.user.id,
          action: action,
          resource_type: resourceType,
          resource_id: req.params.id || req.body.id || null,
          metadata: {
            method: req.method,
            endpoint: req.originalUrl,
            user_agent: req.headers['user-agent'],
            ip_address: req.ip,
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        };

        // Fire and forget - don't wait for this to complete
        supabase
          .from('usage_tracking')
          .insert(usageData)
          .catch(error => {
            console.error('Auto-tracking error:', error);
          });
      }
      
      next();
    } catch (error) {
      console.error('Track usage middleware error:', error);
      next(); // Continue even if tracking fails
    }
  };
};

export default router;