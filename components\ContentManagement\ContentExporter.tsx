/**
 * Content Exporter Component
 * Advanced export functionality with multiple formats and customization options
 */

'use client'

import React, { useState } from 'react'
import { ContentItem } from '@/lib/api/types'
import { contentService } from '@/lib/api'
import { useNotifications } from '@/components/Notifications'
import Card from '@/components/UI/Card'
import Button from '@/components/UI/Button'
import Input from '@/components/UI/Input'
import Badge from '@/components/UI/Badge'
import LoadingSpinner from '@/components/UI/LoadingSpinner'
import {
  ArrowDownTrayIcon,
  DocumentIcon,
  DocumentTextIcon,
  TableCellsIcon,
  CodeBracketIcon,
  PhotoIcon,
  LinkIcon,
  CheckCircleIcon,
  XMarkIcon,
  Cog6ToothIcon,
  ClipboardDocumentIcon
} from '@heroicons/react/24/outline'

interface ContentExporterProps {
  content: ContentItem[]
  onClose?: () => void
  isModal?: boolean
}

interface ExportOptions {
  format: 'docx' | 'pdf' | 'html' | 'markdown' | 'json' | 'csv'
  includeMetadata: boolean
  includeSeoAnalysis: boolean
  includeImages: boolean
  customTemplate: boolean
  templateName: string
  fileName: string
  compression: boolean
}

interface ExportProgress {
  step: string
  progress: number
  status: 'processing' | 'completed' | 'error'
  downloadUrl?: string
}

export default function ContentExporter({
  content,
  onClose,
  isModal = false
}: ContentExporterProps) {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'docx',
    includeMetadata: true,
    includeSeoAnalysis: false,
    includeImages: false,
    customTemplate: false,
    templateName: '',
    fileName: `content-export-${new Date().toISOString().split('T')[0]}`,
    compression: false
  })
  
  const [isExporting, setIsExporting] = useState(false)
  const [exportProgress, setExportProgress] = useState<ExportProgress | null>(null)
  const [showAdvanced, setShowAdvanced] = useState(false)
  
  const { success: notifySuccess, error: notifyError } = useNotifications()

  const formatOptions = [
    {
      value: 'docx',
      label: 'Microsoft Word',
      description: 'Professional document format',
      icon: DocumentIcon,
      size: 'Large',
      features: ['Formatting', 'Images', 'Tables']
    },
    {
      value: 'pdf',
      label: 'PDF Document',
      description: 'Universal document format',
      icon: DocumentTextIcon,
      size: 'Medium',
      features: ['Print-ready', 'Shareable', 'Professional']
    },
    {
      value: 'html',
      label: 'HTML',
      description: 'Web-ready format',
      icon: CodeBracketIcon,
      size: 'Small',
      features: ['Web-compatible', 'SEO-friendly', 'Interactive']
    },
    {
      value: 'markdown',
      label: 'Markdown',
      description: 'Plain text with formatting',
      icon: DocumentTextIcon,
      size: 'Small',
      features: ['Lightweight', 'Version control', 'Platform-agnostic']
    },
    {
      value: 'json',
      label: 'JSON Data',
      description: 'Structured data format',
      icon: CodeBracketIcon,
      size: 'Small',
      features: ['API-ready', 'Structured', 'Programmable']
    },
    {
      value: 'csv',
      label: 'CSV Spreadsheet',
      description: 'Tabular data format',
      icon: TableCellsIcon,
      size: 'Small',
      features: ['Excel-compatible', 'Data analysis', 'Bulk editing']
    }
  ]

  const handleExport = async () => {
    setIsExporting(true)
    setExportProgress({
      step: 'Preparing export...',
      progress: 10,
      status: 'processing'
    })

    try {
      // Simulate progress updates
      const progressSteps = [
        { step: 'Gathering content...', progress: 25 },
        { step: 'Processing metadata...', progress: 50 },
        { step: 'Applying formatting...', progress: 75 },
        { step: 'Generating file...', progress: 90 },
      ]

      for (const progressStep of progressSteps) {
        setExportProgress({ ...progressStep, status: 'processing' })
        await new Promise(resolve => setTimeout(resolve, 800))
      }

      const contentIds = content.map(item => item.id)
      const response = await contentService.exportContent(contentIds, exportOptions.format as any)

      if (response.success && response.data) {
        setExportProgress({
          step: 'Export completed!',
          progress: 100,
          status: 'completed',
          downloadUrl: response.data.download_url
        })
        
        notifySuccess(`Successfully exported ${content.length} items as ${exportOptions.format.toUpperCase()}`)
        
        // Auto-download
        setTimeout(() => {
          if (response.data?.download_url) {
            window.open(response.data.download_url, '_blank')
          }
        }, 1000)
      } else {
        throw new Error('Export failed')
      }
    } catch (error) {
      setExportProgress({
        step: 'Export failed',
        progress: 0,
        status: 'error'
      })
      notifyError('Failed to export content. Please try again.')
    } finally {
      setIsExporting(false)
      setTimeout(() => {
        setExportProgress(null)
      }, 3000)
    }
  }

  const handleOptionChange = (key: keyof ExportOptions, value: any) => {
    setExportOptions(prev => ({ ...prev, [key]: value }))
  }

  const getEstimatedSize = () => {
    const baseSize = content.reduce((acc, item) => acc + item.word_count, 0) * 0.01 // Rough estimate
    const format = formatOptions.find(f => f.value === exportOptions.format)
    const multiplier = format?.size === 'Large' ? 3 : format?.size === 'Medium' ? 2 : 1
    return (baseSize * multiplier).toFixed(1)
  }

  const selectedFormat = formatOptions.find(f => f.value === exportOptions.format)

  const ExportContent = () => (
    <div className="space-y-6">
      {/* Export Summary */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Export {content.length} Content Items
          </h3>
          <Badge variant="secondary">
            ~{getEstimatedSize()} MB
          </Badge>
        </div>
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div>
            <span className="text-gray-500 dark:text-gray-400">Total Words:</span>
            <div className="font-medium text-gray-900 dark:text-gray-100">
              {content.reduce((acc, item) => acc + item.word_count, 0).toLocaleString()}
            </div>
          </div>
          <div>
            <span className="text-gray-500 dark:text-gray-400">Content Types:</span>
            <div className="font-medium text-gray-900 dark:text-gray-100">
              {new Set(content.map(item => item.content_type)).size} types
            </div>
          </div>
          <div>
            <span className="text-gray-500 dark:text-gray-400">Avg SEO Score:</span>
            <div className="font-medium text-gray-900 dark:text-gray-100">
              {Math.round(content.reduce((acc, item) => acc + (item.seo_score || 0), 0) / content.length)}%
            </div>
          </div>
        </div>
      </Card>

      {/* Format Selection */}
      <div>
        <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Choose Export Format
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {formatOptions.map((format) => (
            <button
              key={format.value}
              onClick={() => handleOptionChange('format', format.value)}
              className={`p-4 rounded-lg border-2 transition-all text-left ${
                exportOptions.format === format.value
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
            >
              <div className="flex items-center mb-2">
                <format.icon className="h-5 w-5 text-gray-600 dark:text-gray-400 mr-2" />
                <span className="font-medium text-gray-900 dark:text-gray-100">
                  {format.label}
                </span>
                <Badge variant="secondary" size="sm" className="ml-auto">
                  {format.size}
                </Badge>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                {format.description}
              </p>
              <div className="flex flex-wrap gap-1">
                {format.features.map((feature, index) => (
                  <Badge key={index} variant="outline" size="sm">
                    {feature}
                  </Badge>
                ))}
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Export Options */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-md font-medium text-gray-900 dark:text-gray-100">
            Export Options
          </h4>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
          >
            <Cog6ToothIcon className="h-4 w-4 mr-2" />
            {showAdvanced ? 'Hide' : 'Show'} Advanced
          </Button>
        </div>

        <div className="space-y-4">
          {/* File Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              File Name
            </label>
            <Input
              type="text"
              value={exportOptions.fileName}
              onChange={(e) => handleOptionChange('fileName', e.target.value)}
              placeholder="Enter file name..."
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Final file: {exportOptions.fileName}.{exportOptions.format}
            </p>
          </div>

          {/* Basic Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={exportOptions.includeMetadata}
                onChange={(e) => handleOptionChange('includeMetadata', e.target.checked)}
                className="rounded border-gray-300 dark:border-gray-600"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                Include metadata (titles, descriptions, keywords)
              </span>
            </label>

            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={exportOptions.includeSeoAnalysis}
                onChange={(e) => handleOptionChange('includeSeoAnalysis', e.target.checked)}
                className="rounded border-gray-300 dark:border-gray-600"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                Include SEO analysis and scores
              </span>
            </label>

            {['docx', 'pdf', 'html'].includes(exportOptions.format) && (
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={exportOptions.includeImages}
                  onChange={(e) => handleOptionChange('includeImages', e.target.checked)}
                  className="rounded border-gray-300 dark:border-gray-600"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  Include images and media
                </span>
              </label>
            )}

            {content.length > 5 && (
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={exportOptions.compression}
                  onChange={(e) => handleOptionChange('compression', e.target.checked)}
                  className="rounded border-gray-300 dark:border-gray-600"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  Compress output (ZIP archive)
                </span>
              </label>
            )}
          </div>

          {/* Advanced Options */}
          {showAdvanced && (
            <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
              <div className="space-y-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={exportOptions.customTemplate}
                    onChange={(e) => handleOptionChange('customTemplate', e.target.checked)}
                    className="rounded border-gray-300 dark:border-gray-600"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    Use custom template
                  </span>
                </label>

                {exportOptions.customTemplate && (
                  <div className="ml-6">
                    <Input
                      type="text"
                      value={exportOptions.templateName}
                      onChange={(e) => handleOptionChange('templateName', e.target.value)}
                      placeholder="Template name or URL..."
                      className="max-w-xs"
                    />
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Export Progress */}
      {exportProgress && (
        <Card className="p-4">
          <div className="flex items-center space-x-3">
            {exportProgress.status === 'processing' && <LoadingSpinner size="sm" />}
            {exportProgress.status === 'completed' && (
              <CheckCircleIcon className="h-5 w-5 text-green-500" />
            )}
            {exportProgress.status === 'error' && (
              <XMarkIcon className="h-5 w-5 text-red-500" />
            )}
            <div className="flex-1">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {exportProgress.step}
                </span>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {exportProgress.progress}%
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    exportProgress.status === 'completed'
                      ? 'bg-green-500'
                      : exportProgress.status === 'error'
                      ? 'bg-red-500'
                      : 'bg-blue-500'
                  }`}
                  style={{ width: `${exportProgress.progress}%` }}
                />
              </div>
            </div>
          </div>

          {exportProgress.status === 'completed' && exportProgress.downloadUrl && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <Button
                onClick={() => window.open(exportProgress.downloadUrl, '_blank')}
                className="w-full"
              >
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                Download {exportOptions.format.toUpperCase()} File
              </Button>
            </div>
          )}
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-4">
        {onClose && (
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        )}
        <div className="flex items-center space-x-3 ml-auto">
          <Button
            variant="outline"
            onClick={() => {
              navigator.clipboard.writeText(JSON.stringify(exportOptions, null, 2))
              notifySuccess('Export settings copied to clipboard')
            }}
          >
            <ClipboardDocumentIcon className="h-4 w-4 mr-2" />
            Copy Settings
          </Button>
          <Button
            onClick={handleExport}
            disabled={isExporting || !exportOptions.fileName}
            className="min-w-32"
          >
            {isExporting ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                Exporting...
              </>
            ) : (
              <>
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                Export {selectedFormat?.label}
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  )

  if (isModal) {
    return (
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex min-h-screen items-center justify-center p-4">
          <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onClick={onClose} />
          <div className="relative w-full max-w-4xl bg-white dark:bg-gray-900 rounded-xl shadow-xl">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                  Export Content
                </h2>
                {onClose && (
                  <Button variant="ghost" size="sm" onClick={onClose}>
                    <XMarkIcon className="h-5 w-5" />
                  </Button>
                )}
              </div>
              <ExportContent />
            </div>
          </div>
        </div>
      </div>
    )
  }

  return <ExportContent />
}