/**
 * Content Preview Component
 * Displays generated content with formatting and preview options
 */

'use client'

import React, { useState } from 'react'
import Card from '@/components/UI/Card'
import Button from '@/components/UI/Button'
import Badge from '@/components/UI/Badge'
import { 
  EyeIcon, 
  DocumentTextIcon, 
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  TabletIcon
} from '@heroicons/react/24/outline'

interface ContentPreviewProps {
  content: {
    title: string
    body: string
    meta_description?: string
    meta_title?: string
    sections?: Array<{
      heading: string
      content: string
      word_count: number
    }>
  }
  metadata: {
    word_count: number
    keyword_density: number
    readability_score: number
    seo_score: number
  }
  keyword: string
}

type ViewMode = 'formatted' | 'raw' | 'preview'
type DeviceView = 'desktop' | 'tablet' | 'mobile'

export default function ContentPreview({ content, metadata, keyword }: ContentPreviewProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('formatted')
  const [deviceView, setDeviceView] = useState<DeviceView>('desktop')
  const [expandedSections, setExpandedSections] = useState<Set<number>>(new Set([0]))

  const toggleSection = (index: number) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(index)) {
      newExpanded.delete(index)
    } else {
      newExpanded.add(index)
    }
    setExpandedSections(newExpanded)
  }

  const getDeviceClasses = () => {
    switch (deviceView) {
      case 'mobile':
        return 'max-w-sm mx-auto'
      case 'tablet':
        return 'max-w-2xl mx-auto'
      default:
        return 'max-w-full'
    }
  }

  const formatContent = (text: string) => {
    // Basic markdown-like formatting
    return text
      .replace(/^# (.*$)/gim, '<h1 class="text-3xl font-bold mb-4 text-gray-900 dark:text-gray-100">$1</h1>')
      .replace(/^## (.*$)/gim, '<h2 class="text-2xl font-semibold mb-3 text-gray-900 dark:text-gray-100">$1</h2>')
      .replace(/^### (.*$)/gim, '<h3 class="text-xl font-medium mb-2 text-gray-900 dark:text-gray-100">$1</h3>')
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
      .replace(/\n\n/g, '</p><p class="mb-4 text-gray-700 dark:text-gray-300">')
      .replace(/^(.+)$/gm, '<p class="mb-4 text-gray-700 dark:text-gray-300">$1</p>')
  }

  const highlightKeyword = (text: string) => {
    const regex = new RegExp(`(${keyword})`, 'gi')
    return text.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">$1</mark>')
  }

  return (
    <Card className="p-0 overflow-hidden">
      {/* Header with Controls */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Content Preview
          </h3>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary">
              {metadata.word_count} words
            </Badge>
            <Badge 
              variant={metadata.seo_score >= 80 ? "success" : metadata.seo_score >= 60 ? "warning" : "error"}
            >
              SEO: {metadata.seo_score}%
            </Badge>
          </div>
        </div>

        {/* View Mode Toggles */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button
              variant={viewMode === 'formatted' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setViewMode('formatted')}
            >
              <EyeIcon className="h-4 w-4 mr-1" />
              Formatted
            </Button>
            <Button
              variant={viewMode === 'raw' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setViewMode('raw')}
            >
              <DocumentTextIcon className="h-4 w-4 mr-1" />
              Raw Text
            </Button>
            <Button
              variant={viewMode === 'preview' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setViewMode('preview')}
            >
              Preview
            </Button>
          </div>

          {/* Device View Toggles */}
          <div className="flex items-center space-x-1">
            <Button
              variant={deviceView === 'desktop' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setDeviceView('desktop')}
            >
              <ComputerDesktopIcon className="h-4 w-4" />
            </Button>
            <Button
              variant={deviceView === 'tablet' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setDeviceView('tablet')}
            >
              <TabletIcon className="h-4 w-4" />
            </Button>
            <Button
              variant={deviceView === 'mobile' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setDeviceView('mobile')}
            >
              <DevicePhoneMobileIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Content Display */}
      <div className="p-6">
        <div className={`transition-all duration-300 ${getDeviceClasses()}`}>
          {/* Meta Information */}
          {content.meta_title && (
            <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="text-sm font-medium text-blue-700 dark:text-blue-300 mb-1">
                Meta Title ({content.meta_title.length} chars)
              </div>
              <div className="text-blue-600 dark:text-blue-400">
                {content.meta_title}
              </div>
            </div>
          )}

          {content.meta_description && (
            <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="text-sm font-medium text-green-700 dark:text-green-300 mb-1">
                Meta Description ({content.meta_description.length} chars)
              </div>
              <div className="text-green-600 dark:text-green-400">
                {content.meta_description}
              </div>
            </div>
          )}

          {/* Main Content */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
            {viewMode === 'formatted' && (
              <div 
                className="prose prose-gray dark:prose-invert max-w-none"
                dangerouslySetInnerHTML={{ 
                  __html: highlightKeyword(formatContent(content.body))
                }}
              />
            )}

            {viewMode === 'raw' && (
              <div className="font-mono text-sm bg-gray-50 dark:bg-gray-900 p-4 rounded-lg overflow-auto max-h-96">
                <pre className="whitespace-pre-wrap text-gray-800 dark:text-gray-200">
                  {content.body}
                </pre>
              </div>
            )}

            {viewMode === 'preview' && content.sections && (
              <div className="space-y-4">
                <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-6">
                  {content.title}
                </h1>
                
                {content.sections.map((section, index) => (
                  <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg">
                    <button
                      onClick={() => toggleSection(index)}
                      className="w-full px-4 py-3 text-left bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors rounded-t-lg"
                    >
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium text-gray-900 dark:text-gray-100">
                          {section.heading}
                        </h3>
                        <div className="flex items-center space-x-2">
                          <Badge variant="secondary" size="sm">
                            {section.word_count} words
                          </Badge>
                          <svg
                            className={`h-5 w-5 text-gray-500 transition-transform ${
                              expandedSections.has(index) ? 'rotate-180' : ''
                            }`}
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </div>
                      </div>
                    </button>
                    
                    {expandedSections.has(index) && (
                      <div className="p-4">
                        <div 
                          className="prose prose-gray dark:prose-invert max-w-none"
                          dangerouslySetInnerHTML={{ 
                            __html: highlightKeyword(formatContent(section.content))
                          }}
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Content Stats */}
          <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Words
              </div>
              <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {metadata.word_count}
              </div>
            </div>
            
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Keyword Density
              </div>
              <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {metadata.keyword_density.toFixed(1)}%
              </div>
            </div>
            
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Readability
              </div>
              <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {metadata.readability_score}
              </div>
            </div>
            
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                SEO Score
              </div>
              <div className={`text-lg font-semibold ${
                metadata.seo_score >= 80 ? 'text-green-600 dark:text-green-400' :
                metadata.seo_score >= 60 ? 'text-yellow-600 dark:text-yellow-400' :
                'text-red-600 dark:text-red-400'
              }`}>
                {metadata.seo_score}%
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  )
}