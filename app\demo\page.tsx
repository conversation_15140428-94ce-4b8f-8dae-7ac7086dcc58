'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function DemoPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const generateContent = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      // Direct call to backend since frontend API route has issues
      const response = await fetch('http://localhost:5000/api/content/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: 'Demo Article: The Future of AI',
          prompt: 'Write about how artificial intelligence is transforming modern business',
          contentType: 'blog-post',
          tone: 'professional',
          length: 'medium'
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
          SEO SAAS Content Generation Demo
        </h1>
        
        <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
          <h2 className="text-xl font-semibold mb-4">Test Content Generation</h2>
          <p className="text-gray-600 mb-6">
            Click the button below to test the content generation API with a sample request.
          </p>
          
          <button
            onClick={generateContent}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-300 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            {isLoading ? 'Generating Content...' : 'Generate Demo Content'}
          </button>
          
          {/* Status indicators */}
          <div className="mt-6 space-y-2">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-600">Backend Server: Running on localhost:5000</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-600">Frontend: Running on localhost:3001</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <span className="text-sm text-gray-600">OpenAI: Demo mode (configure API key for real AI)</span>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-red-800 mb-2">Error</h3>
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {/* Results Display */}
        {result && (
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h3 className="text-xl font-semibold mb-4">Generated Content</h3>
            
            <div className="border-b pb-4 mb-4">
              <h4 className="font-medium text-gray-700">Title:</h4>
              <p className="text-lg">{result.content?.title}</p>
            </div>
            
            <div className="border-b pb-4 mb-4">
              <h4 className="font-medium text-gray-700">Metadata:</h4>
              <div className="grid grid-cols-2 gap-4 mt-2 text-sm">
                <div>
                  <span className="text-gray-500">Type:</span> {result.content?.metadata?.contentType}
                </div>
                <div>
                  <span className="text-gray-500">Tone:</span> {result.content?.metadata?.tone}
                </div>
                <div>
                  <span className="text-gray-500">Length:</span> {result.content?.metadata?.length}
                </div>
                <div>
                  <span className="text-gray-500">Word Count:</span> {result.content?.metadata?.wordCount}
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-700 mb-2">Content:</h4>
              <div className="bg-gray-50 p-4 rounded-lg">
                <pre className="whitespace-pre-wrap text-sm text-gray-800">
                  {result.content?.body}
                </pre>
              </div>
            </div>
            
            {result.usage && (
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-700">
                  API Usage: {result.usage.total_tokens} tokens used
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}