# Supabase Database Deployment Guide

## Step 1: Deploy Database Schema

1. Go to your Supabase project dashboard: https://supabase.com/dashboard
2. Navigate to **SQL Editor** in the left sidebar
3. Create a new query and paste the entire contents of `create-tables.sql`
4. Click **Run** to execute the schema

This will create:
- Custom types (subscription_tier, content_status)
- Users table (extends auth.users)
- Projects table
- Generated content table
- SEO analysis table
- Row Level Security policies
- Performance indexes
- Automatic user creation triggers

## Step 2: Configure OAuth Providers

### Google OAuth Setup
1. Go to **Authentication > Providers** in Supabase dashboard
2. Enable **Google** provider
3. Add your Google OAuth credentials:
   - Client ID: `YOUR_GOOGLE_CLIENT_ID`
   - Client Secret: `YOUR_GOOGLE_CLIENT_SECRET`
4. Set redirect URL to: `https://xpcbyzcaidfukddqniny.supabase.co/auth/v1/callback`

### GitHub OAuth Setup
1. In **Authentication > Providers**, enable **GitHub**
2. Add your GitHub OAuth app credentials:
   - Client ID: `YOUR_GITHUB_CLIENT_ID`
   - Client Secret: `YOUR_GITHUB_CLIENT_SECRET`
3. Set redirect URL to: `https://xpcbyzcaidfukddqniny.supabase.co/auth/v1/callback`

## Step 3: Update Environment Variables

After deployment, update `.env.local`:
```
NEXT_PUBLIC_DEMO_MODE=false
```

## Step 4: Test Authentication

1. Restart your development server
2. Visit http://localhost:3002/auth/login
3. Test Google and GitHub login flows
4. Verify user data appears in the users table

## Verification Checklist

- ✅ Database schema deployed successfully
- ✅ OAuth providers configured
- ✅ User registration works
- ✅ RLS policies active
- ✅ User data syncing to custom users table
- ✅ Demo mode disabled

## Troubleshooting

If authentication fails:
1. Check OAuth redirect URLs match exactly
2. Verify client IDs and secrets are correct
3. Check Supabase logs in dashboard
4. Ensure RLS policies allow user operations