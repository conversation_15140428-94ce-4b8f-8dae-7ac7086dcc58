/**
 * Content Publisher Component
 * Advanced publishing workflow with scheduling and platform integration
 */

'use client'

import React, { useState, useEffect } from 'react'
import { ContentItem } from '@/lib/api/types'
import { contentService } from '@/lib/api'
import { useNotifications } from '@/components/Notifications'
import Card from '@/components/UI/Card'
import Button from '@/components/UI/Button'
import Input from '@/components/UI/Input'
import Badge from '@/components/UI/Badge'
import LoadingSpinner from '@/components/UI/LoadingSpinner'
import {
  ShareIcon,
  CalendarIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  GlobeAltIcon,
  CogIcon,
  PlayIcon,
  PauseIcon,
  ArrowPathIcon,
  BellIcon,
  EyeIcon,
  DocumentDuplicateIcon
} from '@heroicons/react/24/outline'

interface ContentPublisherProps {
  content: ContentItem
  onClose?: () => void
  onPublished?: (content: ContentItem) => void
}

interface PublishingPlatform {
  id: string
  name: string
  type: 'cms' | 'social' | 'blog' | 'website'
  icon: React.ComponentType<{ className?: string }>
  status: 'connected' | 'disconnected' | 'error'
  lastSync?: string
  features: string[]
  requiresAuth: boolean
}

interface ScheduleOptions {
  publishNow: boolean
  scheduledDate?: string
  scheduledTime?: string
  timezone: string
  recurring: boolean
  recurringPattern?: 'daily' | 'weekly' | 'monthly'
  autoPublish: boolean
}

interface PublishingStatus {
  platform: string
  status: 'pending' | 'publishing' | 'published' | 'failed' | 'scheduled'
  publishedUrl?: string
  error?: string
  publishedAt?: string
}

export default function ContentPublisher({
  content,
  onClose,
  onPublished
}: ContentPublisherProps) {
  const [selectedPlatforms, setSelectedPlatforms] = useState<Set<string>>(new Set())
  const [scheduleOptions, setScheduleOptions] = useState<ScheduleOptions>({
    publishNow: true,
    timezone: 'UTC',
    recurring: false,
    autoPublish: false
  })
  
  const [publishingStatuses, setPublishingStatuses] = useState<PublishingStatus[]>([])
  const [isPublishing, setIsPublishing] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [customizations, setCustomizations] = useState<Record<string, any>>({})
  
  const { success: notifySuccess, error: notifyError, info: notifyInfo } = useNotifications()

  const platforms: PublishingPlatform[] = [
    {
      id: 'wordpress',
      name: 'WordPress',
      type: 'cms',
      icon: GlobeAltIcon,
      status: 'connected',
      lastSync: '2024-01-15T10:30:00Z',
      features: ['Auto-SEO', 'Categories', 'Tags', 'Featured Images'],
      requiresAuth: true
    },
    {
      id: 'webflow',
      name: 'Webflow',
      type: 'cms',
      icon: GlobeAltIcon,
      status: 'connected',
      features: ['Custom Fields', 'Collections', 'SEO Settings'],
      requiresAuth: true
    },
    {
      id: 'shopify',
      name: 'Shopify Blog',
      type: 'blog',
      icon: GlobeAltIcon,
      status: 'disconnected',
      features: ['Product Integration', 'Blog Categories', 'Customer Comments'],
      requiresAuth: true
    },
    {
      id: 'medium',
      name: 'Medium',
      type: 'blog',
      icon: DocumentDuplicateIcon,
      status: 'connected',
      features: ['Publications', 'Tags', 'Canonical URLs'],
      requiresAuth: true
    },
    {
      id: 'linkedin',
      name: 'LinkedIn',
      type: 'social',
      icon: ShareIcon,
      status: 'connected',
      features: ['Company Pages', 'Professional Network', 'Analytics'],
      requiresAuth: true
    },
    {
      id: 'custom_api',
      name: 'Custom API',
      type: 'website',
      icon: CogIcon,
      status: 'disconnected',
      features: ['Webhook Support', 'Custom Headers', 'Authentication'],
      requiresAuth: false
    }
  ]

  const timezones = [
    'UTC',
    'America/New_York',
    'America/Los_Angeles',
    'Europe/London',
    'Europe/Paris',
    'Asia/Tokyo',
    'Asia/Shanghai',
    'Australia/Sydney'
  ]

  useEffect(() => {
    // Auto-select connected platforms
    const connectedPlatforms = platforms.filter(p => p.status === 'connected').map(p => p.id)
    setSelectedPlatforms(new Set(connectedPlatforms.slice(0, 2))) // Select first 2
  }, [])

  const handlePlatformToggle = (platformId: string) => {
    setSelectedPlatforms(prev => {
      const newSet = new Set(prev)
      if (newSet.has(platformId)) {
        newSet.delete(platformId)
      } else {
        newSet.add(platformId)
      }
      return newSet
    })
  }

  const handleScheduleChange = (key: keyof ScheduleOptions, value: any) => {
    setScheduleOptions(prev => ({ ...prev, [key]: value }))
  }

  const handlePublish = async () => {
    if (selectedPlatforms.size === 0) {
      notifyError('Please select at least one platform to publish to')
      return
    }

    setIsPublishing(true)
    const selectedPlatformList = Array.from(selectedPlatforms)
    
    // Initialize publishing statuses
    const initialStatuses: PublishingStatus[] = selectedPlatformList.map(platformId => ({
      platform: platformId,
      status: scheduleOptions.publishNow ? 'pending' : 'scheduled'
    }))
    setPublishingStatuses(initialStatuses)

    try {
      for (const platformId of selectedPlatformList) {
        const platform = platforms.find(p => p.id === platformId)!
        
        // Update status to publishing
        setPublishingStatuses(prev => prev.map(status => 
          status.platform === platformId 
            ? { ...status, status: 'publishing' as const }
            : status
        ))

        // Simulate publishing delay
        await new Promise(resolve => setTimeout(resolve, 1500))

        try {
          // Here you would make actual API calls to each platform
          const publishResult = await simulatePublishing(platformId, content, scheduleOptions)
          
          setPublishingStatuses(prev => prev.map(status => 
            status.platform === platformId 
              ? { 
                  ...status, 
                  status: scheduleOptions.publishNow ? 'published' : 'scheduled',
                  publishedUrl: publishResult.url,
                  publishedAt: publishResult.publishedAt
                }
              : status
          ))

          notifySuccess(`Successfully ${scheduleOptions.publishNow ? 'published' : 'scheduled'} to ${platform.name}`)
        } catch (error) {
          setPublishingStatuses(prev => prev.map(status => 
            status.platform === platformId 
              ? { 
                  ...status, 
                  status: 'failed',
                  error: `Failed to publish to ${platform.name}`
                }
              : status
          ))
          
          notifyError(`Failed to publish to ${platform.name}`)
        }
      }

      // Update content status
      const newStatus = scheduleOptions.publishNow ? 'published' : 'scheduled'
      await contentService.updateContent(content.id, { status: newStatus })
      
      if (onPublished) {
        onPublished({ ...content, status: newStatus })
      }

      if (!scheduleOptions.publishNow) {
        notifyInfo(`Content scheduled for ${scheduleOptions.scheduledDate} at ${scheduleOptions.scheduledTime}`)
      }

    } catch (error) {
      notifyError('Publishing failed. Please try again.')
    } finally {
      setIsPublishing(false)
    }
  }

  const simulatePublishing = async (platformId: string, content: ContentItem, options: ScheduleOptions) => {
    // Simulate API call delay and potential errors
    if (Math.random() < 0.1) { // 10% chance of failure for demo
      throw new Error('Simulated publishing error')
    }
    
    return {
      url: `https://${platformId}.example.com/posts/${content.id}`,
      publishedAt: options.publishNow ? new Date().toISOString() : `${options.scheduledDate}T${options.scheduledTime}:00Z`
    }
  }

  const getStatusIcon = (status: PublishingStatus['status']) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-4 w-4 text-yellow-500" />
      case 'publishing':
        return <LoadingSpinner size="sm" />
      case 'published':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />
      case 'scheduled':
        return <CalendarIcon className="h-4 w-4 text-blue-500" />
      case 'failed':
        return <XCircleIcon className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'success'
      case 'disconnected': return 'secondary'
      case 'error': return 'error'
      default: return 'secondary'
    }
  }

  const formatDateTime = (date?: string, time?: string) => {
    if (!date || !time) return 'Not set'
    return new Date(`${date}T${time}`).toLocaleString()
  }

  return (
    <div className="space-y-6">
      {/* Content Preview */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Publishing: {content.title}
          </h3>
          <div className="flex items-center space-x-2">
            <Badge variant={content.status === 'published' ? 'success' : 'warning'}>
              {content.status}
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowPreview(!showPreview)}
            >
              <EyeIcon className="h-4 w-4 mr-2" />
              {showPreview ? 'Hide' : 'Show'} Preview
            </Button>
          </div>
        </div>

        {showPreview && (
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">{content.title}</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                {content.meta_description || 'No meta description'}
              </p>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {content.word_count} words • {content.keyword} • SEO Score: {content.seo_score || 0}%
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Platform Selection */}
      <Card className="p-4">
        <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Select Publishing Platforms
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {platforms.map((platform) => (
            <div
              key={platform.id}
              className={`p-4 rounded-lg border-2 transition-all cursor-pointer ${
                selectedPlatforms.has(platform.id)
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
              } ${
                platform.status === 'disconnected' ? 'opacity-60' : ''
              }`}
              onClick={() => platform.status === 'connected' && handlePlatformToggle(platform.id)}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <platform.icon className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {platform.name}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant={getStatusColor(platform.status)} size="sm">
                    {platform.status}
                  </Badge>
                  {selectedPlatforms.has(platform.id) && (
                    <CheckCircleIcon className="h-4 w-4 text-blue-500" />
                  )}
                </div>
              </div>
              <div className="flex flex-wrap gap-1 mb-2">
                {platform.features.slice(0, 3).map((feature, index) => (
                  <Badge key={index} variant="outline" size="sm">
                    {feature}
                  </Badge>
                ))}
              </div>
              {platform.status === 'disconnected' && (
                <div className="text-xs text-red-600 dark:text-red-400">
                  Requires connection to publish
                </div>
              )}
            </div>
          ))}
        </div>
      </Card>

      {/* Scheduling Options */}
      <Card className="p-4">
        <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Publishing Schedule
        </h4>
        <div className="space-y-4">
          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name="publishTime"
                checked={scheduleOptions.publishNow}
                onChange={() => handleScheduleChange('publishNow', true)}
                className="text-blue-600"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                Publish immediately
              </span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name="publishTime"
                checked={!scheduleOptions.publishNow}
                onChange={() => handleScheduleChange('publishNow', false)}
                className="text-blue-600"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                Schedule for later
              </span>
            </label>
          </div>

          {!scheduleOptions.publishNow && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pl-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Date
                </label>
                <Input
                  type="date"
                  value={scheduleOptions.scheduledDate || ''}
                  onChange={(e) => handleScheduleChange('scheduledDate', e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Time
                </label>
                <Input
                  type="time"
                  value={scheduleOptions.scheduledTime || ''}
                  onChange={(e) => handleScheduleChange('scheduledTime', e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Timezone
                </label>
                <select
                  value={scheduleOptions.timezone}
                  onChange={(e) => handleScheduleChange('timezone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                >
                  {timezones.map(tz => (
                    <option key={tz} value={tz}>{tz}</option>
                  ))}
                </select>
              </div>
            </div>
          )}

          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={scheduleOptions.autoPublish}
                onChange={(e) => handleScheduleChange('autoPublish', e.target.checked)}
                className="rounded border-gray-300 dark:border-gray-600"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                Auto-publish on schedule
              </span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={scheduleOptions.recurring}
                onChange={(e) => handleScheduleChange('recurring', e.target.checked)}
                className="rounded border-gray-300 dark:border-gray-600"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                Recurring publication
              </span>
            </label>
          </div>
        </div>
      </Card>

      {/* Publishing Status */}
      {publishingStatuses.length > 0 && (
        <Card className="p-4">
          <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
            Publishing Status
          </h4>
          <div className="space-y-3">
            {publishingStatuses.map((status, index) => {
              const platform = platforms.find(p => p.id === status.platform)!
              return (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(status.status)}
                    <div>
                      <div className="font-medium text-gray-900 dark:text-gray-100">
                        {platform.name}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        {status.status === 'published' && 'Published successfully'}
                        {status.status === 'scheduled' && `Scheduled for ${formatDateTime(scheduleOptions.scheduledDate, scheduleOptions.scheduledTime)}`}
                        {status.status === 'publishing' && 'Publishing...'}
                        {status.status === 'pending' && 'Waiting to publish'}
                        {status.status === 'failed' && status.error}
                      </div>
                    </div>
                  </div>
                  {status.publishedUrl && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(status.publishedUrl, '_blank')}
                    >
                      View Post
                    </Button>
                  )}
                </div>
              )
            })}
          </div>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-4">
        {onClose && (
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        )}
        <div className="flex items-center space-x-3 ml-auto">
          <Button
            variant="outline"
            onClick={() => {
              setPublishingStatuses([])
              setSelectedPlatforms(new Set())
              setScheduleOptions({
                publishNow: true,
                timezone: 'UTC',
                recurring: false,
                autoPublish: false
              })
            }}
          >
            <ArrowPathIcon className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <Button
            onClick={handlePublish}
            disabled={isPublishing || selectedPlatforms.size === 0}
            className="min-w-32"
          >
            {isPublishing ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                Publishing...
              </>
            ) : (
              <>
                <ShareIcon className="h-4 w-4 mr-2" />
                {scheduleOptions.publishNow ? 'Publish Now' : 'Schedule'}
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}