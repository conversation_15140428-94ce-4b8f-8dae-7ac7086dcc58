/**
 * GenerationConfig Component
 * Enterprise SEO SAAS - AI content generation configuration and advanced settings
 */

import { useState } from 'react'
import { SitemapAnalyzer } from '@/utils/sitemapAnalyzer'
import { AuthorityLinkDiscovery } from '@/utils/authorityLinkDiscovery'
import {
  CogIcon,
  SparklesIcon,
  LinkIcon,
  DocumentTextIcon,
  TagIcon,
  GlobeAltIcon,
  AdjustmentsHorizontalIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  BookOpenIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  PuzzlePieceIcon,
  BrainIcon,
  TargetIcon,
  ClockIcon,
  StarIcon,
  MapIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline'

interface GenerationConfigProps {
  formData: {
    includeInternalLinks: boolean
    includeExternalLinks: boolean
    includeFAQs: boolean
    includeSchemaMarkup: boolean
    includeSitemapLinks: boolean
    includeAuthorityLinks: boolean
    authorityLinkSettings: {
      maxLinks: number
      minAuthorityScore: number
      sourceTypes: string[]
      includeWikipedia: boolean
      includeAcademic: boolean
      includeGovernment: boolean
      includeNews: boolean
      includeIndustry: boolean
    }
    customInstructions: string
    contentType: string
    primaryKeyword: string
    tone: string
    wordCount: number
    projectUrl?: string
  }
  onUpdate: (updates: any) => void
}

interface ConfigOption {
  id: string
  title: string
  description: string
  icon: any
  enabled: boolean
  required?: boolean
  premium?: boolean
  impact: 'high' | 'medium' | 'low'
}

interface AdvancedSetting {
  id: string
  title: string
  description: string
  type: 'select' | 'toggle' | 'slider' | 'text'
  value: any
  options?: { value: string; label: string }[]
  min?: number
  max?: number
  step?: number
}

export default function GenerationConfig({ formData, onUpdate }: GenerationConfigProps) {
  const [activeSection, setActiveSection] = useState<'basic' | 'advanced' | 'ai'>('basic')
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [sitemapAnalyzer] = useState(() => new SitemapAnalyzer())
  const [authorityLinkDiscovery] = useState(() => new AuthorityLinkDiscovery())
  const [isSitemapAnalyzing, setIsSitemapAnalyzing] = useState(false)
  const [isAuthorityAnalyzing, setIsAuthorityAnalyzing] = useState(false)
  const [sitemapOpportunities, setSitemapOpportunities] = useState<number>(0)
  const [authorityLinksFound, setAuthorityLinksFound] = useState<number>(0)

  const basicOptions: ConfigOption[] = [
    {
      id: 'includeInternalLinks',
      title: 'Internal Links',
      description: 'Automatically add relevant internal links to boost page authority and user navigation',
      icon: LinkIcon,
      enabled: formData.includeInternalLinks,
      impact: 'high'
    },
    {
      id: 'includeExternalLinks',
      title: 'External Authority Links',
      description: 'Include high-authority external sources to enhance content credibility and E-A-T',
      icon: GlobeAltIcon,
      enabled: formData.includeExternalLinks,
      impact: 'high'
    },
    {
      id: 'includeFAQs',
      title: 'FAQ Section',
      description: 'Generate frequently asked questions optimized for voice search and featured snippets',
      icon: DocumentTextIcon,
      enabled: formData.includeFAQs,
      impact: 'medium'
    },
    {
      id: 'includeSchemaMarkup',
      title: 'Schema Markup',
      description: 'Add structured data markup for rich snippets and enhanced search appearance',
      icon: TagIcon,
      enabled: formData.includeSchemaMarkup,
      impact: 'medium'
    },
    {
      id: 'includeSitemapLinks',
      title: 'Sitemap-Based Linking',
      description: 'Analyze website sitemap to discover intelligent internal linking opportunities',
      icon: MapIcon,
      enabled: formData.includeSitemapLinks,
      impact: 'high',
      premium: true
    },
    {
      id: 'includeAuthorityLinks',
      title: 'Authority Link Discovery',
      description: 'Automatically discover and embed high-authority external links from Wikipedia, academic, and government sources',
      icon: BookOpenIcon,
      enabled: formData.includeAuthorityLinks,
      impact: 'high',
      premium: true
    }
  ]

  const advancedSettings: AdvancedSetting[] = [
    {
      id: 'contentDepth',
      title: 'Content Depth Level',
      description: 'Control how comprehensive and detailed the generated content should be',
      type: 'select',
      value: 'comprehensive',
      options: [
        { value: 'overview', label: 'Overview (Brief introduction)' },
        { value: 'standard', label: 'Standard (Balanced coverage)' },
        { value: 'comprehensive', label: 'Comprehensive (In-depth analysis)' },
        { value: 'expert', label: 'Expert (Technical deep-dive)' }
      ]
    },
    {
      id: 'keywordVariations',
      title: 'Keyword Variation Usage',
      description: 'Automatically include LSI keywords and semantic variations',
      type: 'toggle',
      value: true
    },
    {
      id: 'competitorDifferentiation',
      title: 'Competitor Differentiation',
      description: 'Ensure content stands out from competitor approaches',
      type: 'toggle',
      value: true
    },
    {
      id: 'readabilityTarget',
      title: 'Target Readability Score',
      description: 'Flesch Reading Ease score target (higher = easier to read)',
      type: 'slider',
      value: 65,
      min: 30,
      max: 90,
      step: 5
    },
    {
      id: 'expertiseLevel',
      title: 'Expertise Signals',
      description: 'Include industry-specific terminology and expert insights',
      type: 'select',
      value: 'intermediate',
      options: [
        { value: 'beginner', label: 'Beginner-friendly' },
        { value: 'intermediate', label: 'Intermediate level' },
        { value: 'advanced', label: 'Advanced/Expert' },
        { value: 'mixed', label: 'Mixed audience' }
      ]
    },
    {
      id: 'contentFreshness',
      title: 'Include Latest Trends',
      description: 'Reference current industry trends and recent developments',
      type: 'toggle',
      value: true
    }
  ]

  const aiEnhancements = [
    {
      id: 'sequential-thinking',
      title: 'Sequential AI Thinking',
      description: 'Multi-step reasoning process for higher quality content',
      icon: BrainIcon,
      enabled: true,
      required: true
    },
    {
      id: 'competitor-intelligence',
      title: 'Real-time Competitor Analysis',
      description: 'Live analysis of competitor content for strategic advantages',
      icon: MagnifyingGlassIcon,
      enabled: true,
      premium: true
    },
    {
      id: 'niche-adaptation',
      title: 'Universal Niche Adaptation',
      description: 'Automatic adaptation to any industry or keyword niche',
      icon: TargetIcon,
      enabled: true,
      premium: true
    },
    {
      id: 'seo-optimization',
      title: 'Advanced SEO Optimization',
      description: 'Real-time SEO scoring and optimization suggestions',
      icon: ChartBarIcon,
      enabled: true
    }
  ]

  const handleOptionToggle = async (optionId: string) => {
    onUpdate({ [optionId]: !formData[optionId as keyof typeof formData] })
    
    // If enabling sitemap links, analyze the sitemap
    if (optionId === 'includeSitemapLinks' && !formData.includeSitemapLinks && formData.projectUrl) {
      await analyzeSitemap()
    }
    
    // If enabling authority links, analyze potential authority links
    if (optionId === 'includeAuthorityLinks' && !formData.includeAuthorityLinks && formData.primaryKeyword) {
      await analyzeAuthorityLinks()
    }
  }

  const analyzeSitemap = async () => {
    if (!formData.projectUrl) return
    
    setIsSitemapAnalyzing(true)
    try {
      const analysis = await sitemapAnalyzer.analyzeSitemap(formData.projectUrl)
      setSitemapOpportunities(analysis.linkingOpportunities.length)
    } catch (error) {
      console.error('Sitemap analysis failed:', error)
      setSitemapOpportunities(0)
    } finally {
      setIsSitemapAnalyzing(false)
    }
  }

  const analyzeAuthorityLinks = async () => {
    if (!formData.primaryKeyword) return
    
    setIsAuthorityAnalyzing(true)
    try {
      const discoveryRequest = {
        topic: formData.primaryKeyword,
        keywords: [formData.primaryKeyword],
        targetLanguage: 'en',
        maxResults: formData.authorityLinkSettings.maxLinks,
        sourceTypes: formData.authorityLinkSettings.sourceTypes,
        minAuthorityScore: formData.authorityLinkSettings.minAuthorityScore,
        includeWikipedia: formData.authorityLinkSettings.includeWikipedia,
        includeReferences: true
      }
      
      const result = await authorityLinkDiscovery.discoverAuthorityLinks(discoveryRequest)
      setAuthorityLinksFound(result.totalSources)
    } catch (error) {
      console.error('Authority link analysis failed:', error)
      setAuthorityLinksFound(0)
    } finally {
      setIsAuthorityAnalyzing(false)
    }
  }

  const handleAdvancedSettingChange = (settingId: string, value: any) => {
    // In a real implementation, these would be stored in formData
    console.log(`Setting ${settingId} to:`, value)
  }

  const handleAuthorityLinkSettingChange = (settingKey: string, value: any) => {
    onUpdate({
      authorityLinkSettings: {
        ...formData.authorityLinkSettings,
        [settingKey]: value
      }
    })
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-green-600 bg-green-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'low': return 'text-gray-600 bg-gray-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getReadabilityLabel = (score: number) => {
    if (score >= 80) return 'Very Easy'
    if (score >= 70) return 'Easy'
    if (score >= 60) return 'Standard'
    if (score >= 50) return 'Fairly Difficult'
    return 'Difficult'
  }

  const sections = [
    { id: 'basic', label: 'Basic Settings', icon: CogIcon },
    { id: 'advanced', label: 'Advanced', icon: AdjustmentsHorizontalIcon },
    { id: 'ai', label: 'AI Enhancements', icon: SparklesIcon }
  ]

  return (
    <div className="p-8 space-y-8">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Generation Configuration</h2>
        <p className="text-gray-600">
          Configure AI settings and optimization features for your content generation
        </p>
      </div>

      {/* Section Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {sections.map((section) => {
            const Icon = section.icon
            return (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id as any)}
                className={`
                  py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center gap-2
                  ${activeSection === section.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <Icon className="h-4 w-4" />
                {section.label}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Basic Settings */}
      {activeSection === 'basic' && (
        <div className="space-y-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <InformationCircleIcon className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-1">SEO Content Enhancement</p>
                <p>These features will automatically enhance your content for better search engine visibility and user engagement.</p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {basicOptions.map((option) => {
              const Icon = option.icon
              return (
                <div
                  key={option.id}
                  className={`
                    border-2 rounded-lg p-6 transition-all cursor-pointer
                    ${option.enabled 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300 bg-white'
                    }
                  `}
                  onClick={() => handleOptionToggle(option.id)}
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className={`
                        w-10 h-10 rounded-lg flex items-center justify-center
                        ${option.enabled ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'}
                      `}>
                        <Icon className="h-5 w-5" />
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900 flex items-center gap-2">
                          {option.title}
                          {option.premium && (
                            <StarIcon className="h-4 w-4 text-yellow-500" />
                          )}
                        </h3>
                        <div className="flex items-center gap-2">
                          <span className={`
                            inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                            ${getImpactColor(option.impact)}
                          `}>
                            {option.impact} impact
                          </span>
                          {option.id === 'includeSitemapLinks' && option.enabled && sitemapOpportunities > 0 && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              {sitemapOpportunities} opportunities found
                            </span>
                          )}
                          {option.id === 'includeAuthorityLinks' && option.enabled && authorityLinksFound > 0 && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {authorityLinksFound} authority links found
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <input
                      type="checkbox"
                      checked={option.enabled}
                      onChange={() => handleOptionToggle(option.id)}
                      className="h-5 w-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      disabled={
                        (option.id === 'includeSitemapLinks' && isSitemapAnalyzing) ||
                        (option.id === 'includeAuthorityLinks' && isAuthorityAnalyzing)
                      }
                    />
                  </div>
                  
                  <p className="text-sm text-gray-600">{option.description}</p>
                  
                  {option.id === 'includeSitemapLinks' && isSitemapAnalyzing && (
                    <div className="mt-3 flex items-center gap-2 text-sm text-blue-600">
                      <ClockIcon className="h-4 w-4 animate-pulse" />
                      Analyzing sitemap for linking opportunities...
                    </div>
                  )}
                  
                  {option.id === 'includeAuthorityLinks' && isAuthorityAnalyzing && (
                    <div className="mt-3 flex items-center gap-2 text-sm text-blue-600">
                      <ClockIcon className="h-4 w-4 animate-pulse" />
                      Discovering authority links for "{formData.primaryKeyword}"...
                    </div>
                  )}
                  
                  {option.premium && (
                    <div className="mt-2 text-xs text-yellow-700 bg-yellow-100 rounded px-2 py-1 inline-block">
                      Premium Feature
                    </div>
                  )}
                </div>
              )
            })}
          </div>

          {/* Authority Link Configuration */}
          {formData.includeAuthorityLinks && (
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
                <BookOpenIcon className="h-5 w-5 text-blue-600" />
                Authority Link Settings
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Maximum Links
                  </label>
                  <select
                    value={formData.authorityLinkSettings.maxLinks}
                    onChange={(e) => handleAuthorityLinkSettingChange('maxLinks', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value={5}>5 links</option>
                    <option value={10}>10 links</option>
                    <option value={15}>15 links</option>
                    <option value={20}>20 links</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Minimum Authority Score
                  </label>
                  <select
                    value={formData.authorityLinkSettings.minAuthorityScore}
                    onChange={(e) => handleAuthorityLinkSettingChange('minAuthorityScore', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value={70}>70+ (Good)</option>
                    <option value={80}>80+ (Excellent)</option>
                    <option value={90}>90+ (Premium)</option>
                  </select>
                </div>
              </div>

              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Source Types
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {[
                    { key: 'includeWikipedia', label: 'Wikipedia', icon: BookOpenIcon },
                    { key: 'includeAcademic', label: 'Academic', icon: BrainIcon },
                    { key: 'includeGovernment', label: 'Government', icon: ShieldCheckIcon },
                    { key: 'includeNews', label: 'News', icon: DocumentTextIcon },
                    { key: 'includeIndustry', label: 'Industry', icon: ChartBarIcon }
                  ].map((source) => {
                    const Icon = source.icon
                    return (
                      <label key={source.key} className="flex items-center gap-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={formData.authorityLinkSettings[source.key as keyof typeof formData.authorityLinkSettings] as boolean}
                          onChange={(e) => handleAuthorityLinkSettingChange(source.key, e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <Icon className="h-4 w-4 text-gray-600" />
                        <span className="text-sm text-gray-700">{source.label}</span>
                      </label>
                    )
                  })}
                </div>
              </div>
            </div>
          )}

          {/* Custom Instructions */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Custom Instructions</h3>
            <textarea
              value={formData.customInstructions}
              onChange={(e) => onUpdate({ customInstructions: e.target.value })}
              placeholder="Add any specific requirements, tone adjustments, or content guidelines for the AI to follow..."
              className="w-full h-32 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
            />
            <div className="mt-2 text-sm text-gray-500">
              Examples: "Include statistics from 2024", "Focus on beginner-friendly explanations", "Add call-to-action at the end"
            </div>
          </div>
        </div>
      )}

      {/* Advanced Settings */}
      {activeSection === 'advanced' && (
        <div className="space-y-6">
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <ExclamationTriangleIcon className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-amber-800">
                <p className="font-medium mb-1">Advanced Configuration</p>
                <p>These settings provide fine-grained control over content generation. Default values are optimized for most use cases.</p>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            {advancedSettings.map((setting) => (
              <div key={setting.id} className="bg-white border border-gray-200 rounded-lg p-6">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="font-medium text-gray-900 mb-1">{setting.title}</h3>
                    <p className="text-sm text-gray-600">{setting.description}</p>
                  </div>
                </div>

                <div className="mt-4">
                  {setting.type === 'select' && (
                    <select
                      value={setting.value}
                      onChange={(e) => handleAdvancedSettingChange(setting.id, e.target.value)}
                      className="w-full md:w-auto px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      {setting.options?.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  )}

                  {setting.type === 'toggle' && (
                    <label className="inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={setting.value}
                        onChange={(e) => handleAdvancedSettingChange(setting.id, e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      <span className="ml-3 text-sm text-gray-700">
                        {setting.value ? 'Enabled' : 'Disabled'}
                      </span>
                    </label>
                  )}

                  {setting.type === 'slider' && (
                    <div className="space-y-2">
                      <input
                        type="range"
                        min={setting.min}
                        max={setting.max}
                        step={setting.step}
                        value={setting.value}
                        onChange={(e) => handleAdvancedSettingChange(setting.id, parseInt(e.target.value))}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                      />
                      <div className="flex justify-between text-sm text-gray-500">
                        <span>{setting.min}</span>
                        <span className="font-medium text-gray-900">
                          {setting.value}
                          {setting.id === 'readabilityTarget' && ` (${getReadabilityLabel(setting.value)})`}
                        </span>
                        <span>{setting.max}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* AI Enhancements */}
      {activeSection === 'ai' && (
        <div className="space-y-6">
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <SparklesIcon className="h-5 w-5 text-purple-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-purple-800">
                <p className="font-medium mb-1">Enterprise AI Features</p>
                <p>Advanced AI capabilities for professional content generation with industry-leading quality.</p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {aiEnhancements.map((enhancement) => {
              const Icon = enhancement.icon
              return (
                <div
                  key={enhancement.id}
                  className={`
                    border-2 rounded-lg p-6
                    ${enhancement.enabled 
                      ? 'border-purple-500 bg-purple-50' 
                      : 'border-gray-200 bg-white'
                    }
                  `}
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className={`
                        w-10 h-10 rounded-lg flex items-center justify-center
                        ${enhancement.enabled ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-600'}
                      `}>
                        <Icon className="h-5 w-5" />
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900 flex items-center gap-2">
                          {enhancement.title}
                          {enhancement.premium && (
                            <StarIcon className="h-4 w-4 text-yellow-500" />
                          )}
                        </h3>
                      </div>
                    </div>
                    
                    {enhancement.required ? (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Required
                      </span>
                    ) : (
                      <div className={`
                        w-5 h-5 rounded-full flex items-center justify-center
                        ${enhancement.enabled ? 'bg-purple-600' : 'bg-gray-300'}
                      `}>
                        {enhancement.enabled && <CheckCircleIcon className="h-3 w-3 text-white" />}
                      </div>
                    )}
                  </div>
                  
                  <p className="text-sm text-gray-600 mb-3">{enhancement.description}</p>
                  
                  {enhancement.premium && (
                    <div className="text-xs text-yellow-700 bg-yellow-100 rounded px-2 py-1 inline-block">
                      Premium Feature
                    </div>
                  )}
                </div>
              )
            })}
          </div>

          {/* Generation Summary */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Generation Summary</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <div className="font-medium text-gray-800 mb-1">Content Type</div>
                <div className="text-gray-600">{formData.contentType.replace('_', ' ')}</div>
              </div>
              
              <div>
                <div className="font-medium text-gray-800 mb-1">Target Length</div>
                <div className="text-gray-600">{formData.wordCount.toLocaleString()} words</div>
              </div>
              
              <div>
                <div className="font-medium text-gray-800 mb-1">Tone</div>
                <div className="text-gray-600 capitalize">{formData.tone}</div>
              </div>
            </div>
            
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="font-medium text-gray-800 mb-2">Enabled Features</div>
              <div className="flex flex-wrap gap-2">
                {basicOptions.filter(opt => opt.enabled).map(opt => (
                  <span key={opt.id} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {opt.title}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}