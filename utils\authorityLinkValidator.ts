/**
 * Authority Link Validator
 * Enterprise SEO SAAS - Comprehensive validation and quality checks for authority links
 */

import { AuthorityLink } from './authorityLinkDiscovery'
import { DomainAuthorityAssessment, LinkValidationResult } from './domainAuthorityAssessment'

export interface ValidationConfig {
  minAuthorityScore: number
  maxBrokenLinkTolerance: number
  requiredDomainAge: number
  checkSSL: boolean
  validateContent: boolean
  maxRedirects: number
  timeout: number
}

export interface LinkQualityMetrics {
  authorityScore: number
  trustScore: number
  relevanceScore: number
  technicalScore: number
  contentScore: number
  overallScore: number
  confidence: number
}

export interface ValidationIssue {
  type: 'error' | 'warning' | 'info'
  code: string
  message: string
  link: AuthorityLink
  severity: 'critical' | 'high' | 'medium' | 'low'
  recommendation?: string
}

export interface BatchValidationResult {
  validLinks: AuthorityLink[]
  invalidLinks: AuthorityLink[]
  issues: ValidationIssue[]
  metrics: {
    totalProcessed: number
    passedValidation: number
    failedValidation: number
    averageQualityScore: number
    processingTime: number
  }
  recommendations: string[]
}

export class AuthorityLinkValidator {
  private domainAssessment: DomainAuthorityAssessment
  private defaultConfig: ValidationConfig = {
    minAuthorityScore: 70,
    maxBrokenLinkTolerance: 0.1, // 10%
    requiredDomainAge: 365, // 1 year
    checkSSL: true,
    validateContent: true,
    maxRedirects: 3,
    timeout: 10000
  }

  constructor() {
    this.domainAssessment = new DomainAuthorityAssessment()
  }

  /**
   * Validate a batch of authority links
   */
  async validateAuthorityLinks(
    links: AuthorityLink[],
    config: Partial<ValidationConfig> = {}
  ): Promise<BatchValidationResult> {
    const validationConfig = { ...this.defaultConfig, ...config }
    const startTime = Date.now()

    const validLinks: AuthorityLink[] = []
    const invalidLinks: AuthorityLink[] = []
    const issues: ValidationIssue[] = []

    // Process links in batches for efficiency
    const batchSize = 5
    for (let i = 0; i < links.length; i += batchSize) {
      const batch = links.slice(i, i + batchSize)
      
      const batchResults = await Promise.all(
        batch.map(link => this.validateSingleLink(link, validationConfig))
      )

      batchResults.forEach((result, index) => {
        const link = batch[index]
        
        if (result.isValid) {
          validLinks.push(link)
        } else {
          invalidLinks.push(link)
        }

        issues.push(...result.issues)
      })
    }

    // Calculate metrics
    const metrics = {
      totalProcessed: links.length,
      passedValidation: validLinks.length,
      failedValidation: invalidLinks.length,
      averageQualityScore: this.calculateAverageQualityScore(validLinks),
      processingTime: Date.now() - startTime
    }

    // Generate recommendations
    const recommendations = this.generateRecommendations(validLinks, invalidLinks, issues)

    return {
      validLinks,
      invalidLinks,
      issues,
      metrics,
      recommendations
    }
  }

  /**
   * Validate a single authority link
   */
  private async validateSingleLink(
    link: AuthorityLink,
    config: ValidationConfig
  ): Promise<{ isValid: boolean; issues: ValidationIssue[] }> {
    const issues: ValidationIssue[] = []

    try {
      // Step 1: Basic validation
      const basicIssues = this.performBasicValidation(link, config)
      issues.push(...basicIssues)

      // Step 2: Authority score validation
      if (link.authorityScore < config.minAuthorityScore) {
        issues.push({
          type: 'error',
          code: 'LOW_AUTHORITY_SCORE',
          message: `Authority score ${link.authorityScore} is below minimum ${config.minAuthorityScore}`,
          link,
          severity: 'high',
          recommendation: 'Consider replacing with higher authority sources'
        })
      }

      // Step 3: URL validation
      const urlValidation = await this.validateURL(link, config)
      issues.push(...urlValidation.issues)

      // Step 4: Domain authority assessment
      if (!this.isDemoLink(link)) {
        const domainMetrics = await this.domainAssessment.assessDomainAuthority(link.domain)
        const domainIssues = this.validateDomainMetrics(domainMetrics, link, config)
        issues.push(...domainIssues)
      }

      // Step 5: Content validation
      if (config.validateContent) {
        const contentIssues = await this.validateLinkContent(link, config)
        issues.push(...contentIssues)
      }

      // Step 6: Calculate quality metrics
      const qualityMetrics = this.calculateQualityMetrics(link, issues)
      
      // Step 7: Determine overall validity
      const criticalIssues = issues.filter(issue => issue.severity === 'critical')
      const highIssues = issues.filter(issue => issue.severity === 'high')
      
      const isValid = criticalIssues.length === 0 && 
                     highIssues.length < 2 && 
                     qualityMetrics.overallScore >= 60

      // Add quality score info
      if (qualityMetrics.overallScore < 70) {
        issues.push({
          type: 'warning',
          code: 'LOW_QUALITY_SCORE',
          message: `Overall quality score ${qualityMetrics.overallScore.toFixed(1)} is below recommended threshold`,
          link,
          severity: 'medium',
          recommendation: 'Review link relevance and authority metrics'
        })
      }

      return { isValid, issues }

    } catch (error) {
      issues.push({
        type: 'error',
        code: 'VALIDATION_ERROR',
        message: `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        link,
        severity: 'critical'
      })
      
      return { isValid: false, issues }
    }
  }

  /**
   * Perform basic validation checks
   */
  private performBasicValidation(link: AuthorityLink, config: ValidationConfig): ValidationIssue[] {
    const issues: ValidationIssue[] = []

    // Check for demo/placeholder data
    if (this.isDemoLink(link)) {
      issues.push({
        type: 'error',
        code: 'DEMO_CONTENT',
        message: 'Demo or placeholder link detected',
        link,
        severity: 'critical',
        recommendation: 'Replace with real authority source'
      })
    }

    // Validate URL format
    try {
      const url = new URL(link.url)
      
      // Check protocol
      if (!['http:', 'https:'].includes(url.protocol)) {
        issues.push({
          type: 'error',
          code: 'INVALID_PROTOCOL',
          message: `Invalid protocol: ${url.protocol}`,
          link,
          severity: 'critical'
        })
      }

      // Check for localhost/internal URLs
      if (this.isInternalURL(url)) {
        issues.push({
          type: 'error',
          code: 'INTERNAL_URL',
          message: 'Internal or localhost URL detected',
          link,
          severity: 'critical'
        })
      }

    } catch (error) {
      issues.push({
        type: 'error',
        code: 'INVALID_URL',
        message: 'Invalid URL format',
        link,
        severity: 'critical'
      })
    }

    // Check title and description
    if (!link.title || link.title.length < 10) {
      issues.push({
        type: 'warning',
        code: 'SHORT_TITLE',
        message: 'Link title is too short or missing',
        link,
        severity: 'medium',
        recommendation: 'Provide descriptive title for better context'
      })
    }

    if (!link.description || link.description.length < 20) {
      issues.push({
        type: 'warning',
        code: 'SHORT_DESCRIPTION',
        message: 'Link description is too short or missing',
        link,
        severity: 'low',
        recommendation: 'Add detailed description for better relevance'
      })
    }

    // Check relevance score
    if (link.relevanceScore < 0.3) {
      issues.push({
        type: 'warning',
        code: 'LOW_RELEVANCE',
        message: `Relevance score ${(link.relevanceScore * 100).toFixed(1)}% is low`,
        link,
        severity: 'medium',
        recommendation: 'Consider more contextually relevant sources'
      })
    }

    return issues
  }

  /**
   * Validate URL accessibility and technical aspects
   */
  private async validateURL(
    link: AuthorityLink,
    config: ValidationConfig
  ): Promise<{ isValid: boolean; issues: ValidationIssue[] }> {
    const issues: ValidationIssue[] = []

    try {
      const validationResult = await this.domainAssessment.validateSingleLink(link.url)

      // Check HTTP status
      if (!validationResult.isValid) {
        issues.push({
          type: 'error',
          code: 'BROKEN_LINK',
          message: `HTTP ${validationResult.httpStatus} - Link is not accessible`,
          link,
          severity: 'critical',
          recommendation: 'Remove or replace broken link'
        })
      }

      // Check redirects
      if (validationResult.redirectChain.length > config.maxRedirects) {
        issues.push({
          type: 'warning',
          code: 'EXCESSIVE_REDIRECTS',
          message: `Too many redirects (${validationResult.redirectChain.length})`,
          link,
          severity: 'medium',
          recommendation: 'Update to final destination URL'
        })
      }

      // Check SSL
      if (config.checkSSL && link.url.startsWith('https://')) {
        if (!validationResult.finalUrl.startsWith('https://')) {
          issues.push({
            type: 'warning',
            code: 'SSL_DOWNGRADE',
            message: 'HTTPS link redirects to HTTP',
            link,
            severity: 'high',
            recommendation: 'Use secure HTTPS links only'
          })
        }
      }

      // Check response time
      if (validationResult.responseTime > 5000) {
        issues.push({
          type: 'warning',
          code: 'SLOW_RESPONSE',
          message: `Slow response time: ${validationResult.responseTime}ms`,
          link,
          severity: 'low',
          recommendation: 'Consider faster loading sources'
        })
      }

      // Check content type
      if (!validationResult.contentType.includes('text/html')) {
        issues.push({
          type: 'info',
          code: 'NON_HTML_CONTENT',
          message: `Content type: ${validationResult.contentType}`,
          link,
          severity: 'low'
        })
      }

      return {
        isValid: validationResult.isValid,
        issues
      }

    } catch (error) {
      issues.push({
        type: 'error',
        code: 'URL_VALIDATION_FAILED',
        message: `Failed to validate URL: ${error instanceof Error ? error.message : 'Unknown error'}`,
        link,
        severity: 'critical'
      })
      
      return { isValid: false, issues }
    }
  }

  /**
   * Validate domain metrics
   */
  private validateDomainMetrics(
    metrics: any,
    link: AuthorityLink,
    config: ValidationConfig
  ): ValidationIssue[] {
    const issues: ValidationIssue[] = []

    // Check domain age
    if (metrics.historicalData.domainAge < config.requiredDomainAge / 365) {
      issues.push({
        type: 'warning',
        code: 'NEW_DOMAIN',
        message: `Domain age (${metrics.historicalData.domainAge} years) is below recommended`,
        link,
        severity: 'medium',
        recommendation: 'Prefer established domains with longer history'
      })
    }

    // Check spam score
    if (metrics.spamScore > 30) {
      issues.push({
        type: 'error',
        code: 'HIGH_SPAM_SCORE',
        message: `High spam score detected: ${metrics.spamScore}`,
        link,
        severity: 'high',
        recommendation: 'Avoid potentially spammy domains'
      })
    }

    // Check trust score
    if (metrics.trustScore < 60) {
      issues.push({
        type: 'warning',
        code: 'LOW_TRUST_SCORE',
        message: `Low trust score: ${metrics.trustScore}`,
        link,
        severity: 'medium',
        recommendation: 'Prioritize more trustworthy sources'
      })
    }

    // Check technical health
    if (!metrics.technicalHealth.httpsEnabled) {
      issues.push({
        type: 'warning',
        code: 'NO_HTTPS',
        message: 'Domain does not use HTTPS',
        link,
        severity: 'medium',
        recommendation: 'Prefer secure HTTPS sources'
      })
    }

    if (metrics.technicalHealth.loadSpeed === 'slow') {
      issues.push({
        type: 'info',
        code: 'SLOW_DOMAIN',
        message: 'Domain has slow loading speed',
        link,
        severity: 'low'
      })
    }

    // Check content quality indicators
    if (metrics.contentQuality.originalityScore < 70) {
      issues.push({
        type: 'warning',
        code: 'LOW_ORIGINALITY',
        message: 'Low content originality score',
        link,
        severity: 'medium',
        recommendation: 'Prefer sources with original content'
      })
    }

    return issues
  }

  /**
   * Validate link content relevance and quality
   */
  private async validateLinkContent(
    link: AuthorityLink,
    config: ValidationConfig
  ): Promise<ValidationIssue[]> {
    const issues: ValidationIssue[] = []

    // Content validation would typically involve:
    // 1. Fetching the page content
    // 2. Analyzing relevance to context
    // 3. Checking for quality indicators
    // 4. Verifying content freshness

    // For now, we'll do basic checks based on available data
    
    // Check last verified date
    if (link.lastVerified) {
      const daysSinceVerified = (Date.now() - new Date(link.lastVerified).getTime()) / (1000 * 60 * 60 * 24)
      if (daysSinceVerified > 90) {
        issues.push({
          type: 'info',
          code: 'STALE_VERIFICATION',
          message: `Link not verified for ${Math.round(daysSinceVerified)} days`,
          link,
          severity: 'low',
          recommendation: 'Re-verify link content and availability'
        })
      }
    }

    // Check anchor text
    if (link.anchor && link.anchor.length > 60) {
      issues.push({
        type: 'warning',
        code: 'LONG_ANCHOR_TEXT',
        message: 'Anchor text is too long',
        link,
        severity: 'low',
        recommendation: 'Use concise, descriptive anchor text'
      })
    }

    // Check context
    if (!link.context || link.context.length < 10) {
      issues.push({
        type: 'info',
        code: 'MISSING_CONTEXT',
        message: 'Link context is missing or too short',
        link,
        severity: 'low',
        recommendation: 'Provide context for better integration'
      })
    }

    return issues
  }

  /**
   * Calculate comprehensive quality metrics
   */
  private calculateQualityMetrics(link: AuthorityLink, issues: ValidationIssue[]): LinkQualityMetrics {
    // Base scores
    let authorityScore = link.authorityScore
    let relevanceScore = link.relevanceScore * 100
    let trustScore = 80 // Default trust score
    let technicalScore = 100
    let contentScore = 80

    // Adjust scores based on issues
    issues.forEach(issue => {
      switch (issue.severity) {
        case 'critical':
          technicalScore -= 30
          trustScore -= 25
          break
        case 'high':
          technicalScore -= 20
          trustScore -= 15
          break
        case 'medium':
          technicalScore -= 10
          contentScore -= 10
          break
        case 'low':
          technicalScore -= 5
          contentScore -= 5
          break
      }
    })

    // Ensure scores don't go below 0
    authorityScore = Math.max(0, authorityScore)
    relevanceScore = Math.max(0, relevanceScore)
    trustScore = Math.max(0, trustScore)
    technicalScore = Math.max(0, technicalScore)
    contentScore = Math.max(0, contentScore)

    // Calculate overall score (weighted average)
    const overallScore = (
      authorityScore * 0.3 +
      relevanceScore * 0.25 +
      trustScore * 0.2 +
      technicalScore * 0.15 +
      contentScore * 0.1
    )

    // Calculate confidence based on issue count
    const confidence = Math.max(0, 100 - (issues.length * 10))

    return {
      authorityScore,
      trustScore,
      relevanceScore,
      technicalScore,
      contentScore,
      overallScore,
      confidence
    }
  }

  /**
   * Generate recommendations based on validation results
   */
  private generateRecommendations(
    validLinks: AuthorityLink[],
    invalidLinks: AuthorityLink[],
    issues: ValidationIssue[]
  ): string[] {
    const recommendations: string[] = []

    // Analyze pass rate
    const passRate = validLinks.length / (validLinks.length + invalidLinks.length)
    if (passRate < 0.7) {
      recommendations.push('Consider reviewing your authority link selection criteria - many links failed validation')
    }

    // Analyze common issues
    const issueCounts: Record<string, number> = {}
    issues.forEach(issue => {
      issueCounts[issue.code] = (issueCounts[issue.code] || 0) + 1
    })

    const sortedIssues = Object.entries(issueCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)

    sortedIssues.forEach(([code, count]) => {
      switch (code) {
        case 'LOW_AUTHORITY_SCORE':
          recommendations.push(`${count} links have low authority scores. Consider raising your minimum threshold.`)
          break
        case 'BROKEN_LINK':
          recommendations.push(`${count} broken links detected. Remove or replace these immediately.`)
          break
        case 'LOW_RELEVANCE':
          recommendations.push(`${count} links have low relevance. Focus on more contextually appropriate sources.`)
          break
        case 'NO_HTTPS':
          recommendations.push(`${count} links lack HTTPS. Prioritize secure sources for better trust signals.`)
          break
      }
    })

    // Analyze source diversity
    const sourceTypes = new Set(validLinks.map(link => link.sourceType))
    if (sourceTypes.size < 3) {
      recommendations.push('Diversify your authority sources - include academic, government, and industry sources')
    }

    // Check average quality
    const avgQuality = this.calculateAverageQualityScore(validLinks)
    if (avgQuality < 75) {
      recommendations.push('Overall link quality is below optimal. Focus on higher authority sources.')
    }

    return recommendations
  }

  // Helper methods
  private isDemoLink(link: AuthorityLink): boolean {
    const demoPatterns = [
      /example\.com/i,
      /test\.com/i,
      /demo\.com/i,
      /placeholder/i,
      /lorem ipsum/i,
      /fake\./i,
      /dummy/i,
      /sample/i
    ]

    return demoPatterns.some(pattern => 
      pattern.test(link.url) || 
      pattern.test(link.title) || 
      pattern.test(link.description)
    )
  }

  private isInternalURL(url: URL): boolean {
    const internalPatterns = [
      /^localhost/i,
      /^127\.0\.0\.1/,
      /^192\.168\./,
      /^10\./,
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
      /\.local$/i,
      /\.internal$/i
    ]

    return internalPatterns.some(pattern => pattern.test(url.hostname))
  }

  private calculateAverageQualityScore(links: AuthorityLink[]): number {
    if (links.length === 0) return 0
    
    const totalScore = links.reduce((sum, link) => {
      const qualityScore = (link.authorityScore * 0.6) + (link.relevanceScore * 100 * 0.4)
      return sum + qualityScore
    }, 0)

    return totalScore / links.length
  }

  /**
   * Quick validation check for real-time feedback
   */
  async quickValidate(link: AuthorityLink): Promise<{
    isValid: boolean
    score: number
    mainIssue?: string
  }> {
    const basicIssues = this.performBasicValidation(link, this.defaultConfig)
    
    if (basicIssues.some(issue => issue.severity === 'critical')) {
      return {
        isValid: false,
        score: 0,
        mainIssue: basicIssues.find(issue => issue.severity === 'critical')?.message
      }
    }

    const qualityScore = (link.authorityScore * 0.6) + (link.relevanceScore * 100 * 0.4)
    
    return {
      isValid: qualityScore >= 60,
      score: Math.round(qualityScore),
      mainIssue: qualityScore < 60 ? 'Quality score below threshold' : undefined
    }
  }
}