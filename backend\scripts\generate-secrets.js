#!/usr/bin/env node

import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Generate cryptographically secure random strings
const generateSecret = (length = 64) => {
  return crypto.randomBytes(length).toString('hex');
};

// Generate secrets
const jwtSecret = generateSecret(64);
const sessionSecret = generateSecret(64);

// Read the current .env file
const envPath = path.join(__dirname, '..', '.env');
let envContent = '';

try {
  envContent = fs.readFileSync(envPath, 'utf8');
} catch (error) {
  console.error('Error reading .env file:', error.message);
  console.log('Please ensure .env file exists in the backend directory.');
  process.exit(1);
}

// Replace the JWT_SECRET and SESSION_SECRET values
envContent = envContent.replace(
  /JWT_SECRET=.*/,
  `JWT_SECRET=${jwtSecret}`
);

envContent = envContent.replace(
  /SESSION_SECRET=.*/,
  `SESSION_SECRET=${sessionSecret}`
);

// Create a backup of the original .env file
const backupPath = path.join(__dirname, '..', '.env.backup');
fs.writeFileSync(backupPath, fs.readFileSync(envPath, 'utf8'));

// Write the updated content back to .env
fs.writeFileSync(envPath, envContent);

console.log('✅ Secure secrets generated successfully!');
console.log('📁 Backup created at: .env.backup');
console.log('\n🔒 New JWT Secret (128 chars):', jwtSecret);
console.log('🔒 New Session Secret (128 chars):', sessionSecret);
console.log('\n⚠️  IMPORTANT: These secrets are now in your .env file.');
console.log('⚠️  Never commit the .env file to version control!');
console.log('⚠️  Keep the backup file secure and do not share it.');