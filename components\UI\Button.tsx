'use client';

import React from 'react';
import { ComponentProps, forwardRef } from 'react';
import { componentTokens } from '@/lib/design-system';

// Button variant and size types
type ButtonVariant = 'primary' | 'secondary' | 'ghost' | 'danger';
type ButtonSize = 'sm' | 'md' | 'lg';

interface ButtonProps extends Omit<ComponentProps<'button'>, 'size'> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(({
  children,
  variant = 'primary',
  size = 'md',
  loading = false,
  leftIcon,
  rightIcon,
  fullWidth = false,
  disabled,
  className = '',
  ...props
}, ref) => {
  
  // Get variant styles
  const variantStyles = componentTokens.button[variant];
  
  // Size configurations
  const sizeConfig = {
    sm: {
      padding: 'px-3 py-1.5',
      fontSize: 'text-sm',
      iconSize: 'h-4 w-4',
      gap: 'gap-1.5'
    },
    md: {
      padding: 'px-4 py-2.5',
      fontSize: 'text-sm',
      iconSize: 'h-5 w-5',
      gap: 'gap-2'
    },
    lg: {
      padding: 'px-6 py-3',
      fontSize: 'text-base',
      iconSize: 'h-5 w-5',
      gap: 'gap-2.5'
    }
  };

  const config = sizeConfig[size];
  
  // Build className
  const baseClasses = [
    'inline-flex items-center justify-center',
    'font-medium rounded-lg',
    'transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    'touch-manipulation select-none',
    config.padding,
    config.fontSize,
    config.gap,
    fullWidth ? 'w-full' : '',
    className
  ].filter(Boolean).join(' ');

  // Variant-specific classes
  const variantClasses = {
    primary: [
      'bg-blue-600 text-white border border-transparent',
      'hover:bg-blue-700 active:bg-blue-800',
      'focus:ring-blue-500',
      'shadow-sm hover:shadow-md'
    ].join(' '),
    
    secondary: [
      'bg-white text-gray-900 border border-gray-300',
      'hover:bg-gray-50 active:bg-gray-100',
      'focus:ring-blue-500',
      'shadow-sm hover:shadow-md'
    ].join(' '),
    
    ghost: [
      'bg-transparent text-gray-700 border border-transparent',
      'hover:bg-gray-100 active:bg-gray-200',
      'focus:ring-blue-500'
    ].join(' '),
    
    danger: [
      'bg-red-600 text-white border border-transparent',
      'hover:bg-red-700 active:bg-red-800',
      'focus:ring-red-500',
      'shadow-sm hover:shadow-md'
    ].join(' ')
  };

  // Dark mode classes
  const darkModeClasses = {
    primary: 'dark:bg-blue-600 dark:hover:bg-blue-700 dark:active:bg-blue-800',
    secondary: [
      'dark:bg-gray-800 dark:text-gray-100 dark:border-gray-600',
      'dark:hover:bg-gray-700 dark:active:bg-gray-600'
    ].join(' '),
    ghost: [
      'dark:text-gray-300 dark:hover:bg-gray-700 dark:active:bg-gray-600'
    ].join(' '),
    danger: 'dark:bg-red-600 dark:hover:bg-red-700 dark:active:bg-red-800'
  };

  const finalClassName = [
    baseClasses,
    variantClasses[variant],
    darkModeClasses[variant]
  ].join(' ');

  return (
    <button
      ref={ref}
      className={finalClassName}
      disabled={disabled || loading}
      {...props}
    >
      {/* Loading spinner */}
      {loading && (
        <svg 
          className={`animate-spin ${config.iconSize} mr-2`} 
          fill="none" 
          viewBox="0 0 24 24"
        >
          <circle 
            className="opacity-25" 
            cx="12" 
            cy="12" 
            r="10" 
            stroke="currentColor" 
            strokeWidth="4"
          />
          <path 
            className="opacity-75" 
            fill="currentColor" 
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      
      {/* Left icon */}
      {leftIcon && !loading && (
        <span className={`${config.iconSize} flex-shrink-0`}>
          {leftIcon}
        </span>
      )}
      
      {/* Button text */}
      {children && <span>{children}</span>}
      
      {/* Right icon */}
      {rightIcon && !loading && (
        <span className={`${config.iconSize} flex-shrink-0`}>
          {rightIcon}
        </span>
      )}
    </button>
  );
});

Button.displayName = 'Button';

export default Button;