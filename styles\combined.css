/* 
 * Combined CSS - Non-critical styles
 * Load after critical CSS for better performance
 */

/* Import Tailwind CSS utilities that are not critical */
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Enhanced animations */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-shimmer {
  animation: shimmer 1.5s infinite;
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Enhanced dashboard styles */
.dashboard-grid {
  display: grid;
  gap: var(--space-6);
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
}

/* Metric card enhancements */
.metric-card {
  background: white;
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
  padding: var(--space-6);
  transition: all 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
}

.metric-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
  border-color: var(--gray-300);
}

.metric-card.loading {
  animation: pulse 2s infinite;
}

.metric-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.metric-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-600);
  margin: 0;
}

.metric-icon {
  width: 24px;
  height: 24px;
  color: var(--gray-400);
  flex-shrink: 0;
}

.metric-value {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--gray-900);
  line-height: 1;
  margin-bottom: var(--space-3);
}

.metric-change {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-size: 0.75rem;
  font-weight: 500;
}

.metric-change.positive {
  color: var(--success-600);
}

.metric-change.negative {
  color: var(--error-600);
}

.metric-change.neutral {
  color: var(--gray-500);
}

/* Enhanced navigation styles */
.nav-section {
  margin-bottom: var(--space-6);
}

.nav-section-title {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--gray-500);
  margin-bottom: var(--space-3);
  padding: 0 var(--space-3);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  color: var(--gray-700);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all 0.2s ease-in-out;
  font-weight: 500;
  font-size: 0.875rem;
  margin-bottom: var(--space-1);
  position: relative;
  min-height: 44px;
}

.nav-item:hover {
  background-color: var(--gray-100);
  color: var(--gray-900);
  transform: translateX(2px);
}

.nav-item.active {
  background-color: var(--primary-100);
  color: var(--primary-700);
  font-weight: 600;
}

.nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--primary-600);
  border-radius: 0 2px 2px 0;
}

.nav-item-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-badge {
  background-color: var(--error-500);
  color: white;
  font-size: 0.625rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-full);
  margin-left: auto;
}

/* Enhanced table styles */
.data-table-container {
  background: white;
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
  overflow: hidden;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background-color: var(--gray-50);
  color: var(--gray-700);
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: var(--space-4);
  text-align: left;
  border-bottom: 1px solid var(--gray-200);
  white-space: nowrap;
}

.data-table td {
  padding: var(--space-4);
  border-bottom: 1px solid var(--gray-100);
  vertical-align: middle;
}

.data-table tr:hover {
  background-color: var(--gray-50);
}

.data-table tr:last-child td {
  border-bottom: none;
}

/* Enhanced form styles */
.form-group {
  margin-bottom: var(--space-4);
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: var(--space-2);
}

.form-input {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  transition: all 0.2s ease-in-out;
  background-color: white;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:disabled {
  background-color: var(--gray-50);
  color: var(--gray-500);
  cursor: not-allowed;
}

.form-error {
  color: var(--error-600);
  font-size: 0.75rem;
  margin-top: var(--space-1);
}

.form-help {
  color: var(--gray-500);
  font-size: 0.75rem;
  margin-top: var(--space-1);
}

/* Enhanced modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease-in-out;
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  max-width: 32rem;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  transform: translateY(20px);
  transition: transform 0.3s ease-in-out;
}

.modal-overlay.active .modal-content {
  transform: translateY(0);
}

.modal-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
}

.modal-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.modal-body {
  padding: var(--space-6);
}

.modal-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--gray-200);
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
}

/* Enhanced skeleton loading */
.skeleton {
  background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-md);
}

.skeleton.title {
  height: 1rem;
  width: 60%;
  margin-bottom: var(--space-2);
}

.skeleton.value {
  height: 2rem;
  width: 40%;
  margin-bottom: var(--space-2);
}

.skeleton.change {
  height: 0.75rem;
  width: 50%;
}

.skeleton.text {
  height: 0.875rem;
  margin-bottom: var(--space-2);
}

.skeleton.text:last-child {
  margin-bottom: 0;
}

/* Enhanced status indicators */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.success {
  background-color: #dcfce7;
  color: #166534;
}

.status-badge.warning {
  background-color: #fef3c7;
  color: #92400e;
}

.status-badge.error {
  background-color: #fecaca;
  color: #991b1b;
}

.status-badge.info {
  background-color: var(--primary-100);
  color: var(--primary-700);
}

/* Enhanced responsive utilities */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }
  
  .mobile-stack {
    flex-direction: column;
  }
  
  .mobile-full {
    width: 100%;
  }
}

@media (max-width: 1024px) {
  .tablet-hidden {
    display: none;
  }
  
  .tablet-stack {
    flex-direction: column;
  }
}

/* Enhanced accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-visible:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Enhanced performance optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.contain-layout {
  contain: layout;
}

.contain-paint {
  contain: paint;
}

.contain-strict {
  contain: strict;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  .sidebar {
    display: none !important;
  }
  
  .main-content {
    margin-left: 0 !important;
  }
}