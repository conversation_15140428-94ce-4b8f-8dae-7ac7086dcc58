import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

/**
 * Simple Next.js Middleware
 * Temporarily simplified to avoid dependency issues
 */
export function middleware(req: NextRequest) {
  // For now, just allow all requests to pass through
  // This can be enhanced later when dependencies are properly installed
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};