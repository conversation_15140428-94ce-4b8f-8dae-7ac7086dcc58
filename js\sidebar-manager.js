// Enhanced Sidebar Manager - Collapsible sections with state persistence
class SidebarManager {
    constructor() {
        this.sidebar = null;
        this.sectionStates = {};
        this.isCollapsed = false;
        this.animationDuration = 300;
        this.storageKey = 'sidebarManagerState';
        
        this.init();
    }
    
    init() {
        this.sidebar = document.getElementById('sidebarNav');
        if (!this.sidebar) {
            console.warn('Sidebar element not found');
            return;
        }
        
        this.loadSavedState();
        this.enhanceSidebarSections();
        this.bindEvents();
        this.initializeKeyboardNavigation();
        this.applySavedStates();
    }
    
    // Enhance existing sidebar sections with collapsible functionality
    enhanceSidebarSections() {
        const sections = this.sidebar.querySelectorAll('.nav-section');
        
        sections.forEach((section, index) => {
            const sectionId = this.getSectionId(section, index);
            const header = section.querySelector('.nav-section-header');
            const items = section.querySelector('.nav-items');
            const toggleBtn = section.querySelector('.section-toggle');
            
            if (!header || !items) return;
            
            // Add enhanced structure if toggle button doesn't exist
            if (!toggleBtn) {
                this.addToggleButton(header, sectionId);
            }
            
            // Add CSS classes for enhanced styling
            section.classList.add('enhanced-nav-section');
            header.classList.add('enhanced-section-header');
            items.classList.add('enhanced-nav-items');
            
            // Set up initial state
            if (this.sectionStates[sectionId] === undefined) {
                this.sectionStates[sectionId] = true; // default to expanded
            }
            
            // Add section identification
            section.setAttribute('data-section-id', sectionId);
            
            // Add ARIA attributes for accessibility
            this.setupAccessibility(section, header, items, sectionId);
        });
    }
    
    // Add toggle button to section header
    addToggleButton(header, sectionId) {
        const toggleBtn = document.createElement('button');
        toggleBtn.className = 'section-toggle enhanced-toggle';
        toggleBtn.setAttribute('aria-label', 'Toggle section');
        toggleBtn.setAttribute('data-section-id', sectionId);
        toggleBtn.innerHTML = `
            <svg class="toggle-icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
        `;
        
        header.appendChild(toggleBtn);
        
        // Bind click event
        toggleBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleSection(sectionId);
        });
    }
    
    // Setup accessibility attributes
    setupAccessibility(section, header, items, sectionId) {
        const toggleBtn = section.querySelector('.section-toggle');
        const isExpanded = this.sectionStates[sectionId];
        
        // ARIA attributes
        header.setAttribute('role', 'button');
        header.setAttribute('aria-expanded', isExpanded);
        header.setAttribute('aria-controls', `nav-items-${sectionId}`);
        header.setAttribute('tabindex', '0');
        
        items.setAttribute('id', `nav-items-${sectionId}`);
        items.setAttribute('role', 'region');
        items.setAttribute('aria-labelledby', `nav-header-${sectionId}`);
        
        const title = header.querySelector('.nav-section-title');
        if (title) {
            title.setAttribute('id', `nav-header-${sectionId}`);
        }
        
        if (toggleBtn) {
            toggleBtn.setAttribute('aria-expanded', isExpanded);
            toggleBtn.setAttribute('aria-controls', `nav-items-${sectionId}`);
        }
    }
    
    // Get unique section ID
    getSectionId(section, index) {
        const title = section.querySelector('.nav-section-title');
        if (title && title.textContent) {
            return title.textContent.toLowerCase().replace(/\s+/g, '-');
        }
        return `section-${index}`;
    }
    
    // Toggle section visibility with smooth animation
    toggleSection(sectionId) {
        const section = this.sidebar.querySelector(`[data-section-id="${sectionId}"]`);
        if (!section) return;
        
        const items = section.querySelector('.nav-items');
        const toggleBtn = section.querySelector('.section-toggle');
        const header = section.querySelector('.nav-section-header');
        const icon = toggleBtn?.querySelector('.toggle-icon');
        
        if (!items) return;
        
        const isCurrentlyExpanded = this.sectionStates[sectionId];
        const newState = !isCurrentlyExpanded;
        
        // Update state
        this.sectionStates[sectionId] = newState;
        
        // Update ARIA attributes
        if (header) header.setAttribute('aria-expanded', newState);
        if (toggleBtn) toggleBtn.setAttribute('aria-expanded', newState);
        
        // Animate section toggle
        this.animateSection(section, items, icon, newState);
        
        // Save state
        this.saveState();
        
        // Emit custom event
        this.emitSectionToggleEvent(sectionId, newState);
    }
    
    // Animate section expansion/collapse
    animateSection(section, items, icon, isExpanding) {
        const startHeight = isExpanding ? 0 : items.scrollHeight;
        const endHeight = isExpanding ? items.scrollHeight : 0;
        
        // Prepare for animation
        items.style.height = `${startHeight}px`;
        items.style.overflow = 'hidden';
        items.style.transition = `height ${this.animationDuration}ms cubic-bezier(0.4, 0.0, 0.2, 1)`;
        
        // Animate icon rotation
        if (icon) {
            icon.style.transition = `transform ${this.animationDuration}ms cubic-bezier(0.4, 0.0, 0.2, 1)`;
            icon.style.transform = isExpanding ? 'rotate(0deg)' : 'rotate(-90deg)';
        }
        
        // Apply section state classes
        section.classList.toggle('section-collapsed', !isExpanding);
        section.classList.toggle('section-expanded', isExpanding);
        
        // Start height animation
        requestAnimationFrame(() => {
            items.style.height = `${endHeight}px`;
        });
        
        // Clean up after animation
        setTimeout(() => {
            items.style.height = '';
            items.style.overflow = '';
            items.style.transition = '';
            
            if (!isExpanding) {
                items.style.display = 'none';
            } else {
                items.style.display = '';
            }
        }, this.animationDuration);
        
        // Show/hide items immediately for expanding
        if (isExpanding) {
            items.style.display = '';
        }
    }
    
    // Apply saved states to all sections
    applySavedStates() {
        Object.entries(this.sectionStates).forEach(([sectionId, isExpanded]) => {
            const section = this.sidebar.querySelector(`[data-section-id="${sectionId}"]`);
            if (!section) return;
            
            const items = section.querySelector('.nav-items');
            const toggleBtn = section.querySelector('.section-toggle');
            const header = section.querySelector('.nav-section-header');
            const icon = toggleBtn?.querySelector('.toggle-icon');
            
            if (!items) return;
            
            // Apply state without animation
            if (isExpanded) {
                section.classList.add('section-expanded');
                section.classList.remove('section-collapsed');
                items.style.display = '';
                if (icon) icon.style.transform = 'rotate(0deg)';
            } else {
                section.classList.add('section-collapsed');
                section.classList.remove('section-expanded');
                items.style.display = 'none';
                if (icon) icon.style.transform = 'rotate(-90deg)';
            }
            
            // Update ARIA attributes
            if (header) header.setAttribute('aria-expanded', isExpanded);
            if (toggleBtn) toggleBtn.setAttribute('aria-expanded', isExpanded);
        });
    }
    
    // Initialize keyboard navigation
    initializeKeyboardNavigation() {
        const headers = this.sidebar.querySelectorAll('.nav-section-header');
        
        headers.forEach(header => {
            header.addEventListener('keydown', (e) => {
                this.handleHeaderKeydown(e, header);
            });
        });
        
        // Add focus management
        this.setupFocusManagement();
    }
    
    // Handle keyboard events for section headers
    handleHeaderKeydown(e, header) {
        const section = header.closest('.nav-section');
        const sectionId = section?.getAttribute('data-section-id');
        
        if (!sectionId) return;
        
        switch (e.key) {
            case 'Enter':
            case ' ':
                e.preventDefault();
                this.toggleSection(sectionId);
                break;
            case 'ArrowDown':
                e.preventDefault();
                this.focusNextSection(header);
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.focusPreviousSection(header);
                break;
            case 'Home':
                e.preventDefault();
                this.focusFirstSection();
                break;
            case 'End':
                e.preventDefault();
                this.focusLastSection();
                break;
        }
    }
    
    // Focus management for keyboard navigation
    focusNextSection(currentHeader) {
        const headers = Array.from(this.sidebar.querySelectorAll('.nav-section-header'));
        const currentIndex = headers.indexOf(currentHeader);
        const nextHeader = headers[currentIndex + 1] || headers[0];
        nextHeader.focus();
    }
    
    focusPreviousSection(currentHeader) {
        const headers = Array.from(this.sidebar.querySelectorAll('.nav-section-header'));
        const currentIndex = headers.indexOf(currentHeader);
        const prevHeader = headers[currentIndex - 1] || headers[headers.length - 1];
        prevHeader.focus();
    }
    
    focusFirstSection() {
        const firstHeader = this.sidebar.querySelector('.nav-section-header');
        if (firstHeader) firstHeader.focus();
    }
    
    focusLastSection() {
        const headers = this.sidebar.querySelectorAll('.nav-section-header');
        const lastHeader = headers[headers.length - 1];
        if (lastHeader) lastHeader.focus();
    }
    
    // Setup focus management and visual indicators
    setupFocusManagement() {
        const headers = this.sidebar.querySelectorAll('.nav-section-header');
        
        headers.forEach(header => {
            header.addEventListener('focus', () => {
                header.classList.add('section-header-focused');
            });
            
            header.addEventListener('blur', () => {
                header.classList.remove('section-header-focused');
            });
        });
    }
    
    // Expand all sections
    expandAllSections() {
        Object.keys(this.sectionStates).forEach(sectionId => {
            if (!this.sectionStates[sectionId]) {
                this.toggleSection(sectionId);
            }
        });
    }
    
    // Collapse all sections
    collapseAllSections() {
        Object.keys(this.sectionStates).forEach(sectionId => {
            if (this.sectionStates[sectionId]) {
                this.toggleSection(sectionId);
            }
        });
    }
    
    // Toggle sidebar visibility
    toggleSidebar() {
        if (!this.sidebar) return;
        
        this.isCollapsed = !this.isCollapsed;
        this.sidebar.classList.toggle('sidebar-collapsed', this.isCollapsed);
        
        // Update body class for layout adjustments
        document.body.classList.toggle('sidebar-collapsed', this.isCollapsed);
        
        // Save sidebar state
        this.saveState();
        
        // Emit custom event
        this.emitSidebarToggleEvent(this.isCollapsed);
        
        // Update ARIA attributes
        this.sidebar.setAttribute('aria-hidden', this.isCollapsed);
    }
    
    // Bind global events
    bindEvents() {
        // Global sidebar toggle
        const toggleBtn = document.querySelector('.sidebar-toggle');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }
        
        // Keyboard shortcut for sidebar toggle (Ctrl+B)
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
                e.preventDefault();
                this.toggleSidebar();
            }
        });
        
        // Handle window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });
        
        // Handle escape key to close sidebar on mobile
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && window.innerWidth <= 768 && !this.isCollapsed) {
                this.toggleSidebar();
            }
        });
    }
    
    // Handle window resize
    handleResize() {
        // Auto-collapse sidebar on mobile
        if (window.innerWidth <= 768 && !this.isCollapsed) {
            this.isCollapsed = true;
            this.sidebar.classList.add('sidebar-collapsed');
            document.body.classList.add('sidebar-collapsed');
        }
    }
    
    // Save state to localStorage
    saveState() {
        try {
            const state = {
                sectionStates: this.sectionStates,
                isCollapsed: this.isCollapsed,
                timestamp: Date.now()
            };
            localStorage.setItem(this.storageKey, JSON.stringify(state));
        } catch (error) {
            console.error('Error saving sidebar state:', error);
        }
    }
    
    // Load saved state from localStorage
    loadSavedState() {
        try {
            const savedState = localStorage.getItem(this.storageKey);
            if (savedState) {
                const state = JSON.parse(savedState);
                this.sectionStates = state.sectionStates || {};
                this.isCollapsed = state.isCollapsed || false;
                
                // Apply sidebar collapsed state
                if (this.isCollapsed && this.sidebar) {
                    this.sidebar.classList.add('sidebar-collapsed');
                    document.body.classList.add('sidebar-collapsed');
                }
            }
        } catch (error) {
            console.error('Error loading sidebar state:', error);
            this.sectionStates = {};
            this.isCollapsed = false;
        }
    }
    
    // Emit custom events
    emitSectionToggleEvent(sectionId, isExpanded) {
        const event = new CustomEvent('sectionToggle', {
            detail: { sectionId, isExpanded }
        });
        document.dispatchEvent(event);
    }
    
    emitSidebarToggleEvent(isCollapsed) {
        const event = new CustomEvent('sidebarToggle', {
            detail: { isCollapsed }
        });
        document.dispatchEvent(event);
    }
    
    // Public API methods
    getSectionState(sectionId) {
        return this.sectionStates[sectionId];
    }
    
    setSectionState(sectionId, isExpanded) {
        if (this.sectionStates[sectionId] !== isExpanded) {
            this.toggleSection(sectionId);
        }
    }
    
    getSidebarState() {
        return {
            isCollapsed: this.isCollapsed,
            sectionStates: { ...this.sectionStates }
        };
    }
    
    // Cleanup method
    destroy() {
        // Remove event listeners
        const toggleBtn = document.querySelector('.sidebar-toggle');
        if (toggleBtn) {
            toggleBtn.removeEventListener('click', this.toggleSidebar);
        }
        
        // Remove enhanced classes
        const sections = this.sidebar?.querySelectorAll('.enhanced-nav-section');
        sections?.forEach(section => {
            section.classList.remove('enhanced-nav-section');
        });
        
        // Save final state
        this.saveState();
    }
}

// Initialize sidebar manager when DOM is loaded
let sidebarManager;
document.addEventListener('DOMContentLoaded', function() {
    sidebarManager = new SidebarManager();
});

// Legacy function support for existing code
function toggleSidebar() {
    if (sidebarManager) {
        sidebarManager.toggleSidebar();
    }
}

function toggleSection(element) {
    if (!sidebarManager) return;
    
    // Handle both element and section ID
    let sectionId;
    if (typeof element === 'string') {
        sectionId = element;
    } else if (element && element.dataset) {
        sectionId = element.dataset.sectionId;
    } else if (element) {
        const section = element.closest('.nav-section');
        sectionId = section?.getAttribute('data-section-id');
    }
    
    if (sectionId) {
        sidebarManager.toggleSection(sectionId);
    }
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SidebarManager;
}