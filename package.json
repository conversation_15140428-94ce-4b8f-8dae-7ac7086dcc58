{"name": "seo-saas-html", "version": "1.0.0", "description": "World's most advanced AI-powered SEO content generation platform", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "next dev", "dev:backend": "cd backend && npm run dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest --config jest.config.mjs", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "typecheck": "tsc --noEmit", "backend:dev": "cd backend && npm run dev", "backend:start": "cd backend && npm start", "backend:test": "cd backend && npm test", "setup": "npm install && cd backend && npm install", "db:setup": "node scripts/setup-database.js", "db:migrate": "node scripts/migrate-database.js", "db:seed": "node scripts/seed-database.js", "clean": "rm -rf .next dist backend/dist node_modules backend/node_modules"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@mendable/firecrawl-js": "^1.29.1", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.0.10", "@supabase/supabase-js": "^2.39.3", "@types/jest": "^30.0.0", "@types/lodash": "^4.14.202", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "concurrently": "^8.2.0", "framer-motion": "^10.16.16", "lodash": "^4.17.21", "lucide-react": "^0.302.0", "next": "^14.0.4", "node-fetch": "^3.3.2", "openai": "^4.104.0", "postcss": "^8.4.32", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-markdown": "^9.0.1", "react-query": "^3.39.3", "recharts": "^2.8.0", "remark-gfm": "^4.0.0", "tailwind-merge": "^2.2.0", "tailwindcss": "^3.4.0", "typescript": "^5.3.3", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.6", "@testing-library/react": "^14.1.2", "@types/node": "^24.0.12", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "eslint-config-prettier": "^9.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.1.1", "tailwindcss-animate": "^1.0.7"}, "keywords": ["seo", "content-generation", "ai", "next.js", "typescript", "supabase"], "author": "SEO SAAS Team", "license": "MIT"}