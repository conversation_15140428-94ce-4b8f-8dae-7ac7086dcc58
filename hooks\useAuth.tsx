'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { createClientSupabase } from '@/utils/supabase/client';
import type { User, Session } from '@supabase/supabase-js';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: Error | null }>;
  signUp: (email: string, password: string, metadata?: any) => Promise<{ error: Error | null }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: Error | null }>;
  updateProfile: (data: any) => Promise<{ error: Error | null }>;
  isSubscribed: (tier?: 'free' | 'pro' | 'enterprise') => boolean;
  userTier: 'free' | 'pro' | 'enterprise';
  usageCount: number;
  usageLimit: number;
  refreshUserData: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [userProfile, setUserProfile] = useState<any>(null);
  const router = useRouter();
  
  // Use demo mode if Supabase is not configured
  const isDemoMode = process.env.NEXT_PUBLIC_DEMO_MODE === 'true';
  const supabase = !isDemoMode ? createClientSupabase() : null;

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        if (!supabase) {
          // Demo mode - skip auth
          setLoading(false);
          return;
        }
        
        const { data: { session: initialSession } } = await supabase.auth.getSession();
        setSession(initialSession);
        setUser(initialSession?.user ?? null);
        
        if (initialSession?.user) {
          await fetchUserProfile(initialSession.user.id);
        }
      } catch (error) {
        console.error('Error getting initial session:', error);
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth changes only if not in demo mode
    if (!supabase) {
      return; // No cleanup needed for demo mode
    }

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);
        
        if (session?.user) {
          await fetchUserProfile(session.user.id);
        } else {
          setUserProfile(null);
        }
        
        setLoading(false);

        // Handle auth events
        switch (event) {
          case 'SIGNED_IN':
            console.log('User signed in');
            break;
          case 'SIGNED_OUT':
            console.log('User signed out');
            setUserProfile(null);
            break;
          case 'TOKEN_REFRESHED':
            console.log('Token refreshed');
            break;
          default:
            break;
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [supabase?.auth]);

  const fetchUserProfile = async (userId: string) => {
    if (!supabase) return; // Skip in demo mode
    
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching user profile:', error);
        return;
      }

      setUserProfile(data);
    } catch (error) {
      console.error('Error in fetchUserProfile:', error);
    }
  };

  const signIn = async (email: string, password: string) => {
    if (!supabase) {
      // Demo mode - always return success
      return { error: null };
    }
    
    try {
      setLoading(true);
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        return { error };
      }

      return { error: null };
    } catch (error) {
      return { error: error as Error };
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, metadata?: any) => {
    if (!supabase) {
      // Demo mode - always return success
      return { error: null };
    }
    
    try {
      setLoading(true);
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata,
        },
      });

      if (error) {
        return { error };
      }

      return { error: null };
    } catch (error) {
      return { error: error as Error };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    if (!supabase) {
      // Demo mode - just clear local state
      setUser(null);
      setSession(null);
      setUserProfile(null);
      router.push('/');
      return;
    }
    
    try {
      setLoading(true);
      await supabase.auth.signOut();
      setUser(null);
      setSession(null);
      setUserProfile(null);
      router.push('/');
    } catch (error) {
      console.error('Error signing out:', error);
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email: string) => {
    if (!supabase) {
      // Demo mode - always return success
      return { error: null };
    }
    
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      });

      if (error) {
        return { error };
      }

      return { error: null };
    } catch (error) {
      return { error: error as Error };
    }
  };

  const refreshUserData = async () => {
    if (user) {
      await fetchUserProfile(user.id);
    }
  };

  const updateProfile = async (data: any) => {
    try {
      if (!user) {
        return { error: new Error('No user logged in') };
      }

      setLoading(true);

      // Update auth metadata
      const { error: authError } = await supabase.auth.updateUser({
        data: data,
      });

      if (authError) {
        return { error: authError };
      }

      // Update user profile in database
      const { error: profileError } = await supabase
        .from('users')
        .update(data)
        .eq('id', user.id);

      if (profileError) {
        return { error: profileError };
      }

      // Refresh user profile
      await fetchUserProfile(user.id);

      return { error: null };
    } catch (error) {
      return { error: error as Error };
    } finally {
      setLoading(false);
    }
  };

  const isSubscribed = (tier?: 'free' | 'pro' | 'enterprise') => {
    if (!userProfile) return false;
    
    if (!tier) {
      return userProfile.subscription_tier !== 'free';
    }

    const tierLevels = {
      'free': 1,
      'pro': 2,
      'enterprise': 3
    };

    const userTierLevel = tierLevels[userProfile.subscription_tier] || 0;
    const requiredTierLevel = tierLevels[tier] || 0;

    return userTierLevel >= requiredTierLevel;
  };

  const userTier = userProfile?.subscription_tier || 'free';
  const usageCount = userProfile?.usage_count || 0;
  const usageLimit = userProfile?.usage_limit || 10;

  const value: AuthContextType = {
    user,
    session,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updateProfile,
    isSubscribed,
    userTier,
    usageCount,
    usageLimit,
    refreshUserData,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Protected route wrapper component (using regular auth)
export function ProtectedRoute({ 
  children,
  redirectTo = '/auth/login',
  requireSubscription,
}: {
  children: React.ReactNode;
  redirectTo?: string;
  requireSubscription?: 'free' | 'pro' | 'enterprise';
}) {
  const { user, loading, isSubscribed } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      if (!user) {
        router.push(redirectTo);
      } else if (requireSubscription && !isSubscribed(requireSubscription)) {
        router.push('/pricing');
      }
    }
  }, [user, loading, requireSubscription, isSubscribed, router, redirectTo]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  if (requireSubscription && !isSubscribed(requireSubscription)) {
    return null;
  }

  return <>{children}</>;
}

// Usage limit wrapper component
export function UsageLimitWrapper({ 
  children,
  fallback,
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const { usageCount, usageLimit, userTier } = useAuth();
  
  const hasUsageRemaining = usageCount < usageLimit;
  
  if (!hasUsageRemaining) {
    return (
      fallback || (
        <div className="text-center p-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">
            Usage Limit Reached
          </h3>
          <p className="text-gray-600 mb-6">
            You've reached your {userTier} plan limit of {usageLimit} content generations this month.
          </p>
          <button
            onClick={() => window.location.href = '/pricing'}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold"
          >
            Upgrade Plan
          </button>
        </div>
      )
    );
  }
  
  return <>{children}</>;
}