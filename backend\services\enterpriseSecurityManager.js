import crypto from 'crypto';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import rateLimit from 'express-rate-limit';
import winston from 'winston';
import { createClient } from '@supabase/supabase-js';
import validator from 'validator';

// Initialize logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/security-manager.log' }),
    new winston.transports.Console({
      format: winston.format.simple(),
      silent: process.env.NODE_ENV === 'production'
    })
  ]
});

/**
 * Enterprise Security Manager
 * Multi-layer security protection system
 */
export class EnterpriseSecurityManager {
  constructor() {
    this.supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    // Security configuration
    this.config = {
      jwt: {
        secret: process.env.JWT_SECRET,
        expiresIn: process.env.JWT_EXPIRES_IN || '7d',
        refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d'
      },
      session: {
        secret: process.env.SESSION_SECRET,
        timeout: parseInt(process.env.SESSION_TIMEOUT || '1800000'), // 30 minutes
        secure: process.env.SESSION_SECURE === 'true'
      },
      password: {
        minLength: parseInt(process.env.PASSWORD_MIN_LENGTH || '8'),
        requireSpecialChars: process.env.PASSWORD_REQUIRE_SPECIAL_CHARS === 'true',
        bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12')
      },
      rateLimit: {
        windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
        maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
        loginMax: parseInt(process.env.RATE_LIMIT_LOGIN_MAX || '5'),
        loginWindow: parseInt(process.env.RATE_LIMIT_LOGIN_WINDOW || '900000')
      },
      csrf: {
        secret: process.env.CSRF_SECRET,
        cookieName: process.env.CSRF_COOKIE_NAME || '_csrf'
      }
    };

    // Initialize security components
    this.initializeSecurityComponents();
  }

  /**
   * Initialize security components
   */
  initializeSecurityComponents() {
    // CSRF token storage
    this.csrfTokens = new Map();
    
    // Active sessions tracking
    this.activeSessions = new Map();
    
    // Rate limiting tracking
    this.rateLimitStore = new Map();
    
    // Security event patterns
    this.suspiciousPatterns = [
      /script/gi,
      /eval\(/gi,
      /document\.write/gi,
      /innerHTML/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<\s*script/gi,
      /expression\s*\(/gi,
      /vbscript:/gi,
      /data:text\/html/gi
    ];

    // Malicious payloads detection
    this.maliciousPayloads = [
      /(union|select|insert|update|delete|drop|create|alter|exec|declare)\s+/gi,
      /(\b(or|and)\b\s*\d+\s*=\s*\d+)|(\b(or|and)\b\s*['"]\w*['"]\s*=\s*['"]\w*['"])/gi,
      /'(\s*(or|and)\s*')/gi,
      /\/\*[\s\S]*?\*\//g,
      /-{2,}/g,
      /;\s*(drop|delete|insert|update|create)/gi
    ];

    logger.info('Enterprise security components initialized');
  }

  /**
   * Generate CSRF token
   */
  generateCSRFToken(sessionId) {
    const token = crypto.randomBytes(32).toString('hex');
    const timestamp = Date.now();
    
    this.csrfTokens.set(sessionId, {
      token,
      timestamp,
      used: false
    });

    // Clean up old tokens
    this.cleanupCSRFTokens();

    logger.info('CSRF token generated', { sessionId });
    return token;
  }

  /**
   * Validate CSRF token
   */
  validateCSRFToken(sessionId, token) {
    const storedToken = this.csrfTokens.get(sessionId);
    
    if (!storedToken) {
      logger.warn('CSRF token validation failed - no token found', { sessionId });
      return false;
    }

    if (storedToken.used) {
      logger.warn('CSRF token validation failed - token already used', { sessionId });
      return false;
    }

    if (Date.now() - storedToken.timestamp > 3600000) { // 1 hour expiry
      logger.warn('CSRF token validation failed - token expired', { sessionId });
      this.csrfTokens.delete(sessionId);
      return false;
    }

    if (storedToken.token !== token) {
      logger.warn('CSRF token validation failed - token mismatch', { sessionId });
      return false;
    }

    // Mark token as used
    storedToken.used = true;
    
    logger.info('CSRF token validated successfully', { sessionId });
    return true;
  }

  /**
   * Clean up expired CSRF tokens
   */
  cleanupCSRFTokens() {
    const now = Date.now();
    const expiry = 3600000; // 1 hour

    for (const [sessionId, tokenData] of this.csrfTokens.entries()) {
      if (now - tokenData.timestamp > expiry) {
        this.csrfTokens.delete(sessionId);
      }
    }
  }

  /**
   * Hash password securely
   */
  async hashPassword(password) {
    try {
      // Validate password strength
      const passwordValidation = this.validatePasswordStrength(password);
      if (!passwordValidation.valid) {
        throw new Error(`Password validation failed: ${passwordValidation.reason}`);
      }

      const salt = await bcrypt.genSalt(this.config.password.bcryptRounds);
      const hashedPassword = await bcrypt.hash(password, salt);
      
      logger.info('Password hashed successfully');
      return hashedPassword;
    } catch (error) {
      logger.error('Password hashing failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Verify password
   */
  async verifyPassword(password, hashedPassword) {
    try {
      const isValid = await bcrypt.compare(password, hashedPassword);
      logger.info('Password verification completed', { valid: isValid });
      return isValid;
    } catch (error) {
      logger.error('Password verification failed', { error: error.message });
      return false;
    }
  }

  /**
   * Validate password strength
   */
  validatePasswordStrength(password) {
    const requirements = [];
    let score = 0;

    // Length check
    if (password.length < this.config.password.minLength) {
      requirements.push(`Password must be at least ${this.config.password.minLength} characters long`);
    } else {
      score += 1;
    }

    // Uppercase check
    if (!/[A-Z]/.test(password)) {
      requirements.push('Password must contain at least one uppercase letter');
    } else {
      score += 1;
    }

    // Lowercase check
    if (!/[a-z]/.test(password)) {
      requirements.push('Password must contain at least one lowercase letter');
    } else {
      score += 1;
    }

    // Number check
    if (!/\d/.test(password)) {
      requirements.push('Password must contain at least one number');
    } else {
      score += 1;
    }

    // Special character check (if required)
    if (this.config.password.requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      requirements.push('Password must contain at least one special character');
    } else if (this.config.password.requireSpecialChars) {
      score += 1;
    }

    // Common password check
    if (this.isCommonPassword(password)) {
      requirements.push('Password is too common - please choose a more unique password');
    } else {
      score += 1;
    }

    const valid = requirements.length === 0;
    const strength = score >= 5 ? 'strong' : score >= 3 ? 'medium' : 'weak';

    return {
      valid,
      strength,
      score,
      requirements,
      reason: valid ? 'Password meets all requirements' : requirements.join(', ')
    };
  }

  /**
   * Check if password is commonly used
   */
  isCommonPassword(password) {
    const commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123', 'password123',
      'admin', 'letmein', 'welcome', 'monkey', '1234567890', 'password1',
      'qwerty123', 'admin123', 'root', 'user', 'test', 'guest', 'login'
    ];

    return commonPasswords.includes(password.toLowerCase());
  }

  /**
   * Generate JWT token
   */
  generateJWTToken(payload, options = {}) {
    try {
      const tokenPayload = {
        ...payload,
        iat: Math.floor(Date.now() / 1000),
        jti: crypto.randomBytes(16).toString('hex') // Unique token ID
      };

      const token = jwt.sign(
        tokenPayload,
        this.config.jwt.secret,
        {
          expiresIn: options.expiresIn || this.config.jwt.expiresIn,
          issuer: 'seo-saas-api',
          audience: 'seo-saas-client'
        }
      );

      logger.info('JWT token generated', { 
        userId: payload.userId,
        expiresIn: options.expiresIn || this.config.jwt.expiresIn
      });

      return token;
    } catch (error) {
      logger.error('JWT token generation failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Verify JWT token
   */
  verifyJWTToken(token) {
    try {
      const decoded = jwt.verify(token, this.config.jwt.secret, {
        issuer: 'seo-saas-api',
        audience: 'seo-saas-client'
      });

      logger.info('JWT token verified successfully', { userId: decoded.userId });
      return { valid: true, decoded };
    } catch (error) {
      logger.warn('JWT token verification failed', { 
        error: error.message,
        tokenPreview: token.substring(0, 20) + '...'
      });
      return { valid: false, error: error.message };
    }
  }

  /**
   * Create secure session
   */
  async createSession(userId, metadata = {}) {
    try {
      const sessionId = crypto.randomBytes(32).toString('hex');
      const sessionData = {
        id: sessionId,
        userId,
        metadata,
        createdAt: new Date(),
        lastAccessedAt: new Date(),
        expiresAt: new Date(Date.now() + this.config.session.timeout),
        isActive: true
      };

      // Store in memory cache
      this.activeSessions.set(sessionId, sessionData);

      // Store in database
      const { error } = await this.supabase
        .from('user_sessions')
        .insert([{
          id: sessionId,
          user_id: userId,
          session_token: sessionId,
          ip_address: metadata.ipAddress,
          user_agent: metadata.userAgent,
          location: metadata.location,
          device_type: metadata.deviceType,
          expires_at: sessionData.expiresAt.toISOString()
        }]);

      if (error) {
        logger.error('Failed to store session in database', { error });
      }

      logger.info('Session created successfully', { sessionId, userId });
      return sessionData;
    } catch (error) {
      logger.error('Session creation failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Validate session
   */
  async validateSession(sessionId) {
    try {
      // Check memory cache first
      const sessionData = this.activeSessions.get(sessionId);
      
      if (!sessionData) {
        logger.warn('Session validation failed - session not found', { sessionId });
        return { valid: false, reason: 'Session not found' };
      }

      if (!sessionData.isActive) {
        logger.warn('Session validation failed - session inactive', { sessionId });
        return { valid: false, reason: 'Session inactive' };
      }

      if (Date.now() > sessionData.expiresAt.getTime()) {
        logger.warn('Session validation failed - session expired', { sessionId });
        await this.destroySession(sessionId);
        return { valid: false, reason: 'Session expired' };
      }

      // Update last accessed time
      sessionData.lastAccessedAt = new Date();
      this.activeSessions.set(sessionId, sessionData);

      logger.info('Session validated successfully', { sessionId });
      return { valid: true, session: sessionData };
    } catch (error) {
      logger.error('Session validation error', { error: error.message, sessionId });
      return { valid: false, reason: 'Validation error' };
    }
  }

  /**
   * Destroy session
   */
  async destroySession(sessionId) {
    try {
      // Remove from memory cache
      this.activeSessions.delete(sessionId);

      // Deactivate in database
      const { error } = await this.supabase
        .from('user_sessions')
        .update({ is_active: false })
        .eq('id', sessionId);

      if (error) {
        logger.error('Failed to deactivate session in database', { error });
      }

      logger.info('Session destroyed successfully', { sessionId });
    } catch (error) {
      logger.error('Session destruction failed', { error: error.message });
    }
  }

  /**
   * Input sanitization
   */
  sanitizeInput(input, type = 'text') {
    if (typeof input !== 'string') {
      return '';
    }

    try {
      switch (type) {
        case 'html':
          return this.sanitizeHTML(input);
        case 'sql':
          return this.sanitizeSQL(input);
        case 'url':
          return this.sanitizeURL(input);
        case 'email':
          return this.sanitizeEmail(input);
        case 'filename':
          return this.sanitizeFilename(input);
        default:
          return this.sanitizeText(input);
      }
    } catch (error) {
      logger.error('Input sanitization failed', { error: error.message, type });
      return '';
    }
  }

  /**
   * Sanitize HTML input
   */
  sanitizeHTML(input) {
    // Remove script tags and event handlers
    let sanitized = input
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
      .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/data:text\/html/gi, '')
      .replace(/vbscript:/gi, '');

    // Escape remaining HTML
    sanitized = validator.escape(sanitized);

    return sanitized;
  }

  /**
   * Sanitize SQL input
   */
  sanitizeSQL(input) {
    // Remove SQL injection patterns
    let sanitized = input
      .replace(/['";\\]/g, '') // Remove quotes and backslashes
      .replace(/(-{2,}|\/\*[\s\S]*?\*\/)/g, '') // Remove SQL comments
      .replace(/\b(union|select|insert|update|delete|drop|create|alter|exec|declare|sp_|xp_)\b/gi, '') // Remove SQL keywords
      .replace(/\b(or|and)\s+\d+\s*=\s*\d+/gi, '') // Remove boolean logic injections
      .replace(/\b(or|and)\s+['"][^'"]*['"]\s*=\s*['"][^'"]*['"]/gi, ''); // Remove string logic injections

    return sanitized.trim();
  }

  /**
   * Sanitize URL input
   */
  sanitizeURL(input) {
    try {
      // Validate and sanitize URL
      if (!validator.isURL(input, { 
        protocols: ['http', 'https'],
        require_protocol: true,
        allow_fragments: true,
        allow_query_components: true
      })) {
        return '';
      }

      const url = new URL(input);
      
      // Only allow http and https protocols
      if (!['http:', 'https:'].includes(url.protocol)) {
        return '';
      }

      return url.toString();
    } catch {
      return '';
    }
  }

  /**
   * Sanitize email input
   */
  sanitizeEmail(input) {
    const email = input.toLowerCase().trim();
    
    if (!validator.isEmail(email)) {
      return '';
    }

    return email;
  }

  /**
   * Sanitize filename
   */
  sanitizeFilename(input) {
    return input
      .replace(/[^a-zA-Z0-9.\-_]/g, '') // Only allow alphanumeric, dots, hyphens, underscores
      .replace(/\.{2,}/g, '.') // Prevent directory traversal
      .replace(/^\.+/, '') // Remove leading dots
      .substring(0, 255); // Limit length
  }

  /**
   * Sanitize text input
   */
  sanitizeText(input) {
    return input
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim();
  }

  /**
   * Detect suspicious activity
   */
  detectSuspiciousActivity(input, context = {}) {
    const suspiciousIndicators = [];
    let riskScore = 0;

    // Check for malicious patterns
    this.suspiciousPatterns.forEach((pattern, index) => {
      const matches = input.match(pattern);
      if (matches) {
        suspiciousIndicators.push({
          pattern: pattern.toString(),
          matches: matches.length,
          type: 'xss_attempt',
          severity: 'high'
        });
        riskScore += matches.length * 20;
      }
    });

    // Check for SQL injection patterns
    this.maliciousPayloads.forEach((pattern, index) => {
      const matches = input.match(pattern);
      if (matches) {
        suspiciousIndicators.push({
          pattern: pattern.toString(),
          matches: matches.length,
          type: 'sql_injection_attempt',
          severity: 'critical'
        });
        riskScore += matches.length * 30;
      }
    });

    // Check for unusual patterns
    if (input.length > 10000) {
      suspiciousIndicators.push({
        type: 'oversized_input',
        severity: 'medium',
        details: `Input size: ${input.length} characters`
      });
      riskScore += 10;
    }

    // Check for excessive special characters
    const specialCharCount = (input.match(/[^a-zA-Z0-9\s]/g) || []).length;
    const specialCharRatio = specialCharCount / input.length;
    
    if (specialCharRatio > 0.3) {
      suspiciousIndicators.push({
        type: 'excessive_special_chars',
        severity: 'medium',
        details: `Special character ratio: ${(specialCharRatio * 100).toFixed(2)}%`
      });
      riskScore += 15;
    }

    // Check for repeated patterns
    const repeatedPatterns = this.detectRepeatedPatterns(input);
    if (repeatedPatterns.length > 0) {
      suspiciousIndicators.push({
        type: 'repeated_patterns',
        severity: 'low',
        details: repeatedPatterns
      });
      riskScore += 5;
    }

    const isSuspicious = riskScore > 25;
    const severity = riskScore > 50 ? 'critical' : riskScore > 25 ? 'high' : 'low';

    return {
      isSuspicious,
      riskScore,
      severity,
      indicators: suspiciousIndicators,
      recommendation: this.getSecurityRecommendation(riskScore, suspiciousIndicators)
    };
  }

  /**
   * Detect repeated patterns in input
   */
  detectRepeatedPatterns(input) {
    const patterns = [];
    const minLength = 3;
    const maxLength = 20;

    for (let length = minLength; length <= maxLength; length++) {
      const substrings = {};
      
      for (let i = 0; i <= input.length - length; i++) {
        const substring = input.substring(i, i + length);
        substrings[substring] = (substrings[substring] || 0) + 1;
      }

      Object.entries(substrings).forEach(([pattern, count]) => {
        if (count >= 3 && pattern.trim().length === pattern.length) {
          patterns.push({ pattern, count, length });
        }
      });
    }

    return patterns.slice(0, 5); // Return top 5 patterns
  }

  /**
   * Get security recommendation
   */
  getSecurityRecommendation(riskScore, indicators) {
    if (riskScore > 50) {
      return 'BLOCK_REQUEST - Critical security threat detected';
    }
    
    if (riskScore > 25) {
      return 'ENHANCED_MONITORING - Suspicious activity detected';
    }
    
    if (indicators.length > 0) {
      return 'LOG_EVENT - Minor security indicators found';
    }
    
    return 'ALLOW - No security threats detected';
  }

  /**
   * Log security event
   */
  async logSecurityEvent(eventType, details, context = {}) {
    try {
      const eventData = {
        event_type: eventType,
        event_description: details.description || '',
        severity_level: details.severity || 'low',
        ip_address: context.ipAddress,
        user_agent: context.userAgent,
        endpoint: context.endpoint,
        request_method: context.method,
        was_blocked: details.blocked || false,
        response_action: details.action || 'none',
        session_id: context.sessionId,
        user_id: context.userId,
        metadata: details.metadata || {}
      };

      const { error } = await this.supabase
        .from('security_events')
        .insert([eventData]);

      if (error) {
        logger.error('Failed to log security event', { error });
      } else {
        logger.info('Security event logged', { eventType, severity: details.severity });
      }
    } catch (error) {
      logger.error('Security event logging failed', { error: error.message });
    }
  }

  /**
   * Get security middleware
   */
  getSecurityMiddleware() {
    return {
      // Input sanitization middleware
      sanitizeInputs: (req, res, next) => {
        try {
          // Sanitize body
          if (req.body) {
            this.sanitizeObject(req.body);
          }

          // Sanitize query parameters
          if (req.query) {
            this.sanitizeObject(req.query);
          }

          // Sanitize params
          if (req.params) {
            this.sanitizeObject(req.params);
          }

          next();
        } catch (error) {
          logger.error('Input sanitization middleware error', { error: error.message });
          res.status(400).json({ error: 'Invalid input detected' });
        }
      },

      // Suspicious activity detection middleware
      detectSuspiciousActivity: (req, res, next) => {
        try {
          const inputToCheck = JSON.stringify({
            body: req.body,
            query: req.query,
            params: req.params
          });

          const analysis = this.detectSuspiciousActivity(inputToCheck, {
            ipAddress: req.ip,
            userAgent: req.get('User-Agent'),
            endpoint: req.path,
            method: req.method
          });

          if (analysis.isSuspicious && analysis.severity === 'critical') {
            this.logSecurityEvent('SUSPICIOUS_ACTIVITY_BLOCKED', {
              description: 'Critical security threat detected and blocked',
              severity: analysis.severity,
              blocked: true,
              action: 'request_blocked',
              metadata: analysis
            }, {
              ipAddress: req.ip,
              userAgent: req.get('User-Agent'),
              endpoint: req.path,
              method: req.method
            });

            return res.status(403).json({ 
              error: 'Request blocked due to security policy',
              code: 'SECURITY_VIOLATION'
            });
          }

          if (analysis.isSuspicious) {
            this.logSecurityEvent('SUSPICIOUS_ACTIVITY_DETECTED', {
              description: 'Suspicious activity detected but allowed',
              severity: analysis.severity,
              blocked: false,
              action: 'enhanced_monitoring',
              metadata: analysis
            }, {
              ipAddress: req.ip,
              userAgent: req.get('User-Agent'),
              endpoint: req.path,
              method: req.method
            });
          }

          req.securityAnalysis = analysis;
          next();
        } catch (error) {
          logger.error('Suspicious activity detection middleware error', { error: error.message });
          next(); // Allow request to continue
        }
      },

      // CSRF protection middleware
      csrfProtection: (req, res, next) => {
        if (req.method === 'GET' || req.method === 'HEAD' || req.method === 'OPTIONS') {
          return next();
        }

        const sessionId = req.sessionID || req.session?.id;
        const csrfToken = req.headers['x-csrf-token'] || req.body._csrf_token;

        if (!sessionId || !csrfToken) {
          return res.status(403).json({ 
            error: 'CSRF token required',
            code: 'CSRF_TOKEN_MISSING'
          });
        }

        if (!this.validateCSRFToken(sessionId, csrfToken)) {
          this.logSecurityEvent('CSRF_ATTACK_BLOCKED', {
            description: 'Invalid CSRF token - potential attack blocked',
            severity: 'high',
            blocked: true,
            action: 'request_blocked'
          }, {
            ipAddress: req.ip,
            userAgent: req.get('User-Agent'),
            endpoint: req.path,
            method: req.method,
            sessionId
          });

          return res.status(403).json({ 
            error: 'Invalid CSRF token',
            code: 'CSRF_TOKEN_INVALID'
          });
        }

        next();
      }
    };
  }

  /**
   * Sanitize object recursively
   */
  sanitizeObject(obj, maxDepth = 5, currentDepth = 0) {
    if (currentDepth >= maxDepth || !obj || typeof obj !== 'object') {
      return;
    }

    Object.keys(obj).forEach(key => {
      if (typeof obj[key] === 'string') {
        obj[key] = this.sanitizeInput(obj[key]);
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        this.sanitizeObject(obj[key], maxDepth, currentDepth + 1);
      }
    });
  }

  /**
   * Generate security headers
   */
  getSecurityHeaders() {
    return {
      'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https:; font-src 'self' https:; object-src 'none'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'",
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload'
    };
  }
}

export default EnterpriseSecurityManager;