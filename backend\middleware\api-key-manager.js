import crypto from 'crypto';

/**
 * Secure API Key Management System
 * 
 * Handles API key generation, validation, and rotation
 * Addresses security vulnerability SEC-001 by securing API keys
 */

class APIKeyManager {
  constructor() {
    this.keys = new Map();
    this.keyRotationSchedule = new Map();
    this.keyUsageTracking = new Map();
  }

  /**
   * Generate a secure API key
   */
  generateAPIKey(length = 32) {
    const key = crypto.randomBytes(length).toString('hex');
    return {
      key: `sk_${key}`,
      created: new Date(),
      lastUsed: null,
      usageCount: 0,
      active: true
    };
  }

  /**
   * Store API key securely
   */
  storeAPIKey(keyId, keyData) {
    // Hash the key for storage
    const hashedKey = crypto.createHash('sha256').update(keyData.key).digest('hex');
    
    this.keys.set(keyId, {
      ...keyData,
      hash: hashedKey,
      key: undefined // Remove plain text key
    });
  }

  /**
   * Validate API key
   */
  validateAPIKey(providedKey) {
    const hashedProvided = crypto.createHash('sha256').update(providedKey).digest('hex');
    
    for (const [keyId, keyData] of this.keys.entries()) {
      if (keyData.hash === hashedProvided && keyData.active) {
        // Update usage tracking
        keyData.lastUsed = new Date();
        keyData.usageCount++;
        
        return {
          valid: true,
          keyId,
          keyData
        };
      }
    }
    
    return {
      valid: false,
      keyId: null,
      keyData: null
    };
  }

  /**
   * Rotate API key
   */
  rotateAPIKey(keyId) {
    const oldKey = this.keys.get(keyId);
    if (!oldKey) {
      throw new Error('Key not found');
    }

    // Generate new key
    const newKey = this.generateAPIKey();
    
    // Store new key
    this.storeAPIKey(keyId, newKey);
    
    // Schedule old key for deletion
    setTimeout(() => {
      this.keys.delete(keyId);
    }, 24 * 60 * 60 * 1000); // 24 hours grace period
    
    return newKey;
  }

  /**
   * Get API key usage stats
   */
  getKeyUsage(keyId) {
    const keyData = this.keys.get(keyId);
    if (!keyData) {
      return null;
    }
    
    return {
      keyId,
      created: keyData.created,
      lastUsed: keyData.lastUsed,
      usageCount: keyData.usageCount,
      active: keyData.active
    };
  }
}

// Singleton instance
const apiKeyManager = new APIKeyManager();

/**
 * Environment-based API key configuration
 */
const getAPIKeyConfig = () => {
  return {
    openai: {
      key: process.env.OPENAI_API_KEY,
      endpoint: 'https://api.openai.com/v1',
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json'
      }
    },
    supabase: {
      url: process.env.SUPABASE_URL,
      key: process.env.SUPABASE_SERVICE_ROLE_KEY,
      anonKey: process.env.SUPABASE_ANON_KEY
    },
    serper: {
      key: process.env.SERPER_API_KEY,
      endpoint: 'https://google.serper.dev/search',
      headers: {
        'X-API-KEY': process.env.SERPER_API_KEY,
        'Content-Type': 'application/json'
      }
    },
    firecrawl: {
      key: process.env.FIRECRAWL_API_KEY,
      endpoint: 'https://api.firecrawl.dev',
      headers: {
        'Authorization': `Bearer ${process.env.FIRECRAWL_API_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  };
};

/**
 * API key validation middleware
 */
const validateAPIKeys = () => {
  return (req, res, next) => {
    const config = getAPIKeyConfig();
    const missingKeys = [];
    
    // Check required API keys
    if (!config.openai.key) missingKeys.push('OPENAI_API_KEY');
    if (!config.supabase.key) missingKeys.push('SUPABASE_SERVICE_ROLE_KEY');
    if (!config.serper.key) missingKeys.push('SERPER_API_KEY');
    
    if (missingKeys.length > 0) {
      return res.status(500).json({
        success: false,
        error: 'API configuration error',
        message: `Missing required API keys: ${missingKeys.join(', ')}`,
        missingKeys
      });
    }
    
    // Add API config to request
    req.apiConfig = config;
    next();
  };
};

/**
 * API key rotation middleware
 */
const rotateAPIKeys = () => {
  return (req, res, next) => {
    // Check if keys need rotation (daily check)
    const lastRotation = req.session?.lastApiKeyRotation;
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    if (!lastRotation || new Date(lastRotation) < oneDayAgo) {
      // Schedule key rotation
      setImmediate(() => {
        console.log('Checking API key rotation requirements...');
        // Add rotation logic here
      });
      
      if (req.session) {
        req.session.lastApiKeyRotation = now.toISOString();
      }
    }
    
    next();
  };
};

/**
 * API usage tracking middleware
 */
const trackAPIUsage = () => {
  return (req, res, next) => {
    const startTime = Date.now();
    
    // Track API usage
    res.on('finish', () => {
      const duration = Date.now() - startTime;
      const endpoint = req.path;
      const method = req.method;
      
      // Log API usage
      console.log(`API Usage: ${method} ${endpoint} - ${duration}ms`);
      
      // Track in database or external service
      // This would typically go to an analytics service
      setImmediate(() => {
        // trackUsage(endpoint, method, duration, req.user?.id);
      });
    });
    
    next();
  };
};

/**
 * Secure API client factory
 */
const createSecureAPIClient = (service) => {
  const config = getAPIKeyConfig();
  const serviceConfig = config[service];
  
  if (!serviceConfig) {
    throw new Error(`Unknown service: ${service}`);
  }
  
  return {
    async request(endpoint, options = {}) {
      const url = serviceConfig.endpoint + endpoint;
      const headers = {
        ...serviceConfig.headers,
        ...options.headers
      };
      
      try {
        const response = await fetch(url, {
          ...options,
          headers,
          timeout: 30000 // 30 second timeout
        });
        
        if (!response.ok) {
          throw new Error(`${service} API error: ${response.status} ${response.statusText}`);
        }
        
        return await response.json();
      } catch (error) {
        console.error(`${service} API request failed:`, error);
        throw error;
      }
    },
    
    async get(endpoint, params = {}) {
      const queryString = new URLSearchParams(params).toString();
      const url = queryString ? `${endpoint}?${queryString}` : endpoint;
      
      return this.request(url, {
        method: 'GET'
      });
    },
    
    async post(endpoint, data = {}) {
      return this.request(endpoint, {
        method: 'POST',
        body: JSON.stringify(data)
      });
    },
    
    async put(endpoint, data = {}) {
      return this.request(endpoint, {
        method: 'PUT',
        body: JSON.stringify(data)
      });
    },
    
    async delete(endpoint) {
      return this.request(endpoint, {
        method: 'DELETE'
      });
    }
  };
};

/**
 * API key health check
 */
const healthCheckAPIs = async () => {
  const config = getAPIKeyConfig();
  const results = {};
  
  // Test OpenAI
  try {
    const openaiClient = createSecureAPIClient('openai');
    await openaiClient.get('/models');
    results.openai = { status: 'healthy', timestamp: new Date().toISOString() };
  } catch (error) {
    results.openai = { status: 'error', error: error.message, timestamp: new Date().toISOString() };
  }
  
  // Test Supabase
  try {
    const supabaseResponse = await fetch(`${config.supabase.url}/rest/v1/`, {
      headers: {
        'apikey': config.supabase.key,
        'Authorization': `Bearer ${config.supabase.key}`
      }
    });
    
    results.supabase = { 
      status: supabaseResponse.ok ? 'healthy' : 'error',
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    results.supabase = { status: 'error', error: error.message, timestamp: new Date().toISOString() };
  }
  
  // Test Serper
  try {
    const serperClient = createSecureAPIClient('serper');
    await serperClient.post('/search', { q: 'test' });
    results.serper = { status: 'healthy', timestamp: new Date().toISOString() };
  } catch (error) {
    results.serper = { status: 'error', error: error.message, timestamp: new Date().toISOString() };
  }
  
  return results;
};

/**
 * API key security audit
 */
const auditAPIKeys = () => {
  const config = getAPIKeyConfig();
  const audit = {
    timestamp: new Date().toISOString(),
    issues: [],
    recommendations: []
  };
  
  // Check for weak keys
  Object.entries(config).forEach(([service, serviceConfig]) => {
    if (serviceConfig.key && serviceConfig.key.length < 20) {
      audit.issues.push(`${service} API key appears to be too short`);
    }
    
    if (serviceConfig.key && serviceConfig.key.includes('test') || serviceConfig.key.includes('demo')) {
      audit.issues.push(`${service} API key appears to be a test/demo key`);
    }
  });
  
  // Add recommendations
  audit.recommendations.push('Rotate API keys regularly');
  audit.recommendations.push('Use environment variables for all API keys');
  audit.recommendations.push('Implement API key usage monitoring');
  audit.recommendations.push('Set up API key expiration alerts');
  
  return audit;
};

export {
  apiKeyManager,
  getAPIKeyConfig,
  createSecureAPIClient,
  healthCheckAPIs,
  auditAPIKeys,
  validateAPIKeys,
  trackAPIUsage
};