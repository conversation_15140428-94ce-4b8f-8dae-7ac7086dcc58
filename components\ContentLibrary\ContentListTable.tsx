/**
 * Content List Table Component
 * Displays content in a sortable, selectable table
 */

'use client'

import React from 'react'
import { ContentItem } from '@/lib/api/types'
import Badge from '@/components/UI/Badge'
import But<PERSON> from '@/components/UI/Button'
import {
  EyeIcon,
  PencilIcon,
  DocumentDuplicateIcon,
  ChevronUpIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline'

interface ContentListTableProps {
  content: ContentItem[]
  selectedContent: Set<string>
  onSelectAll: (checked: boolean) => void
  onSelectContent: (contentId: string, checked: boolean) => void
  onPreview: (item: ContentItem) => void
  onEdit: (item: ContentItem) => void
  onDuplicate: (item: ContentItem) => void
  sortBy: string
  sortOrder: 'asc' | 'desc'
  onSort: (field: string) => void
}

export default function ContentListTable({
  content,
  selectedContent,
  onSelectAll,
  onSelectContent,
  onPreview,
  onEdit,
  onDuplicate,
  sortBy,
  sortOrder,
  onSort
}: ContentListTableProps) {
  const allSelected = content.length > 0 && content.every(item => selectedContent.has(item.id))
  const someSelected = content.some(item => selectedContent.has(item.id))

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'success'
      case 'draft':
        return 'warning'
      case 'scheduled':
        return 'info'
      default:
        return 'secondary'
    }
  }

  const getSeoScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 dark:text-green-400'
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400'
    return 'text-red-600 dark:text-red-400'
  }

  const SortIcon = ({ field }: { field: string }) => {
    if (sortBy !== field) {
      return <div className="w-4 h-4" />
    }
    return sortOrder === 'asc' 
      ? <ChevronUpIcon className="h-4 w-4" />
      : <ChevronDownIcon className="h-4 w-4" />
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead className="bg-gray-50 dark:bg-gray-800">
          <tr>
            <th className="px-6 py-3 text-left">
              <input
                type="checkbox"
                checked={allSelected}
                indeterminate={!allSelected && someSelected}
                onChange={(e) => onSelectAll(e.target.checked)}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </th>
            <th 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
              onClick={() => onSort('title')}
            >
              <div className="flex items-center space-x-1">
                <span>Title</span>
                <SortIcon field="title" />
              </div>
            </th>
            <th 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
              onClick={() => onSort('keyword')}
            >
              <div className="flex items-center space-x-1">
                <span>Keyword</span>
                <SortIcon field="keyword" />
              </div>
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Type
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Status
            </th>
            <th 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
              onClick={() => onSort('seo_score')}
            >
              <div className="flex items-center space-x-1">
                <span>SEO Score</span>
                <SortIcon field="seo_score" />
              </div>
            </th>
            <th 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
              onClick={() => onSort('word_count')}
            >
              <div className="flex items-center space-x-1">
                <span>Words</span>
                <SortIcon field="word_count" />
              </div>
            </th>
            <th 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
              onClick={() => onSort('created_at')}
            >
              <div className="flex items-center space-x-1">
                <span>Created</span>
                <SortIcon field="created_at" />
              </div>
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
          {content.map((item) => (
            <tr 
              key={item.id}
              className="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <td className="px-6 py-4 whitespace-nowrap">
                <input
                  type="checkbox"
                  checked={selectedContent.has(item.id)}
                  onChange={(e) => onSelectContent(item.id, e.target.checked)}
                  className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </td>
              <td className="px-6 py-4">
                <div className="text-sm font-medium text-gray-900 dark:text-gray-100 line-clamp-2">
                  {item.title}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {item.keyword}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <Badge variant="secondary" size="sm">
                  {item.content_type.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </Badge>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <Badge 
                  variant={getStatusColor(item.status)} 
                  size="sm"
                >
                  {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                </Badge>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`text-sm font-medium ${getSeoScoreColor(item.seo_score || 0)}`}>
                  {item.seo_score || 0}%
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                {item.word_count.toLocaleString()}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                {formatDate(item.created_at)}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div className="flex items-center justify-end space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onPreview(item)}
                    title="Preview"
                  >
                    <EyeIcon className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onEdit(item)}
                    title="Edit"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onDuplicate(item)}
                    title="Duplicate"
                  >
                    <DocumentDuplicateIcon className="h-4 w-4" />
                  </Button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}