/**
 * Simple SEO SAAS Backend Server
 * Minimal implementation to get content generation working
 */

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import OpenAI from 'openai';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Initialize OpenAI
const openai = process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'sk-your_openai_key_here' 
  ? new OpenAI({ apiKey: process.env.OPENAI_API_KEY })
  : null;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://127.0.0.1:3000', 'http://127.0.0.1:3001'],
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    service: 'SEO SAAS Backend',
    version: '1.0.0'
  });
});

// SEO-specific keyword content generation endpoint
app.post('/api/seo/generate-content', async (req, res) => {
  try {
    const { keyword, target_country, content_type = 'blog-post', tone = 'professional', length = 'medium' } = req.body;

    if (!keyword || !target_country) {
      return res.status(400).json({
        error: 'Keyword and target country are required',
        message: 'Please provide both a target keyword and target country for SEO content generation.'
      });
    }

    if (!openai) {
      console.log('ERROR: OpenAI API key not configured for SEO content generation');
      return res.status(500).json({
        error: 'OpenAI API key not configured',
        message: 'SEO content generation requires a valid OpenAI API key. Demo content is disabled.'
      });
    }

    // Determine word count based on length
    const wordCounts = {
      short: '800-1200',
      medium: '1500-2500', 
      long: '2500-4000'
    };
    
    const wordCount = wordCounts[length] || '1500-2500';

    // Create enhanced SEO prompt with specific targeting
    const seoPrompt = `
You are an expert SEO content strategist and writer specializing in ${target_country} market content. Create a comprehensive, high-ranking ${content_type} optimized for the keyword "${keyword}" targeting the ${target_country} market.

CONTENT REQUIREMENTS:
- Primary Keyword: ${keyword}
- Target Market: ${target_country}
- Content Type: ${content_type}
- Tone: ${tone}
- Target Length: ${wordCount} words
- SEO Optimization Level: Advanced

STRATEGIC APPROACH:
1. Research the competitive landscape for "${keyword}" in ${target_country}
2. Understand search intent and user behavior patterns in ${target_country}
3. Include location-specific considerations and cultural nuances
4. Use natural keyword variations and semantic keywords
5. Structure for both users and search engines

CONTENT STRUCTURE:
1. **Compelling H1 Title** - Include primary keyword naturally
2. **Engaging Introduction** - Hook readers while establishing keyword relevance
3. **Comprehensive Main Content** with:
   - H2 and H3 subheadings with keyword variations
   - Detailed, valuable information specific to ${target_country} audience
   - Natural keyword integration (1-2% density)
   - Related semantic keywords
4. **Actionable Takeaways** - Practical advice for ${target_country} market
5. **Strong Conclusion** - Reinforce value and include call-to-action

ADVANCED SEO ELEMENTS:
- Include long-tail keyword variations
- Address common questions about ${keyword} in ${target_country}
- Provide market-specific insights and data
- Use authoritative, expert tone
- Include relevant statistics and facts
- Address user search intent comprehensively

Create content that would rank #1 for "${keyword}" in ${target_country} search results.
`;

    console.log(`Generating SEO content for keyword: "${keyword}" targeting ${target_country}...`);
    
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        {
          role: 'system',
          content: `You are an expert SEO content strategist and writer with deep knowledge of ${target_country} market dynamics, search behavior, and cultural preferences. You create content that ranks #1 in search results.`
        },
        {
          role: 'user',
          content: seoPrompt
        }
      ],
      max_tokens: 4000,
      temperature: 0.3,
      presence_penalty: 0.1,
      frequency_penalty: 0.1
    });

    const generatedContent = completion.choices[0]?.message?.content;
    const usage = completion.usage;

    if (!generatedContent) {
      throw new Error('No content generated by OpenAI');
    }

    // Analyze generated content for SEO metrics
    const wordCountActual = generatedContent.split(' ').length;
    const keywordDensity = (generatedContent.toLowerCase().split(keyword.toLowerCase()).length - 1) / wordCountActual * 100;

    res.json({
      success: true,
      content: {
        keyword,
        target_country,
        body: generatedContent,
        metadata: {
          content_type,
          tone,
          length,
          word_count: wordCountActual,
          keyword_density: keywordDensity.toFixed(2) + '%',
          generated_at: new Date().toISOString(),
          model: 'gpt-4o',
          seo_optimized: true
        }
      },
      usage: usage,
      seo_analysis: {
        keyword_density: keywordDensity.toFixed(2) + '%',
        target_word_count: wordCount,
        actual_word_count: wordCountActual,
        keyword_occurrences: generatedContent.toLowerCase().split(keyword.toLowerCase()).length - 1,
        market_targeted: target_country
      }
    });

  } catch (error) {
    console.error('SEO content generation error:', error);
    
    res.status(500).json({
      error: 'Failed to generate SEO content',
      message: error.message,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Original content generation endpoint (kept for backward compatibility)
app.post('/api/content/generate', async (req, res) => {
  try {
    const { title, prompt, contentType = 'blog-post', tone = 'professional', length = 'medium' } = req.body;

    if (!title || !prompt) {
      return res.status(400).json({
        error: 'Title and prompt are required'
      });
    }

    // Determine word count based on length
    const wordCounts = {
      short: '300-500',
      medium: '800-1200',
      long: '1500-2500'
    };
    
    const wordCount = wordCounts[length] || '800-1200';

    // Create enhanced prompt for SEO content generation
    const enhancedPrompt = `
You are an expert SEO content writer. Create a comprehensive ${contentType} with the following specifications:

Title: ${title}
Content Request: ${prompt}
Tone: ${tone}
Target Length: ${wordCount} words

Please write high-quality, SEO-optimized content that:
1. Is engaging and valuable to readers
2. Uses proper heading structure (H1, H2, H3)
3. Includes relevant keywords naturally
4. Has a clear introduction, body, and conclusion
5. Is well-researched and authoritative

Format the response in clean, well-structured text with proper headings and paragraphs.
`;

    let generatedContent;
    let usage = null;

    if (openai) {
      console.log('Generating content with OpenAI...');
      
      const completion = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: 'You are an expert SEO content writer who creates high-quality, engaging, and well-structured content.'
          },
          {
            role: 'user',
            content: enhancedPrompt
          }
        ],
        max_tokens: 4000,
        temperature: 0.3,
        presence_penalty: 0.1,
        frequency_penalty: 0.1
      });

      generatedContent = completion.choices[0]?.message?.content;
      usage = completion.usage;

      if (!generatedContent) {
        throw new Error('No content generated');
      }
    } else {
      console.log('ERROR: OpenAI API key not configured');
      return res.status(500).json({
        error: 'OpenAI API key not configured',
        message: 'Real AI content generation requires a valid OpenAI API key. Demo content has been disabled.'
      });
    }

    res.json({
      success: true,
      content: {
        title,
        body: generatedContent,
        metadata: {
          contentType,
          tone,
          length,
          wordCount: generatedContent.split(' ').length,
          generatedAt: new Date().toISOString(),
          model: 'gpt-4o'
        }
      },
      usage: usage
    });

  } catch (error) {
    console.error('Content generation error:', error);
    
    res.status(500).json({
      error: 'Failed to generate content',
      message: error.message,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Projects endpoint (mock for now)
app.get('/api/projects', (req, res) => {
  res.json({
    projects: [
      {
        id: '1',
        name: 'Sample Project',
        description: 'A sample project for testing',
        niche: 'Technology',
        createdAt: new Date().toISOString()
      }
    ]
  });
});

app.post('/api/projects', (req, res) => {
  const { name, description, niche } = req.body;
  
  const project = {
    id: Date.now().toString(),
    name,
    description,
    niche,
    createdAt: new Date().toISOString()
  };
  
  res.status(201).json({
    success: true,
    project
  });
});

// Analytics endpoint (mock for now)
app.get('/api/analytics/report', (req, res) => {
  res.json({
    metrics: {
      totalUsers: 150,
      totalContent: 45,
      totalProjects: 12,
      generatedToday: 8
    },
    charts: [
      {
        name: 'Content Generation',
        data: [
          { date: '2024-01', count: 10 },
          { date: '2024-02', count: 15 },
          { date: '2024-03', count: 20 }
        ]
      }
    ]
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 SEO SAAS Backend Server running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🤖 Content API: http://localhost:${PORT}/api/content/generate`);
  console.log('🌟 Ready to generate amazing content!');
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Server shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 Server shutting down gracefully...');
  process.exit(0);
});