import express from 'express';
import { AuthorityLinkDiscoverer } from '../services/authorityLinkDiscoverer.js';
import { LinkQualityValidator } from '../services/linkQualityValidator.js';
import { RealDataValidator } from '../services/realDataValidator.js';
import { 
  authorityLinkRateLimit, 
  validationRateLimit,
  smartAuthorityLinkCache 
} from '../middleware/rateLimiting.js';
import { body, validationResult } from 'express-validator';
import winston from 'winston';

// Initialize logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/authority-links-api.log' })
  ]
});

const router = express.Router();

/**
 * Enhanced Authority Link System with Demo Data Validation
 */
class EnhancedAuthorityLinkSystem {
  constructor() {
    this.discoverer = new AuthorityLinkDiscoverer();
    this.qualityValidator = new LinkQualityValidator();
    this.realDataValidator = new RealDataValidator();
    
    // Performance tracking
    this.performanceMetrics = {
      discoveryRequests: 0,
      validationRequests: 0,
      averageResponseTime: 0,
      successRate: 0
    };
  }

  /**
   * Discover authority links with strict real data validation
   */
  async discoverLinks(params) {
    const startTime = Date.now();
    this.performanceMetrics.discoveryRequests++;

    try {
      logger.info('Starting authority link discovery', { params });

      // Phase 1: Validate inputs are real data
      const validationResult = await this.validateDiscoveryInputs(params);
      if (!validationResult.valid) {
        throw new Error(`Demo data detected: ${validationResult.blockedReason}`);
      }

      // Phase 2: Discover authority links using new service
      const discoveredLinks = await this.discoverer.discoverAuthorityLinks(
        params.keyword,
        params.industry,
        {
          maxLinks: params.maxLinks || 15,
          minAuthorityScore: params.minAuthorityScore || 70,
          includeWikipedia: params.includeWikipedia !== false,
          includeGovernment: params.includeGovernment !== false,
          includeEducational: params.includeEducational !== false,
          includeNews: params.includeNews !== false,
          industrySpecific: params.industrySpecific !== false
        }
      );

      // Phase 3: Quality validation for discovered links
      const qualityValidationResults = await this.performQualityValidation(discoveredLinks);

      // Phase 4: Calculate performance metrics
      const responseTime = Date.now() - startTime;
      this.updatePerformanceMetrics(responseTime, true);

      const result = {
        discovered: discoveredLinks,
        qualityValidation: qualityValidationResults,
        summary: {
          totalDiscovered: discoveredLinks.length,
          validLinks: qualityValidationResults.validLinks.length,
          averageQualityScore: qualityValidationResults.averageQualityScore,
          averageAuthorityScore: this.calculateAverageScore(discoveredLinks, 'authorityScore'),
          averageRelevanceScore: this.calculateAverageScore(discoveredLinks, 'relevanceScore'),
          responseTime: responseTime
        },
        metadata: {
          timestamp: new Date().toISOString(),
          method: 'enhanced_discovery',
          version: '2.0'
        }
      };

      logger.info('Authority link discovery completed successfully', {
        keyword: params.keyword,
        totalDiscovered: result.summary.totalDiscovered,
        responseTime
      });

      return result;

    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.updatePerformanceMetrics(responseTime, false);
      
      logger.error('Authority link discovery failed', {
        keyword: params.keyword,
        error: error.message,
        responseTime
      });
      
      throw error;
    }
  }

  /**
   * Validate discovery inputs for demo data
   */
  async validateDiscoveryInputs(params) {
    try {
      // Validate keyword
      if (params.keyword) {
        const keywordValidation = await this.realDataValidator.validateRealData(
          params.keyword, 
          'keyword', 
          { industry: params.industry }
        );
        
        if (!keywordValidation.valid) {
          return keywordValidation;
        }
      }
      
      // Validate context if provided
      if (params.context) {
        const contextValidation = await this.realDataValidator.validateRealData(
          params.context, 
          'content', 
          { industry: params.industry }
        );
        
        if (!contextValidation.valid) {
          return contextValidation;
        }
      }
      
      // Validate industry if provided
      if (params.industry) {
        const industryValidation = await this.realDataValidator.validateRealData(
          params.industry, 
          'general', 
          {}
        );
        
        if (!industryValidation.valid) {
          return industryValidation;
        }
      }
      
      return {
        valid: true,
        message: 'All inputs validated successfully'
      };
      
    } catch (error) {
      logger.error('Input validation failed', { error: error.message });
      return {
        valid: false,
        blockedReason: 'Validation system error',
        error: error.message
      };
    }
  }

  /**
   * Perform quality validation on discovered links
   */
  async performQualityValidation(links) {
    try {
      const validationResults = [];
      let totalQualityScore = 0;
      let validLinksCount = 0;

      // Validate each link for quality
      for (const link of links.slice(0, 10)) { // Limit to avoid timeout
        try {
          const validation = await this.qualityValidator.validateLinkQuality(link, {
            timeout: 8000,
            validateAccessibility: true,
            analyzeContent: true,
            checkTechnical: false, // Skip to improve performance
            assessEEAT: true
          });

          validationResults.push({
            url: link.url,
            domain: link.domain,
            qualityScore: validation.qualityScore,
            status: validation.status,
            issues: validation.issues.slice(0, 3), // Limit issues
            isValid: validation.qualityScore >= 60
          });

          if (validation.qualityScore >= 60) {
            validLinksCount++;
            totalQualityScore += validation.qualityScore;
          }

        } catch (validationError) {
          logger.warn('Individual link validation failed', {
            url: link.url,
            error: validationError.message
          });
          
          validationResults.push({
            url: link.url,
            domain: link.domain,
            qualityScore: 0,
            status: 'error',
            issues: ['Validation failed'],
            isValid: false,
            error: validationError.message
          });
        }
      }

      return {
        validLinks: validationResults.filter(r => r.isValid),
        invalidLinks: validationResults.filter(r => !r.isValid),
        averageQualityScore: validLinksCount > 0 ? Math.round(totalQualityScore / validLinksCount) : 0,
        totalValidated: validationResults.length,
        validationResults: validationResults
      };

    } catch (error) {
      logger.error('Quality validation failed', { error: error.message });
      return {
        validLinks: [],
        invalidLinks: [],
        averageQualityScore: 0,
        totalValidated: 0,
        error: error.message
      };
    }
  }

  /**
   * Calculate average score for a specific metric
   */
  calculateAverageScore(links, scoreField) {
    if (!links || links.length === 0) return 0;
    
    const scores = links
      .map(link => link[scoreField])
      .filter(score => typeof score === 'number' && !isNaN(score));
      
    if (scores.length === 0) return 0;
    
    return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
  }

  /**
   * Update performance metrics
   */
  updatePerformanceMetrics(responseTime, success) {
    // Update average response time
    const totalRequests = this.performanceMetrics.discoveryRequests + this.performanceMetrics.validationRequests;
    this.performanceMetrics.averageResponseTime = 
      ((this.performanceMetrics.averageResponseTime * (totalRequests - 1)) + responseTime) / totalRequests;

    // Update success rate
    const currentSuccessRate = this.performanceMetrics.successRate;
    this.performanceMetrics.successRate = 
      ((currentSuccessRate * (totalRequests - 1)) + (success ? 100 : 0)) / totalRequests;
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics() {
    return {
      ...this.performanceMetrics,
      averageResponseTime: Math.round(this.performanceMetrics.averageResponseTime),
      successRate: Math.round(this.performanceMetrics.successRate * 100) / 100
    };
  }

  /**
   * Enhanced validation with demo data detection
   */
  async validateWithDemoDetection(links) {
    try {
      let demoLinksRejected = 0;
      const linksToValidate = [];
      
      // First pass: Remove obvious demo links
      for (const link of links) {
        const isDemoLink = this.checkForDemoLink(link);
        if (isDemoLink.isDemo) {
          demoLinksRejected++;
          logger.info(`Demo link rejected: ${link.url} - ${isDemoLink.reason}`);
        } else {
          linksToValidate.push(link);
        }
      }
      
      // Second pass: Quality validation on remaining links
      const qualityValidation = await this.performQualityValidation(linksToValidate);
      
      return {
        validLinks: qualityValidation.validLinks,
        invalidLinks: qualityValidation.invalidLinks,
        demoLinksRejected,
        totalProcessed: links.length,
        averageQualityScore: qualityValidation.averageQualityScore,
        enhancedMetrics: {
          demoDataDetection: {
            totalChecked: links.length,
            demoLinksFound: demoLinksRejected,
            cleanLinksProcessed: linksToValidate.length
          },
          qualityAssessment: {
            validLinks: qualityValidation.validLinks.length,
            invalidLinks: qualityValidation.invalidLinks.length,
            averageScore: qualityValidation.averageQualityScore
          }
        }
      };
    } catch (error) {
      logger.error('Enhanced validation failed', { error: error.message });
      return {
        validLinks: [],
        invalidLinks: [],
        demoLinksRejected: 0,
        totalProcessed: 0,
        averageQualityScore: 0,
        error: error.message
      };
    }
  }

  /**
   * Check if a link contains demo/placeholder data
   */
  checkForDemoLink(link) {
    const demoPatterns = [
      // URL patterns
      /example\.com/i,
      /test\.com/i,
      /demo\.com/i,
      /placeholder\.com/i,
      /sample\.com/i,
      /fake\./i,
      /dummy\./i,
      /mock\./i,
      
      // Content patterns
      /lorem ipsum/i,
      /placeholder/i,
      /demo content/i,
      /test content/i,
      /sample text/i,
      /example/i,
      
      // Authority patterns
      /example authority/i,
      /test source/i,
      /demo source/i,
      /placeholder source/i
    ];

    // Check URL
    for (const pattern of demoPatterns) {
      if (pattern.test(link.url)) {
        return { isDemo: true, reason: `Demo URL pattern: ${pattern.source}` };
      }
    }

    // Check title
    if (link.title) {
      for (const pattern of demoPatterns) {
        if (pattern.test(link.title)) {
          return { isDemo: true, reason: `Demo title pattern: ${pattern.source}` };
        }
      }
    }

    // Check description
    if (link.description) {
      for (const pattern of demoPatterns) {
        if (pattern.test(link.description)) {
          return { isDemo: true, reason: `Demo description pattern: ${pattern.source}` };
        }
      }
    }

    // Check domain for demo indicators
    if (link.domain) {
      const suspiciousDomains = [
        'localhost',
        '127.0.0.1',
        'example.com',
        'test.com',
        'demo.com',
        'placeholder.com',
        'fake.com',
        'dummy.com'
      ];
      
      if (suspiciousDomains.some(domain => link.domain.includes(domain))) {
        return { isDemo: true, reason: 'Suspicious demo domain' };
      }
    }

    return { isDemo: false };
  }

  /**
   * Validate single authority link with enhanced checking
   */
  async validateSingleLink(link) {
    try {
      // Check for demo data first
      const demoCheck = this.checkForDemoLink(link);
      if (demoCheck.isDemo) {
        return {
          isValid: false,
          isDemoData: true,
          score: 0,
          issues: [`Demo data detected: ${demoCheck.reason}`],
          timestamp: new Date().toISOString()
        };
      }

      // Perform quality validation using the new service
      const validation = await this.qualityValidator.validateLinkQuality(link, {
        timeout: 5000,
        validateAccessibility: true,
        analyzeContent: false, // Skip for quick validation
        checkTechnical: false,
        assessEEAT: false
      });
      
      return {
        isValid: validation.qualityScore >= 60,
        isDemoData: false,
        score: validation.qualityScore,
        issues: validation.issues || [],
        accessibility: validation.validation?.accessibility,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Single link validation failed', {
        url: link.url,
        error: error.message
      });
      
      return {
        isValid: false,
        isDemoData: false,
        score: 0,
        issues: ['Validation error occurred'],
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Initialize the enhanced authority link system
const authorityLinkSystem = new EnhancedAuthorityLinkSystem();

/**
 * Validation middleware for authority link requests
 */
const validateAuthorityLinkRequest = [
  body('keyword')
    .notEmpty()
    .withMessage('Keyword is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Keyword must be between 2 and 100 characters')
    .custom(async (value) => {
      // Use the real data validator from the authority link system
      const realDataValidator = new (await import('../services/realDataValidator.js')).RealDataValidator();
      const result = await realDataValidator.validateRealData(value, 'keyword');
      if (!result.valid) {
        throw new Error(result.blockedReason || 'Invalid keyword detected');
      }
      return true;
    }),
  body('context')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Context must be less than 500 characters')
    .custom(async (value) => {
      if (value) {
        const realDataValidator = new (await import('../services/realDataValidator.js')).RealDataValidator();
        const result = await realDataValidator.validateRealData(value, 'content');
        if (!result.valid) {
          throw new Error(result.blockedReason || 'Invalid context detected');
        }
      }
      return true;
    }),
  body('industry')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Industry must be less than 50 characters')
    .custom(async (value) => {
      if (value) {
        const realDataValidator = new (await import('../services/realDataValidator.js')).RealDataValidator();
        const result = await realDataValidator.validateRealData(value, 'general');
        if (!result.valid) {
          throw new Error(result.blockedReason || 'Invalid industry detected');
        }
      }
      return true;
    }),
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array(),
        code: 'DEMO_DATA_DETECTED'
      });
    }
    next();
  }
];

/**
 * POST /api/authority-links/discover
 * Discover authority links with enhanced demo data validation
 */
router.post('/discover', 
  authorityLinkRateLimit,
  validateAuthorityLinkRequest, 
  async (req, res) => {
  try {
    console.log('Authority link discovery request:', req.body);
    
    const result = await authorityLinkSystem.discoverLinks(req.body);
    
    res.json({
      success: true,
      data: result,
      message: 'Authority links discovered and validated successfully'
    });
  } catch (error) {
    console.error('Authority link discovery error:', error);
    
    res.status(400).json({
      success: false,
      error: error.message,
      code: 'AUTHORITY_LINK_DISCOVERY_FAILED'
    });
  }
});

/**
 * POST /api/authority-links/validate
 * Validate authority links with demo data detection
 */
router.post('/validate', 
  authorityLinkRateLimit,
  smartAuthorityLinkCache,
  async (req, res) => {
  try {
    const { links } = req.body;
    
    if (!Array.isArray(links)) {
      return res.status(400).json({
        success: false,
        error: 'Links must be an array',
        code: 'INVALID_INPUT'
      });
    }
    
    const validationResult = await authorityLinkSystem.validateWithDemoDetection(links);
    
    res.json({
      success: true,
      data: validationResult,
      message: 'Authority links validated successfully'
    });
  } catch (error) {
    console.error('Authority link validation error:', error);
    
    res.status(500).json({
      success: false,
      error: error.message,
      code: 'AUTHORITY_LINK_VALIDATION_FAILED'
    });
  }
});

/**
 * POST /api/authority-links/validate-single
 * Quick validation for a single authority link
 */
router.post('/validate-single', validationRateLimit, async (req, res) => {
  try {
    const { link } = req.body;
    
    if (!link || !link.url) {
      return res.status(400).json({
        success: false,
        error: 'Link object with URL is required',
        code: 'INVALID_INPUT'
      });
    }
    
    const result = await authorityLinkSystem.validateSingleLink(link);
    
    res.json({
      success: true,
      data: result,
      message: result.isValid ? 'Link is valid' : 'Link validation failed'
    });
  } catch (error) {
    console.error('Single link validation error:', error);
    
    res.status(500).json({
      success: false,
      error: error.message,
      code: 'SINGLE_LINK_VALIDATION_FAILED'
    });
  }
});

/**
 * POST /api/authority-links/check-demo-data
 * Check if authority links contain demo/placeholder data
 */
router.post('/check-demo-data', validationRateLimit, async (req, res) => {
  try {
    const { links } = req.body;
    
    if (!Array.isArray(links)) {
      return res.status(400).json({
        success: false,
        error: 'Links must be an array',
        code: 'INVALID_INPUT'
      });
    }
    
    const results = links.map(link => {
      const demoCheck = authorityLinkSystem.checkForDemoLink(link);
      return {
        url: link.url,
        isDemo: demoCheck.isDemo,
        reason: demoCheck.reason,
        recommendation: demoCheck.isDemo ? 'Replace with real authority source' : 'Link appears valid'
      };
    });
    
    const summary = {
      totalChecked: links.length,
      demoLinksFound: results.filter(r => r.isDemo).length,
      cleanLinks: results.filter(r => !r.isDemo).length,
      demoRate: (results.filter(r => r.isDemo).length / links.length) * 100
    };
    
    res.json({
      success: true,
      data: {
        results: results,
        summary: summary
      },
      message: `Demo data check completed. Found ${summary.demoLinksFound} demo links out of ${summary.totalChecked} total links.`
    });
  } catch (error) {
    console.error('Demo data check error:', error);
    
    res.status(500).json({
      success: false,
      error: error.message,
      code: 'DEMO_DATA_CHECK_FAILED'
    });
  }
});

/**
 * POST /api/authority-links/comprehensive-analysis
 * Comprehensive authority link discovery and validation
 */
router.post('/comprehensive-analysis',
  authorityLinkRateLimit,
  validateAuthorityLinkRequest,
  async (req, res) => {
  try {
    const { keyword, industry, context, maxLinks = 15, minAuthorityScore = 70 } = req.body;
    
    logger.info('Starting comprehensive authority link analysis', {
      keyword,
      industry,
      maxLinks,
      minAuthorityScore
    });
    
    // Discover authority links
    const discoveryResult = await authorityLinkSystem.discoverLinks({
      keyword,
      industry,
      context,
      maxLinks,
      minAuthorityScore,
      includeWikipedia: true,
      includeGovernment: true,
      includeEducational: true,
      includeNews: true,
      industrySpecific: true
    });
    
    res.json({
      success: true,
      data: {
        keyword,
        industry,
        analysis: discoveryResult,
        metadata: {
          timestamp: new Date().toISOString(),
          processingTime: discoveryResult.summary?.responseTime || 0,
          version: '2.0'
        }
      },
      message: 'Comprehensive authority link analysis completed successfully'
    });
  } catch (error) {
    logger.error('Comprehensive analysis failed', {
      keyword: req.body.keyword,
      error: error.message
    });
    
    res.status(400).json({
      success: false,
      error: error.message,
      code: 'COMPREHENSIVE_ANALYSIS_FAILED'
    });
  }
});

/**
 * POST /api/authority-links/bulk-validate
 * Validate multiple authority links in bulk
 */
router.post('/bulk-validate',
  validationRateLimit,
  async (req, res) => {
  try {
    const { links, validationOptions = {} } = req.body;
    
    if (!Array.isArray(links) || links.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Links array is required and must not be empty',
        code: 'INVALID_INPUT'
      });
    }
    
    if (links.length > 50) {
      return res.status(400).json({
        success: false,
        error: 'Maximum 50 links allowed per bulk validation request',
        code: 'TOO_MANY_LINKS'
      });
    }
    
    logger.info('Starting bulk link validation', {
      linkCount: links.length,
      options: validationOptions
    });
    
    const bulkResults = [];
    const processingErrors = [];
    
    // Process links in batches of 10 to avoid overwhelming the system
    const batchSize = 10;
    for (let i = 0; i < links.length; i += batchSize) {
      const batch = links.slice(i, i + batchSize);
      
      const batchPromises = batch.map(async (link, index) => {
        try {
          const validation = await authorityLinkSystem.validateSingleLink(link);
          return {
            index: i + index,
            url: link.url,
            domain: link.domain,
            validation
          };
        } catch (error) {
          processingErrors.push({
            index: i + index,
            url: link.url,
            error: error.message
          });
          return null;
        }
      });
      
      const batchResults = await Promise.all(batchPromises);
      bulkResults.push(...batchResults.filter(result => result !== null));
    }
    
    const summary = {
      totalProcessed: links.length,
      successful: bulkResults.length,
      failed: processingErrors.length,
      validLinks: bulkResults.filter(r => r.validation.isValid).length,
      invalidLinks: bulkResults.filter(r => !r.validation.isValid).length,
      averageScore: bulkResults.length > 0 
        ? Math.round(bulkResults.reduce((sum, r) => sum + r.validation.score, 0) / bulkResults.length)
        : 0
    };
    
    res.json({
      success: true,
      data: {
        results: bulkResults,
        errors: processingErrors,
        summary
      },
      message: `Bulk validation completed. ${summary.successful} links processed successfully.`
    });
  } catch (error) {
    logger.error('Bulk validation failed', { error: error.message });
    
    res.status(500).json({
      success: false,
      error: error.message,
      code: 'BULK_VALIDATION_FAILED'
    });
  }
});

/**
 * GET /api/authority-links/performance-metrics
 * Get system performance metrics
 */
router.get('/performance-metrics', async (req, res) => {
  try {
    const metrics = authorityLinkSystem.getPerformanceMetrics();
    
    res.json({
      success: true,
      data: {
        performance: metrics,
        systemStatus: {
          uptime: process.uptime(),
          memoryUsage: process.memoryUsage(),
          timestamp: new Date().toISOString()
        }
      },
      message: 'Performance metrics retrieved successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      code: 'METRICS_RETRIEVAL_FAILED'
    });
  }
});

/**
 * GET /api/authority-links/health
 * Check authority link system health
 */
router.get('/health', async (req, res) => {
  try {
    const health = {
      status: 'operational',
      services: {
        linkDiscovery: true,
        linkValidation: true,
        demoDataDetection: true,
        enhancedValidation: true,
        realDataValidation: true
      },
      capabilities: {
        discoveryEnabled: true,
        validationEnabled: true,
        demoDataRejection: true,
        qualityScoring: true,
        eeatzAssessment: true,
        bulkProcessing: true
      },
      version: '2.0',
      timestamp: new Date().toISOString()
    };
    
    res.json({
      success: true,
      health: health
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      code: 'HEALTH_CHECK_FAILED'
    });
  }
});

// Export the router
export default router;

// Export the authority link system for use in other modules
export { authorityLinkSystem };
