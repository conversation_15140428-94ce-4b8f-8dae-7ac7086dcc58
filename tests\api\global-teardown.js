/**
 * Global API Test Teardown
 * 
 * Global teardown for API tests including database cleanup,
 * service cleanup, and test artifact management.
 */

const { createClient } = require('@supabase/supabase-js');

module.exports = async () => {
  console.log('🧹 Running API test teardown...');
  
  try {
    // Clean up test database
    await cleanupTestDatabase();
    
    // Clean up external service mocks
    await cleanupExternalMocks();
    
    // Generate test reports
    await generateTestReports();
    
    console.log('✅ API test teardown complete');
    
  } catch (error) {
    console.error('❌ API test teardown failed:', error);
    // Don't throw here - teardown failures shouldn't fail the test run
  }
};

/**
 * Clean up test database
 */
async function cleanupTestDatabase() {
  console.log('🗑️ Cleaning up test database...');
  
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL_TEST || process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_KEY_TEST || process.env.SUPABASE_SERVICE_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      console.warn('Missing Supabase configuration for cleanup');
      return;
    }
    
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Clean up test data
    const testTables = [
      'analytics_events',
      'reports',
      'content',
      'projects',
      'users'
    ];
    
    for (const table of testTables) {
      try {
        const { error } = await supabase
          .from(table)
          .delete()
          .like('id', 'test-%');
        
        if (error && !error.message.includes('does not exist')) {
          console.warn(`Failed to clean up ${table}:`, error.message);
        }
      } catch (error) {
        console.warn(`Failed to clean up ${table}:`, error);
      }
    }
    
    console.log('✅ Test database cleaned up');
    
  } catch (error) {
    console.error('❌ Test database cleanup failed:', error);
  }
}

/**
 * Clean up external service mocks
 */
async function cleanupExternalMocks() {
  console.log('🎭 Cleaning up external service mocks...');
  
  try {
    // Reset environment variables
    delete process.env.OPENAI_API_KEY_TEST;
    delete process.env.SENDGRID_API_KEY_TEST;
    delete process.env.GOOGLE_ANALYTICS_ID_TEST;
    
    // Clean up any mock servers or interceptors
    // This would be specific to the mocking library used
    
    console.log('✅ External service mocks cleaned up');
    
  } catch (error) {
    console.error('❌ External service mocks cleanup failed:', error);
  }
}

/**
 * Generate test reports
 */
async function generateTestReports() {
  console.log('📊 Generating test reports...');
  
  try {
    const fs = require('fs');
    const path = require('path');
    
    // Create test results directory if it doesn't exist
    const resultsDir = path.join(process.cwd(), 'test-results');
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    // Generate API test summary
    const summary = {
      timestamp: new Date().toISOString(),
      testType: 'api',
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        ci: !!process.env.CI
      },
      configuration: {
        testTimeout: 30000,
        database: 'supabase',
        mocks: ['openai', 'sendgrid', 'analytics']
      }
    };
    
    fs.writeFileSync(
      path.join(resultsDir, 'api-test-summary.json'),
      JSON.stringify(summary, null, 2)
    );
    
    console.log('✅ Test reports generated');
    
  } catch (error) {
    console.error('❌ Test reports generation failed:', error);
  }
}