/**
 * SEO Analysis Component
 * Detailed SEO score breakdown and recommendations
 */

'use client'

import React from 'react'
import Card from '@/components/UI/Card'
import Badge from '@/components/UI/Badge'
import { 
  ChartBarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline'

interface SEOAnalysisProps {
  analysis: {
    overall_score: number
    title_optimization: number
    meta_optimization: number
    content_structure: number
    keyword_usage: number
    readability: number
    technical_seo: number
  }
  recommendations: string[]
  keyword: string
  metadata: {
    word_count: number
    keyword_density: number
    readability_score: number
  }
}

interface SEOMetric {
  label: string
  score: number
  description: string
  icon: React.ReactNode
  weight: number
}

export default function SEOAnalysis({ analysis, recommendations, keyword, metadata }: SEOAnalysisProps) {
  const metrics: SEOMetric[] = [
    {
      label: 'Title Optimization',
      score: analysis.title_optimization,
      description: 'How well the title is optimized for SEO',
      icon: <CheckCircleIcon className="h-5 w-5" />,
      weight: 20
    },
    {
      label: 'Meta Description',
      score: analysis.meta_optimization,
      description: 'Quality and optimization of meta description',
      icon: <CheckCircleIcon className="h-5 w-5" />,
      weight: 15
    },
    {
      label: 'Content Structure',
      score: analysis.content_structure,
      description: 'Heading hierarchy and content organization',
      icon: <ChartBarIcon className="h-5 w-5" />,
      weight: 25
    },
    {
      label: 'Keyword Usage',
      score: analysis.keyword_usage,
      description: 'Strategic placement and density of target keyword',
      icon: <CheckCircleIcon className="h-5 w-5" />,
      weight: 25
    },
    {
      label: 'Readability',
      score: analysis.readability,
      description: 'Content readability and user experience',
      icon: <CheckCircleIcon className="h-5 w-5" />,
      weight: 10
    },
    {
      label: 'Technical SEO',
      score: analysis.technical_seo,
      description: 'Technical SEO elements and optimization',
      icon: <CheckCircleIcon className="h-5 w-5" />,
      weight: 5
    }
  ]

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 dark:text-green-400'
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400'
    return 'text-red-600 dark:text-red-400'
  }

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return 'success'
    if (score >= 60) return 'warning'
    return 'error'
  }

  const getScoreIcon = (score: number) => {
    if (score >= 80) return <CheckCircleIcon className="h-5 w-5 text-green-500" />
    if (score >= 60) return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
    return <XCircleIcon className="h-5 w-5 text-red-500" />
  }

  const calculateWeightedScore = () => {
    return metrics.reduce((total, metric) => {
      return total + (metric.score * metric.weight / 100)
    }, 0)
  }

  return (
    <div className="space-y-6">
      {/* Overall SEO Score */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
            <ChartBarIcon className="h-6 w-6 mr-2" />
            SEO Analysis
          </h3>
          <div className="text-right">
            <div className="text-3xl font-bold text-gray-900 dark:text-gray-100">
              {analysis.overall_score}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Overall Score
            </div>
          </div>
        </div>

        {/* Score Visualization */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              SEO Performance
            </span>
            <Badge variant={getScoreBadgeVariant(analysis.overall_score)}>
              {analysis.overall_score >= 80 ? 'Excellent' : 
               analysis.overall_score >= 60 ? 'Good' : 'Needs Improvement'}
            </Badge>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
            <div 
              className={`h-3 rounded-full transition-all duration-500 ${
                analysis.overall_score >= 80 ? 'bg-green-500' :
                analysis.overall_score >= 60 ? 'bg-yellow-500' : 'bg-red-500'
              }`}
              style={{ width: `${analysis.overall_score}%` }}
            />
          </div>
        </div>

        {/* Detailed Metrics */}
        <div className="space-y-4">
          {metrics.map((metric, index) => (
            <div key={index} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className={getScoreColor(metric.score)}>
                  {getScoreIcon(metric.score)}
                </div>
                <div>
                  <div className="font-medium text-gray-900 dark:text-gray-100">
                    {metric.label}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {metric.description}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className={`text-lg font-semibold ${getScoreColor(metric.score)}`}>
                  {metric.score}%
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Weight: {metric.weight}%
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Key Metrics Summary */}
      <Card className="p-6">
        <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Content Metrics
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {metadata.word_count}
            </div>
            <div className="text-sm text-blue-700 dark:text-blue-300 mt-1">
              Total Words
            </div>
            <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">
              {metadata.word_count < 300 ? 'Too Short' : 
               metadata.word_count > 3000 ? 'Very Long' : 'Good Length'}
            </div>
          </div>

          <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {metadata.keyword_density.toFixed(1)}%
            </div>
            <div className="text-sm text-green-700 dark:text-green-300 mt-1">
              Keyword Density
            </div>
            <div className="text-xs text-green-600 dark:text-green-400 mt-1">
              {metadata.keyword_density < 0.5 ? 'Too Low' :
               metadata.keyword_density > 3 ? 'Too High' : 'Optimal'}
            </div>
          </div>

          <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {metadata.readability_score}
            </div>
            <div className="text-sm text-purple-700 dark:text-purple-300 mt-1">
              Readability Score
            </div>
            <div className="text-xs text-purple-600 dark:text-purple-400 mt-1">
              {metadata.readability_score >= 80 ? 'Excellent' :
               metadata.readability_score >= 60 ? 'Good' : 'Needs Work'}
            </div>
          </div>
        </div>
      </Card>

      {/* SEO Recommendations */}
      <Card className="p-6">
        <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
          <LightBulbIcon className="h-5 w-5 mr-2 text-yellow-500" />
          SEO Recommendations
        </h4>
        
        {recommendations.length > 0 ? (
          <div className="space-y-3">
            {recommendations.map((recommendation, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                <LightBulbIcon className="h-5 w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-gray-700 dark:text-gray-300">
                  {recommendation}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-4 text-gray-500 dark:text-gray-400">
            <CheckCircleIcon className="h-8 w-8 text-green-500 mx-auto mb-2" />
            <div className="text-sm">
              Great job! No immediate SEO improvements needed.
            </div>
          </div>
        )}
      </Card>

      {/* Keyword Analysis */}
      <Card className="p-6">
        <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Keyword Analysis: "{keyword}"
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Optimization Status
            </h5>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Title Tag</span>
                {analysis.title_optimization >= 80 ? (
                  <CheckCircleIcon className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircleIcon className="h-4 w-4 text-red-500" />
                )}
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Meta Description</span>
                {analysis.meta_optimization >= 80 ? (
                  <CheckCircleIcon className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircleIcon className="h-4 w-4 text-red-500" />
                )}
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Content Body</span>
                {analysis.keyword_usage >= 80 ? (
                  <CheckCircleIcon className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircleIcon className="h-4 w-4 text-red-500" />
                )}
              </div>
            </div>
          </div>

          <div>
            <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Density Analysis
            </h5>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Current Density</span>
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {metadata.keyword_density.toFixed(1)}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Recommended</span>
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  1-3%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Status</span>
                <Badge 
                  variant={
                    metadata.keyword_density >= 1 && metadata.keyword_density <= 3 
                      ? 'success' 
                      : 'warning'
                  }
                  size="sm"
                >
                  {metadata.keyword_density >= 1 && metadata.keyword_density <= 3 
                    ? 'Optimal' 
                    : metadata.keyword_density < 1 
                      ? 'Too Low' 
                      : 'Too High'
                  }
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}