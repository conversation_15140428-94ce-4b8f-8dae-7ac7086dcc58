/**
 * SERP Analysis Dashboard
 * Advanced SERP tracking with country-specific visualization and feature analysis
 */

'use client'

import React, { useState, useEffect } from 'react'
import { useNotifications } from '@/components/Notifications'
import Card from '@/components/UI/Card'
import Button from '@/components/UI/Button'
import Input from '@/components/UI/Input'
import Badge from '@/components/UI/Badge'
import LoadingSpinner from '@/components/UI/LoadingSpinner'
import {
  MagnifyingGlassIcon,
  GlobeAltIcon,
  ChartBarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  EyeIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  MapPinIcon,
  CalendarIcon,
  FlagIcon,
  StarIcon,
  PhotoIcon,
  PlayIcon,
  ShoppingBagIcon,
  NewspaperIcon,
  LinkIcon,
  ClockIcon,
  ArrowPathIcon,
  PlusIcon,
  FunnelIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'

interface SERPAnalysisDashboardProps {
  onKeywordSelect?: (keyword: string) => void
}

interface SERPResult {
  position: number
  url: string
  title: string
  description: string
  domain: string
  isOwned: boolean
  features: SERPFeature[]
  metrics: {
    traffic: number
    clicks: number
    impressions: number
    ctr: number
  }
}

interface SERPFeature {
  type: 'featured_snippet' | 'people_also_ask' | 'image_pack' | 'video' | 'shopping' | 'local_pack' | 'news' | 'knowledge_panel'
  position: number
  content?: string
  source?: string
}

interface KeywordSERP {
  keyword: string
  searchVolume: number
  difficulty: number
  intent: 'informational' | 'navigational' | 'commercial' | 'transactional'
  country: string
  city?: string
  device: 'desktop' | 'mobile'
  language: string
  results: SERPResult[]
  features: SERPFeature[]
  totalResults: number
  lastUpdated: string
  rankingHistory: Array<{
    date: string
    position: number
    url: string
  }>
}

interface SERPOverview {
  totalKeywords: number
  avgPosition: number
  featuredSnippets: number
  topThreeResults: number
  visibility: number
  improvementOpportunities: Array<{
    keyword: string
    currentPosition: number
    opportunity: string
    impact: 'high' | 'medium' | 'low'
  }>
}

interface CountryData {
  code: string
  name: string
  flag: string
  keywords: number
  avgPosition: number
  visibility: number
  language: string
}

export default function SERPAnalysisDashboard({
  onKeywordSelect
}: SERPAnalysisDashboardProps) {
  const [serpData, setSerpData] = useState<KeywordSERP[]>([])
  const [selectedKeyword, setSelectedKeyword] = useState<KeywordSERP | null>(null)
  const [overview, setOverview] = useState<SERPOverview | null>(null)
  const [countries, setCountries] = useState<CountryData[]>([])
  const [selectedCountry, setSelectedCountry] = useState('US')
  const [selectedDevice, setSelectedDevice] = useState<'desktop' | 'mobile'>('desktop')
  const [activeView, setActiveView] = useState<'overview' | 'keywords' | 'features' | 'competitors'>('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [dateRange, setDateRange] = useState('30d')
  
  const { success: notifySuccess, error: notifyError } = useNotifications()

  // Mock data - in real app this would come from API
  useEffect(() => {
    loadSERPData()
  }, [selectedCountry, selectedDevice, dateRange])

  const loadSERPData = async () => {
    setIsLoading(true)
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // Mock SERP data
      setSerpData([
        {
          keyword: 'digital marketing services',
          searchVolume: 49500,
          difficulty: 78,
          intent: 'commercial',
          country: selectedCountry,
          device: selectedDevice,
          language: 'en',
          results: [
            {
              position: 1,
              url: 'https://competitor1.com/digital-marketing',
              title: 'Professional Digital Marketing Services | Grow Your Business',
              description: 'Transform your business with our comprehensive digital marketing services. SEO, PPC, social media, and content marketing solutions.',
              domain: 'competitor1.com',
              isOwned: false,
              features: [{ type: 'featured_snippet', position: 0, content: 'Digital marketing encompasses...', source: 'competitor1.com' }],
              metrics: { traffic: 45000, clicks: 1440, impressions: 98000, ctr: 1.47 }
            },
            {
              position: 2,
              url: 'https://yourdomain.com/services/digital-marketing',
              title: 'Expert Digital Marketing Solutions - Your Company',
              description: 'Data-driven digital marketing strategies that deliver results. Custom SEO, PPC, and content marketing for your business growth.',
              domain: 'yourdomain.com',
              isOwned: true,
              features: [],
              metrics: { traffic: 32000, clicks: 960, impressions: 67000, ctr: 1.43 }
            },
            {
              position: 3,
              url: 'https://competitor2.com/marketing-agency',
              title: 'Top Digital Marketing Agency | ROI-Focused Campaigns',
              description: 'Award-winning digital marketing agency specializing in performance marketing. Proven strategies for B2B and B2C companies.',
              domain: 'competitor2.com',
              isOwned: false,
              features: [{ type: 'people_also_ask', position: 3 }],
              metrics: { traffic: 28000, clicks: 840, impressions: 58000, ctr: 1.45 }
            }
          ],
          features: [
            { type: 'featured_snippet', position: 0, source: 'competitor1.com' },
            { type: 'people_also_ask', position: 3 },
            { type: 'image_pack', position: 5 },
            { type: 'video', position: 7 }
          ],
          totalResults: 89500000,
          lastUpdated: '2024-01-15T12:00:00Z',
          rankingHistory: [
            { date: '2024-01-15', position: 2, url: 'https://yourdomain.com/services/digital-marketing' },
            { date: '2024-01-14', position: 3, url: 'https://yourdomain.com/services/digital-marketing' },
            { date: '2024-01-13', position: 3, url: 'https://yourdomain.com/services/digital-marketing' },
            { date: '2024-01-12', position: 4, url: 'https://yourdomain.com/services/digital-marketing' }
          ]
        },
        {
          keyword: 'seo optimization',
          searchVolume: 33100,
          difficulty: 82,
          intent: 'informational',
          country: selectedCountry,
          device: selectedDevice,
          language: 'en',
          results: [
            {
              position: 1,
              url: 'https://seoexpert.com/guide',
              title: 'Complete SEO Optimization Guide 2024 | Step-by-Step',
              description: 'Master SEO optimization with our comprehensive guide. Learn technical SEO, keyword research, content optimization, and more.',
              domain: 'seoexpert.com',
              isOwned: false,
              features: [],
              metrics: { traffic: 67000, clicks: 2010, impressions: 156000, ctr: 1.29 }
            },
            {
              position: 4,
              url: 'https://yourdomain.com/blog/seo-optimization-tips',
              title: 'SEO Optimization Best Practices - Proven Strategies',
              description: 'Discover effective SEO optimization techniques to improve your search rankings. Expert tips for on-page and technical SEO.',
              domain: 'yourdomain.com',
              isOwned: true,
              features: [],
              metrics: { traffic: 18000, clicks: 540, impressions: 42000, ctr: 1.29 }
            }
          ],
          features: [
            { type: 'people_also_ask', position: 2 },
            { type: 'image_pack', position: 6 },
            { type: 'video', position: 8 }
          ],
          totalResults: 45600000,
          lastUpdated: '2024-01-15T11:45:00Z',
          rankingHistory: [
            { date: '2024-01-15', position: 4, url: 'https://yourdomain.com/blog/seo-optimization-tips' },
            { date: '2024-01-14', position: 5, url: 'https://yourdomain.com/blog/seo-optimization-tips' },
            { date: '2024-01-13', position: 5, url: 'https://yourdomain.com/blog/seo-optimization-tips' },
            { date: '2024-01-12', position: 6, url: 'https://yourdomain.com/blog/seo-optimization-tips' }
          ]
        }
      ])
      
      // Mock overview data
      setOverview({
        totalKeywords: 2847,
        avgPosition: 12.4,
        featuredSnippets: 23,
        topThreeResults: 156,
        visibility: 67.8,
        improvementOpportunities: [
          {
            keyword: 'content marketing strategy',
            currentPosition: 4,
            opportunity: 'Add FAQ section to capture People Also Ask',
            impact: 'high'
          },
          {
            keyword: 'digital advertising',
            currentPosition: 6,
            opportunity: 'Optimize for featured snippet with structured content',
            impact: 'high'
          },
          {
            keyword: 'social media marketing',
            currentPosition: 8,
            opportunity: 'Update content with recent statistics and trends',
            impact: 'medium'
          }
        ]
      })
      
      // Mock countries data
      setCountries([
        { code: 'US', name: 'United States', flag: '🇺🇸', keywords: 1240, avgPosition: 8.2, visibility: 72.4, language: 'English' },
        { code: 'GB', name: 'United Kingdom', flag: '🇬🇧', keywords: 890, avgPosition: 9.1, visibility: 68.7, language: 'English' },
        { code: 'CA', name: 'Canada', flag: '🇨🇦', keywords: 567, avgPosition: 10.3, visibility: 65.2, language: 'English' },
        { code: 'AU', name: 'Australia', flag: '🇦🇺', keywords: 423, avgPosition: 11.8, visibility: 61.9, language: 'English' },
        { code: 'DE', name: 'Germany', flag: '🇩🇪', keywords: 356, avgPosition: 13.2, visibility: 58.4, language: 'German' },
        { code: 'FR', name: 'France', flag: '🇫🇷', keywords: 298, avgPosition: 14.1, visibility: 55.7, language: 'French' }
      ])
      
    } catch (error) {
      notifyError('Failed to load SERP data')
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeywordSelect = (serp: KeywordSERP) => {
    setSelectedKeyword(serp)
    if (onKeywordSelect) {
      onKeywordSelect(serp.keyword)
    }
  }

  const filteredSerpData = serpData.filter(serp => 
    serp.keyword.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
    return num.toString()
  }

  const getFeatureIcon = (type: string) => {
    switch (type) {
      case 'featured_snippet': return <StarIcon className="h-4 w-4 text-yellow-500" />
      case 'people_also_ask': return <MagnifyingGlassIcon className="h-4 w-4 text-blue-500" />
      case 'image_pack': return <PhotoIcon className="h-4 w-4 text-green-500" />
      case 'video': return <PlayIcon className="h-4 w-4 text-red-500" />
      case 'shopping': return <ShoppingBagIcon className="h-4 w-4 text-purple-500" />
      case 'local_pack': return <MapPinIcon className="h-4 w-4 text-orange-500" />
      case 'news': return <NewspaperIcon className="h-4 w-4 text-indigo-500" />
      case 'knowledge_panel': return <InformationCircleIcon className="h-4 w-4 text-gray-500" />
      default: return <LinkIcon className="h-4 w-4 text-gray-400" />
    }
  }

  const getPositionColor = (position: number) => {
    if (position <= 3) return 'text-green-600 dark:text-green-400'
    if (position <= 10) return 'text-yellow-600 dark:text-yellow-400'
    return 'text-red-600 dark:text-red-400'
  }

  const getPositionChange = (history: Array<{ date: string, position: number }>) => {
    if (history.length < 2) return 0
    return history[0].position - history[1].position
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-red-600 dark:text-red-400'
      case 'medium': return 'text-yellow-600 dark:text-yellow-400'
      case 'low': return 'text-green-600 dark:text-green-400'
      default: return 'text-gray-600 dark:text-gray-400'
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading SERP analysis data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            SERP Analysis Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Track rankings, monitor SERP features, and analyze search results by location
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <select
            value={selectedCountry}
            onChange={(e) => setSelectedCountry(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          >
            {countries.map(country => (
              <option key={country.code} value={country.code}>
                {country.flag} {country.name}
              </option>
            ))}
          </select>
          
          <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
            <button
              onClick={() => setSelectedDevice('desktop')}
              className={`p-2 rounded-md transition-colors ${
                selectedDevice === 'desktop'
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
              }`}
              title="Desktop"
            >
              <ComputerDesktopIcon className="h-4 w-4" />
            </button>
            <button
              onClick={() => setSelectedDevice('mobile')}
              className={`p-2 rounded-md transition-colors ${
                selectedDevice === 'mobile'
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
              }`}
              title="Mobile"
            >
              <DevicePhoneMobileIcon className="h-4 w-4" />
            </button>
          </div>
          
          <Button
            variant="outline"
            onClick={() => loadSERPData()}
          >
            <ArrowPathIcon className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      {overview && (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card className="p-4">
            <div className="flex items-center">
              <MagnifyingGlassIcon className="h-8 w-8 text-blue-600 dark:text-blue-400 mr-3" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Keywords</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {formatNumber(overview.totalKeywords)}
                </p>
              </div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center">
              <TrendingUpIcon className="h-8 w-8 text-green-600 dark:text-green-400 mr-3" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Avg Position</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {overview.avgPosition.toFixed(1)}
                </p>
              </div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center">
              <StarIcon className="h-8 w-8 text-yellow-600 dark:text-yellow-400 mr-3" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Featured Snippets</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {overview.featuredSnippets}
                </p>
              </div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center">
              <CheckCircleIcon className="h-8 w-8 text-purple-600 dark:text-purple-400 mr-3" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Top 3 Results</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {overview.topThreeResults}
                </p>
              </div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center">
              <EyeIcon className="h-8 w-8 text-indigo-600 dark:text-indigo-400 mr-3" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Visibility Score</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {overview.visibility}%
                </p>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Country Performance */}
      <Card className="p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          Country Performance Overview
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {countries.map((country) => (
            <div
              key={country.code}
              className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                selectedCountry === country.code
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
              onClick={() => setSelectedCountry(country.code)}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <span className="text-2xl">{country.flag}</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {country.name}
                  </span>
                </div>
                <Badge variant="outline">{country.language}</Badge>
              </div>
              
              <div className="grid grid-cols-3 gap-3 text-center">
                <div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Keywords</div>
                  <div className="font-medium text-gray-900 dark:text-gray-100">
                    {formatNumber(country.keywords)}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Avg Pos</div>
                  <div className="font-medium text-gray-900 dark:text-gray-100">
                    {country.avgPosition.toFixed(1)}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Visibility</div>
                  <div className="font-medium text-gray-900 dark:text-gray-100">
                    {country.visibility}%
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Search and Filters */}
      <Card className="p-4">
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              type="text"
              placeholder="Search keywords..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>
          <Badge variant="secondary">
            {filteredSerpData.length} keywords
          </Badge>
        </div>
      </Card>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <div className="flex space-x-8">
          <button
            onClick={() => setActiveView('overview')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeView === 'overview'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <ChartBarIcon className="h-4 w-4 inline mr-2" />
            Keywords Overview
          </button>
          <button
            onClick={() => setActiveView('keywords')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeView === 'keywords'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <MagnifyingGlassIcon className="h-4 w-4 inline mr-2" />
            Keyword Analysis
          </button>
          <button
            onClick={() => setActiveView('features')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeView === 'features'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <StarIcon className="h-4 w-4 inline mr-2" />
            SERP Features
          </button>
          <button
            onClick={() => setActiveView('competitors')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeView === 'competitors'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <GlobeAltIcon className="h-4 w-4 inline mr-2" />
            SERP Competitors
          </button>
        </div>
      </div>

      {/* Tab Content */}
      {activeView === 'overview' && (
        <div className="space-y-6">
          {/* Improvement Opportunities */}
          {overview && overview.improvementOpportunities.length > 0 && (
            <Card className="p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Ranking Improvement Opportunities
              </h3>
              <div className="space-y-4">
                {overview.improvementOpportunities.map((opp, index) => (
                  <div key={index} className="flex items-start space-x-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex-shrink-0">
                      <Badge variant={opp.impact === 'high' ? 'error' : opp.impact === 'medium' ? 'warning' : 'success'}>
                        {opp.impact} impact
                      </Badge>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-gray-900 dark:text-gray-100">
                          {opp.keyword}
                        </h4>
                        <div className={`text-sm font-medium ${getPositionColor(opp.currentPosition)}`}>
                          Position #{opp.currentPosition}
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {opp.opportunity}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          )}

          {/* Keywords List */}
          <div className="space-y-4">
            {filteredSerpData.map((serp, index) => (
              <Card
                key={index}
                className={`p-4 cursor-pointer transition-all hover:shadow-md ${
                  selectedKeyword?.keyword === serp.keyword ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : ''
                }`}
                onClick={() => handleKeywordSelect(serp)}
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <h3 className="font-medium text-gray-900 dark:text-gray-100">
                      {serp.keyword}
                    </h3>
                    <Badge variant="outline">{serp.intent}</Badge>
                    <div className="flex items-center space-x-1">
                      {serp.features.slice(0, 3).map((feature, featureIndex) => (
                        <div key={featureIndex} title={feature.type.replace('_', ' ')}>
                          {getFeatureIcon(feature.type)}
                        </div>
                      ))}
                      {serp.features.length > 3 && (
                        <span className="text-xs text-gray-500 dark:text-gray-400">+{serp.features.length - 3}</span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <div className="text-sm text-gray-500 dark:text-gray-400">Volume</div>
                      <div className="font-medium text-gray-900 dark:text-gray-100">
                        {formatNumber(serp.searchVolume)}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-500 dark:text-gray-400">Difficulty</div>
                      <div className="font-medium text-gray-900 dark:text-gray-100">
                        {serp.difficulty}%
                      </div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  {serp.results.slice(0, 4).map((result, resultIndex) => (
                    <div
                      key={resultIndex}
                      className={`p-3 rounded-lg border ${
                        result.isOwned
                          ? 'border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20'
                          : 'border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className={`text-sm font-medium ${getPositionColor(result.position)}`}>
                          #{result.position}
                        </div>
                        {result.isOwned && (
                          <Badge variant="success" size="sm">Your Site</Badge>
                        )}
                      </div>
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1 truncate">
                        {result.title}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400 truncate">
                        {result.domain}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                        {formatNumber(result.metrics.traffic)} traffic
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {activeView === 'keywords' && selectedKeyword && (
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                {selectedKeyword.keyword}
              </h3>
              <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
                <span>Volume: {formatNumber(selectedKeyword.searchVolume)}</span>
                <span>Difficulty: {selectedKeyword.difficulty}%</span>
                <span>Intent: {selectedKeyword.intent}</span>
                <span>Last updated: {new Date(selectedKeyword.lastUpdated).toLocaleDateString()}</span>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            {/* Ranking History */}
            <div>
              <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                Ranking History
              </h4>
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 h-32 flex items-center justify-center">
                <div className="text-center text-gray-500 dark:text-gray-400">
                  <ChartBarIcon className="h-8 w-8 mx-auto mb-2" />
                  <p className="text-sm">Ranking trend chart will be rendered here</p>
                </div>
              </div>
            </div>

            {/* SERP Results */}
            <div>
              <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                Current SERP Results
              </h4>
              <div className="space-y-3">
                {selectedKeyword.results.map((result, index) => (
                  <div
                    key={index}
                    className={`p-4 rounded-lg border ${
                      result.isOwned
                        ? 'border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20'
                        : 'border-gray-200 dark:border-gray-700'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <div className={`text-lg font-bold ${getPositionColor(result.position)}`}>
                            #{result.position}
                          </div>
                          <div className="font-medium text-blue-600 dark:text-blue-400 hover:underline">
                            <a href={result.url} target="_blank" rel="noopener noreferrer">
                              {result.title}
                            </a>
                          </div>
                          {result.isOwned && (
                            <Badge variant="success" size="sm">Your Site</Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          {result.description}
                        </p>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {result.domain}
                        </div>
                      </div>
                      
                      <div className="ml-6 text-right">
                        <div className="grid grid-cols-2 gap-3 text-xs">
                          <div>
                            <div className="text-gray-500 dark:text-gray-400">Traffic</div>
                            <div className="font-medium text-gray-900 dark:text-gray-100">
                              {formatNumber(result.metrics.traffic)}
                            </div>
                          </div>
                          <div>
                            <div className="text-gray-500 dark:text-gray-400">CTR</div>
                            <div className="font-medium text-gray-900 dark:text-gray-100">
                              {result.metrics.ctr.toFixed(2)}%
                            </div>
                          </div>
                          <div>
                            <div className="text-gray-500 dark:text-gray-400">Clicks</div>
                            <div className="font-medium text-gray-900 dark:text-gray-100">
                              {formatNumber(result.metrics.clicks)}
                            </div>
                          </div>
                          <div>
                            <div className="text-gray-500 dark:text-gray-400">Impressions</div>
                            <div className="font-medium text-gray-900 dark:text-gray-100">
                              {formatNumber(result.metrics.impressions)}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {result.features.length > 0 && (
                      <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-gray-500 dark:text-gray-400">SERP Features:</span>
                          {result.features.map((feature, featureIndex) => (
                            <div key={featureIndex} className="flex items-center space-x-1">
                              {getFeatureIcon(feature.type)}
                              <span className="text-xs text-gray-600 dark:text-gray-400">
                                {feature.type.replace('_', ' ')}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </Card>
      )}

      {activeView === 'features' && (
        <Card className="p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">
            SERP Features Analysis
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {[
              { type: 'featured_snippet', label: 'Featured Snippets', count: 23, icon: StarIcon },
              { type: 'people_also_ask', label: 'People Also Ask', count: 45, icon: MagnifyingGlassIcon },
              { type: 'image_pack', label: 'Image Packs', count: 34, icon: PhotoIcon },
              { type: 'video', label: 'Video Results', count: 18, icon: PlayIcon }
            ].map((feature, index) => (
              <Card key={index} className="p-4 text-center">
                <div className="flex items-center justify-center mb-2">
                  <feature.icon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {feature.count}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {feature.label}
                </div>
              </Card>
            ))}
          </div>
          
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 text-center">
            <StarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              SERP Features Visualization
            </h4>
            <p className="text-gray-600 dark:text-gray-400">
              Detailed SERP features analysis and opportunity identification
            </p>
          </div>
        </Card>
      )}

      {activeView === 'competitors' && (
        <Card className="p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">
            SERP Competitors Analysis
          </h3>
          
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 text-center">
            <GlobeAltIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Competitor SERP Analysis
            </h4>
            <p className="text-gray-600 dark:text-gray-400">
              Analyze competitor presence across different SERP features and positions
            </p>
          </div>
        </Card>
      )}
    </div>
  )
}