// Dynamic Breadcrumb Navigation System - Context-aware navigation trails
class BreadcrumbNavigation {
    constructor() {
        this.breadcrumbContainer = null;
        this.currentPath = [];
        this.pageMap = {};
        this.routeConfig = {};
        this.animationDuration = 200;
        this.maxBreadcrumbs = 5;
        
        this.init();
    }
    
    init() {
        this.setupPageMapping();
        this.setupRouteConfiguration();
        this.createBreadcrumbContainer();
        this.detectCurrentPath();
        this.bindEvents();
        this.renderBreadcrumbs();
    }
    
    // Define page mapping and hierarchy
    setupPageMapping() {
        this.pageMap = {
            'index.html': {
                title: 'Home',
                icon: 'home',
                level: 0,
                parent: null
            },
            'dashboard.html': {
                title: 'Dashboard',
                icon: 'dashboard',
                level: 1,
                parent: 'index.html'
            },
            'analytics.html': {
                title: 'Analytics',
                icon: 'analytics',
                level: 1,
                parent: 'dashboard.html'
            },
            'projects.html': {
                title: 'Projects',
                icon: 'projects',
                level: 1,
                parent: 'dashboard.html'
            },
            'project-details.html': {
                title: 'Project Details',
                icon: 'project',
                level: 2,
                parent: 'projects.html',
                dynamic: true
            },
            'content-creator.html': {
                title: 'Content Creator',
                icon: 'create',
                level: 2,
                parent: 'projects.html'
            },
            'content-editor.html': {
                title: 'Content Editor',
                icon: 'edit',
                level: 3,
                parent: 'content-creator.html',
                dynamic: true
            },
            'bulk-generator.html': {
                title: 'Bulk Generator',
                icon: 'bulk',
                level: 2,
                parent: 'projects.html'
            },
            'pricing.html': {
                title: 'Pricing',
                icon: 'pricing',
                level: 1,
                parent: 'index.html'
            },
            'login.html': {
                title: 'Login',
                icon: 'login',
                level: 1,
                parent: 'index.html'
            },
            'register.html': {
                title: 'Register',
                icon: 'register',
                level: 1,
                parent: 'index.html'
            }
        };
    }
    
    // Setup route configuration for dynamic content
    setupRouteConfiguration() {
        this.routeConfig = {
            'project-details.html': {
                getTitleFromParams: (params) => {
                    const projectId = params.get('id');
                    // In real implementation, this would fetch from API
                    const projectNames = {
                        '1': 'Digital Marketing Agency',
                        '2': 'Property Investment Hub', 
                        '3': 'MedTech Solutions'
                    };
                    return projectNames[projectId] || 'Project Details';
                }
            },
            'content-editor.html': {
                getTitleFromParams: (params) => {
                    const contentId = params.get('id');
                    const action = params.get('action');
                    if (action === 'edit') return 'Edit Content';
                    if (action === 'create') return 'Create Content';
                    return 'Content Editor';
                }
            }
        };
    }
    
    // Create breadcrumb container if it doesn't exist
    createBreadcrumbContainer() {
        // Look for existing breadcrumb container
        this.breadcrumbContainer = document.querySelector('.breadcrumb-container');
        
        if (!this.breadcrumbContainer) {
            // Create new breadcrumb container
            this.breadcrumbContainer = document.createElement('nav');
            this.breadcrumbContainer.className = 'breadcrumb-container';
            this.breadcrumbContainer.setAttribute('aria-label', 'Breadcrumb navigation');
            
            // Insert after header or at top of main content
            const header = document.querySelector('.top-navigation');
            const mainContent = document.querySelector('.main-content');
            
            if (header && mainContent) {
                mainContent.insertBefore(this.breadcrumbContainer, mainContent.firstChild);
            } else {
                document.body.insertBefore(this.breadcrumbContainer, document.body.firstChild);
            }
        }
    }
    
    // Detect current page and build path
    detectCurrentPath() {
        const currentPage = this.getCurrentPageKey();
        const urlParams = new URLSearchParams(window.location.search);
        
        this.currentPath = this.buildPathToPage(currentPage, urlParams);
    }
    
    // Get current page key from URL
    getCurrentPageKey() {
        const path = window.location.pathname;
        let fileName = path.split('/').pop();
        
        // Handle index case
        if (!fileName || fileName === '') {
            fileName = 'index.html';
        }
        
        // Handle cases without .html extension
        if (!fileName.includes('.')) {
            fileName += '.html';
        }
        
        return fileName;
    }
    
    // Build complete path from root to current page
    buildPathToPage(pageKey, urlParams = null) {
        const path = [];
        let currentPage = pageKey;
        
        // Build path by following parent chain
        while (currentPage && this.pageMap[currentPage]) {
            const pageInfo = { ...this.pageMap[currentPage] };
            pageInfo.key = currentPage;
            
            // Handle dynamic titles
            if (pageInfo.dynamic && this.routeConfig[currentPage] && urlParams) {
                pageInfo.title = this.routeConfig[currentPage].getTitleFromParams(urlParams);
            }
            
            // Add URL parameters for context
            if (urlParams && (currentPage === pageKey)) {
                pageInfo.params = urlParams;
            }
            
            path.unshift(pageInfo);
            currentPage = pageInfo.parent;
        }
        
        return path;
    }
    
    // Render breadcrumbs in the container
    renderBreadcrumbs() {
        if (!this.breadcrumbContainer) return;
        
        // Limit breadcrumbs to prevent overflow
        const displayPath = this.currentPath.length > this.maxBreadcrumbs 
            ? this.getTrimmedPath() 
            : this.currentPath;
        
        const breadcrumbHTML = this.generateBreadcrumbHTML(displayPath);
        
        // Animate out old content
        this.breadcrumbContainer.style.opacity = '0';
        
        setTimeout(() => {
            this.breadcrumbContainer.innerHTML = breadcrumbHTML;
            this.breadcrumbContainer.style.opacity = '1';
            this.addBreadcrumbInteractions();
        }, this.animationDuration / 2);
    }
    
    // Generate HTML for breadcrumbs
    generateBreadcrumbHTML(path) {
        if (path.length === 0) return '';
        
        const breadcrumbItems = path.map((page, index) => {
            const isLast = index === path.length - 1;
            const href = this.getPageHref(page);
            
            return `
                <li class="breadcrumb-item ${isLast ? 'active' : ''}">
                    ${!isLast ? `
                        <a href="${href}" class="breadcrumb-link" data-page="${page.key}">
                            ${this.getPageIcon(page.icon)}
                            <span class="breadcrumb-text">${page.title}</span>
                        </a>
                    ` : `
                        <span class="breadcrumb-current" aria-current="page">
                            ${this.getPageIcon(page.icon)}
                            <span class="breadcrumb-text">${page.title}</span>
                        </span>
                    `}
                    ${!isLast ? this.getBreadcrumbSeparator() : ''}
                </li>
            `;
        }).join('');
        
        return `
            <ol class="breadcrumb-list" role="list">
                ${breadcrumbItems}
            </ol>
        `;
    }
    
    // Get trimmed path when too many breadcrumbs
    getTrimmedPath() {
        const start = this.currentPath.slice(0, 2);
        const end = this.currentPath.slice(-2);
        
        if (start.length + end.length >= this.currentPath.length) {
            return this.currentPath;
        }
        
        return [
            ...start,
            { title: '...', icon: 'more', key: 'ellipsis', ellipsis: true },
            ...end
        ];
    }
    
    // Get page href with preserved parameters
    getPageHref(page) {
        if (page.ellipsis) return '#';
        
        let href = page.key;
        
        // Preserve relevant URL parameters
        if (page.params && page.params.toString()) {
            href += '?' + page.params.toString();
        }
        
        return href;
    }
    
    // Get SVG icon for page type
    getPageIcon(iconType) {
        const icons = {
            home: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>',
            dashboard: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z"></path>',
            analytics: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>',
            projects: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>',
            project: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>',
            create: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>',
            edit: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>',
            bulk: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>',
            pricing: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>',
            login: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>',
            register: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>',
            more: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z"></path>'
        };
        
        return `
            <svg class="breadcrumb-icon w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                ${icons[iconType] || icons.home}
            </svg>
        `;
    }
    
    // Get breadcrumb separator
    getBreadcrumbSeparator() {
        return `
            <svg class="breadcrumb-separator w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
        `;
    }
    
    // Add interactions to breadcrumb links
    addBreadcrumbInteractions() {
        const links = this.breadcrumbContainer.querySelectorAll('.breadcrumb-link');
        
        links.forEach(link => {
            // Smooth scroll on navigation
            link.addEventListener('click', (e) => {
                const href = link.getAttribute('href');
                
                // Handle same-page navigation
                if (href.startsWith('#')) {
                    e.preventDefault();
                    const target = document.querySelector(href);
                    if (target) {
                        target.scrollIntoView({ behavior: 'smooth' });
                    }
                }
                
                // Track breadcrumb navigation
                this.trackBreadcrumbClick(link.dataset.page);
            });
            
            // Hover effects
            link.addEventListener('mouseenter', () => {
                link.classList.add('breadcrumb-hover');
            });
            
            link.addEventListener('mouseleave', () => {
                link.classList.remove('breadcrumb-hover');
            });
        });
    }
    
    // Handle URL changes (for SPA-like behavior)
    handleUrlChange() {
        this.detectCurrentPath();
        this.renderBreadcrumbs();
    }
    
    // Bind global events
    bindEvents() {
        // Handle browser back/forward
        window.addEventListener('popstate', () => {
            this.handleUrlChange();
        });
        
        // Handle hash changes
        window.addEventListener('hashchange', () => {
            this.handleUrlChange();
        });
        
        // Update breadcrumbs when content changes dynamically
        document.addEventListener('contentUpdated', (e) => {
            if (e.detail && e.detail.updateBreadcrumbs) {
                this.handleUrlChange();
            }
        });
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'Home') {
                e.preventDefault();
                this.navigateToHome();
            }
        });
    }
    
    // Navigation helpers
    navigateToHome() {
        window.location.href = 'dashboard.html';
    }
    
    navigateToParent() {
        if (this.currentPath.length > 1) {
            const parent = this.currentPath[this.currentPath.length - 2];
            window.location.href = this.getPageHref(parent);
        }
    }
    
    // Update breadcrumbs programmatically
    updateBreadcrumbs(customPath = null) {
        if (customPath) {
            this.currentPath = customPath;
        } else {
            this.detectCurrentPath();
        }
        this.renderBreadcrumbs();
    }
    
    // Add custom breadcrumb
    addCustomBreadcrumb(title, href = '#', icon = 'more') {
        const customCrumb = {
            title,
            href,
            icon,
            key: 'custom-' + Date.now(),
            custom: true
        };
        
        this.currentPath.push(customCrumb);
        this.renderBreadcrumbs();
    }
    
    // Get current breadcrumb path
    getCurrentPath() {
        return [...this.currentPath];
    }
    
    // Track breadcrumb usage
    trackBreadcrumbClick(pageKey) {
        // Analytics tracking
        if (window.analytics) {
            window.analytics.track('breadcrumb_navigation', {
                page: pageKey,
                timestamp: Date.now()
            });
        }
        
        console.log('Breadcrumb navigation:', pageKey);
    }
    
    // Cleanup method
    destroy() {
        if (this.breadcrumbContainer) {
            this.breadcrumbContainer.remove();
        }
        
        // Remove event listeners
        window.removeEventListener('popstate', this.handleUrlChange);
        window.removeEventListener('hashchange', this.handleUrlChange);
    }
}

// Initialize breadcrumb navigation when DOM is loaded
let breadcrumbNavigation;
document.addEventListener('DOMContentLoaded', function() {
    breadcrumbNavigation = new BreadcrumbNavigation();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BreadcrumbNavigation;
}