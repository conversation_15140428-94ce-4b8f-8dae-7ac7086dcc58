import winston from 'winston';
import { createClient } from '@supabase/supabase-js';

// Initialize logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/real-data-validator.log' })
  ]
});

/**
 * 100% Real Data Policy Validation System
 * Zero tolerance for demo, placeholder, or fake data
 */
export class RealDataValidator {
  constructor() {
    this.supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    // Demo data detection patterns (comprehensive)
    this.demoPatterns = {
      // Website URLs
      demoUrls: [
        /example\.com/gi,
        /test\.com/gi,
        /demo\.com/gi,
        /placeholder\.com/gi,
        /sample\.com/gi,
        /tempuri\.org/gi,
        /localhost/gi,
        /127\.0\.0\.1/gi,
        /\b\d+\.\d+\.\d+\.\d+\b/g, // IP addresses
        /\.local/gi,
        /\.test/gi,
        /\.invalid/gi,
        /\.example/gi
      ],

      // Email addresses
      demoEmails: [
        /test@/gi,
        /demo@/gi,
        /example@/gi,
        /placeholder@/gi,
        /sample@/gi,
        /fake@/gi,
        /noreply@example\.com/gi,
        /admin@test\.com/gi,
        /user@demo\.com/gi
      ],

      // Company names
      demoCompanies: [
        /acme\s*(corp|corporation|company|inc)/gi,
        /demo\s*(company|corp|inc)/gi,
        /test\s*(company|corp|inc)/gi,
        /sample\s*(business|company|corp)/gi,
        /placeholder\s*(company|corp)/gi,
        /example\s*(company|corp|business)/gi,
        /abc\s*(company|corp)/gi,
        /xyz\s*(company|corp)/gi,
        /generic\s*(company|corp)/gi,
        /fake\s*(company|corp)/gi
      ],

      // Phone numbers
      demoPhones: [
        /555-?0000/gi,
        /123-?456-?7890/gi,
        /\(555\)/gi,
        /000-?000-?0000/gi,
        /111-?111-?1111/gi,
        /123-?123-?1234/gi
      ],

      // Addresses
      demoAddresses: [
        /123\s*main\s*street/gi,
        /sample\s*address/gi,
        /test\s*address/gi,
        /placeholder\s*address/gi,
        /demo\s*street/gi,
        /example\s*avenue/gi,
        /fake\s*street/gi,
        /your\s*address\s*here/gi
      ],

      // Content patterns
      demoContent: [
        /lorem\s*ipsum/gi,
        /placeholder\s*text/gi,
        /sample\s*content/gi,
        /demo\s*text/gi,
        /test\s*content/gi,
        /\[placeholder\]/gi,
        /\[sample\]/gi,
        /\[demo\]/gi,
        /\[test\]/gi,
        /\[your\s*\w+\s*here\]/gi,
        /dolor\s*sit\s*amet/gi,
        /consectetur\s*adipiscing/gi,
        /sed\s*do\s*eiusmod/gi
      ],

      // Keywords and topics
      demoKeywords: [
        /test\s*keyword/gi,
        /sample\s*keyword/gi,
        /demo\s*keyword/gi,
        /placeholder\s*keyword/gi,
        /example\s*keyword/gi,
        /generic\s*keyword/gi,
        /fake\s*keyword/gi,
        /test\s*niche/gi,
        /sample\s*industry/gi
      ],

      // Names
      demoNames: [
        /john\s*doe/gi,
        /jane\s*doe/gi,
        /test\s*user/gi,
        /demo\s*user/gi,
        /sample\s*user/gi,
        /placeholder\s*name/gi,
        /first\s*last/gi,
        /your\s*name/gi
      ],

      // Products and services
      demoProducts: [
        /widget/gi,
        /product\s*x/gi,
        /service\s*a/gi,
        /sample\s*product/gi,
        /demo\s*product/gi,
        /test\s*service/gi,
        /placeholder\s*product/gi,
        /generic\s*service/gi
      ],

      // Financial data
      demoFinancial: [
        /\$999\.99/g,
        /\$99\.99/g,
        /\$9\.99/g,
        /\$1234\.56/g,
        /free\.99/gi,
        /\$xx\.xx/g,
        /\$\[amount\]/gi
      ],

      // Dates
      demoDates: [
        /01\/01\/2000/g,
        /12\/31\/1999/g,
        /xx\/xx\/xxxx/g,
        /\[date\]/gi,
        /sample\s*date/gi,
        /placeholder\s*date/gi
      ]
    };

    // Confidence scoring weights
    this.confidenceWeights = {
      demoUrls: 0.95,
      demoEmails: 0.90,
      demoCompanies: 0.85,
      demoPhones: 0.80,
      demoAddresses: 0.75,
      demoContent: 0.90,
      demoKeywords: 0.85,
      demoNames: 0.70,
      demoProducts: 0.75,
      demoFinancial: 0.65,
      demoDates: 0.60
    };

    // Real data validation patterns
    this.realDataValidators = {
      // Real website patterns
      realWebsites: [
        /\.(com|org|net|edu|gov|mil|int|co\.uk|ca|au|de|fr|jp)$/gi,
        /https?:\/\/(?!(?:test|demo|example|localhost))/gi
      ],

      // Real email patterns
      realEmails: [
        /@(?!(?:test|demo|example)\.)/gi,
        /\.(com|org|net|edu|gov|mil|int|co\.uk|ca|au|de|fr|jp)$/gi
      ],

      // Real business indicators
      realBusinessIndicators: [
        /\b(?:llc|inc|corp|ltd|company|corporation|enterprises|solutions|services|group|international|consulting|technologies|systems|partners|associates)\b/gi,
        /\b(?:marketing|advertising|consulting|software|technology|healthcare|finance|education|manufacturing|retail|hospitality|construction|automotive|aerospace|biotechnology|telecommunications|energy|transportation|logistics|pharmaceutical|insurance|banking|real estate|media|entertainment|publishing|agriculture|mining|utilities|government|nonprofit)\b/gi
      ]
    };

    // Industry-specific real data indicators
    this.industryIndicators = {
      healthcare: [
        /\b(?:hospital|clinic|medical|doctor|physician|nurse|patient|treatment|diagnosis|therapy|surgery|medication|pharmaceutical|healthcare|medicine|wellness|rehabilitation|cardiology|oncology|pediatric|geriatric|radiology|pathology|neurology|orthopedic|dermatology|psychiatry|anesthesia|emergency|intensive care|surgical|clinical trial|FDA|CDC|NIH|WHO)\b/gi
      ],
      technology: [
        /\b(?:software|hardware|programming|development|coding|algorithm|database|cloud|server|network|security|encryption|API|framework|platform|mobile|web|application|system|infrastructure|DevOps|agile|machine learning|artificial intelligence|blockchain|cybersecurity|data science|IoT|SaaS|PaaS|IaaS)\b/gi
      ],
      finance: [
        /\b(?:bank|banking|financial|investment|portfolio|asset|liability|equity|debt|loan|mortgage|insurance|credit|debit|transaction|payment|revenue|profit|loss|budget|accounting|audit|tax|compliance|regulation|SEC|FDIC|IRS|401k|IRA|mutual fund|stock|bond|commodity|forex|cryptocurrency|fintech)\b/gi
      ],
      education: [
        /\b(?:school|university|college|education|academic|student|teacher|professor|curriculum|degree|diploma|certificate|learning|instruction|pedagogy|assessment|accreditation|scholarship|tuition|enrollment|graduation|classroom|lecture|seminar|research|thesis|dissertation|faculty|administration|K-12|higher education|online learning|e-learning)\b/gi
      ],
      business: [
        /\b(?:business|company|corporation|enterprise|organization|management|strategy|operations|sales|marketing|human resources|customer|client|revenue|profit|growth|market|competition|industry|sector|supply chain|logistics|procurement|vendor|contract|partnership|merger|acquisition|IPO|startup|entrepreneur|innovation|productivity|efficiency)\b/gi
      ]
    };
  }

  /**
   * Validate input data for demo content
   * @param {string} input - Input data to validate
   * @param {string} inputType - Type of input (keyword, url, email, etc.)
   * @param {string} context - Additional context for validation
   * @returns {Object} Validation result
   */
  async validateRealData(input, inputType = 'general', context = {}) {
    try {
      logger.info('Starting real data validation', {
        inputType,
        inputLength: input.length,
        context
      });

      // Step 1: Basic demo data detection
      const demoDetection = this.detectDemoData(input, inputType);

      // Step 2: Real data validation
      const realDataValidation = this.validateRealDataPatterns(input, inputType);

      // Step 3: Industry-specific validation
      const industryValidation = this.validateIndustrySpecificData(input, context.industry);

      // Step 4: Calculate overall confidence score
      const confidenceScore = this.calculateConfidenceScore(
        demoDetection,
        realDataValidation,
        industryValidation
      );

      // Step 5: Make final determination
      const isDemoData = this.makeFinalDetermination(
        demoDetection,
        realDataValidation,
        confidenceScore
      );

      const validationResult = {
        valid: !isDemoData,
        isDemoData,
        confidenceScore,
        inputType,
        detectionDetails: {
          demoPatterns: demoDetection,
          realDataIndicators: realDataValidation,
          industryAlignment: industryValidation
        },
        recommendations: this.generateRecommendations(input, inputType, isDemoData),
        blockedReason: isDemoData ? this.generateBlockedReason(demoDetection) : null
      };

      // Log validation attempt
      await this.logValidationAttempt(input, inputType, validationResult, context);

      logger.info('Real data validation completed', {
        valid: validationResult.valid,
        confidenceScore: validationResult.confidenceScore,
        isDemoData
      });

      return validationResult;

    } catch (error) {
      logger.error('Real data validation failed', {
        error: error.message,
        inputType,
        inputLength: input.length
      });

      // Fail securely - reject suspicious input
      return {
        valid: false,
        isDemoData: true,
        confidenceScore: 100,
        error: error.message,
        blockedReason: 'Validation system error - input rejected for security'
      };
    }
  }

  /**
   * Detect demo data patterns in input
   */
  detectDemoData(input, inputType) {
    const detectedPatterns = [];
    let totalMatches = 0;
    let maxConfidence = 0;

    // Check each pattern category
    Object.entries(this.demoPatterns).forEach(([category, patterns]) => {
      patterns.forEach((pattern, index) => {
        const matches = input.match(pattern);
        if (matches) {
          const confidence = this.confidenceWeights[category] || 0.5;
          const patternInfo = {
            category,
            pattern: pattern.toString(),
            matches: matches.length,
            confidence,
            examples: matches.slice(0, 3) // First 3 matches for reference
          };

          detectedPatterns.push(patternInfo);
          totalMatches += matches.length;
          maxConfidence = Math.max(maxConfidence, confidence);
        }
      });
    });

    return {
      detected: detectedPatterns.length > 0,
      patterns: detectedPatterns,
      totalMatches,
      maxConfidence,
      overallDemoScore: this.calculateDemoScore(detectedPatterns, totalMatches)
    };
  }

  /**
   * Validate real data patterns
   */
  validateRealDataPatterns(input, inputType) {
    const realIndicators = [];
    let totalIndicators = 0;

    // Check for real data patterns
    Object.entries(this.realDataValidators).forEach(([category, patterns]) => {
      patterns.forEach(pattern => {
        const matches = input.match(pattern);
        if (matches) {
          realIndicators.push({
            category,
            pattern: pattern.toString(),
            matches: matches.length,
            confidence: 0.8 // Base confidence for real data indicators
          });
          totalIndicators += matches.length;
        }
      });
    });

    // Type-specific validation
    const typeSpecificValidation = this.performTypeSpecificValidation(input, inputType);

    return {
      hasRealIndicators: realIndicators.length > 0,
      indicators: realIndicators,
      totalIndicators,
      typeSpecific: typeSpecificValidation,
      realDataScore: this.calculateRealDataScore(realIndicators, typeSpecificValidation)
    };
  }

  /**
   * Validate industry-specific data patterns
   */
  validateIndustrySpecificData(input, industry) {
    if (!industry || !this.industryIndicators[industry]) {
      return {
        hasIndustryTerms: false,
        industry: industry || 'unknown',
        score: 0,
        terms: []
      };
    }

    const industryPatterns = this.industryIndicators[industry];
    const foundTerms = [];
    let totalMatches = 0;

    industryPatterns.forEach(pattern => {
      const matches = input.match(pattern);
      if (matches) {
        foundTerms.push(...matches);
        totalMatches += matches.length;
      }
    });

    return {
      hasIndustryTerms: foundTerms.length > 0,
      industry,
      terms: [...new Set(foundTerms)], // Unique terms
      totalMatches,
      score: Math.min(totalMatches * 10, 100) // Max 100
    };
  }

  /**
   * Perform type-specific validation
   */
  performTypeSpecificValidation(input, inputType) {
    switch (inputType.toLowerCase()) {
      case 'url':
      case 'website':
        return this.validateWebsiteUrl(input);
      
      case 'email':
        return this.validateEmailAddress(input);
      
      case 'keyword':
        return this.validateKeyword(input);
      
      case 'company':
      case 'business':
        return this.validateCompanyName(input);
      
      case 'phone':
        return this.validatePhoneNumber(input);
      
      default:
        return this.validateGeneralContent(input);
    }
  }

  /**
   * Validate website URL
   */
  validateWebsiteUrl(url) {
    const validations = {
      hasValidTLD: /\.(com|org|net|edu|gov|mil|biz|info|name|museum|co\.uk|ca|au|de|fr|jp|in|br|cn|ru|mx|it|es|nl|be|ch|se|no|dk|pl|cz|hu|gr|tr|za|kr|sg|my|th|ph|vn|tw|hk|nz|ie|pt|fi|at|il|ar|cl|pe|co|ve|ec|uy|py|bo|cr|pa|gt|hn|sv|ni|cu|jm|ht|do|pr|bb|tt|gd|lc|vc|ag|kn|dm|bs|bz|fj|vu|to|ws|tk|tv|nu|ki|pw|fm|mh|mp|gu|as|vi|um)$/i.test(url),
      hasProtocol: /^https?:\/\//i.test(url),
      notLocalhost: !/localhost|127\.0\.0\.1|\.local|\.test/.test(url),
      notDemoPattern: !/example|test|demo|placeholder|sample/.test(url),
      hasValidFormat: /^https?:\/\/[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*/.test(url)
    };

    const score = Object.values(validations).filter(Boolean).length * 20;

    return {
      type: 'url',
      validations,
      score,
      isValid: score >= 60
    };
  }

  /**
   * Validate email address
   */
  validateEmailAddress(email) {
    const validations = {
      hasValidFormat: /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email),
      notDemoPattern: !/test@|demo@|example@|placeholder@|sample@/.test(email),
      hasValidDomain: !/example\.com|test\.com|demo\.com/.test(email),
      hasRealTLD: /\.(com|org|net|edu|gov|mil|biz|info|co\.uk|ca|au|de|fr|jp)$/i.test(email)
    };

    const score = Object.values(validations).filter(Boolean).length * 25;

    return {
      type: 'email',
      validations,
      score,
      isValid: score >= 75
    };
  }

  /**
   * Validate keyword
   */
  validateKeyword(keyword) {
    const validations = {
      notGeneric: !/test|demo|sample|placeholder|example|generic|widget/.test(keyword),
      hasRealWords: !/lorem|ipsum|dolor|consectetur|adipiscing/.test(keyword),
      notTooShort: keyword.trim().length >= 2,
      notTooLong: keyword.trim().length <= 100,
      hasValidChars: /^[a-zA-Z0-9\s\-_'".()]+$/.test(keyword)
    };

    const score = Object.values(validations).filter(Boolean).length * 20;

    return {
      type: 'keyword',
      validations,
      score,
      isValid: score >= 60
    };
  }

  /**
   * Validate company name
   */
  validateCompanyName(company) {
    const validations = {
      notAcme: !/acme|demo|test|sample|placeholder|example|generic|abc|xyz/.test(company.toLowerCase()),
      hasBusinessTerms: /\b(llc|inc|corp|ltd|company|corporation|group|partners|associates|solutions|services|consulting|technologies|systems|enterprises)\b/i.test(company),
      notTooGeneric: !/company|business|corp|inc$/i.test(company.trim()),
      hasRealStructure: company.trim().split(/\s+/).length <= 8
    };

    const score = Object.values(validations).filter(Boolean).length * 25;

    return {
      type: 'company',
      validations,
      score,
      isValid: score >= 50
    };
  }

  /**
   * Validate phone number
   */
  validatePhoneNumber(phone) {
    const validations = {
      notDemo: !/555-?0000|123-?456-?7890|000-?000-?0000|111-?111-?1111/.test(phone),
      hasValidFormat: /^[\+]?[1-9]?[\d\s\-\(\)\.]{7,15}$/.test(phone),
      notTooManyRepeats: !/(.)\1{4,}/.test(phone.replace(/[\s\-\(\)\.]/g, '')),
      hasValidLength: phone.replace(/[\s\-\(\)\.]/g, '').length >= 7
    };

    const score = Object.values(validations).filter(Boolean).length * 25;

    return {
      type: 'phone',
      validations,
      score,
      isValid: score >= 75
    };
  }

  /**
   * Validate general content
   */
  validateGeneralContent(content) {
    const validations = {
      notLorem: !/lorem\s*ipsum|dolor\s*sit\s*amet|consectetur\s*adipiscing/.test(content),
      notPlaceholder: !/placeholder|sample|demo|test\s*content|your\s*\w+\s*here/.test(content),
      hasSubstance: content.trim().length > 10,
      hasVariedWords: new Set(content.toLowerCase().split(/\s+/)).size > 5,
      notTooRepetitive: !this.isContentRepetitive(content)
    };

    const score = Object.values(validations).filter(Boolean).length * 20;

    return {
      type: 'content',
      validations,
      score,
      isValid: score >= 60
    };
  }

  /**
   * Check if content is repetitive
   */
  isContentRepetitive(content) {
    const words = content.toLowerCase().split(/\s+/);
    const wordCounts = {};
    
    words.forEach(word => {
      wordCounts[word] = (wordCounts[word] || 0) + 1;
    });

    const totalWords = words.length;
    const maxRepeats = Math.max(...Object.values(wordCounts));
    
    return maxRepeats / totalWords > 0.3; // More than 30% repetition
  }

  /**
   * Calculate demo score
   */
  calculateDemoScore(patterns, totalMatches) {
    if (patterns.length === 0) return 0;

    const weightedScore = patterns.reduce((sum, pattern) => {
      return sum + (pattern.confidence * pattern.matches);
    }, 0);

    return Math.min(weightedScore / totalMatches * 100, 100);
  }

  /**
   * Calculate real data score
   */
  calculateRealDataScore(indicators, typeSpecific) {
    let score = 0;

    // Real indicators score
    if (indicators.length > 0) {
      const indicatorScore = indicators.reduce((sum, indicator) => {
        return sum + (indicator.confidence * indicator.matches);
      }, 0);
      score += indicatorScore * 10;
    }

    // Type-specific score
    if (typeSpecific && typeSpecific.score) {
      score += typeSpecific.score;
    }

    return Math.min(score, 100);
  }

  /**
   * Calculate overall confidence score
   */
  calculateConfidenceScore(demoDetection, realDataValidation, industryValidation) {
    let confidence = 0;

    // Demo data detected - high confidence it's fake
    if (demoDetection.detected) {
      confidence += demoDetection.maxConfidence * 100;
    }

    // Real data indicators - reduce confidence in demo detection
    if (realDataValidation.hasRealIndicators) {
      confidence -= realDataValidation.realDataScore * 0.5;
    }

    // Industry alignment - further validation
    if (industryValidation.hasIndustryTerms) {
      confidence -= industryValidation.score * 0.3;
    }

    return Math.max(0, Math.min(confidence, 100));
  }

  /**
   * Make final determination
   */
  makeFinalDetermination(demoDetection, realDataValidation, confidenceScore) {
    const threshold = parseInt(process.env.VALIDATION_CONFIDENCE_THRESHOLD || '80');

    // High confidence demo data detection
    if (confidenceScore >= threshold) {
      return true;
    }

    // Strong demo patterns with no real indicators
    if (demoDetection.detected && !realDataValidation.hasRealIndicators) {
      return true;
    }

    // Multiple demo patterns
    if (demoDetection.patterns.length >= 3) {
      return true;
    }

    // High demo score
    if (demoDetection.overallDemoScore >= 70) {
      return true;
    }

    return false;
  }

  /**
   * Generate recommendations for valid alternatives
   */
  generateRecommendations(input, inputType, isDemoData) {
    if (!isDemoData) {
      return ['Input appears to be valid real data'];
    }

    const recommendations = [];

    switch (inputType.toLowerCase()) {
      case 'url':
      case 'website':
        recommendations.push(
          'Use a real business website URL',
          'Ensure the domain has a valid TLD (.com, .org, .net)',
          'Avoid localhost, IP addresses, or demo domains'
        );
        break;

      case 'email':
        recommendations.push(
          'Use a real business email address',
          'Avoid test@, demo@, or example@ addresses',
          'Use a proper domain with valid TLD'
        );
        break;

      case 'keyword':
        recommendations.push(
          'Use real industry-specific keywords',
          'Research actual search terms for your target market',
          'Avoid generic terms like "widget" or "sample product"'
        );
        break;

      case 'company':
      case 'business':
        recommendations.push(
          'Use real company names',
          'Include proper business designations (LLC, Inc, Corp)',
          'Avoid generic names like "Acme Corp" or "Demo Company"'
        );
        break;

      default:
        recommendations.push(
          'Use real, industry-specific information',
          'Research actual data from your target market',
          'Avoid placeholder or sample content'
        );
    }

    recommendations.push('All data must reflect real business scenarios and legitimate use cases');
    return recommendations;
  }

  /**
   * Generate blocked reason
   */
  generateBlockedReason(demoDetection) {
    const primaryPattern = demoDetection.patterns
      .sort((a, b) => b.confidence - a.confidence)[0];

    if (!primaryPattern) {
      return 'Input contains demo or placeholder data';
    }

    const categoryMessages = {
      demoUrls: 'Demo or test website URLs detected',
      demoEmails: 'Test or demo email addresses detected',
      demoCompanies: 'Generic or demo company names detected',
      demoContent: 'Placeholder or sample content detected',
      demoKeywords: 'Generic or test keywords detected',
      demoNames: 'Placeholder names detected'
    };

    return categoryMessages[primaryPattern.category] || 'Demo data patterns detected';
  }

  /**
   * Log validation attempt for analysis
   */
  async logValidationAttempt(input, inputType, validationResult, context) {
    try {
      const logData = {
        input_data: input.substring(0, 1000), // Truncate for storage
        input_type: inputType,
        detection_method: 'real_data_validator',
        confidence_score: validationResult.confidenceScore,
        is_demo_data: validationResult.isDemoData,
        blocked_reason: validationResult.blockedReason,
        suggested_alternatives: validationResult.recommendations.slice(0, 3),
        ip_address: context.ipAddress || null,
        user_agent: context.userAgent || null,
        endpoint_accessed: context.endpoint || null,
        user_id: context.userId || null
      };

      const { error } = await this.supabase
        .from('demo_data_attempts')
        .insert([logData]);

      if (error) {
        logger.error('Failed to log validation attempt', { error });
      }
    } catch (error) {
      logger.error('Validation logging error', { error });
    }
  }

  /**
   * Get validation statistics
   */
  async getValidationStatistics(timeframe = '24h') {
    try {
      const timeframeMap = {
        '1h': 1,
        '24h': 24,
        '7d': 24 * 7,
        '30d': 24 * 30
      };

      const hours = timeframeMap[timeframe] || 24;
      const since = new Date(Date.now() - hours * 60 * 60 * 1000);

      const { data, error } = await this.supabase
        .from('demo_data_attempts')
        .select('*')
        .gte('created_at', since.toISOString());

      if (error) throw error;

      const stats = {
        totalAttempts: data.length,
        blockedAttempts: data.filter(d => d.is_demo_data).length,
        allowedAttempts: data.filter(d => !d.is_demo_data).length,
        blockRate: data.length > 0 ? (data.filter(d => d.is_demo_data).length / data.length * 100).toFixed(2) : 0,
        mostCommonBlocks: this.getMostCommonBlocks(data.filter(d => d.is_demo_data)),
        timeframe
      };

      return stats;
    } catch (error) {
      logger.error('Failed to get validation statistics', { error });
      return null;
    }
  }

  /**
   * Get most common block reasons
   */
  getMostCommonBlocks(blockedData) {
    const reasons = {};
    
    blockedData.forEach(attempt => {
      const reason = attempt.blocked_reason || 'Unknown';
      reasons[reason] = (reasons[reason] || 0) + 1;
    });

    return Object.entries(reasons)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([reason, count]) => ({ reason, count }));
  }

  /**
   * Update validation rules
   */
  async updateValidationRules() {
    try {
      const { data, error } = await this.supabase
        .from('validation_rules')
        .select('*')
        .eq('is_active', true);

      if (error) throw error;

      // Update patterns based on database rules
      data.forEach(rule => {
        if (rule.rule_pattern && rule.rule_type === 'demo_pattern') {
          // Add dynamic patterns to detection
          this.addDynamicPattern(rule.rule_name, rule.rule_pattern, rule.severity_level);
        }
      });

      logger.info('Validation rules updated', { rulesCount: data.length });
    } catch (error) {
      logger.error('Failed to update validation rules', { error });
    }
  }

  /**
   * Add dynamic pattern
   */
  addDynamicPattern(name, pattern, severity) {
    try {
      const regex = new RegExp(pattern, 'gi');
      const confidence = severity === 'high' ? 0.9 : severity === 'medium' ? 0.7 : 0.5;
      
      if (!this.demoPatterns.dynamic) {
        this.demoPatterns.dynamic = [];
        this.confidenceWeights.dynamic = confidence;
      }
      
      this.demoPatterns.dynamic.push(regex);
      
      logger.info('Dynamic validation pattern added', { name, severity });
    } catch (error) {
      logger.error('Failed to add dynamic pattern', { name, error: error.message });
    }
  }
}

export default RealDataValidator;