'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { User, Session } from '@supabase/supabase-js';

// Unified auth interface that works for both demo and production
interface UnifiedAuthContextType {
  user: User | null;
  session: Session | null;
  userProfile: any;
  loading: boolean;
  isDemoMode: boolean;
  signIn: (email: string, password: string) => Promise<{ error: Error | null }>;
  signUp: (email: string, password: string, metadata?: any) => Promise<{ error: Error | null }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: Error | null }>;
  updateProfile: (data: any) => Promise<{ error: Error | null }>;
  isSubscribed: (tier?: 'free' | 'pro' | 'enterprise') => boolean;
  userTier: 'free' | 'pro' | 'enterprise';
  usageCount: number;
  usageLimit: number;
  refreshUserData: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
  isOnboardingComplete: boolean;
  completeOnboarding: () => Promise<void>;
}

const UnifiedAuthContext = createContext<UnifiedAuthContextType | undefined>(undefined);

// Demo user profiles with different tiers for testing
const DEMO_PROFILES = {
  '<EMAIL>': {
    id: 'demo-user-123',
    email: '<EMAIL>',
    full_name: 'Demo User',
    avatar_url: null,
    subscription_tier: 'pro',
    usage_count: 15,
    usage_limit: 100,
    created_at: '2024-01-01T00:00:00Z',
    onboarding_completed: true,
    user_metadata: {
      full_name: 'Demo User',
      avatar_url: null
    }
  },
  '<EMAIL>': {
    id: 'free-user-456',
    email: '<EMAIL>',
    full_name: 'Free User',
    avatar_url: null,
    subscription_tier: 'free',
    usage_count: 8,
    usage_limit: 10,
    created_at: '2024-01-15T00:00:00Z',
    onboarding_completed: false,
    user_metadata: {
      full_name: 'Free User',
      avatar_url: null
    }
  },
  '<EMAIL>': {
    id: 'enterprise-user-789',
    email: '<EMAIL>',
    full_name: 'Enterprise User',
    avatar_url: null,
    subscription_tier: 'enterprise',
    usage_count: 50,
    usage_limit: 1000,
    created_at: '2024-01-10T00:00:00Z',
    onboarding_completed: true,
    user_metadata: {
      full_name: 'Enterprise User',
      avatar_url: null
    }
  }
};

export function UnifiedAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [userProfile, setUserProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  
  // Determine if we're in demo mode
  const isDemoMode = process.env.NEXT_PUBLIC_DEMO_MODE === 'true' || 
    typeof window !== 'undefined' && window.location.hostname === 'localhost';

  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    setLoading(true);
    
    if (isDemoMode) {
      // Demo mode: Check for stored demo session
      const savedSession = localStorage.getItem('demo_session');
      const savedEmail = localStorage.getItem('demo_user_email');
      
      if (savedSession && savedEmail && DEMO_PROFILES[savedEmail as keyof typeof DEMO_PROFILES]) {
        const profile = DEMO_PROFILES[savedEmail as keyof typeof DEMO_PROFILES];
        setUser({
          id: profile.id,
          email: profile.email,
          user_metadata: profile.user_metadata
        } as User);
        setUserProfile(profile);
        setSession({
          access_token: 'demo-token',
          token_type: 'bearer',
          expires_in: 3600,
          user: {
            id: profile.id,
            email: profile.email,
            user_metadata: profile.user_metadata
          }
        } as Session);
      }
    } else {
      // Production mode: Initialize Supabase auth
      try {
        const { createClientSupabase } = await import('@/utils/supabase/client');
        const supabase = createClientSupabase();
        
        const { data: { session } } = await supabase.auth.getSession();
        setSession(session);
        setUser(session?.user ?? null);
        
        if (session?.user) {
          await fetchUserProfile(session.user.id);
        }

        // Listen for auth changes
        const { data: { subscription } } = supabase.auth.onAuthStateChange(
          async (event, session) => {
            setSession(session);
            setUser(session?.user ?? null);
            
            if (session?.user) {
              await fetchUserProfile(session.user.id);
            } else {
              setUserProfile(null);
            }
          }
        );

        return () => subscription.unsubscribe();
      } catch (error) {
        console.error('Error initializing auth:', error);
      }
    }
    
    setLoading(false);
  };

  const fetchUserProfile = async (userId: string) => {
    if (isDemoMode) return;
    
    try {
      const { createClientSupabase } = await import('@/utils/supabase/client');
      const supabase = createClientSupabase();
      
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching user profile:', error);
        return;
      }

      setUserProfile(data);
    } catch (error) {
      console.error('Error in fetchUserProfile:', error);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      
      if (isDemoMode) {
        // Demo mode: Check if email exists in demo profiles
        if (DEMO_PROFILES[email as keyof typeof DEMO_PROFILES] && password) {
          const profile = DEMO_PROFILES[email as keyof typeof DEMO_PROFILES];
          
          setUser({
            id: profile.id,
            email: profile.email,
            user_metadata: profile.user_metadata
          } as User);
          setUserProfile(profile);
          setSession({
            access_token: 'demo-token',
            token_type: 'bearer',
            expires_in: 3600,
            user: {
              id: profile.id,
              email: profile.email,
              user_metadata: profile.user_metadata
            }
          } as Session);
          
          localStorage.setItem('demo_session', 'true');
          localStorage.setItem('demo_user_email', email);
          
          return { error: null };
        }
        
        return { error: new Error('Invalid demo credentials. Try: <EMAIL>, <EMAIL>, or <EMAIL>') };
      }
      
      // Production mode
      const { createClientSupabase } = await import('@/utils/supabase/client');
      const supabase = createClientSupabase();
      
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      return { error };
    } catch (error) {
      return { error: error as Error };
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, metadata?: any) => {
    if (isDemoMode) {
      // In demo mode, signup just does the same as signin
      return signIn(email, password);
    }
    
    try {
      setLoading(true);
      const { createClientSupabase } = await import('@/utils/supabase/client');
      const supabase = createClientSupabase();
      
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata,
        },
      });

      return { error };
    } catch (error) {
      return { error: error as Error };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    setLoading(true);
    
    if (isDemoMode) {
      setUser(null);
      setSession(null);
      setUserProfile(null);
      localStorage.removeItem('demo_session');
      localStorage.removeItem('demo_user_email');
      router.push('/');
    } else {
      try {
        const { createClientSupabase } = await import('@/utils/supabase/client');
        const supabase = createClientSupabase();
        
        await supabase.auth.signOut();
        setUser(null);
        setSession(null);
        setUserProfile(null);
        router.push('/');
      } catch (error) {
        console.error('Error signing out:', error);
      }
    }
    
    setLoading(false);
  };

  const resetPassword = async (email: string) => {
    if (isDemoMode) {
      return { error: null };
    }
    
    try {
      const { createClientSupabase } = await import('@/utils/supabase/client');
      const supabase = createClientSupabase();
      
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      });

      return { error };
    } catch (error) {
      return { error: error as Error };
    }
  };

  const updateProfile = async (data: any) => {
    try {
      if (!user) {
        return { error: new Error('No user logged in') };
      }

      setLoading(true);

      if (isDemoMode) {
        // Update demo profile
        const updatedProfile = { ...userProfile, ...data };
        setUserProfile(updatedProfile);
        
        // Update demo profiles for persistence
        const email = user.email;
        if (email && DEMO_PROFILES[email as keyof typeof DEMO_PROFILES]) {
          (DEMO_PROFILES[email as keyof typeof DEMO_PROFILES] as any) = updatedProfile;
        }
        
        return { error: null };
      }

      // Production mode
      const { createClientSupabase } = await import('@/utils/supabase/client');
      const supabase = createClientSupabase();

      // Update auth metadata
      const { error: authError } = await supabase.auth.updateUser({
        data: data,
      });

      if (authError) {
        return { error: authError };
      }

      // Update user profile in database
      const { error: profileError } = await supabase
        .from('users')
        .update(data)
        .eq('id', user.id);

      if (profileError) {
        return { error: profileError };
      }

      await fetchUserProfile(user.id);
      return { error: null };
    } catch (error) {
      return { error: error as Error };
    } finally {
      setLoading(false);
    }
  };

  const refreshUserData = async () => {
    if (user) {
      if (isDemoMode) {
        const email = user.email;
        if (email && DEMO_PROFILES[email as keyof typeof DEMO_PROFILES]) {
          setUserProfile(DEMO_PROFILES[email as keyof typeof DEMO_PROFILES]);
        }
      } else {
        await fetchUserProfile(user.id);
      }
    }
  };

  const isSubscribed = (tier?: 'free' | 'pro' | 'enterprise') => {
    if (!userProfile) return false;
    
    if (!tier) {
      return userProfile.subscription_tier !== 'free';
    }

    const tierLevels = {
      'free': 1,
      'pro': 2,
      'enterprise': 3
    };

    const userTierLevel = tierLevels[userProfile.subscription_tier] || 0;
    const requiredTierLevel = tierLevels[tier] || 0;

    return userTierLevel >= requiredTierLevel;
  };

  const hasPermission = (permission: string) => {
    if (!userProfile) return false;
    
    // Define permissions by tier
    const permissions = {
      'free': ['basic_content', 'view_analytics'],
      'pro': ['basic_content', 'view_analytics', 'bulk_content', 'advanced_analytics', 'competitor_analysis'],
      'enterprise': ['basic_content', 'view_analytics', 'bulk_content', 'advanced_analytics', 'competitor_analysis', 'team_management', 'white_label', 'api_access']
    };
    
    const userPermissions = permissions[userProfile.subscription_tier] || permissions.free;
    return userPermissions.includes(permission);
  };

  const completeOnboarding = async () => {
    if (isDemoMode) {
      setUserProfile({ ...userProfile, onboarding_completed: true });
      return;
    }
    
    try {
      const { createClientSupabase } = await import('@/utils/supabase/client');
      const supabase = createClientSupabase();
      
      if (user) {
        await supabase
          .from('users')
          .update({ onboarding_completed: true })
          .eq('id', user.id);
        
        await refreshUserData();
      }
    } catch (error) {
      console.error('Error completing onboarding:', error);
    }
  };

  const userTier = userProfile?.subscription_tier || 'free';
  const usageCount = userProfile?.usage_count || 0;
  const usageLimit = userProfile?.usage_limit || (userTier === 'free' ? 10 : userTier === 'pro' ? 100 : 1000);
  const isOnboardingComplete = userProfile?.onboarding_completed !== false;

  const value: UnifiedAuthContextType = {
    user,
    session,
    userProfile,
    loading,
    isDemoMode,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updateProfile,
    isSubscribed,
    userTier,
    usageCount,
    usageLimit,
    refreshUserData,
    hasPermission,
    isOnboardingComplete,
    completeOnboarding,
  };

  return (
    <UnifiedAuthContext.Provider value={value}>
      {children}
    </UnifiedAuthContext.Provider>
  );
}

export function useUnifiedAuth() {
  const context = useContext(UnifiedAuthContext);
  if (context === undefined) {
    throw new Error('useUnifiedAuth must be used within a UnifiedAuthProvider');
  }
  return context;
}

// Enhanced protected route wrapper
export function EnhancedProtectedRoute({ 
  children,
  redirectTo = '/auth/login',
  requireSubscription,
  requiredPermissions = [],
  fallback,
}: {
  children: React.ReactNode;
  redirectTo?: string;
  requireSubscription?: 'free' | 'pro' | 'enterprise';
  requiredPermissions?: string[];
  fallback?: React.ReactNode;
}) {
  const { 
    user, 
    loading, 
    isSubscribed, 
    hasPermission, 
    userTier,
    isOnboardingComplete 
  } = useUnifiedAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      if (!user) {
        router.push(redirectTo);
      } else if (requireSubscription && !isSubscribed(requireSubscription)) {
        router.push('/pricing');
      } else if (requiredPermissions.length > 0 && !requiredPermissions.every(hasPermission)) {
        router.push('/pricing');
      }
    }
  }, [user, loading, requireSubscription, isSubscribed, requiredPermissions, hasPermission, router, redirectTo]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  if (requireSubscription && !isSubscribed(requireSubscription)) {
    return fallback || null;
  }

  if (requiredPermissions.length > 0 && !requiredPermissions.every(hasPermission)) {
    return fallback || (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
            Access Restricted
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            This feature requires a {requireSubscription || 'pro'} subscription or higher.
          </p>
          <button
            onClick={() => router.push('/pricing')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold"
          >
            Upgrade Plan
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}