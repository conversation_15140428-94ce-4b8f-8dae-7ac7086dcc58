<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Choose the perfect SEO SAAS plan for your business. Professional AI-powered content generation for any niche with transparent pricing.">
    <title>Pricing Plans - SEO SAAS</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/layout.css">
    <link rel="stylesheet" href="css/animations.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
    
    <!-- Open Graph -->
    <meta property="og:title" content="Pricing Plans - SEO SAAS">
    <meta property="og:description" content="Transparent pricing for AI-powered SEO content generation. Plans for every business size.">
    <meta property="og:type" content="website">
</head>
<body>
    <!-- Professional Header Navigation -->
    <header class="header-main bg-white border-b sticky top-0 z-50">
        <nav class="container-xl mx-auto">
            <div class="flex items-center justify-between py-4">
                <!-- Brand -->
                <div class="nav-brand flex items-center gap-3">
                    <a href="index.html" class="flex items-center gap-3">
                        <svg class="w-10 h-10 text-primary-600" viewBox="0 0 40 40" fill="currentColor">
                            <path d="M20 5L5 15v10l15 10 15-10V15L20 5z"/>
                            <path d="M20 15l-10 6v8l10 6 10-6v-8l-10-6z" fill="white" opacity="0.5"/>
                        </svg>
                        <span class="text-xl font-bold text-gray-900">SEO SAAS</span>
                    </a>
                </div>
                
                <!-- Desktop Navigation -->
                <nav class="hidden md:flex items-center gap-8">
                    <a href="index.html#features" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Features</a>
                    <a href="index.html#how-it-works" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">How It Works</a>
                    <a href="pricing.html" class="text-primary-600 font-medium">Pricing</a>
                    <a href="index.html#testimonials" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Testimonials</a>
                </nav>
                
                <!-- CTA Buttons -->
                <div class="flex items-center gap-4">
                    <a href="login.html" class="btn btn-ghost hidden sm:inline-flex">Login</a>
                    <a href="register.html" class="btn btn-primary">Get Started Free</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Pricing Hero Section -->
    <section class="pricing-hero bg-gradient-to-b from-gray-50 to-white py-16">
        <div class="container-xl mx-auto text-center">
            <div class="max-w-3xl mx-auto animate-fade-in-up">
                <div class="badge badge-primary mb-4">
                    <span class="badge-dot"></span>
                    14-Day Free Trial • No Credit Card Required
                </div>
                
                <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    Simple, Transparent Pricing
                </h1>
                
                <p class="text-xl text-gray-600 mb-8">
                    Choose the perfect plan for your content generation needs. All plans include our revolutionary Universal Niche Adaptation technology.
                </p>
                
                <!-- Billing Toggle -->
                <div class="inline-flex items-center bg-gray-100 rounded-lg p-1 mb-12">
                    <button class="billing-toggle active" data-billing="monthly">
                        <span>Monthly</span>
                    </button>
                    <button class="billing-toggle" data-billing="yearly">
                        <span>Yearly</span>
                        <span class="badge badge-success ml-2">Save 20%</span>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Plans Section -->
    <section class="pricing-plans pb-20">
        <div class="container-xl mx-auto">
            <div class="grid lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
                
                <!-- Starter Plan -->
                <div class="pricing-card" data-plan="starter">
                    <div class="card-header">
                        <h3 class="text-xl font-semibold text-gray-900">Starter</h3>
                        <p class="text-gray-600 text-sm mt-1">Perfect for small businesses and freelancers</p>
                    </div>
                    
                    <div class="card-pricing">
                        <div class="price-display">
                            <span class="currency">$</span>
                            <span class="amount" data-monthly="49" data-yearly="39">49</span>
                            <span class="period">/month</span>
                        </div>
                        <div class="annual-price">$468/year when billed annually</div>
                    </div>
                    
                    <div class="card-features">
                        <ul class="feature-list">
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>50 content generations per month</span>
                            </li>
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Universal Niche Adaptation</span>
                            </li>
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Real competitor analysis</span>
                            </li>
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Sequential AI thinking</span>
                            </li>
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Authority link discovery</span>
                            </li>
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>3 projects</span>
                            </li>
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Email support</span>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="card-action">
                        <a href="register.html?plan=starter" class="btn btn-secondary w-full btn-lg">
                            Start Free Trial
                        </a>
                    </div>
                </div>

                <!-- Professional Plan -->
                <div class="pricing-card featured" data-plan="professional">
                    <div class="featured-badge">
                        <span>Most Popular</span>
                    </div>
                    
                    <div class="card-header">
                        <h3 class="text-xl font-semibold text-gray-900">Professional</h3>
                        <p class="text-gray-600 text-sm mt-1">Ideal for growing agencies and content teams</p>
                    </div>
                    
                    <div class="card-pricing">
                        <div class="price-display">
                            <span class="currency">$</span>
                            <span class="amount" data-monthly="149" data-yearly="119">149</span>
                            <span class="period">/month</span>
                        </div>
                        <div class="annual-price">$1,428/year when billed annually</div>
                    </div>
                    
                    <div class="card-features">
                        <ul class="feature-list">
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>500 content generations per month</span>
                            </li>
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Everything in Starter</span>
                            </li>
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Bulk content generation</span>
                            </li>
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Advanced content editor</span>
                            </li>
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Custom content templates</span>
                            </li>
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>15 projects</span>
                            </li>
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Priority support</span>
                            </li>
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Team collaboration</span>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="card-action">
                        <a href="register.html?plan=professional" class="btn btn-primary w-full btn-lg">
                            Start Free Trial
                        </a>
                    </div>
                </div>

                <!-- Enterprise Plan -->
                <div class="pricing-card" data-plan="enterprise">
                    <div class="card-header">
                        <h3 class="text-xl font-semibold text-gray-900">Enterprise</h3>
                        <p class="text-gray-600 text-sm mt-1">For large organizations with advanced needs</p>
                    </div>
                    
                    <div class="card-pricing">
                        <div class="price-display">
                            <span class="currency">$</span>
                            <span class="amount" data-monthly="449" data-yearly="359">449</span>
                            <span class="period">/month</span>
                        </div>
                        <div class="annual-price">$4,308/year when billed annually</div>
                    </div>
                    
                    <div class="card-features">
                        <ul class="feature-list">
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Unlimited content generations</span>
                            </li>
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Everything in Professional</span>
                            </li>
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>White-label solutions</span>
                            </li>
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>API access</span>
                            </li>
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Custom integrations</span>
                            </li>
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Unlimited projects</span>
                            </li>
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Dedicated account manager</span>
                            </li>
                            <li class="feature-item">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>24/7 phone support</span>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="card-action">
                        <a href="contact.html?plan=enterprise" class="btn btn-secondary w-full btn-lg">
                            Contact Sales
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Feature Comparison Section -->
    <section class="feature-comparison bg-gray-50 py-20">
        <div class="container-xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Compare All Features</h2>
                <p class="text-xl text-gray-600">See exactly what's included in each plan</p>
            </div>
            
            <div class="comparison-table-container">
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th class="feature-col">Features</th>
                            <th class="plan-col">Starter</th>
                            <th class="plan-col featured-col">Professional</th>
                            <th class="plan-col">Enterprise</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="category-row">
                            <td colspan="4" class="category-header">Content Generation</td>
                        </tr>
                        <tr>
                            <td class="feature-name">Monthly generations</td>
                            <td class="plan-value">50</td>
                            <td class="plan-value">500</td>
                            <td class="plan-value">Unlimited</td>
                        </tr>
                        <tr>
                            <td class="feature-name">Universal Niche Adaptation</td>
                            <td class="plan-value"><span class="check-icon">✓</span></td>
                            <td class="plan-value"><span class="check-icon">✓</span></td>
                            <td class="plan-value"><span class="check-icon">✓</span></td>
                        </tr>
                        <tr>
                            <td class="feature-name">Sequential AI Thinking</td>
                            <td class="plan-value"><span class="check-icon">✓</span></td>
                            <td class="plan-value"><span class="check-icon">✓</span></td>
                            <td class="plan-value"><span class="check-icon">✓</span></td>
                        </tr>
                        <tr>
                            <td class="feature-name">Bulk content generation</td>
                            <td class="plan-value">—</td>
                            <td class="plan-value"><span class="check-icon">✓</span></td>
                            <td class="plan-value"><span class="check-icon">✓</span></td>
                        </tr>
                        
                        <tr class="category-row">
                            <td colspan="4" class="category-header">Analysis & Research</td>
                        </tr>
                        <tr>
                            <td class="feature-name">Real competitor analysis</td>
                            <td class="plan-value"><span class="check-icon">✓</span></td>
                            <td class="plan-value"><span class="check-icon">✓</span></td>
                            <td class="plan-value"><span class="check-icon">✓</span></td>
                        </tr>
                        <tr>
                            <td class="feature-name">Authority link discovery</td>
                            <td class="plan-value"><span class="check-icon">✓</span></td>
                            <td class="plan-value"><span class="check-icon">✓</span></td>
                            <td class="plan-value"><span class="check-icon">✓</span></td>
                        </tr>
                        <tr>
                            <td class="feature-name">SERP analysis</td>
                            <td class="plan-value">Basic</td>
                            <td class="plan-value">Advanced</td>
                            <td class="plan-value">Enterprise</td>
                        </tr>
                        
                        <tr class="category-row">
                            <td colspan="4" class="category-header">Project Management</td>
                        </tr>
                        <tr>
                            <td class="feature-name">Projects</td>
                            <td class="plan-value">3</td>
                            <td class="plan-value">15</td>
                            <td class="plan-value">Unlimited</td>
                        </tr>
                        <tr>
                            <td class="feature-name">Team collaboration</td>
                            <td class="plan-value">—</td>
                            <td class="plan-value"><span class="check-icon">✓</span></td>
                            <td class="plan-value"><span class="check-icon">✓</span></td>
                        </tr>
                        <tr>
                            <td class="feature-name">Custom templates</td>
                            <td class="plan-value">—</td>
                            <td class="plan-value"><span class="check-icon">✓</span></td>
                            <td class="plan-value"><span class="check-icon">✓</span></td>
                        </tr>
                        
                        <tr class="category-row">
                            <td colspan="4" class="category-header">Support & Integration</td>
                        </tr>
                        <tr>
                            <td class="feature-name">Support</td>
                            <td class="plan-value">Email</td>
                            <td class="plan-value">Priority</td>
                            <td class="plan-value">24/7 Phone</td>
                        </tr>
                        <tr>
                            <td class="feature-name">API access</td>
                            <td class="plan-value">—</td>
                            <td class="plan-value">—</td>
                            <td class="plan-value"><span class="check-icon">✓</span></td>
                        </tr>
                        <tr>
                            <td class="feature-name">White-label</td>
                            <td class="plan-value">—</td>
                            <td class="plan-value">—</td>
                            <td class="plan-value"><span class="check-icon">✓</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section py-20">
        <div class="container-xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
                <p class="text-xl text-gray-600">Get answers to common questions about our pricing</p>
            </div>
            
            <div class="max-w-4xl mx-auto">
                <div class="faq-grid">
                    <div class="faq-item">
                        <button class="faq-question" onclick="toggleFAQ(this)">
                            <span>What does "Universal Niche Adaptation" mean?</span>
                            <svg class="faq-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="faq-answer">
                            <p>Our AI can understand and generate content for any keyword in any industry, from "plumbing services" to "cryptocurrency trading" to "pet grooming." No manual setup or training required.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <button class="faq-question" onclick="toggleFAQ(this)">
                            <span>What is Sequential AI Thinking?</span>
                            <svg class="faq-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="faq-answer">
                            <p>Our AI follows a 6-stage reasoning process: strategic analysis, expert knowledge application, content architecture, generation, enhancement, and validation. This produces superior content compared to single-pass generation.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <button class="faq-question" onclick="toggleFAQ(this)">
                            <span>Do you use demo data or real competitor analysis?</span>
                            <svg class="faq-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="faq-answer">
                            <p>We use 100% real data only. No demo content, placeholder text, or mock data. Every analysis is based on real competitor research and live SERP data for your specific keyword and location.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <button class="faq-question" onclick="toggleFAQ(this)">
                            <span>Can I change plans anytime?</span>
                            <svg class="faq-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="faq-answer">
                            <p>Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately, and we'll prorate any billing differences.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <button class="faq-question" onclick="toggleFAQ(this)">
                            <span>What happens after my free trial ends?</span>
                            <svg class="faq-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="faq-answer">
                            <p>Your account will be automatically converted to the plan you selected during signup. You can cancel anytime before the trial ends without being charged.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <button class="faq-question" onclick="toggleFAQ(this)">
                            <span>Do you offer refunds?</span>
                            <svg class="faq-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="faq-answer">
                            <p>We offer a 30-day money-back guarantee on all plans. If you're not satisfied, contact our support team for a full refund.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section bg-primary-600 py-20">
        <div class="container-xl mx-auto text-center">
            <div class="max-w-3xl mx-auto text-white">
                <h2 class="text-3xl md:text-4xl font-bold mb-6">
                    Ready to Dominate Any Niche?
                </h2>
                <p class="text-xl text-primary-100 mb-8">
                    Start your 14-day free trial today. No credit card required. Cancel anytime.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="register.html" class="btn btn-white btn-lg">
                        Start Free Trial
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                    </a>
                    <a href="contact.html" class="btn btn-primary-light btn-lg">
                        Contact Sales
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-16">
        <div class="container-xl mx-auto">
            <div class="grid md:grid-cols-4 gap-8 mb-12">
                <div class="col-span-2 md:col-span-1">
                    <div class="flex items-center gap-3 mb-4">
                        <svg class="w-8 h-8 text-primary-400" viewBox="0 0 40 40" fill="currentColor">
                            <path d="M20 5L5 15v10l15 10 15-10V15L20 5z"/>
                            <path d="M20 15l-10 6v8l10 6 10-6v-8l-10-6z" fill="white" opacity="0.5"/>
                        </svg>
                        <span class="text-xl font-bold">SEO SAAS</span>
                    </div>
                    <p class="text-gray-400 mb-6">AI-powered content generation that works for any niche. No demo data, only real intelligence.</p>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-4">Product</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="index.html#features" class="hover:text-white transition-colors">Features</a></li>
                        <li><a href="pricing.html" class="hover:text-white transition-colors">Pricing</a></li>
                        <li><a href="api.html" class="hover:text-white transition-colors">API</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-4">Company</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="about.html" class="hover:text-white transition-colors">About</a></li>
                        <li><a href="contact.html" class="hover:text-white transition-colors">Contact</a></li>
                        <li><a href="careers.html" class="hover:text-white transition-colors">Careers</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-4">Support</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="help.html" class="hover:text-white transition-colors">Help Center</a></li>
                        <li><a href="privacy.html" class="hover:text-white transition-colors">Privacy</a></li>
                        <li><a href="terms.html" class="hover:text-white transition-colors">Terms</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400">&copy; 2025 SEO SAAS. All rights reserved.</p>
                <div class="flex gap-6 mt-4 md:mt-0">
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">Twitter</a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">LinkedIn</a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">GitHub</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Billing toggle functionality
            const billingToggles = document.querySelectorAll('.billing-toggle');
            const priceAmounts = document.querySelectorAll('.amount');
            const annualPrices = document.querySelectorAll('.annual-price');
            
            billingToggles.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    const billing = this.getAttribute('data-billing');
                    
                    // Update active state
                    billingToggles.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    // Update prices
                    priceAmounts.forEach(amount => {
                        const monthlyPrice = amount.getAttribute('data-monthly');
                        const yearlyPrice = amount.getAttribute('data-yearly');
                        amount.textContent = billing === 'yearly' ? yearlyPrice : monthlyPrice;
                    });
                    
                    // Update annual price visibility
                    annualPrices.forEach(price => {
                        price.style.display = billing === 'yearly' ? 'block' : 'none';
                    });
                });
            });
        });
        
        function toggleFAQ(button) {
            const faqItem = button.parentElement;
            const answer = faqItem.querySelector('.faq-answer');
            const icon = button.querySelector('.faq-icon');
            
            const isOpen = faqItem.classList.contains('active');
            
            // Close all other FAQs
            document.querySelectorAll('.faq-item').forEach(item => {
                item.classList.remove('active');
                item.querySelector('.faq-answer').style.display = 'none';
                item.querySelector('.faq-icon').style.transform = 'rotate(0deg)';
            });
            
            // Toggle current FAQ
            if (!isOpen) {
                faqItem.classList.add('active');
                answer.style.display = 'block';
                icon.style.transform = 'rotate(180deg)';
            }
        }
    </script>
</body>
</html>