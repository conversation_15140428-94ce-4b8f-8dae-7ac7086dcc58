/**
 * @jest-environment node
 */

const request = require('supertest');
const express = require('express');
const jwt = require('jsonwebtoken');

// Mock Supabase
const mockSupabaseClient = {
  from: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  update: jest.fn().mockReturnThis(),
  delete: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  order: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  single: jest.fn().mockReturnThis(),
};

// Mock the projects router
const mockProjectsRouter = express.Router();

// Mock middleware
const mockAuth = (req, res, next) => {
  req.user = { id: 'test-user-id', email: '<EMAIL>' };
  next();
};

// Mock project data
const mockProjects = [
  {
    id: 'project-1',
    name: 'Test Project 1',
    description: 'A test project',
    user_id: 'test-user-id',
    target_keywords: ['seo', 'content'],
    created_at: '2024-01-01T00:00:00.000Z',
    updated_at: '2024-01-01T00:00:00.000Z',
  },
  {
    id: 'project-2',
    name: 'Test Project 2',
    description: 'Another test project',
    user_id: 'test-user-id',
    target_keywords: ['marketing', 'digital'],
    created_at: '2024-01-02T00:00:00.000Z',
    updated_at: '2024-01-02T00:00:00.000Z',
  },
];

// Setup mock routes
mockProjectsRouter.use(mockAuth);

mockProjectsRouter.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 50, search = '', sortBy = 'created_at', sortOrder = 'desc' } = req.query;
    
    let filteredProjects = mockProjects;
    
    // Apply search filter
    if (search) {
      filteredProjects = filteredProjects.filter(project => 
        project.name.toLowerCase().includes(search.toLowerCase()) ||
        project.description.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    // Apply sorting
    filteredProjects.sort((a, b) => {
      const aValue = a[sortBy];
      const bValue = b[sortBy];
      
      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : -1;
      }
      return aValue > bValue ? 1 : -1;
    });
    
    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedProjects = filteredProjects.slice(startIndex, endIndex);
    
    res.json({
      success: true,
      data: {
        projects: paginatedProjects,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: filteredProjects.length,
          pages: Math.ceil(filteredProjects.length / limit),
        },
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Internal server error',
    });
  }
});

mockProjectsRouter.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const project = mockProjects.find(p => p.id === id);
    
    if (!project) {
      return res.status(404).json({
        success: false,
        error: 'Project not found',
      });
    }
    
    res.json({
      success: true,
      data: project,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Internal server error',
    });
  }
});

mockProjectsRouter.post('/', async (req, res) => {
  try {
    const { name, description, target_keywords, industry } = req.body;
    
    // Validate required fields
    if (!name || !description) {
      return res.status(400).json({
        success: false,
        error: 'Name and description are required',
      });
    }
    
    const newProject = {
      id: `project-${Date.now()}`,
      name,
      description,
      target_keywords: target_keywords || [],
      industry: industry || 'general',
      user_id: req.user.id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    mockProjects.push(newProject);
    
    res.status(201).json({
      success: true,
      data: newProject,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Internal server error',
    });
  }
});

mockProjectsRouter.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, target_keywords, industry } = req.body;
    
    const projectIndex = mockProjects.findIndex(p => p.id === id);
    
    if (projectIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Project not found',
      });
    }
    
    const updatedProject = {
      ...mockProjects[projectIndex],
      name: name || mockProjects[projectIndex].name,
      description: description || mockProjects[projectIndex].description,
      target_keywords: target_keywords || mockProjects[projectIndex].target_keywords,
      industry: industry || mockProjects[projectIndex].industry,
      updated_at: new Date().toISOString(),
    };
    
    mockProjects[projectIndex] = updatedProject;
    
    res.json({
      success: true,
      data: updatedProject,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Internal server error',
    });
  }
});

mockProjectsRouter.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const projectIndex = mockProjects.findIndex(p => p.id === id);
    
    if (projectIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Project not found',
      });
    }
    
    mockProjects.splice(projectIndex, 1);
    
    res.json({
      success: true,
      message: 'Project deleted successfully',
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Internal server error',
    });
  }
});

// Test metrics endpoint
mockProjectsRouter.get('/:id/metrics', async (req, res) => {
  try {
    const { id } = req.params;
    const project = mockProjects.find(p => p.id === id);
    
    if (!project) {
      return res.status(404).json({
        success: false,
        error: 'Project not found',
      });
    }
    
    // Mock metrics data
    const metrics = {
      contentGenerated: 12,
      keywordsTracked: 25,
      averageSeoScore: 87.5,
      totalWords: 15000,
      lastUpdated: new Date().toISOString(),
    };
    
    res.json({
      success: true,
      data: metrics,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Internal server error',
    });
  }
});

// Create test app
const app = express();
app.use(express.json());
app.use('/api/projects', mockProjectsRouter);

describe('Projects API', () => {
  beforeEach(() => {
    // Reset mock projects
    mockProjects.length = 0;
    mockProjects.push(
      {
        id: 'project-1',
        name: 'Test Project 1',
        description: 'A test project',
        user_id: 'test-user-id',
        target_keywords: ['seo', 'content'],
        created_at: '2024-01-01T00:00:00.000Z',
        updated_at: '2024-01-01T00:00:00.000Z',
      },
      {
        id: 'project-2',
        name: 'Test Project 2',
        description: 'Another test project',
        user_id: 'test-user-id',
        target_keywords: ['marketing', 'digital'],
        created_at: '2024-01-02T00:00:00.000Z',
        updated_at: '2024-01-02T00:00:00.000Z',
      }
    );
  });

  describe('GET /api/projects', () => {
    it('should return all projects for authenticated user', async () => {
      const response = await request(app).get('/api/projects');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.projects).toHaveLength(2);
      expect(response.body.data.pagination.total).toBe(2);
    });

    it('should support search functionality', async () => {
      const response = await request(app).get('/api/projects?search=Test Project 1');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.projects).toHaveLength(1);
      expect(response.body.data.projects[0].name).toBe('Test Project 1');
    });

    it('should support pagination', async () => {
      const response = await request(app).get('/api/projects?page=1&limit=1');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.projects).toHaveLength(1);
      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.limit).toBe(1);
      expect(response.body.data.pagination.pages).toBe(2);
    });

    it('should support sorting', async () => {
      const response = await request(app).get('/api/projects?sortBy=name&sortOrder=asc');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.projects[0].name).toBe('Test Project 1');
    });
  });

  describe('GET /api/projects/:id', () => {
    it('should return a specific project', async () => {
      const response = await request(app).get('/api/projects/project-1');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBe('project-1');
      expect(response.body.data.name).toBe('Test Project 1');
    });

    it('should return 404 for non-existent project', async () => {
      const response = await request(app).get('/api/projects/non-existent');

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Project not found');
    });
  });

  describe('POST /api/projects', () => {
    it('should create a new project', async () => {
      const projectData = {
        name: 'New Test Project',
        description: 'A new test project',
        target_keywords: ['new', 'test'],
        industry: 'technology',
      };

      const response = await request(app)
        .post('/api/projects')
        .send(projectData);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe(projectData.name);
      expect(response.body.data.description).toBe(projectData.description);
      expect(response.body.data.target_keywords).toEqual(projectData.target_keywords);
      expect(response.body.data.industry).toBe(projectData.industry);
      expect(response.body.data.user_id).toBe('test-user-id');
    });

    it('should return 400 for missing required fields', async () => {
      const projectData = {
        name: 'New Test Project',
        // Missing description
      };

      const response = await request(app)
        .post('/api/projects')
        .send(projectData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Name and description are required');
    });
  });

  describe('PUT /api/projects/:id', () => {
    it('should update an existing project', async () => {
      const updateData = {
        name: 'Updated Project Name',
        description: 'Updated description',
        target_keywords: ['updated', 'keywords'],
      };

      const response = await request(app)
        .put('/api/projects/project-1')
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe(updateData.name);
      expect(response.body.data.description).toBe(updateData.description);
      expect(response.body.data.target_keywords).toEqual(updateData.target_keywords);
    });

    it('should return 404 for non-existent project', async () => {
      const updateData = {
        name: 'Updated Project Name',
      };

      const response = await request(app)
        .put('/api/projects/non-existent')
        .send(updateData);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Project not found');
    });
  });

  describe('DELETE /api/projects/:id', () => {
    it('should delete an existing project', async () => {
      const response = await request(app).delete('/api/projects/project-1');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Project deleted successfully');
    });

    it('should return 404 for non-existent project', async () => {
      const response = await request(app).delete('/api/projects/non-existent');

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Project not found');
    });
  });

  describe('GET /api/projects/:id/metrics', () => {
    it('should return project metrics', async () => {
      const response = await request(app).get('/api/projects/project-1/metrics');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('contentGenerated');
      expect(response.body.data).toHaveProperty('keywordsTracked');
      expect(response.body.data).toHaveProperty('averageSeoScore');
      expect(response.body.data).toHaveProperty('totalWords');
      expect(response.body.data).toHaveProperty('lastUpdated');
    });

    it('should return 404 for non-existent project', async () => {
      const response = await request(app).get('/api/projects/non-existent/metrics');

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Project not found');
    });
  });
});