/**
 * Content Library Page
 * Manage all generated content with search, filter, and bulk operations
 */

'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import { AuthenticatedLayout, PageHeader, DashboardGrid } from '@/components/Layout/DashboardLayout'
import Card from '@/components/UI/Card'
import Button from '@/components/UI/Button'
import Input from '@/components/UI/Input'
import Badge from '@/components/UI/Badge'
import LoadingSpinner from '@/components/UI/LoadingSpinner'
import { useNotifications } from '@/components/Notifications'
import { contentService } from '@/lib/api'
import { ContentItem } from '@/lib/api/types'
import ContentListTable from '@/components/ContentLibrary/ContentListTable'
import ContentFilters from '@/components/ContentLibrary/ContentFilters'
import BulkActions from '@/components/ContentLibrary/BulkActions'
import ContentPreviewModal from '@/components/ContentLibrary/ContentPreviewModal'
import { 
  DocumentTextIcon, 
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowDownTrayIcon,
  TrashIcon,
  PlusIcon,
  FolderIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'

interface ContentFilters {
  search: string
  contentType: string
  status: string
  dateRange: string
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

interface ContentStats {
  total: number
  published: number
  draft: number
  avgSeoScore: number
}

export default function ContentLibraryPage() {
  const [content, setContent] = useState<ContentItem[]>([])
  const [selectedContent, setSelectedContent] = useState<Set<string>>(new Set())
  const [isLoading, setIsLoading] = useState(true)
  const [stats, setStats] = useState<ContentStats>({
    total: 0,
    published: 0,
    draft: 0,
    avgSeoScore: 0
  })
  const [filters, setFilters] = useState<ContentFilters>({
    search: '',
    contentType: 'all',
    status: 'all',
    dateRange: 'all',
    sortBy: 'created_at',
    sortOrder: 'desc'
  })
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [previewContent, setPreviewContent] = useState<ContentItem | null>(null)
  const [showFilters, setShowFilters] = useState(false)

  const { error: notifyError, success: notifySuccess, info: notifyInfo } = useNotifications()

  // Load content on mount and when filters change
  useEffect(() => {
    loadContent()
  }, [filters, currentPage])

  const loadContent = async () => {
    setIsLoading(true)
    try {
      const response = await contentService.getContentLibrary({
        page: currentPage,
        per_page: 10,
        search: filters.search,
        content_type: filters.contentType !== 'all' ? filters.contentType : undefined,
        status: filters.status !== 'all' ? filters.status : undefined,
        sort_by: filters.sortBy,
        sort_order: filters.sortOrder
      })

      if (response.success && response.data) {
        setContent(response.data.items)
        setTotalPages(response.data.total_pages)
        
        // Calculate stats
        const stats: ContentStats = {
          total: response.data.total,
          published: response.data.items.filter(item => item.status === 'published').length,
          draft: response.data.items.filter(item => item.status === 'draft').length,
          avgSeoScore: response.data.items.reduce((acc, item) => 
            acc + (item.seo_score || 0), 0) / response.data.items.length || 0
        }
        setStats(stats)
      }
    } catch (error) {
      console.error('Failed to load content:', error)
      notifyError('Failed to load content library')
    } finally {
      setIsLoading(false)
    }
  }

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (filters.search !== '') {
        loadContent()
      }
    }, 500)

    return () => clearTimeout(timer)
  }, [filters.search])

  const handleSelectAll = useCallback((checked: boolean) => {
    if (checked) {
      setSelectedContent(new Set(content.map(item => item.id)))
    } else {
      setSelectedContent(new Set())
    }
  }, [content])

  const handleSelectContent = useCallback((contentId: string, checked: boolean) => {
    const newSelected = new Set(selectedContent)
    if (checked) {
      newSelected.add(contentId)
    } else {
      newSelected.delete(contentId)
    }
    setSelectedContent(newSelected)
  }, [selectedContent])

  const handleBulkDelete = async () => {
    if (selectedContent.size === 0) return

    const confirmed = window.confirm(
      `Are you sure you want to delete ${selectedContent.size} items? This action cannot be undone.`
    )
    
    if (!confirmed) return

    try {
      const response = await contentService.bulkDeleteContent(Array.from(selectedContent))
      if (response.success) {
        notifySuccess(`Successfully deleted ${selectedContent.size} items`)
        setSelectedContent(new Set())
        loadContent()
      } else {
        throw new Error(response.error || 'Failed to delete content')
      }
    } catch (error) {
      notifyError('Failed to delete selected content')
    }
  }

  const handleBulkExport = async (format: 'json' | 'csv' | 'markdown') => {
    if (selectedContent.size === 0) return

    try {
      notifyInfo(`Exporting ${selectedContent.size} items...`)
      const response = await contentService.exportContent(
        Array.from(selectedContent), 
        format
      )
      
      if (response.success && response.data) {
        window.open(response.data.download_url, '_blank')
        notifySuccess('Export started successfully')
      } else {
        throw new Error('Export failed')
      }
    } catch (error) {
      notifyError('Failed to export content')
    }
  }

  const handlePreview = (item: ContentItem) => {
    setPreviewContent(item)
  }

  const handleEdit = (item: ContentItem) => {
    // Navigate to content editor
    window.location.href = `/content/editor?id=${item.id}`
  }

  const handleDuplicate = async (item: ContentItem) => {
    try {
      const response = await contentService.duplicateContent(item.id)
      if (response.success) {
        notifySuccess('Content duplicated successfully')
        loadContent()
      } else {
        throw new Error('Failed to duplicate content')
      }
    } catch (error) {
      notifyError('Failed to duplicate content')
    }
  }

  // Filter content based on search
  const filteredContent = useMemo(() => {
    if (!filters.search) return content
    
    const searchLower = filters.search.toLowerCase()
    return content.filter(item => 
      item.title.toLowerCase().includes(searchLower) ||
      item.keyword.toLowerCase().includes(searchLower) ||
      item.content.toLowerCase().includes(searchLower)
    )
  }, [content, filters.search])

  return (
    <AuthenticatedLayout>
      <PageHeader 
        title="Content Library"
        description="Manage and organize all your generated content"
        actions={
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="inline-flex items-center"
            >
              <FunnelIcon className="h-4 w-4 mr-2" />
              Filters
              {Object.values(filters).some(v => v !== 'all' && v !== '' && v !== 'created_at' && v !== 'desc') && (
                <Badge variant="primary" size="sm" className="ml-2">Active</Badge>
              )}
            </Button>
            <Button
              href="/content-generator"
              className="inline-flex items-center"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Generate New
            </Button>
          </div>
        }
      />

      <div className="space-y-6">
        {/* Stats Overview */}
        <DashboardGrid cols={4}>
          <Card className="p-6">
            <div className="flex items-center">
              <DocumentTextIcon className="h-8 w-8 text-blue-600 dark:text-blue-400 mr-3" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Content</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.total}</p>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center">
              <FolderIcon className="h-8 w-8 text-green-600 dark:text-green-400 mr-3" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Published</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.published}</p>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center">
              <DocumentTextIcon className="h-8 w-8 text-yellow-600 dark:text-yellow-400 mr-3" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Drafts</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.draft}</p>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <div className="flex items-center">
              <ChartBarIcon className="h-8 w-8 text-purple-600 dark:text-purple-400 mr-3" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Avg SEO Score</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {Math.round(stats.avgSeoScore)}%
                </p>
              </div>
            </div>
          </Card>
        </DashboardGrid>

        {/* Search Bar */}
        <Card className="p-4">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                type="text"
                placeholder="Search by title, keyword, or content..."
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                className="pl-10"
              />
            </div>
            <Badge variant="secondary">
              {filteredContent.length} results
            </Badge>
          </div>
        </Card>

        {/* Filters Panel */}
        {showFilters && (
          <ContentFilters
            filters={filters}
            onFilterChange={setFilters}
            onReset={() => setFilters({
              search: '',
              contentType: 'all',
              status: 'all',
              dateRange: 'all',
              sortBy: 'created_at',
              sortOrder: 'desc'
            })}
          />
        )}

        {/* Bulk Actions */}
        {selectedContent.size > 0 && (
          <BulkActions
            selectedCount={selectedContent.size}
            onDelete={handleBulkDelete}
            onExport={handleBulkExport}
            onClearSelection={() => setSelectedContent(new Set())}
          />
        )}

        {/* Content Table */}
        <Card className="p-0 overflow-hidden">
          {isLoading ? (
            <div className="p-8 text-center">
              <LoadingSpinner size="lg" />
              <p className="mt-4 text-gray-600 dark:text-gray-400">Loading content...</p>
            </div>
          ) : filteredContent.length === 0 ? (
            <div className="p-8 text-center">
              <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                No content found
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {filters.search ? 'Try adjusting your search or filters' : 'Start by generating your first content'}
              </p>
              <Button href="/content-generator">
                <PlusIcon className="h-4 w-4 mr-2" />
                Generate Content
              </Button>
            </div>
          ) : (
            <ContentListTable
              content={filteredContent}
              selectedContent={selectedContent}
              onSelectAll={handleSelectAll}
              onSelectContent={handleSelectContent}
              onPreview={handlePreview}
              onEdit={handleEdit}
              onDuplicate={handleDuplicate}
              sortBy={filters.sortBy}
              sortOrder={filters.sortOrder}
              onSort={(field) => {
                if (filters.sortBy === field) {
                  setFilters({
                    ...filters,
                    sortOrder: filters.sortOrder === 'asc' ? 'desc' : 'asc'
                  })
                } else {
                  setFilters({
                    ...filters,
                    sortBy: field,
                    sortOrder: 'desc'
                  })
                }
              }}
            />
          )}
        </Card>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Page {currentPage} of {totalPages}
            </p>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Preview Modal */}
      {previewContent && (
        <ContentPreviewModal
          content={previewContent}
          onClose={() => setPreviewContent(null)}
          onEdit={() => handleEdit(previewContent)}
        />
      )}
    </AuthenticatedLayout>
  )
}