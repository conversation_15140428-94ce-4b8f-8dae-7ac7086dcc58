/**
 * Authority Link Discovery Page
 * Enterprise SEO SAAS - Comprehensive authority link discovery and analysis
 */

'use client'

import { useState } from 'react'
import DashboardLayout from '@/components/Layout/DashboardLayout'
import AuthorityLinkAnalyzer from '@/components/AuthorityLinks/AuthorityLinkAnalyzer'
import { AuthorityLink } from '@/utils/authorityLinkDiscovery'
import {
  LinkIcon,
  AcademicCapIcon,
  BuildingOfficeIcon,
  GlobeAltIcon,
  ShieldCheckIcon,
  BookOpenIcon,
  NewspaperIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  ArrowDownTrayIcon,
  StarIcon,
  ClockIcon,
  BoltIcon
} from '@heroicons/react/24/outline'

export default function AuthorityLinksPage() {
  const [discoveredLinks, setDiscoveredLinks] = useState<AuthorityLink[]>([])
  const [analysisHistory, setAnalysisHistory] = useState<Array<{
    topic: string
    date: string
    linksFound: number
    avgAuthority: number
  }>>([])

  const handleLinksDiscovered = (links: AuthorityLink[]) => {
    setDiscoveredLinks(links)
    
    // Add to analysis history
    const newEntry = {
      topic: 'Latest Analysis',
      date: new Date().toISOString(),
      linksFound: links.length,
      avgAuthority: Math.round(links.reduce((sum, link) => sum + link.authorityScore, 0) / links.length)
    }
    setAnalysisHistory(prev => [newEntry, ...prev.slice(0, 4)])
  }

  const exportBulkAnalysis = () => {
    if (discoveredLinks.length === 0) {
      alert('No links to export. Please run an analysis first.')
      return
    }

    const exportData = {
      analysisDate: new Date().toISOString(),
      totalLinks: discoveredLinks.length,
      linksByType: discoveredLinks.reduce((acc, link) => {
        acc[link.sourceType] = (acc[link.sourceType] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      highAuthorityLinks: discoveredLinks.filter(link => link.authorityScore >= 90),
      topDomains: [...new Set(discoveredLinks.map(link => link.domain))].slice(0, 10),
      averageScores: {
        authority: Math.round(discoveredLinks.reduce((sum, link) => sum + link.authorityScore, 0) / discoveredLinks.length),
        relevance: Math.round(discoveredLinks.reduce((sum, link) => sum + link.relevanceScore, 0) / discoveredLinks.length * 100)
      },
      allLinks: discoveredLinks.map(link => ({
        url: link.url,
        title: link.title,
        domain: link.domain,
        authorityScore: link.authorityScore,
        relevanceScore: link.relevanceScore,
        sourceType: link.sourceType,
        anchor: link.anchor,
        description: link.description
      }))
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `authority-links-bulk-analysis-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  const linksBySourceType = discoveredLinks.reduce((acc, link) => {
    acc[link.sourceType] = (acc[link.sourceType] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const highAuthorityLinks = discoveredLinks.filter(link => link.authorityScore >= 90).length
  const averageAuthority = discoveredLinks.length > 0 
    ? Math.round(discoveredLinks.reduce((sum, link) => sum + link.authorityScore, 0) / discoveredLinks.length)
    : 0

  return (
    <DashboardLayout
      title="Authority Link Discovery"
      subtitle="Discover high-authority external links to enhance content credibility and E-A-T"
    >
      <div className="space-y-8">
        {/* Page Header */}
        <div className="bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg p-6">
          <div className="flex items-start gap-4">
            <div className="bg-purple-600 p-3 rounded-lg">
              <LinkIcon className="h-8 w-8 text-white" />
            </div>
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-purple-900 mb-2">Professional Authority Link Discovery</h1>
              <p className="text-purple-800 mb-4">
                Discover high-quality external sources from Wikipedia, academic institutions, government agencies, and industry leaders to enhance your content's E-A-T signals and search credibility.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div className="bg-white bg-opacity-70 rounded-lg p-3 text-center">
                  <BookOpenIcon className="h-6 w-6 text-purple-600 mx-auto mb-1" />
                  <div className="text-sm font-medium text-purple-900">Wikipedia</div>
                </div>
                <div className="bg-white bg-opacity-70 rounded-lg p-3 text-center">
                  <AcademicCapIcon className="h-6 w-6 text-purple-600 mx-auto mb-1" />
                  <div className="text-sm font-medium text-purple-900">Academic</div>
                </div>
                <div className="bg-white bg-opacity-70 rounded-lg p-3 text-center">
                  <BuildingOfficeIcon className="h-6 w-6 text-purple-600 mx-auto mb-1" />
                  <div className="text-sm font-medium text-purple-900">Government</div>
                </div>
                <div className="bg-white bg-opacity-70 rounded-lg p-3 text-center">
                  <NewspaperIcon className="h-6 w-6 text-purple-600 mx-auto mb-1" />
                  <div className="text-sm font-medium text-purple-900">News</div>
                </div>
                <div className="bg-white bg-opacity-70 rounded-lg p-3 text-center">
                  <ChartBarIcon className="h-6 w-6 text-purple-600 mx-auto mb-1" />
                  <div className="text-sm font-medium text-purple-900">Industry</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Statistics */}
        {discoveredLinks.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <LinkIcon className="h-8 w-8 text-blue-600" />
                <div>
                  <div className="text-2xl font-bold text-gray-900">{discoveredLinks.length}</div>
                  <div className="text-sm text-gray-500">Authority Links Found</div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <StarIcon className="h-8 w-8 text-yellow-600" />
                <div>
                  <div className="text-2xl font-bold text-gray-900">{highAuthorityLinks}</div>
                  <div className="text-sm text-gray-500">Premium Sources (90+)</div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <ShieldCheckIcon className="h-8 w-8 text-green-600" />
                <div>
                  <div className="text-2xl font-bold text-gray-900">{averageAuthority}</div>
                  <div className="text-sm text-gray-500">Avg Authority Score</div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <GlobeAltIcon className="h-8 w-8 text-purple-600" />
                <div>
                  <div className="text-2xl font-bold text-gray-900">{Object.keys(linksBySourceType).length}</div>
                  <div className="text-sm text-gray-500">Source Types</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Source Type Distribution */}
        {discoveredLinks.length > 0 && (
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-gray-900">Source Type Distribution</h3>
              <button
                onClick={exportBulkAnalysis}
                className="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors gap-2"
              >
                <ArrowDownTrayIcon className="h-4 w-4" />
                Export Full Analysis
              </button>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
              {Object.entries(linksBySourceType).map(([type, count]) => {
                const getTypeIcon = (sourceType: string) => {
                  switch (sourceType) {
                    case 'wikipedia': return BookOpenIcon
                    case 'academic': return AcademicCapIcon
                    case 'government': return BuildingOfficeIcon
                    case 'news': return NewspaperIcon
                    case 'industry': return ChartBarIcon
                    default: return LinkIcon
                  }
                }

                const getTypeColor = (sourceType: string) => {
                  switch (sourceType) {
                    case 'wikipedia': return 'text-blue-600 bg-blue-100'
                    case 'academic': return 'text-purple-600 bg-purple-100'
                    case 'government': return 'text-green-600 bg-green-100'
                    case 'news': return 'text-orange-600 bg-orange-100'
                    case 'industry': return 'text-indigo-600 bg-indigo-100'
                    default: return 'text-gray-600 bg-gray-100'
                  }
                }

                const Icon = getTypeIcon(type)
                return (
                  <div key={type} className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className={`w-12 h-12 rounded-lg mx-auto mb-3 flex items-center justify-center ${getTypeColor(type)}`}>
                      <Icon className="h-6 w-6" />
                    </div>
                    <div className="text-lg font-bold text-gray-900">{count}</div>
                    <div className="text-sm text-gray-600 capitalize">{type}</div>
                    <div className="text-xs text-gray-500">
                      {Math.round((count / discoveredLinks.length) * 100)}%
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {/* Analysis History */}
        {analysisHistory.length > 0 && (
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Analysis History</h3>
            <div className="space-y-3">
              {analysisHistory.map((entry, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <ClockIcon className="h-5 w-5 text-gray-500" />
                    <div>
                      <div className="font-medium text-gray-900">{entry.topic}</div>
                      <div className="text-sm text-gray-500">
                        {new Date(entry.date).toLocaleDateString()} at {new Date(entry.date).toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      {entry.linksFound} links • {entry.avgAuthority} avg authority
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Feature Information */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <BookOpenIcon className="h-6 w-6 text-blue-600" />
              <h3 className="font-medium text-gray-900">Wikipedia Integration</h3>
            </div>
            <p className="text-gray-600 text-sm mb-4">
              Automatically discover relevant Wikipedia articles and extract high-quality references to authoritative sources.
            </p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Real-time Wikipedia API integration</li>
              <li>• Article quality assessment</li>
              <li>• Reference extraction and validation</li>
              <li>• Multi-language support</li>
            </ul>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <ShieldCheckIcon className="h-6 w-6 text-green-600" />
              <h3 className="font-medium text-gray-900">Authority Assessment</h3>
            </div>
            <p className="text-gray-600 text-sm mb-4">
              Comprehensive domain authority scoring based on multiple trust signals and quality indicators.
            </p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Multi-factor authority scoring</li>
              <li>• Link validation and health checks</li>
              <li>• Trust signal analysis</li>
              <li>• Spam and quality detection</li>
            </ul>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <ChartBarIcon className="h-6 w-6 text-purple-600" />
              <h3 className="font-medium text-gray-900">Smart Discovery</h3>
            </div>
            <p className="text-gray-600 text-sm mb-4">
              Intelligent source discovery across academic, government, industry, and news domains with relevance scoring.
            </p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Contextual relevance analysis</li>
              <li>• Source type categorization</li>
              <li>• Batch link validation</li>
              <li>• Export and integration tools</li>
            </ul>
          </div>
        </div>

        {/* Main Analyzer Component */}
        <AuthorityLinkAnalyzer onLinksDiscovered={handleLinksDiscovered} />

        {/* Best Practices */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-6">
          <div className="flex items-start gap-3">
            <CheckCircleIcon className="h-6 w-6 text-green-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-green-900 mb-2">Authority Link Best Practices</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-green-800">
                <div>
                  <div className="font-medium mb-1">Quality Over Quantity:</div>
                  <ul className="space-y-1">
                    <li>• Prioritize high-authority sources (90+ scores)</li>
                    <li>• Ensure contextual relevance to your content</li>
                    <li>• Verify link health and accessibility</li>
                    <li>• Use descriptive, natural anchor text</li>
                  </ul>
                </div>
                <div>
                  <div className="font-medium mb-1">E-A-T Enhancement:</div>
                  <ul className="space-y-1">
                    <li>• Link to authoritative sources for claims</li>
                    <li>• Include government and academic references</li>
                    <li>• Reference recent and up-to-date sources</li>
                    <li>• Maintain a diverse link profile</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Implementation Guidelines */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start gap-3">
            <InformationCircleIcon className="h-6 w-6 text-blue-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-blue-900 mb-2">Implementation Guidelines</h3>
              <div className="text-sm text-blue-800 space-y-2">
                <p>
                  <strong>Strategic Placement:</strong> Place authority links contextually within your content where they 
                  support key claims or provide additional value to readers. Avoid forced or unnatural link placement.
                </p>
                <p>
                  <strong>Link Ratio:</strong> Maintain a balanced ratio of internal to external links. Generally, 
                  2-5 high-quality external authority links per 1000 words of content is optimal for most topics.
                </p>
                <p>
                  <strong>Regular Auditing:</strong> Periodically validate authority links to ensure they remain active 
                  and relevant. Broken or outdated authority links can negatively impact your content's credibility.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Important Disclaimers */}
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-6">
          <div className="flex items-start gap-3">
            <ExclamationTriangleIcon className="h-6 w-6 text-amber-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-amber-900 mb-2">Important Considerations</h3>
              <div className="text-sm text-amber-800 space-y-2">
                <p>
                  <strong>API Limitations:</strong> This tool respects Wikipedia's API rate limits and terms of service. 
                  Large-scale analysis may require longer processing times to maintain compliance.
                </p>
                <p>
                  <strong>Link Validation:</strong> While we perform comprehensive link validation, external sites can 
                  change without notice. Always verify critical links before publishing content.
                </p>
                <p>
                  <strong>Context Matters:</strong> Authority scores are general indicators. The relevance and appropriateness 
                  of a source depend heavily on your specific content context and target audience.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}