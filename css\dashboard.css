/* 
 * SEO SAAS Dashboard Styling
 * Enterprise dashboard layout and navigation components
 * Professional data visualization and widgets
 */

/* ===========================
   DASHBOARD LAYOUT FOUNDATION
   =========================== */

.dashboard-layout {
  display: flex;
  min-height: 100vh;
  background-color: var(--bg-secondary);
  font-family: var(--font-primary);
}

.dashboard-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* ===========================
   PROFESSIONAL SIDEBAR NAVIGATION
   =========================== */

.sidebar {
  width: 280px;
  background-color: white;
  border-right: 1px solid var(--gray-200);
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.3s ease-in-out;
  z-index: 30;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);
}

.sidebar.collapsed {
  width: 64px;
}

.sidebar-header {
  padding: var(--space-6) var(--space-4);
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  min-height: 72px;
}

.sidebar-logo {
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

.sidebar-brand {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  transition: opacity 0.3s ease-in-out;
}

.sidebar.collapsed .sidebar-brand {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

/* Sidebar toggle button */
.sidebar-toggle {
  position: absolute;
  top: var(--space-6);
  right: -12px;
  width: 24px;
  height: 24px;
  background-color: white;
  border: 1px solid var(--gray-200);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  z-index: 10;
}

.sidebar-toggle:hover {
  background-color: var(--gray-50);
  border-color: var(--gray-300);
}

/* Navigation sections */
.sidebar-nav {
  flex: 1;
  padding: var(--space-4);
  overflow-y: auto;
}

.nav-section {
  margin-bottom: var(--space-6);
}

.nav-section-title {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--gray-500);
  margin-bottom: var(--space-3);
  padding: 0 var(--space-3);
  transition: opacity 0.3s ease-in-out;
}

.sidebar.collapsed .nav-section-title {
  opacity: 0;
  height: 0;
  margin: 0;
  overflow: hidden;
}

/* Navigation items */
.nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-3);
  color: var(--gray-700);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all 0.2s ease-in-out;
  font-weight: 500;
  font-size: 0.875rem;
  margin-bottom: var(--space-1);
  position: relative;
  min-height: 44px;
}

.nav-item:hover {
  background-color: var(--gray-100);
  color: var(--gray-900);
  transform: translateX(2px);
}

.nav-item.active {
  background-color: var(--primary-100);
  color: var(--primary-700);
  font-weight: 600;
}

.nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--primary-600);
  border-radius: 0 2px 2px 0;
}

.nav-item-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-item-text {
  transition: opacity 0.3s ease-in-out;
}

.sidebar.collapsed .nav-item-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.sidebar.collapsed .nav-item {
  justify-content: center;
  padding: var(--space-3) var(--space-2);
}

/* Navigation badges */
.nav-badge {
  background-color: var(--error-500);
  color: white;
  font-size: 0.625rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-full);
  margin-left: auto;
  transition: opacity 0.3s ease-in-out;
}

.sidebar.collapsed .nav-badge {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

/* ===========================
   TOP NAVIGATION BAR
   =========================== */

.top-nav {
  background-color: white;
  border-bottom: 1px solid var(--gray-200);
  padding: 0 var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 72px;
  position: sticky;
  top: 0;
  z-index: 20;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.nav-right {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.page-subtitle {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
  margin-top: var(--space-1);
}

/* Search bar */
.nav-search {
  position: relative;
  width: 320px;
}

.nav-search-input {
  width: 100%;
  padding: var(--space-2) var(--space-3) var(--space-2) var(--space-10);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  transition: all 0.2s ease-in-out;
}

.nav-search-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.nav-search-icon {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
  width: 16px;
  height: 16px;
}

/* User menu */
.user-menu {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid var(--gray-200);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.user-avatar:hover {
  border-color: var(--primary-500);
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.user-name {
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--text-primary);
}

.user-role {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* Notifications */
.notifications-btn {
  position: relative;
  background: none;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-lg);
  padding: var(--space-2);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notifications-btn:hover {
  background-color: var(--gray-50);
  border-color: var(--gray-400);
}

.notification-dot {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 8px;
  height: 8px;
  background-color: var(--error-500);
  border-radius: 50%;
  border: 2px solid white;
}

/* ===========================
   DASHBOARD CONTENT AREAS
   =========================== */

.dashboard-content {
  flex: 1;
  padding: var(--space-4) var(--space-6);
  overflow-y: auto;
  background-color: var(--bg-secondary);
}

@media (min-width: 1280px) {
  .dashboard-content {
    padding: var(--space-6) var(--space-8);
  }
}

@media (min-width: 1536px) {
  .dashboard-content {
    padding: var(--space-6) var(--space-12);
  }
}

.content-section {
  margin-bottom: var(--space-8);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-6);
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.section-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: var(--space-1) 0 0 0;
}

/* ===========================
   METRIC CARDS & WIDGETS
   =========================== */

.metrics-section {
  margin-bottom: var(--space-8);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
}

@media (min-width: 1280px) {
  .metrics-grid {
    gap: var(--space-6);
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (min-width: 1536px) {
  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

.metric-card {
  background: white;
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
  padding: var(--space-6);
  transition: all 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
}

.metric-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
  border-color: var(--gray-300);
}

.metric-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.metric-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-600);
  margin: 0;
}

.metric-icon {
  width: 24px;
  height: 24px;
  color: var(--gray-400);
  flex-shrink: 0;
}

.metric-value {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--gray-900);
  line-height: 1;
  margin-bottom: var(--space-3);
}

.metric-change {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-size: 0.75rem;
  font-weight: 500;
}

.metric-change.positive {
  color: var(--success-600);
}

.metric-change.negative {
  color: var(--error-600);
}

.metric-change.neutral {
  color: var(--gray-500);
}

.metric-change-icon {
  width: 12px;
  height: 12px;
}

/* Metric card variants */
.metric-card.primary {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  color: white;
  border: none;
}

.metric-card.primary .metric-title,
.metric-card.primary .metric-value {
  color: white;
}

.metric-card.primary .metric-icon {
  color: rgba(255, 255, 255, 0.8);
}

/* ===========================
   DATA TABLES
   =========================== */

.data-table-container {
  background: white;
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
  overflow: hidden;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background-color: var(--gray-50);
  color: var(--gray-700);
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: var(--space-4);
  text-align: left;
  border-bottom: 1px solid var(--gray-200);
  white-space: nowrap;
}

.data-table td {
  padding: var(--space-4);
  border-bottom: 1px solid var(--gray-100);
  vertical-align: middle;
}

.data-table tr:hover {
  background-color: var(--gray-50);
}

.data-table tr:last-child td {
  border-bottom: none;
}

/* Table cell content */
.table-cell-content {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.table-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  flex-shrink: 0;
}

.table-text {
  display: flex;
  flex-direction: column;
}

.table-primary {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.table-secondary {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* ===========================
   ACTION BUTTONS & CONTROLS
   =========================== */

.action-buttons {
  display: flex;
  gap: var(--space-2);
  align-items: center;
}

.btn-action {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-300);
  background-color: white;
  color: var(--gray-600);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-action:hover {
  background-color: var(--gray-50);
  border-color: var(--gray-400);
  color: var(--gray-700);
}

.btn-action.primary {
  background-color: var(--primary-100);
  border-color: var(--primary-200);
  color: var(--primary-700);
}

.btn-action.primary:hover {
  background-color: var(--primary-200);
  border-color: var(--primary-300);
}

/* Quick actions */
.quick-actions {
  display: flex;
  gap: var(--space-3);
  margin-bottom: var(--space-6);
}

.quick-action-card {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  flex: 1;
  text-align: center;
}

.quick-action-card:hover {
  border-color: var(--primary-500);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.quick-action-icon {
  width: 32px;
  height: 32px;
  margin: 0 auto var(--space-2);
  color: var(--primary-600);
}

.quick-action-title {
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.quick-action-description {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* ===========================
   RESPONSIVE DASHBOARD
   =========================== */

@media (max-width: 1024px) {
  .sidebar {
    position: fixed;
    left: -280px;
    top: 0;
    height: 100vh;
    z-index: 50;
  }
  
  .sidebar.open {
    left: 0;
  }
  
  .dashboard-main {
    width: 100%;
  }
  
  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
  
  .nav-search {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .top-nav {
    padding: 0 var(--space-4);
    flex-wrap: wrap;
    min-height: auto;
    padding-top: var(--space-3);
    padding-bottom: var(--space-3);
  }
  
  .nav-left {
    flex: 1;
  }
  
  .nav-search {
    width: 100%;
    order: 3;
    margin-top: var(--space-3);
  }
  
  .dashboard-content {
    padding: var(--space-4);
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    flex-direction: column;
  }
  
  .user-info {
    display: none;
  }
  
  .data-table-container {
    overflow-x: auto;
  }
  
  .data-table {
    min-width: 600px;
  }
}

/* Mobile sidebar overlay */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 40;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease-in-out;
}

.sidebar-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Dashboard loading states */
.dashboard-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.metric-skeleton {
  background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-md);
}

.metric-skeleton.title {
  height: 1rem;
  width: 60%;
  margin-bottom: var(--space-2);
}

.metric-skeleton.value {
  height: 2rem;
  width: 40%;
  margin-bottom: var(--space-2);
}

.metric-skeleton.change {
  height: 0.75rem;
  width: 50%;
}