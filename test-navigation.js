#!/usr/bin/env node

import http from 'http';
import https from 'https';

// Define all routes to test
const routes = [
  { path: '/', name: 'Homepage' },
  { path: '/auth/login', name: 'Login Page' },
  { path: '/auth/register', name: '<PERSON> Page' },
  { path: '/dashboard', name: 'Dashboard' },
  { path: '/dashboard/content-creator', name: 'Content Generator' },
  { path: '/content-generator', name: 'Content Generator (Alt)' },
  { path: '/projects', name: 'Projects' },
  { path: '/analytics', name: 'Analytics' },
  { path: '/activity', name: 'Activity' },
  { path: '/profile', name: 'Profile' },
  { path: '/settings', name: '<PERSON>ting<PERSON>' },
  { path: '/pricing', name: 'Pricing' },
  { path: '/demo', name: 'Demo' },
  { path: '/content/create', name: 'Create Content' },
  { path: '/content/editor', name: 'Content Editor' },
  { path: '/authority-links', name: 'Authority Links' },
  { path: '/sitemap-analysis', name: 'Sitemap Analysis' },
  { path: '/dashboard/live', name: 'Live Dashboard' },
  { path: '/dashboard-simple', name: 'Simple Dashboard' }
];

// Function to test a single route
function testRoute(route) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: route.path,
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        const redirectLocation = res.headers.location;
        const contentType = res.headers['content-type'];
        const hasContent = body.length > 0;
        
        resolve({
          name: route.name,
          path: route.path,
          status: res.statusCode,
          redirectTo: redirectLocation,
          contentType: contentType,
          hasContent: hasContent,
          title: extractTitle(body)
        });
      });
    });

    req.on('error', (err) => {
      resolve({
        name: route.name,
        path: route.path,
        status: 'ERROR',
        error: err.message
      });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({
        name: route.name,
        path: route.path,
        status: 'TIMEOUT',
        error: 'Request timed out'
      });
    });

    req.end();
  });
}

// Extract title from HTML
function extractTitle(html) {
  const match = html.match(/<title>(.*?)<\/title>/i);
  return match ? match[1].trim() : 'No title found';
}

// Main test function
async function runTests() {
  console.log('Testing SEO SAAS Application Navigation\n');
  console.log('=' .repeat(80));
  
  const results = [];
  
  for (const route of routes) {
    const result = await testRoute(route);
    results.push(result);
    
    // Print result
    const statusColor = result.status === 200 ? '\x1b[32m' : 
                       result.status === 301 || result.status === 302 || result.status === 307 ? '\x1b[33m' : 
                       '\x1b[31m';
    const reset = '\x1b[0m';
    
    console.log(`${statusColor}[${result.status}]${reset} ${result.name.padEnd(25)} ${result.path}`);
    
    if (result.redirectTo) {
      console.log(`     → Redirects to: ${result.redirectTo}`);
    }
    if (result.error) {
      console.log(`     ✗ Error: ${result.error}`);
    }
    if (result.status === 200 && result.title !== 'No title found') {
      console.log(`     ✓ Title: ${result.title}`);
    }
  }
  
  console.log('\n' + '=' .repeat(80));
  console.log('Summary:');
  
  const successCount = results.filter(r => r.status === 200).length;
  const redirectCount = results.filter(r => [301, 302, 307].includes(r.status)).length;
  const errorCount = results.filter(r => r.status >= 400 || r.status === 'ERROR' || r.status === 'TIMEOUT').length;
  
  console.log(`✓ Successful (200): ${successCount}`);
  console.log(`↻ Redirects (3xx): ${redirectCount}`);
  console.log(`✗ Errors (4xx/5xx): ${errorCount}`);
  
  // Check for broken links
  console.log('\n' + '=' .repeat(80));
  console.log('Detailed Analysis:\n');
  
  const brokenLinks = results.filter(r => r.status >= 400 || r.status === 'ERROR' || r.status === 'TIMEOUT');
  if (brokenLinks.length > 0) {
    console.log('🔴 Broken Links Found:');
    brokenLinks.forEach(link => {
      console.log(`   - ${link.path} (${link.name}): ${link.status} ${link.error || ''}`);
    });
  } else {
    console.log('✅ No broken links found!');
  }
  
  // Check redirects
  const redirects = results.filter(r => [301, 302, 307].includes(r.status));
  if (redirects.length > 0) {
    console.log('\n🔄 Redirects:');
    redirects.forEach(redirect => {
      console.log(`   - ${redirect.path} → ${redirect.redirectTo}`);
    });
  }
  
  // Check auth-protected routes
  const authRoutes = results.filter(r => 
    r.path.includes('/dashboard') || 
    r.path.includes('/projects') || 
    r.path.includes('/profile') ||
    r.path.includes('/settings') ||
    r.path.includes('/content/') ||
    r.path.includes('/analytics') ||
    r.path.includes('/activity')
  );
  
  console.log('\n🔐 Auth-Protected Routes:');
  authRoutes.forEach(route => {
    if (route.status === 307 && route.redirectTo && route.redirectTo.includes('/auth/login')) {
      console.log(`   ✓ ${route.path} - Properly redirects to login`);
    } else if (route.status === 200) {
      console.log(`   ⚠️  ${route.path} - Accessible without auth (Status: ${route.status})`);
    } else {
      console.log(`   ? ${route.path} - Status: ${route.status}`);
    }
  });
}

// Run the tests
runTests().catch(console.error);