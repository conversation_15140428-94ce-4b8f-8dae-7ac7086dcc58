/**
 * ProjectHeader Component
 * Enterprise SEO SAAS - Project header with actions and navigation
 */

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Project } from '@/types/project'
import { formatProjectStatus, getIndustryInfo } from '@/utils/projectHelpers'
import {
  ChevronLeftIcon,
  PencilIcon,
  TrashIcon,
  ArchiveBoxIcon,
  DocumentArrowDownIcon,
  ShareIcon,
  EllipsisVerticalIcon,
  CalendarIcon,
  GlobeAltIcon,
  MapPinIcon
} from '@heroicons/react/24/outline'

interface ProjectHeaderProps {
  project: Project
  onProjectUpdate: (project: Project) => void
  onProjectDelete: () => void
}

export default function ProjectHeader({ project, onProjectUpdate, onProjectDelete }: ProjectHeaderProps) {
  const router = useRouter()
  const [showDropdown, setShowDropdown] = useState(false)
  
  const statusInfo = formatProjectStatus(project.status)
  const industryInfo = getIndustryInfo(project.industry)

  const handleEdit = () => {
    // Open edit modal or navigate to edit page
    setShowDropdown(false)
  }

  const handleArchive = async () => {
    try {
      const updatedProject = { ...project, status: 'archived' as const, updatedAt: new Date().toISOString() }
      onProjectUpdate(updatedProject)
      setShowDropdown(false)
    } catch (error) {
      console.error('Error archiving project:', error)
    }
  }

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
      onProjectDelete()
    }
    setShowDropdown(false)
  }

  const handleExport = () => {
    // Implement project export functionality
    setShowDropdown(false)
  }

  const handleShare = () => {
    // Implement project sharing functionality
    setShowDropdown(false)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      {/* Breadcrumb Navigation */}
      <div className="flex items-center gap-2 text-sm text-gray-500 mb-4">
        <Link 
          href="/projects" 
          className="hover:text-gray-700 flex items-center gap-1"
        >
          <ChevronLeftIcon className="h-4 w-4" />
          Projects
        </Link>
        <span>/</span>
        <span className="text-gray-900 font-medium truncate">{project.name}</span>
      </div>

      {/* Main Header Content */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        {/* Project Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-3 mb-2">
            <span className="text-2xl flex-shrink-0">{industryInfo.icon}</span>
            <h1 className="text-2xl font-bold text-gray-900 truncate">{project.name}</h1>
            <span className={`
              inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium
              ${statusInfo.color} ${statusInfo.bgColor}
            `}>
              {statusInfo.label}
            </span>
          </div>

          {project.description && (
            <p className="text-gray-600 mb-3 line-clamp-2">{project.description}</p>
          )}

          {/* Project Metadata */}
          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
            <div className="flex items-center gap-1">
              <span className={`w-2 h-2 rounded-full ${industryInfo.color.includes('blue') ? 'bg-blue-500' : 
                industryInfo.color.includes('red') ? 'bg-red-500' :
                industryInfo.color.includes('green') ? 'bg-green-500' :
                industryInfo.color.includes('purple') ? 'bg-purple-500' :
                industryInfo.color.includes('orange') ? 'bg-orange-500' :
                industryInfo.color.includes('yellow') ? 'bg-yellow-500' :
                industryInfo.color.includes('pink') ? 'bg-pink-500' :
                industryInfo.color.includes('indigo') ? 'bg-indigo-500' :
                industryInfo.color.includes('sky') ? 'bg-sky-500' :
                industryInfo.color.includes('emerald') ? 'bg-emerald-500' :
                industryInfo.color.includes('amber') ? 'bg-amber-500' :
                industryInfo.color.includes('slate') ? 'bg-slate-500' :
                'bg-gray-500'}`} />
              <span>{industryInfo.label}</span>
            </div>

            {project.website && (
              <div className="flex items-center gap-1">
                <GlobeAltIcon className="h-4 w-4" />
                <a 
                  href={project.website} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="hover:text-blue-600 truncate max-w-40"
                >
                  {new URL(project.website).hostname}
                </a>
              </div>
            )}

            {project.targetLocation && (
              <div className="flex items-center gap-1">
                <MapPinIcon className="h-4 w-4" />
                <span>{project.targetLocation}</span>
              </div>
            )}

            <div className="flex items-center gap-1">
              <CalendarIcon className="h-4 w-4" />
              <span>Updated {formatDate(project.updatedAt)}</span>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-3 flex-shrink-0">
          {/* Primary Actions */}
          <button
            onClick={handleEdit}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <PencilIcon className="h-4 w-4 mr-2" />
            Edit
          </button>

          <button
            onClick={handleExport}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
            Export
          </button>

          {/* Dropdown Menu */}
          <div className="relative">
            <button
              onClick={() => setShowDropdown(!showDropdown)}
              className="inline-flex items-center p-2 border border-gray-300 rounded-lg bg-white text-gray-500 hover:text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <EllipsisVerticalIcon className="h-5 w-5" />
            </button>

            {showDropdown && (
              <>
                {/* Backdrop */}
                <div 
                  className="fixed inset-0 z-10" 
                  onClick={() => setShowDropdown(false)}
                />
                
                {/* Dropdown Menu */}
                <div className="absolute right-0 top-full mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-20">
                  <button
                    onClick={handleShare}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    <ShareIcon className="h-4 w-4 mr-3" />
                    Share Project
                  </button>
                  
                  <hr className="my-1" />
                  
                  <button
                    onClick={handleArchive}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                    disabled={project.status === 'archived'}
                  >
                    <ArchiveBoxIcon className="h-4 w-4 mr-3" />
                    Archive Project
                  </button>
                  
                  <button
                    onClick={handleDelete}
                    className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                  >
                    <TrashIcon className="h-4 w-4 mr-3" />
                    Delete Project
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-gray-50 rounded-lg p-3 text-center">
          <div className="text-lg font-semibold text-gray-900">{project.keywords.length}</div>
          <div className="text-xs text-gray-500">Keywords</div>
        </div>
        <div className="bg-gray-50 rounded-lg p-3 text-center">
          <div className="text-lg font-semibold text-gray-900">{project.competitors.length}</div>
          <div className="text-xs text-gray-500">Competitors</div>
        </div>
        <div className="bg-gray-50 rounded-lg p-3 text-center">
          <div className="text-lg font-semibold text-gray-900">{project.metrics.totalContent}</div>
          <div className="text-xs text-gray-500">Content</div>
        </div>
        <div className="bg-gray-50 rounded-lg p-3 text-center">
          <div className="text-lg font-semibold text-gray-900">{project.metrics.averageSeoScore.toFixed(1)}</div>
          <div className="text-xs text-gray-500">SEO Score</div>
        </div>
      </div>
    </div>
  )
}