# 🔄 TODO-IMPLEMENTATION SYNCHRONIZATION SYSTEM
**Custom Prompt Templates for Project Alignment**

---

## 📋 SYSTEM OVERVIEW

This synchronization system ensures that:
1. **Every todo aligns** with the master implementation plan
2. **Progress tracking** remains accurate across all documentation
3. **Priorities propagate** correctly from project level to task level
4. **Gaps are identified** between planned work and actual todos

---

## 🎯 MASTER-TO-TODO SYNC PROMPT

### Purpose: Extract implementation goals and generate aligned todos

```markdown
# IMPLEMENTATION-TO-TODO SYNC PROMPT

## CONTEXT
You are synchronizing the master implementation document with current todo lists to ensure all project goals are represented as actionable tasks.

## INSTRUCTIONS
1. Read the IMPLEMENTATION_CONSOLIDATED.md file completely
2. Identify all pending/in-progress items from the implementation plan
3. Extract specific actionable tasks from each implementation phase
4. Create todo items that directly map to implementation goals
5. Ensure todo priorities match implementation criticality levels

## ANALYSIS FRAMEWORK
For each implementation item, determine:
- **Current Status**: Pending/In-Progress/Blocked/Complete
- **Priority Level**: Critical/High/Medium/Low (map from implementation)
- **Dependencies**: What must complete before this task
- **Time Estimate**: Based on implementation complexity
- **Actionable Steps**: Break down into specific todo items

## TODO GENERATION RULES
1. **Map implementation phases to todo categories**
2. **Preserve priority hierarchy** from implementation plan
3. **Break large goals into smaller tasks** (max 2-3 hours per todo)
4. **Include validation criteria** for each todo
5. **Maintain traceability** back to implementation goals

## OUTPUT FORMAT
Generate todos using the TodoWrite tool with:
- Content: Specific, actionable task description
- Status: pending/in_progress/completed
- Priority: high/medium/low (mapped from implementation)
- ID: Sequential numbering

## VALIDATION CHECKLIST
- [ ] All pending implementation items have corresponding todos
- [ ] Todo priorities align with implementation criticality
- [ ] No duplicate todos for the same implementation goal
- [ ] Dependencies are properly reflected in todo ordering
- [ ] Time estimates are realistic for each todo item
```

---

## 📊 TODO-TO-MASTER UPDATE PROMPT

### Purpose: Update implementation progress based on completed todos

```markdown
# TODO-TO-IMPLEMENTATION UPDATE PROMPT

## CONTEXT
You are updating the master implementation document based on completed todos to maintain accurate progress tracking.

## INSTRUCTIONS
1. Review all completed todos since last sync
2. Map completed todos to their corresponding implementation items
3. Update implementation status and progress percentages
4. Identify newly unlocked tasks due to dependency completion
5. Recalculate overall project completion percentage

## UPDATE FRAMEWORK
For each completed todo:
- **Identify corresponding implementation item**
- **Update implementation status** (if fully complete)
- **Adjust progress percentage** based on todo completion
- **Check for dependency resolution** (what new tasks are now possible)
- **Update "Last Verified" timestamps**

## PROGRESS CALCULATION RULES
1. **Component Progress** = (Completed Tasks / Total Tasks) × 100
2. **Phase Progress** = Average of all component progress in phase
3. **Overall Progress** = Weighted average of all phases
4. **Update completion dates** for newly finished items

## VALIDATION REQUIREMENTS
- [ ] All completed todos are reflected in implementation status
- [ ] Progress percentages are mathematically accurate
- [ ] Dependencies are properly updated when prerequisites complete
- [ ] "Last Verified" timestamps are current
- [ ] Overall completion percentage is recalculated correctly

## OUTPUT ACTIONS
1. Update IMPLEMENTATION_CONSOLIDATED.md with new progress
2. Identify any implementation items that are now 100% complete
3. Flag any discrepancies between todos and implementation goals
4. Generate next-priority todos for newly unlocked tasks
```

---

## 🔍 GAP ANALYSIS PROMPT

### Purpose: Identify misalignments between master plan and current todos

```markdown
# IMPLEMENTATION-TODO GAP ANALYSIS PROMPT

## CONTEXT
You are performing a comprehensive gap analysis to identify disconnects between the master implementation plan and current todo activities.

## ANALYSIS DIMENSIONS

### 1. MISSING TODO COVERAGE
Identify implementation items that lack corresponding todos:
- **Critical tasks without todos** - Implementation items marked as critical/high priority but no active todos
- **Orphaned implementation goals** - Planned features with no execution path
- **Dependency bottlenecks** - Blocked items that should have preparatory todos

### 2. TODO-IMPLEMENTATION MISALIGNMENT
Find todos that don't map to implementation goals:
- **Orphaned todos** - Tasks not connected to any implementation objective
- **Priority mismatches** - High-priority todos for low-priority implementation items
- **Scope creep todos** - Tasks that exceed planned implementation scope

### 3. PROGRESS INCONSISTENCIES
Detect progress tracking discrepancies:
- **Completion status conflicts** - Todos marked complete but implementation shows pending
- **Progress percentage errors** - Mathematical inconsistencies in completion calculations
- **Timeline violations** - Completed todos for incomplete dependency prerequisites

## GAP IDENTIFICATION FRAMEWORK

### Missing Coverage Analysis:
```javascript
for each implementation_item in IMPLEMENTATION_CONSOLIDATED.md:
  if implementation_item.status == "pending" or "in_progress":
    find corresponding_todos = search todos for implementation_item
    if corresponding_todos.length == 0:
      flag as MISSING_TODO_COVERAGE
    if implementation_item.priority == "critical" and corresponding_todos.priority != "high":
      flag as PRIORITY_MISMATCH
```

### Orphaned Todo Analysis:
```javascript
for each todo in current_todo_list:
  find implementation_mapping = search IMPLEMENTATION_CONSOLIDATED.md for todo.content
  if implementation_mapping == null:
    flag as ORPHANED_TODO
  if todo.priority > implementation_mapping.priority:
    flag as PRIORITY_ESCALATION
```

## OUTPUT FORMAT

### Gap Analysis Report:
1. **CRITICAL GAPS** - Must address immediately
2. **HIGH PRIORITY GAPS** - Address this week
3. **MEDIUM PRIORITY GAPS** - Address next week
4. **RECOMMENDATIONS** - Specific actions to resolve gaps

### Recommended Actions:
- **Todos to create** - For uncovered implementation items
- **Todos to modify** - Priority or scope adjustments needed
- **Todos to remove** - Orphaned or out-of-scope tasks
- **Implementation updates** - Documentation corrections needed
```

---

## ⚖️ PRIORITY ALIGNMENT PROMPT

### Purpose: Ensure todo priorities match implementation criticality

```markdown
# PRIORITY ALIGNMENT SYNCHRONIZATION PROMPT

## CONTEXT
You are ensuring that todo task priorities correctly reflect the criticality levels defined in the master implementation plan.

## PRIORITY MAPPING SYSTEM

### Implementation → Todo Priority Translation:
```javascript
Implementation Priority → Todo Priority
"Critical" → "high"
"High" → "high" 
"Medium" → "medium"
"Low" → "low"
"Blocked" → "pending" (until dependencies resolve)
```

### Priority Validation Rules:
1. **Critical implementation items** must have at least one "high" priority todo
2. **Dependency chains** must respect priority inheritance
3. **Timeline constraints** should escalate priority when deadlines approach
4. **Resource availability** may temporarily adjust priorities

## ALIGNMENT ANALYSIS

### Priority Audit Process:
1. **Map each todo to implementation item**
2. **Compare current priority vs expected priority**
3. **Identify priority inflation** (todos higher than implementation item)
4. **Identify priority deflation** (todos lower than implementation item)
5. **Check dependency-based priority inheritance**

### Dependency Priority Rules:
```javascript
if implementation_item.dependencies.includes(blocking_item):
  if blocking_item.status != "complete":
    blocking_item.priority = max(blocking_item.priority, implementation_item.priority)
```

## PRIORITY CORRECTION ACTIONS

### Automatic Corrections:
- **Escalate todo priority** when implementation item is critical
- **Inherit priority from dependents** when todo blocks critical items
- **Reduce priority** for todos without implementation backing

### Manual Review Required:
- **Cross-phase dependencies** - May need project manager input
- **Resource conflicts** - Multiple high-priority items competing
- **Timeline adjustments** - External deadline changes

## VALIDATION OUTPUT
Generate a priority alignment report showing:
1. **Correctly aligned todos** - Priority matches implementation
2. **Priority escalations needed** - Todos that should be higher priority
3. **Priority reductions needed** - Todos that should be lower priority
4. **Dependency-based adjustments** - Priority changes due to blocking relationships
```

---

## 🔄 CONTINUOUS SYNC WORKFLOW

### Daily Sync Process:
1. **Run Gap Analysis** - Identify misalignments
2. **Update Progress** - Reflect completed todos in implementation
3. **Priority Alignment** - Ensure priorities match current needs
4. **Generate New Todos** - For newly unlocked implementation items

### Weekly Deep Sync:
1. **Full Implementation Review** - Comprehensive status update
2. **Progress Recalculation** - Mathematical verification of percentages
3. **Milestone Assessment** - Check if major milestones achieved
4. **Timeline Adjustment** - Update estimates based on actual progress

### Monthly Master Sync:
1. **Implementation Document Update** - Major revisions as needed
2. **Scope Validation** - Ensure todos align with current project scope
3. **Priority Reassessment** - Adjust priorities based on business changes
4. **Process Improvement** - Optimize sync procedures based on learnings

---

## 📝 SYNC VALIDATION CHECKLIST

### Before Each Sync Session:
- [ ] Read current IMPLEMENTATION_CONSOLIDATED.md completely
- [ ] Review all pending/in_progress/completed todos
- [ ] Identify any new business requirements or scope changes
- [ ] Check for external dependency updates

### After Each Sync Session:
- [ ] All implementation items have corresponding todos or marked complete
- [ ] Todo priorities align with implementation criticality
- [ ] Progress percentages are mathematically accurate
- [ ] No orphaned todos exist without implementation backing
- [ ] Dependencies are properly reflected in todo sequencing
- [ ] "Last Verified" timestamps are updated appropriately

### Quality Assurance:
- [ ] Spot-check 3-5 todo items for proper implementation mapping
- [ ] Verify critical implementation items have active high-priority todos
- [ ] Confirm completed todos resulted in implementation progress updates
- [ ] Validate overall completion percentage calculation accuracy

---

**This synchronization system ensures perfect alignment between high-level project goals and day-to-day task execution, maintaining consistency and accuracy across all project documentation.**