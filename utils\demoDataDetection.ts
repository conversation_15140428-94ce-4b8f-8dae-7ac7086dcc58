/**
 * Demo Data Detection Utility
 * Enterprise SEO SAAS - Comprehensive validation to reject demo/placeholder content
 */

export interface ValidationResult {
  isValid: boolean
  isDemo: boolean
  errors: string[]
  warnings: string[]
  field?: string
}

export interface ValidationOptions {
  strict?: boolean // Enable strict mode for enhanced detection
  allowTestEnvironment?: boolean // Allow demo data in test environment
  customPatterns?: RegExp[] // Additional patterns to check
  field?: string // Field name for better error messages
}

export class DemoDataValidator {
  private static readonly DEMO_PATTERNS = [
    // Basic demo patterns
    /example/i,
    /demo/i,
    /test(?!ing|ed|s\b)/i, // Match "test" but not "testing", "tested", "tests"
    /sample/i,
    /placeholder/i,
    /lorem ipsum/i,
    /dummy/i,
    /mock/i,
    /fake/i,
    /template/i,
    
    // Keyword-specific patterns
    /your keyword/i,
    /insert keyword/i,
    /keyword here/i,
    /add keyword/i,
    /replace this/i,
    /enter keyword/i,
    /target keyword/i,
    
    // URL patterns
    /example\.com/i,
    /test\.com/i,
    /demo\.com/i,
    /sample\.com/i,
    /placeholder\.com/i,
    /yoursite\.com/i,
    /mysite\.com/i,
    
    // Business/project patterns
    /acme corp/i,
    /john doe/i,
    /jane doe/i,
    /your company/i,
    /company name/i,
    /business name/i,
    /your business/i,
    /my business/i,
    /your brand/i,
    /brand name/i,
    
    // Content patterns
    /this is a test/i,
    /testing content/i,
    /sample content/i,
    /placeholder text/i,
    /dummy content/i,
    /example text/i,
    
    // Common demo values
    /^test$/i,
    /^demo$/i,
    /^example$/i,
    /^sample$/i,
    /^placeholder$/i,
    /^123$/,
    /^abc$/i,
    /^asdf$/i,
    /^qwerty$/i,
    
    // Suspicious repeated patterns
    /(.)\1{4,}/, // Same character repeated 5+ times (aaaaa, 11111)
    /^(test|demo|example)\d*$/i, // test1, demo2, example3, etc.
  ]

  private static readonly URL_DEMO_PATTERNS = [
    /localhost/i,
    /127\.0\.0\.1/,
    /192\.168\./,
    /10\./,
    /172\.(1[6-9]|2[0-9]|3[0-1])\./,
    /\.local$/i,
    /\.test$/i,
    /\.example$/i,
    /\.invalid$/i,
    /httpbin\.org/i,
    /jsonplaceholder/i,
    /via\.placeholder/i,
  ]

  private static readonly CONTENT_DEMO_PATTERNS = [
    /lorem ipsum/i,
    /dolor sit amet/i,
    /consectetur adipiscing/i,
    /sed do eiusmod/i,
    /tempor incididunt/i,
    /labore et dolore/i,
    /magna aliqua/i,
    /ut enim ad minim/i,
    /quis nostrud/i,
    /exercitation ullamco/i,
    /laboris nisi/i,
    /aliquip ex ea/i,
    /commodo consequat/i,
    /duis aute irure/i,
    /sunt in culpa/i,
    /officia deserunt/i,
    /mollit anim/i,
    /est laborum/i,
  ]

  /**
   * Comprehensive validation of input data
   */
  static validate(
    input: string | string[] | null | undefined,
    options: ValidationOptions = {}
  ): ValidationResult {
    const {
      strict = true,
      allowTestEnvironment = false,
      customPatterns = [],
      field = 'input'
    } = options

    // Handle null/undefined
    if (!input) {
      return {
        isValid: false,
        isDemo: false,
        errors: [`${field} is required`],
        warnings: [],
        field
      }
    }

    // Convert to array for uniform processing
    const inputs = Array.isArray(input) ? input : [input]
    const errors: string[] = []
    const warnings: string[] = []
    let isDemoDetected = false

    // Check environment
    if (!allowTestEnvironment && this.isTestEnvironment()) {
      warnings.push('Running in test environment - demo data detection may be relaxed')
    }

    for (const item of inputs) {
      const itemStr = String(item).trim()
      
      // Check minimum length requirements
      if (itemStr.length < 2) {
        errors.push(`${field} is too short (minimum 2 characters)`)
        continue
      }

      // Check for demo patterns
      const demoResult = this.checkDemoPatterns(itemStr, strict, customPatterns)
      if (demoResult.isDemo) {
        isDemoDetected = true
        errors.push(`${field} contains demo/placeholder content: "${itemStr}" (detected: ${demoResult.reason})`)
      }

      // Check for suspicious patterns
      const suspiciousResult = this.checkSuspiciousPatterns(itemStr, strict)
      if (suspiciousResult.isSuspicious) {
        if (strict) {
          errors.push(`${field} contains suspicious content: "${itemStr}" (${suspiciousResult.reason})`)
        } else {
          warnings.push(`${field} may contain placeholder content: "${itemStr}" (${suspiciousResult.reason})`)
        }
      }
    }

    return {
      isValid: errors.length === 0,
      isDemo: isDemoDetected,
      errors,
      warnings,
      field
    }
  }

  /**
   * Validate URLs specifically
   */
  static validateURL(url: string, options: ValidationOptions = {}): ValidationResult {
    const baseResult = this.validate(url, { ...options, field: options.field || 'URL' })
    
    if (!baseResult.isValid) {
      return baseResult
    }

    try {
      const urlObj = new URL(url)
      
      // Check against URL-specific demo patterns
      for (const pattern of this.URL_DEMO_PATTERNS) {
        if (pattern.test(urlObj.hostname) || pattern.test(url)) {
          return {
            isValid: false,
            isDemo: true,
            errors: [`URL contains demo/placeholder domain: "${url}"`],
            warnings: baseResult.warnings,
            field: baseResult.field
          }
        }
      }

      // Check for suspicious TLDs
      const suspiciousTLDs = ['.test', '.example', '.invalid', '.localhost']
      if (suspiciousTLDs.some(tld => urlObj.hostname.endsWith(tld))) {
        return {
          isValid: false,
          isDemo: true,
          errors: [`URL uses demo/test TLD: "${url}"`],
          warnings: baseResult.warnings,
          field: baseResult.field
        }
      }

    } catch (error) {
      return {
        isValid: false,
        isDemo: false,
        errors: [`Invalid URL format: "${url}"`],
        warnings: baseResult.warnings,
        field: baseResult.field
      }
    }

    return baseResult
  }

  /**
   * Validate content text (for blog posts, descriptions, etc.)
   */
  static validateContent(content: string, options: ValidationOptions = {}): ValidationResult {
    const baseResult = this.validate(content, { ...options, field: options.field || 'content' })
    
    if (!baseResult.isValid) {
      return baseResult
    }

    // Check for Lorem Ipsum and other placeholder text
    for (const pattern of this.CONTENT_DEMO_PATTERNS) {
      if (pattern.test(content)) {
        return {
          isValid: false,
          isDemo: true,
          errors: [`Content contains Lorem Ipsum or placeholder text`],
          warnings: baseResult.warnings,
          field: baseResult.field
        }
      }
    }

    // Check content quality indicators
    const words = content.trim().split(/\s+/)
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0)
    
    if (words.length < 10) {
      baseResult.warnings.push('Content is very short (less than 10 words)')
    }

    if (sentences.length < 2) {
      baseResult.warnings.push('Content should contain multiple sentences for better quality')
    }

    // Check for repetitive content
    const uniqueWords = new Set(words.map(w => w.toLowerCase()))
    const uniqueRatio = uniqueWords.size / words.length
    
    if (uniqueRatio < 0.3) {
      baseResult.warnings.push('Content appears to be highly repetitive')
    }

    return baseResult
  }

  /**
   * Batch validation for arrays of data
   */
  static validateBatch(
    items: string[],
    options: ValidationOptions = {}
  ): { valid: string[]; invalid: string[]; results: ValidationResult[] } {
    const valid: string[] = []
    const invalid: string[] = []
    const results: ValidationResult[] = []

    items.forEach((item, index) => {
      const result = this.validate(item, { ...options, field: `${options.field || 'item'}[${index}]` })
      results.push(result)
      
      if (result.isValid) {
        valid.push(item)
      } else {
        invalid.push(item)
      }
    })

    return { valid, invalid, results }
  }

  /**
   * Check if input matches demo patterns
   */
  private static checkDemoPatterns(
    input: string,
    strict: boolean,
    customPatterns: RegExp[]
  ): { isDemo: boolean; reason?: string } {
    const allPatterns = [...this.DEMO_PATTERNS, ...customPatterns]
    
    for (const pattern of allPatterns) {
      if (pattern.test(input)) {
        return { isDemo: true, reason: `matches pattern: ${pattern.source}` }
      }
    }

    return { isDemo: false }
  }

  /**
   * Check for suspicious patterns that might indicate placeholder content
   */
  private static checkSuspiciousPatterns(
    input: string,
    strict: boolean
  ): { isSuspicious: boolean; reason?: string } {
    // Check for very short inputs
    if (input.length < 3) {
      return { isSuspicious: true, reason: 'too short' }
    }

    // Check for single character repeated
    if (/^(.)\1+$/.test(input)) {
      return { isSuspicious: true, reason: 'single character repeated' }
    }

    // Check for keyboard patterns
    const keyboardPatterns = [
      /qwerty/i,
      /asdf/i,
      /zxcv/i,
      /1234/,
      /abcd/i
    ]

    for (const pattern of keyboardPatterns) {
      if (pattern.test(input)) {
        return { isSuspicious: true, reason: 'keyboard pattern detected' }
      }
    }

    // Check for number-only inputs that are suspiciously simple
    if (/^\d{1,3}$/.test(input)) {
      return { isSuspicious: true, reason: 'simple number pattern' }
    }

    return { isSuspicious: false }
  }

  /**
   * Check if running in test environment
   */
  private static isTestEnvironment(): boolean {
    if (typeof process !== 'undefined') {
      return process.env.NODE_ENV === 'test' || 
             process.env.NODE_ENV === 'development' ||
             !!process.env.JEST_WORKER_ID
    }
    return false
  }

  /**
   * Generate user-friendly error messages
   */
  static formatValidationErrors(results: ValidationResult[]): string[] {
    const messages: string[] = []
    
    const demoDetected = results.filter(r => r.isDemo).length
    const totalErrors = results.reduce((sum, r) => sum + r.errors.length, 0)
    
    if (demoDetected > 0) {
      messages.push(`⚠️ Demo/placeholder content detected in ${demoDetected} field(s). Please provide real data.`)
    }
    
    if (totalErrors > 0) {
      messages.push(`❌ ${totalErrors} validation error(s) found:`)
      results.forEach(result => {
        result.errors.forEach(error => {
          messages.push(`  • ${error}`)
        })
      })
    }

    const totalWarnings = results.reduce((sum, r) => sum + r.warnings.length, 0)
    if (totalWarnings > 0) {
      messages.push(`⚠️ ${totalWarnings} warning(s):`)
      results.forEach(result => {
        result.warnings.forEach(warning => {
          messages.push(`  • ${warning}`)
        })
      })
    }

    return messages
  }

  /**
   * Create validation summary
   */
  static createSummary(results: ValidationResult[]): {
    isValid: boolean
    totalChecked: number
    validCount: number
    demoCount: number
    errorCount: number
    warningCount: number
  } {
    const totalChecked = results.length
    const validCount = results.filter(r => r.isValid).length
    const demoCount = results.filter(r => r.isDemo).length
    const errorCount = results.reduce((sum, r) => sum + r.errors.length, 0)
    const warningCount = results.reduce((sum, r) => sum + r.warnings.length, 0)
    
    return {
      isValid: validCount === totalChecked,
      totalChecked,
      validCount,
      demoCount,
      errorCount,
      warningCount
    }
  }
}

// Export convenience functions
export const validateInput = DemoDataValidator.validate
export const validateURL = DemoDataValidator.validateURL
export const validateContent = DemoDataValidator.validateContent
export const validateBatch = DemoDataValidator.validateBatch
export const formatErrors = DemoDataValidator.formatValidationErrors
export const createSummary = DemoDataValidator.createSummary