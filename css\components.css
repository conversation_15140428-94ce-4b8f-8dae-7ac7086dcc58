/* 
 * SEO SAAS Component Library
 * Professional UI components with enterprise design standards
 */

/* ===========================
   ADVANCED BUTTON VARIANTS
   =========================== */

.btn-group {
  display: inline-flex;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.btn-group .btn {
  border-radius: 0;
  border-left-width: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: var(--radius-lg);
  border-bottom-left-radius: var(--radius-lg);
  border-left-width: 1px;
}

.btn-group .btn:last-child {
  border-top-right-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
}

/* Icon buttons */
.btn-icon {
  padding: var(--space-2);
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-300);
  background-color: white;
  color: var(--gray-600);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-icon:hover {
  background-color: var(--gray-50);
  border-color: var(--gray-400);
  color: var(--gray-700);
  transform: translateY(-1px);
}

.btn-icon:active {
  transform: translateY(0);
}

/* Floating action button */
.btn-fab {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background-color: var(--primary-600);
  color: white;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  bottom: var(--space-6);
  right: var(--space-6);
  z-index: 50;
}

.btn-fab:hover {
  background-color: var(--primary-700);
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* ===========================
   PROFESSIONAL BADGES & CHIPS
   =========================== */

.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.125rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: var(--radius-full);
  white-space: nowrap;
  line-height: 1.25;
}

.badge-primary {
  background-color: var(--primary-100);
  color: var(--primary-800);
}

.badge-secondary {
  background-color: var(--gray-100);
  color: var(--gray-800);
}

.badge-success {
  background-color: var(--success-100);
  color: var(--success-800);
}

.badge-warning {
  background-color: var(--warning-100);
  color: var(--warning-800);
}

.badge-error {
  background-color: var(--error-100);
  color: var(--error-800);
}

.badge-dot {
  position: relative;
  padding-left: 1rem;
}

.badge-dot::before {
  content: '';
  position: absolute;
  left: 0.25rem;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: currentColor;
}

/* Score badges */
.score-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 1.75rem;
  font-size: 0.875rem;
  font-weight: 600;
  border-radius: var(--radius-lg);
}

.score-excellent {
  background-color: var(--success-100);
  color: var(--success-800);
}

.score-good {
  background-color: var(--primary-100);
  color: var(--primary-800);
}

.score-fair {
  background-color: var(--warning-100);
  color: var(--warning-800);
}

.score-poor {
  background-color: var(--error-100);
  color: var(--error-800);
}

/* ===========================
   PROGRESS INDICATORS
   =========================== */

.progress {
  width: 100%;
  height: 0.5rem;
  background-color: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: var(--primary-600);
  border-radius: var(--radius-full);
  transition: width 0.6s ease-in-out;
}

.progress-bar-success {
  background-color: var(--success-600);
}

.progress-bar-warning {
  background-color: var(--warning-600);
}

.progress-bar-error {
  background-color: var(--error-600);
}

/* Circular progress */
.progress-circle {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: conic-gradient(var(--primary-600) 0deg, var(--gray-200) 0deg);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-circle::before {
  content: '';
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: white;
  position: absolute;
}

.progress-text {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--gray-700);
  z-index: 1;
}

/* ===========================
   DROPDOWN & MENU COMPONENTS
   =========================== */

.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 50;
  min-width: 12rem;
  background-color: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--space-2);
  margin-top: var(--space-1);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease-in-out;
}

.dropdown:hover .dropdown-menu,
.dropdown.open .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: var(--space-2) var(--space-3);
  font-size: 0.875rem;
  color: var(--gray-700);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: all 0.2s ease-in-out;
}

.dropdown-item:hover {
  background-color: var(--gray-100);
  color: var(--gray-900);
}

.dropdown-divider {
  height: 1px;
  background-color: var(--gray-200);
  margin: var(--space-2) 0;
}

/* Context menu */
.context-menu {
  position: fixed;
  z-index: 1000;
  background-color: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--space-2);
  min-width: 10rem;
}

/* ===========================
   TABS & NAVIGATION
   =========================== */

.tabs {
  border-bottom: 1px solid var(--gray-200);
}

.tab-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--space-6);
}

.tab-item {
  position: relative;
}

.tab-link {
  display: block;
  padding: var(--space-3) 0;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-600);
  text-decoration: none;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease-in-out;
}

.tab-link:hover {
  color: var(--gray-900);
}

.tab-link.active {
  color: var(--primary-600);
  border-bottom-color: var(--primary-600);
}

.tab-content {
  padding: var(--space-6) 0;
}

.tab-panel {
  display: none;
}

.tab-panel.active {
  display: block;
  animation: fadeIn 0.3s ease-in-out;
}

/* ===========================
   ALERTS & NOTIFICATIONS
   =========================== */

.alert {
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  border: 1px solid;
  margin-bottom: var(--space-4);
  position: relative;
}

.alert-success {
  background-color: var(--success-50);
  border-color: var(--success-200);
  color: var(--success-800);
}

.alert-warning {
  background-color: var(--warning-50);
  border-color: var(--warning-200);
  color: var(--warning-800);
}

.alert-error {
  background-color: var(--error-50);
  border-color: var(--error-200);
  color: var(--error-800);
}

.alert-info {
  background-color: var(--info-50);
  border-color: var(--info-200);
  color: var(--info-800);
}

.alert-title {
  font-weight: 600;
  margin-bottom: var(--space-1);
}

.alert-close {
  position: absolute;
  top: var(--space-2);
  right: var(--space-2);
  background: none;
  border: none;
  color: currentColor;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s ease-in-out;
}

.alert-close:hover {
  opacity: 1;
}

/* Toast notifications */
.toast-container {
  position: fixed;
  top: var(--space-4);
  right: var(--space-4);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  max-width: 24rem;
}

.toast {
  background-color: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--space-4);
  transform: translateX(100%);
  animation: slideInRight 0.3s ease-out forwards;
}

.toast.removing {
  animation: slideOutRight 0.3s ease-in forwards;
}

@keyframes slideInRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

@keyframes slideOutRight {
  from { transform: translateX(0); }
  to { transform: translateX(100%); }
}

/* ===========================
   MODAL & DIALOG COMPONENTS
   =========================== */

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-4);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease-in-out;
}

.modal-overlay.open {
  opacity: 1;
  visibility: visible;
}

.modal {
  background-color: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 32rem;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.95);
  transition: transform 0.3s ease-in-out;
}

.modal-overlay.open .modal {
  transform: scale(1);
}

.modal-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  color: var(--gray-400);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-md);
  transition: all 0.2s ease-in-out;
}

.modal-close:hover {
  color: var(--gray-600);
  background-color: var(--gray-100);
}

.modal-body {
  padding: var(--space-6);
}

.modal-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--gray-200);
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
}

/* ===========================
   TOOLTIP COMPONENT
   =========================== */

.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip-content {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: var(--space-2);
  background-color: var(--gray-900);
  color: white;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease-in-out;
  z-index: 1000;
}

.tooltip-content::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: var(--gray-900);
}

.tooltip:hover .tooltip-content {
  opacity: 1;
  visibility: visible;
}

/* ===========================
   LOADING STATES
   =========================== */

.spinner {
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid var(--gray-200);
  border-top-color: var(--primary-600);
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.skeleton {
  background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-md);
}

.skeleton-text {
  height: 1rem;
  margin-bottom: var(--space-2);
}

.skeleton-title {
  height: 1.5rem;
  width: 75%;
  margin-bottom: var(--space-3);
}

.skeleton-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
}

/* ===========================
   ENTERPRISE DATA COMPONENTS
   =========================== */

.data-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.data-table th {
  background-color: var(--gray-50);
  color: var(--gray-700);
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: var(--space-3) var(--space-4);
  text-align: left;
  border-bottom: 1px solid var(--gray-200);
}

.data-table td {
  padding: var(--space-4);
  border-bottom: 1px solid var(--gray-100);
  vertical-align: top;
}

.data-table tr:hover {
  background-color: var(--gray-50);
}

.data-table tr:last-child td {
  border-bottom: none;
}

/* Status indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 0.875rem;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-active .status-dot {
  background-color: var(--success-500);
}

.status-pending .status-dot {
  background-color: var(--warning-500);
}

.status-inactive .status-dot {
  background-color: var(--gray-400);
}

.status-error .status-dot {
  background-color: var(--error-500);
}