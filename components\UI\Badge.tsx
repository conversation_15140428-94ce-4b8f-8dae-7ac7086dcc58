'use client';

import React from 'react';
import { ComponentProps } from 'react';

type BadgeVariant = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
type BadgeSize = 'sm' | 'md' | 'lg';

interface BadgeProps extends ComponentProps<'span'> {
  variant?: BadgeVariant;
  size?: BadgeSize;
  dot?: boolean;
  icon?: React.ReactNode;
}

const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  size = 'md',
  dot = false,
  icon,
  className = '',
  ...props
}) => {
  
  // Size configurations
  const sizeConfig = {
    sm: {
      text: 'text-xs',
      padding: dot ? 'p-1' : 'px-2 py-0.5',
      iconSize: 'h-3 w-3',
      dotSize: 'h-2 w-2'
    },
    md: {
      text: 'text-sm',
      padding: dot ? 'p-1.5' : 'px-2.5 py-1',
      iconSize: 'h-4 w-4',
      dotSize: 'h-2.5 w-2.5'
    },
    lg: {
      text: 'text-base',
      padding: dot ? 'p-2' : 'px-3 py-1.5',
      iconSize: 'h-5 w-5',
      dotSize: 'h-3 w-3'
    }
  };

  const config = sizeConfig[size];

  // Variant styles
  const variantClasses = {
    default: [
      'bg-gray-100 text-gray-800',
      'dark:bg-gray-700 dark:text-gray-300'
    ].join(' '),
    
    primary: [
      'bg-blue-100 text-blue-800',
      'dark:bg-blue-900/50 dark:text-blue-200'
    ].join(' '),
    
    secondary: [
      'bg-purple-100 text-purple-800',
      'dark:bg-purple-900/50 dark:text-purple-200'
    ].join(' '),
    
    success: [
      'bg-green-100 text-green-800',
      'dark:bg-green-900/50 dark:text-green-200'
    ].join(' '),
    
    warning: [
      'bg-yellow-100 text-yellow-800',
      'dark:bg-yellow-900/50 dark:text-yellow-200'
    ].join(' '),
    
    error: [
      'bg-red-100 text-red-800',
      'dark:bg-red-900/50 dark:text-red-200'
    ].join(' '),
    
    info: [
      'bg-cyan-100 text-cyan-800',
      'dark:bg-cyan-900/50 dark:text-cyan-200'
    ].join(' ')
  };

  // Base classes
  const baseClasses = [
    'inline-flex items-center justify-center',
    'font-medium rounded-full',
    'transition-colors duration-200',
    config.text,
    config.padding,
    dot ? config.dotSize : ''
  ].filter(Boolean).join(' ');

  const finalClassName = [
    baseClasses,
    variantClasses[variant],
    className
  ].join(' ');

  if (dot) {
    return (
      <span className={finalClassName} {...props} />
    );
  }

  return (
    <span className={finalClassName} {...props}>
      {icon && (
        <span className={`${config.iconSize} mr-1 flex-shrink-0`}>
          {icon}
        </span>
      )}
      {children}
    </span>
  );
};

export default Badge;