/**
 * AuthorityLinkValidation Component
 * Enterprise SEO SAAS - Real-time validation UI for authority links
 */

import { useState, useEffect } from 'react'
import { AuthorityLink } from '@/utils/authorityLinkDiscovery'
import { AuthorityLinkValidator, BatchValidationResult, ValidationIssue } from '@/utils/authorityLinkValidator'
import {
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  ArrowPathIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  ClockIcon,
  DocumentMagnifyingGlassIcon,
  BoltIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline'

interface AuthorityLinkValidationProps {
  links: AuthorityLink[]
  onValidationComplete?: (result: BatchValidationResult) => void
  autoValidate?: boolean
  showDetails?: boolean
}

interface ValidationState {
  isValidating: boolean
  validationResult: BatchValidationResult | null
  currentProgress: number
  currentLink: string
}

export default function AuthorityLinkValidation({
  links,
  onValidationComplete,
  autoValidate = true,
  showDetails = true
}: AuthorityLinkValidationProps) {
  const [validationState, setValidationState] = useState<ValidationState>({
    isValidating: false,
    validationResult: null,
    currentProgress: 0,
    currentLink: ''
  })

  const [expandedIssues, setExpandedIssues] = useState<Set<string>>(new Set())
  const [filterSeverity, setFilterSeverity] = useState<string>('all')
  const [validator] = useState(() => new AuthorityLinkValidator())

  useEffect(() => {
    if (autoValidate && links.length > 0 && !validationState.validationResult) {
      validateLinks()
    }
  }, [links, autoValidate])

  const validateLinks = async () => {
    if (links.length === 0) return

    setValidationState(prev => ({
      ...prev,
      isValidating: true,
      currentProgress: 0,
      currentLink: ''
    }))

    try {
      // Simulate progress updates (in real implementation, would use progress callbacks)
      const progressInterval = setInterval(() => {
        setValidationState(prev => ({
          ...prev,
          currentProgress: Math.min(prev.currentProgress + 10, 90)
        }))
      }, 500)

      const result = await validator.validateAuthorityLinks(links, {
        minAuthorityScore: 70,
        checkSSL: true,
        validateContent: true
      })

      clearInterval(progressInterval)

      setValidationState({
        isValidating: false,
        validationResult: result,
        currentProgress: 100,
        currentLink: ''
      })

      onValidationComplete?.(result)

    } catch (error) {
      console.error('Validation failed:', error)
      setValidationState(prev => ({
        ...prev,
        isValidating: false,
        currentProgress: 0,
        currentLink: 'Validation failed'
      }))
    }
  }

  const getIssueIcon = (type: string) => {
    switch (type) {
      case 'error': return XCircleIcon
      case 'warning': return ExclamationTriangleIcon
      case 'info': return InformationCircleIcon
      default: return InformationCircleIcon
    }
  }

  const getIssueColor = (type: string) => {
    switch (type) {
      case 'error': return 'text-red-600 bg-red-100'
      case 'warning': return 'text-amber-600 bg-amber-100'
      case 'info': return 'text-blue-600 bg-blue-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getSeverityBadgeColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800'
      case 'high': return 'bg-orange-100 text-orange-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getFilteredIssues = () => {
    if (!validationState.validationResult) return []
    
    const { issues } = validationState.validationResult
    
    if (filterSeverity === 'all') return issues
    
    return issues.filter(issue => issue.severity === filterSeverity)
  }

  const groupIssuesByLink = () => {
    const issues = getFilteredIssues()
    const grouped: Record<string, ValidationIssue[]> = {}
    
    issues.forEach(issue => {
      const linkId = issue.link.id
      if (!grouped[linkId]) {
        grouped[linkId] = []
      }
      grouped[linkId].push(issue)
    })
    
    return grouped
  }

  const { validationResult } = validationState

  return (
    <div className="space-y-6">
      {/* Validation Header */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Authority Link Validation</h3>
            <p className="text-sm text-gray-600">
              Comprehensive quality checks to ensure link authority and reliability
            </p>
          </div>
          
          {!validationState.isValidating && (
            <button
              onClick={validateLinks}
              className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors gap-2"
            >
              <ArrowPathIcon className="h-4 w-4" />
              {validationResult ? 'Re-validate' : 'Validate Links'}
            </button>
          )}
        </div>

        {/* Progress Bar */}
        {validationState.isValidating && (
          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Validating {links.length} links...</span>
              <span className="font-medium text-gray-900">{validationState.currentProgress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${validationState.currentProgress}%` }}
              />
            </div>
            {validationState.currentLink && (
              <p className="text-xs text-gray-500 flex items-center gap-2">
                <ArrowPathIcon className="h-3 w-3 animate-spin" />
                {validationState.currentLink}
              </p>
            )}
          </div>
        )}

        {/* Validation Summary */}
        {validationResult && !validationState.isValidating && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <DocumentMagnifyingGlassIcon className="h-8 w-8 text-gray-600" />
                <div>
                  <div className="text-2xl font-bold text-gray-900">
                    {validationResult.metrics.totalProcessed}
                  </div>
                  <div className="text-sm text-gray-500">Total Processed</div>
                </div>
              </div>
            </div>

            <div className="bg-green-50 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <CheckCircleIcon className="h-8 w-8 text-green-600" />
                <div>
                  <div className="text-2xl font-bold text-gray-900">
                    {validationResult.metrics.passedValidation}
                  </div>
                  <div className="text-sm text-gray-500">Passed</div>
                </div>
              </div>
            </div>

            <div className="bg-red-50 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <XCircleIcon className="h-8 w-8 text-red-600" />
                <div>
                  <div className="text-2xl font-bold text-gray-900">
                    {validationResult.metrics.failedValidation}
                  </div>
                  <div className="text-sm text-gray-500">Failed</div>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <ChartBarIcon className="h-8 w-8 text-blue-600" />
                <div>
                  <div className="text-2xl font-bold text-gray-900">
                    {validationResult.metrics.averageQualityScore.toFixed(1)}
                  </div>
                  <div className="text-sm text-gray-500">Avg Quality</div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Issues and Details */}
      {validationResult && showDetails && (
        <>
          {/* Issue Filter */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <span className="text-sm font-medium text-gray-700">Filter by severity:</span>
                <div className="flex items-center gap-2">
                  {['all', 'critical', 'high', 'medium', 'low'].map(severity => (
                    <button
                      key={severity}
                      onClick={() => setFilterSeverity(severity)}
                      className={`
                        px-3 py-1 text-sm font-medium rounded-full transition-colors
                        ${filterSeverity === severity
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                        }
                      `}
                    >
                      {severity.charAt(0).toUpperCase() + severity.slice(1)}
                    </button>
                  ))}
                </div>
              </div>
              
              <div className="text-sm text-gray-500">
                {getFilteredIssues().length} issues found
              </div>
            </div>
          </div>

          {/* Issues by Link */}
          <div className="space-y-4">
            {Object.entries(groupIssuesByLink()).map(([linkId, linkIssues]) => {
              const link = links.find(l => l.id === linkId)
              if (!link) return null

              const isExpanded = expandedIssues.has(linkId)
              const hasErrors = linkIssues.some(issue => issue.type === 'error')
              const isValid = validationResult.validLinks.some(l => l.id === linkId)

              return (
                <div
                  key={linkId}
                  className={`
                    bg-white border rounded-lg overflow-hidden
                    ${hasErrors ? 'border-red-200' : isValid ? 'border-green-200' : 'border-gray-200'}
                  `}
                >
                  <div
                    className="p-4 cursor-pointer hover:bg-gray-50"
                    onClick={() => {
                      const newExpanded = new Set(expandedIssues)
                      if (isExpanded) {
                        newExpanded.delete(linkId)
                      } else {
                        newExpanded.add(linkId)
                      }
                      setExpandedIssues(newExpanded)
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`
                          w-8 h-8 rounded-full flex items-center justify-center
                          ${hasErrors ? 'bg-red-100' : isValid ? 'bg-green-100' : 'bg-amber-100'}
                        `}>
                          {hasErrors ? (
                            <XCircleIcon className="h-5 w-5 text-red-600" />
                          ) : isValid ? (
                            <CheckCircleIcon className="h-5 w-5 text-green-600" />
                          ) : (
                            <ExclamationTriangleIcon className="h-5 w-5 text-amber-600" />
                          )}
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{link.title}</h4>
                          <p className="text-sm text-gray-500">{link.domain}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-gray-500">{linkIssues.length} issues</span>
                          <span className={`
                            inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                            ${isValid ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}
                          `}>
                            {isValid ? 'Valid' : 'Invalid'}
                          </span>
                        </div>
                        <ChevronDownIcon className={`
                          h-5 w-5 text-gray-400 transition-transform
                          ${isExpanded ? 'transform rotate-180' : ''}
                        `} />
                      </div>
                    </div>
                  </div>

                  {isExpanded && (
                    <div className="border-t border-gray-200 p-4 bg-gray-50">
                      <div className="space-y-3">
                        {linkIssues.map((issue, index) => {
                          const Icon = getIssueIcon(issue.type)
                          return (
                            <div key={index} className="flex items-start gap-3">
                              <div className={`
                                w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0
                                ${getIssueColor(issue.type)}
                              `}>
                                <Icon className="h-4 w-4" />
                              </div>
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-1">
                                  <span className="font-medium text-gray-900 text-sm">
                                    {issue.message}
                                  </span>
                                  <span className={`
                                    inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                                    ${getSeverityBadgeColor(issue.severity)}
                                  `}>
                                    {issue.severity}
                                  </span>
                                </div>
                                {issue.recommendation && (
                                  <p className="text-sm text-gray-600">
                                    <BoltIcon className="h-3 w-3 inline mr-1" />
                                    {issue.recommendation}
                                  </p>
                                )}
                                <p className="text-xs text-gray-500 mt-1">
                                  Code: {issue.code}
                                </p>
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>

          {/* Recommendations */}
          {validationResult.recommendations.length > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <div className="flex items-start gap-3">
                <BoltIcon className="h-6 w-6 text-blue-600 mt-0.5" />
                <div>
                  <h3 className="font-medium text-blue-900 mb-2">Recommendations</h3>
                  <ul className="space-y-2 text-sm text-blue-800">
                    {validationResult.recommendations.map((recommendation, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-blue-600">•</span>
                        <span>{recommendation}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {/* Empty State */}
      {!validationState.isValidating && !validationResult && links.length === 0 && (
        <div className="text-center py-12">
          <ShieldCheckIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Links to Validate</h3>
          <p className="text-gray-600">
            Add authority links to perform validation checks
          </p>
        </div>
      )}
    </div>
  )
}

// Missing import
import { ChevronDownIcon } from '@heroicons/react/24/outline'