'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  DocumentTextIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  FolderIcon,
  ArrowTrendingUpIcon,
  PlayIcon
} from '@heroicons/react/24/outline';
import { AuthenticatedLayout, PageHeader, DashboardGrid, ContentContainer } from '@/components/Layout/DashboardLayout';
import Card from '@/components/UI/Card';
import Button from '@/components/UI/Button';
import Badge from '@/components/UI/Badge';
import LoadingSpinner from '@/components/UI/LoadingSpinner';
import { useNotifications } from '@/components/Notifications';

// Mock API functions for demo
const fetchDashboardMetrics = async () => {
  await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate API delay
  return {
    contentGenerated: 127,
    activeProjects: 8,
    keywordsRanking: 89,
    avgSeoScore: 78
  };
};

const fetchRecentActivity = async () => {
  await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
  return [
    {
      id: '1',
      action: 'Generated content for "digital marketing strategies"',
      type: 'success',
      timestamp: '5 minutes ago'
    },
    {
      id: '2',
      action: 'Created new project "E-commerce SEO"',
      type: 'info',
      timestamp: '1 hour ago'
    },
    {
      id: '3',
      action: 'Analyzed competitor content',
      type: 'analysis',
      timestamp: '2 hours ago'
    }
  ];
};

const fetchPerformanceInsights = async () => {
  await new Promise(resolve => setTimeout(resolve, 800)); // Simulate API delay
  return [
    {
      label: 'Content Quality Score',
      value: '+12% this week',
      type: 'positive'
    },
    {
      label: 'Keyword Rankings',
      value: '+8 positions',
      type: 'positive'
    },
    {
      label: 'Content Generation',
      value: '23 this month',
      type: 'neutral'
    }
  ];
};

export default function DashboardPage() {
  const [metrics, setMetrics] = useState<{
    contentGenerated: number;
    activeProjects: number;
    keywordsRanking: number;
    avgSeoScore: number;
  } | null>(null);
  const [recentActivity, setRecentActivity] = useState<Array<{
    id: string;
    action: string;
    type: string;
    timestamp: string;
  }>>([]);
  const [performanceInsights, setPerformanceInsights] = useState<Array<{
    label: string;
    value: string;
    type: string;
  }>>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { error: notifyError, info: notifyInfo } = useNotifications();

  useEffect(() => {
    const loadDashboardData = async () => {
      setIsLoading(true);
      try {
        const [metricsData, activityData, insightsData] = await Promise.all([
          fetchDashboardMetrics(),
          fetchRecentActivity(),
          fetchPerformanceInsights()
        ]);
        
        setMetrics(metricsData);
        setRecentActivity(activityData);
        setPerformanceInsights(insightsData);
      } catch (error) {
        console.error('Failed to load dashboard data:', error);
        notifyError('Failed to load dashboard data');
      } finally {
        setIsLoading(false);
      }
    };
    
    loadDashboardData();
  }, []);
  const quickActions = [
    {
      title: 'Generate Content',
      description: 'Create SEO-optimized content',
      icon: DocumentTextIcon,
      href: '/content-generator',
      color: 'blue'
    },
    {
      title: 'Competitor Analysis',
      description: 'Research competition',
      icon: MagnifyingGlassIcon,
      onClick: () => notifyInfo('Feature coming soon!'),
      color: 'green'
    },
    {
      title: 'New Project',
      description: 'Start a new SEO project',
      icon: FolderIcon,
      onClick: () => notifyInfo('Feature coming soon!'),
      color: 'purple'
    },
    {
      title: 'Analytics',
      description: 'View performance metrics',
      icon: ChartBarIcon,
      onClick: () => notifyInfo('Feature coming soon!'),
      color: 'orange'
    }
  ];

  return (
    <AuthenticatedLayout>
      <PageHeader 
        title="SEO Dashboard"
        description="Manage your SEO content generation and analytics"
        actions={
          <Button 
            href="/content-generator"
            className="inline-flex items-center"
          >
            <PlayIcon className="h-4 w-4 mr-2" />
            Generate Content
          </Button>
        }
      />

      <div className="space-y-8">
        {/* Quick Stats */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Overview</h2>
          <DashboardGrid cols={4}>
            {isLoading ? (
              Array.from({ length: 4 }).map((_, i) => (
                <Card key={i} className="p-6 animate-pulse">
                  <div className="flex items-center">
                    <div className="h-8 w-8 bg-gray-300 dark:bg-gray-600 rounded mr-3"></div>
                    <div>
                      <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-24 mb-2"></div>
                      <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-16"></div>
                    </div>
                  </div>
                </Card>
              ))
            ) : (
              <>
                <Card className="p-6">
                  <div className="flex items-center">
                    <DocumentTextIcon className="h-8 w-8 text-blue-600 dark:text-blue-400 mr-3" />
                    <div>
                      <p className="text-gray-600 dark:text-gray-400 text-sm">Content Generated</p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{metrics?.contentGenerated || 0}</p>
                    </div>
                  </div>
                </Card>

                <Card className="p-6">
                  <div className="flex items-center">
                    <FolderIcon className="h-8 w-8 text-green-600 dark:text-green-400 mr-3" />
                    <div>
                      <p className="text-gray-600 dark:text-gray-400 text-sm">Active Projects</p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{metrics?.activeProjects || 0}</p>
                    </div>
                  </div>
                </Card>

                <Card className="p-6">
                  <div className="flex items-center">
                    <ArrowTrendingUpIcon className="h-8 w-8 text-purple-600 dark:text-purple-400 mr-3" />
                    <div>
                      <p className="text-gray-600 dark:text-gray-400 text-sm">Keywords Ranking</p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{metrics?.keywordsRanking || 0}</p>
                    </div>
                  </div>
                </Card>

                <Card className="p-6">
                  <div className="flex items-center">
                    <ChartBarIcon className="h-8 w-8 text-orange-600 dark:text-orange-400 mr-3" />
                    <div>
                      <p className="text-gray-600 dark:text-gray-400 text-sm">Avg SEO Score</p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{metrics?.avgSeoScore || 0}</p>
                    </div>
                  </div>
                </Card>
              </>
            )}
          </DashboardGrid>
        </div>

        {/* Quick Actions */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Quick Actions</h2>
          <DashboardGrid cols={4}>
            {quickActions.map((action, index) => {
              const IconComponent = action.icon;
              const colorClasses = {
                blue: 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30',
                green: 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/30',
                purple: 'text-purple-600 dark:text-purple-400 bg-purple-50 dark:bg-purple-900/20 hover:bg-purple-100 dark:hover:bg-purple-900/30',
                orange: 'text-orange-600 dark:text-orange-400 bg-orange-50 dark:bg-orange-900/20 hover:bg-orange-100 dark:hover:bg-orange-900/30'
              }[action.color];

              if (action.href) {
                return (
                  <Link key={index} href={action.href}>
                    <Card className={`p-6 cursor-pointer transition-colors group ${colorClasses}`}>
                      <div className="flex items-center space-x-3">
                        <IconComponent className="h-6 w-6" />
                        <div>
                          <h3 className="font-medium text-gray-900 dark:text-gray-100">{action.title}</h3>
                          <p className="text-gray-600 dark:text-gray-400 text-sm">{action.description}</p>
                        </div>
                      </div>
                    </Card>
                  </Link>
                );
              }

              return (
                <Card 
                  key={index}
                  className={`p-6 cursor-pointer transition-colors group ${colorClasses}`}
                  onClick={action.onClick}
                >
                  <div className="flex items-center space-x-3">
                    <IconComponent className="h-6 w-6" />
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-gray-100">{action.title}</h3>
                      <p className="text-gray-600 dark:text-gray-400 text-sm">{action.description}</p>
                    </div>
                  </div>
                </Card>
              );
            })}
          </DashboardGrid>
        </div>

        {/* Recent Activity & Performance Insights */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Recent Activity</h3>
            {isLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="flex items-center space-x-3 animate-pulse">
                    <div className="w-2 h-2 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                    <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded flex-1"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {recentActivity.map((activity) => {
                  const colors = {
                    success: 'bg-green-500',
                    info: 'bg-blue-500',
                    analysis: 'bg-purple-500'
                  };
                  return (
                    <div key={activity.id} className="flex items-center space-x-3">
                      <div className={`w-2 h-2 ${colors[activity.type] || 'bg-gray-500'} rounded-full`}></div>
                      <div className="flex-1">
                        <span className="text-gray-900 dark:text-gray-100 text-sm">{activity.action}</span>
                        <p className="text-xs text-gray-500 dark:text-gray-400">{activity.timestamp}</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Performance Insights</h3>
            {isLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="flex items-center justify-between animate-pulse">
                    <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-32"></div>
                    <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-20"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {performanceInsights.map((insight, index) => {
                  const colors = {
                    positive: 'text-green-600 dark:text-green-400',
                    neutral: 'text-purple-600 dark:text-purple-400',
                    negative: 'text-red-600 dark:text-red-400'
                  };
                  return (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-gray-700 dark:text-gray-300 text-sm">{insight.label}</span>
                      <Badge variant="secondary" className={colors[insight.type] || 'text-gray-600 dark:text-gray-400'}>
                        {insight.value}
                      </Badge>
                    </div>
                  );
                })}
              </div>
            )}
          </Card>
        </div>
      </div>
    </AuthenticatedLayout>
  )
}