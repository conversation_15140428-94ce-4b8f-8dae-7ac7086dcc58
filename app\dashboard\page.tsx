'use client';

import React from 'react';
import Link from 'next/link';
import {
  DocumentTextIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  FolderIcon,
  TrendingUpIcon
} from '@heroicons/react/24/outline';

export default function DashboardPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900">
      {/* Header */}
      <header className="bg-white/10 backdrop-blur-md border-b border-white/20">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="text-2xl font-bold text-white">
              SEO SAAS
            </Link>
            <nav className="flex space-x-6">
              <Link href="/content-generator" className="text-white hover:text-blue-300 transition-colors">
                Content Generator
              </Link>
              <Link href="/" className="text-white hover:text-blue-300 transition-colors">
                Home
              </Link>
            </nav>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-12">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-white mb-4">
              SEO Dashboard
            </h1>
            <p className="text-xl text-gray-300">
              Manage your SEO content generation and analytics
            </p>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
              <div className="flex items-center">
                <DocumentTextIcon className="h-8 w-8 text-blue-400 mr-3" />
                <div>
                  <p className="text-gray-300 text-sm">Content Generated</p>
                  <p className="text-2xl font-bold text-white">127</p>
                </div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
              <div className="flex items-center">
                <FolderIcon className="h-8 w-8 text-green-400 mr-3" />
                <div>
                  <p className="text-gray-300 text-sm">Active Projects</p>
                  <p className="text-2xl font-bold text-white">8</p>
                </div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
              <div className="flex items-center">
                <TrendingUpIcon className="h-8 w-8 text-purple-400 mr-3" />
                <div>
                  <p className="text-gray-300 text-sm">Keywords Ranking</p>
                  <p className="text-2xl font-bold text-white">89</p>
                </div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
              <div className="flex items-center">
                <ChartBarIcon className="h-8 w-8 text-orange-400 mr-3" />
                <div>
                  <p className="text-gray-300 text-sm">Avg SEO Score</p>
                  <p className="text-2xl font-bold text-white">78</p>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-8 border border-white/20 mb-12">
            <h2 className="text-2xl font-bold text-white mb-6">Quick Actions</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Link
                href="/content-generator"
                className="bg-blue-600/20 hover:bg-blue-600/30 border border-blue-500/50 rounded-lg p-4 transition-colors group"
              >
                <div className="flex items-center space-x-3">
                  <DocumentTextIcon className="h-6 w-6 text-blue-400" />
                  <div>
                    <h3 className="text-white font-medium">Generate Content</h3>
                    <p className="text-gray-300 text-sm">Create SEO-optimized content</p>
                  </div>
                </div>
              </Link>

              <div className="bg-green-600/20 hover:bg-green-600/30 border border-green-500/50 rounded-lg p-4 transition-colors group cursor-pointer">
                <div className="flex items-center space-x-3">
                  <MagnifyingGlassIcon className="h-6 w-6 text-green-400" />
                  <div>
                    <h3 className="text-white font-medium">Competitor Analysis</h3>
                    <p className="text-gray-300 text-sm">Research competition</p>
                  </div>
                </div>
              </div>

              <div className="bg-purple-600/20 hover:bg-purple-600/30 border border-purple-500/50 rounded-lg p-4 transition-colors group cursor-pointer">
                <div className="flex items-center space-x-3">
                  <FolderIcon className="h-6 w-6 text-purple-400" />
                  <div>
                    <h3 className="text-white font-medium">New Project</h3>
                    <p className="text-gray-300 text-sm">Start a new SEO project</p>
                  </div>
                </div>
              </div>

              <div className="bg-orange-600/20 hover:bg-orange-600/30 border border-orange-500/50 rounded-lg p-4 transition-colors group cursor-pointer">
                <div className="flex items-center space-x-3">
                  <ChartBarIcon className="h-6 w-6 text-orange-400" />
                  <div>
                    <h3 className="text-white font-medium">Analytics</h3>
                    <p className="text-gray-300 text-sm">View performance metrics</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
              <h3 className="text-xl font-bold text-white mb-4">Recent Activity</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-gray-300 text-sm">Generated content for "digital marketing strategies"</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-300 text-sm">Created new project "E-commerce SEO"</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span className="text-gray-300 text-sm">Analyzed competitor content</span>
                </div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
              <h3 className="text-xl font-bold text-white mb-4">Performance Insights</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-300 text-sm">Content Quality Score</span>
                  <span className="text-green-400 font-semibold">+12% this week</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-300 text-sm">Keyword Rankings</span>
                  <span className="text-blue-400 font-semibold">+8 positions</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-300 text-sm">Content Generation</span>
                  <span className="text-purple-400 font-semibold">23 this month</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}