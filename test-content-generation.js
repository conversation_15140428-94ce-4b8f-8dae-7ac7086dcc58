#!/usr/bin/env node

/**
 * Content Generation End-to-End Test
 * Tests the fixed /api/seo/generate-content endpoint
 */

import fetch from 'node-fetch';

const API_URL = 'http://localhost:5000/api/seo/generate-content';
const TEST_DATA = {
  keyword: 'artificial intelligence trends',
  target_country: 'United States',
  content_type: 'blog-post',
  tone: 'professional',
  length: 'short'
};

async function testContentGeneration() {
  console.log('🧪 Testing Content Generation API...');
  console.log('📋 Test Data:', JSON.stringify(TEST_DATA, null, 2));
  
  try {
    console.log('🚀 Sending request to:', API_URL);
    
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(TEST_DATA)
    });
    
    console.log('📊 Response Status:', response.status);
    console.log('📊 Response Headers:', Object.fromEntries(response.headers.entries()));
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error:', errorText);
      return false;
    }
    
    const result = await response.json();
    
    console.log('✅ API Response Structure:');
    console.log('  - success:', result.success);
    console.log('  - content.keyword:', result.content?.keyword);
    console.log('  - content.target_country:', result.content?.target_country);
    console.log('  - content.metadata.word_count:', result.content?.metadata?.word_count);
    console.log('  - content.metadata.keyword_density:', result.content?.metadata?.keyword_density);
    console.log('  - content.metadata.model:', result.content?.metadata?.model);
    
    if (result.success && result.content && result.content.body) {
      const contentPreview = result.content.body.substring(0, 200);
      console.log('📝 Content Preview:');
      console.log(contentPreview + '...');
      
      console.log('\n🎉 Content Generation Test PASSED!');
      console.log('📈 SEO Analysis:');
      console.log('  - Target Word Count:', result.seo_analysis?.target_word_count);
      console.log('  - Actual Word Count:', result.seo_analysis?.actual_word_count);
      console.log('  - Keyword Density:', result.seo_analysis?.keyword_density);
      console.log('  - Keyword Occurrences:', result.seo_analysis?.keyword_occurrences);
      
      return true;
    } else {
      console.error('❌ Invalid response structure');
      console.error('Full response:', JSON.stringify(result, null, 2));
      return false;
    }
    
  } catch (error) {
    console.error('❌ Test Failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 Tip: Make sure the backend server is running on http://localhost:5000');
      console.error('    Run: cd backend && npm run dev');
    }
    
    return false;
  }
}

async function testHealthCheck() {
  console.log('\n🏥 Testing Health Check...');
  
  try {
    const response = await fetch('http://localhost:5000/api/health');
    
    if (response.ok) {
      const health = await response.json();
      console.log('✅ Health Check Passed');
      console.log('   Service:', health.service);
      console.log('   Status:', health.status);
      console.log('   Version:', health.version);
      return true;
    } else {
      console.error('❌ Health Check Failed:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ Health Check Error:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🔧 SEO SAAS Content Generation - End-to-End Test');
  console.log('='.repeat(50));
  
  const healthOk = await testHealthCheck();
  if (!healthOk) {
    console.log('\n❌ Health check failed. Please start the backend server first.');
    process.exit(1);
  }
  
  const contentOk = await testContentGeneration();
  
  console.log('\n' + '='.repeat(50));
  
  if (contentOk) {
    console.log('🎉 ALL TESTS PASSED! Content generation is working correctly.');
    console.log('💡 Next steps:');
    console.log('   1. Start the frontend: npm run dev');
    console.log('   2. Visit: http://localhost:3000/content-generator');
    console.log('   3. Test the UI with real content generation');
  } else {
    console.log('❌ TESTS FAILED! Please check the server configuration.');
    process.exit(1);
  }
}

// Run the tests
runTests().catch(console.error);