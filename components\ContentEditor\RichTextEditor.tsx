/**
 * Rich Text Editor Component
 * Advanced markdown editor with real-time preview and SEO-focused features
 */

'use client'

import React, { useState, useCallback, useRef, useEffect } from 'react'
import { 
  EyeIcon,
  PencilIcon,
  DocumentTextIcon,
  BoltIcon,
  MagnifyingGlassIcon,
  CodeBracketIcon,
  PhotoIcon,
  ListBulletIcon,
  NumberedListIcon,
  LinkIcon,
  TableCellsIcon
} from '@heroicons/react/24/outline'
import Button from '@/components/UI/Button'
import Card from '@/components/UI/Card'
import Input from '@/components/UI/Input'
import LoadingSpinner from '@/components/UI/LoadingSpinner'

interface EditorContent {
  title: string
  content: string
  metaTitle: string
  metaDescription: string
  keyword: string
  contentType: string
  status: 'draft' | 'published' | 'scheduled'
}

interface SEOAnalysis {
  overallScore: number
  titleScore: number
  metaScore: number
  keywordDensity: number
  readabilityScore: number
  wordCount: number
  recommendations: string[]
  keywordUsage: {
    inTitle: boolean
    inMeta: boolean
    inFirstParagraph: boolean
    density: number
    frequency: number
  }
}

interface RichTextEditorProps {
  content: EditorContent
  onChange: (content: EditorContent) => void
  showPreview: boolean
  isAnalyzing: boolean
  seoAnalysis: SEOAnalysis | null
}

export default function RichTextEditor({
  content,
  onChange,
  showPreview,
  isAnalyzing,
  seoAnalysis
}: RichTextEditorProps) {
  const [activeTab, setActiveTab] = useState<'content' | 'meta'>('content')
  const [showToolbar, setShowToolbar] = useState(true)
  const [wordCount, setWordCount] = useState(0)
  const [cursorPosition, setCursorPosition] = useState(0)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    const words = content.content.split(/\s+/).filter(word => word.length > 0)
    setWordCount(words.length)
  }, [content.content])

  const handleContentChange = useCallback((field: keyof EditorContent, value: string) => {
    onChange({
      ...content,
      [field]: value
    })
  }, [content, onChange])

  const insertMarkdown = useCallback((syntax: string, placeholder = '') => {
    const textarea = textareaRef.current
    if (!textarea) return

    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const selectedText = content.content.substring(start, end)
    const replacement = selectedText || placeholder
    
    let newText = ''
    if (syntax.includes('$1')) {
      newText = syntax.replace('$1', replacement)
    } else {
      newText = syntax + replacement + syntax
    }

    const newContent = 
      content.content.substring(0, start) +
      newText +
      content.content.substring(end)

    handleContentChange('content', newContent)

    // Reset cursor position
    setTimeout(() => {
      textarea.focus()
      textarea.setSelectionRange(
        start + newText.length,
        start + newText.length
      )
    }, 0)
  }, [content.content, handleContentChange])

  const formatMarkdown = (text: string) => {
    return text
      .replace(/^# (.*$)/gim, '<h1 class="text-3xl font-bold mb-4 text-gray-900 dark:text-gray-100">$1</h1>')
      .replace(/^## (.*$)/gim, '<h2 class="text-2xl font-semibold mb-3 text-gray-900 dark:text-gray-100">$1</h2>')
      .replace(/^### (.*$)/gim, '<h3 class="text-xl font-medium mb-2 text-gray-900 dark:text-gray-100">$1</h3>')
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
      .replace(/`(.*?)`/g, '<code class="bg-gray-100 dark:bg-gray-800 px-1 rounded text-sm">$1</code>')
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-600 dark:text-blue-400 hover:underline">$1</a>')
      .replace(/^\* (.+)$/gm, '<li class="ml-4 list-disc">$1</li>')
      .replace(/^\d+\. (.+)$/gm, '<li class="ml-4 list-decimal">$1</li>')
      .replace(/\n\n/g, '</p><p class="mb-4 text-gray-700 dark:text-gray-300">')
      .replace(/^(.+)$/gm, '<p class="mb-4 text-gray-700 dark:text-gray-300">$1</p>')
  }

  const highlightKeywords = (text: string) => {
    if (!content.keyword) return text
    const regex = new RegExp(`(${content.keyword})`, 'gi')
    return text.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">$1</mark>')
  }

  const getKeywordDensityColor = () => {
    if (!seoAnalysis) return 'text-gray-500'
    const density = seoAnalysis.keywordDensity
    if (density >= 1 && density <= 3) return 'text-green-600 dark:text-green-400'
    if (density >= 0.5 && density <= 5) return 'text-yellow-600 dark:text-yellow-400'
    return 'text-red-600 dark:text-red-400'
  }

  const toolbarButtons = [
    { icon: BoltIcon, label: 'Bold', syntax: '**', placeholder: 'bold text' },
    { icon: PencilIcon, label: 'Italic', syntax: '*', placeholder: 'italic text' },
    { icon: CodeBracketIcon, label: 'Code', syntax: '`', placeholder: 'code' },
    { icon: LinkIcon, label: 'Link', syntax: '[link text](url)', placeholder: '' },
    { icon: PhotoIcon, label: 'Image', syntax: '![alt text](image-url)', placeholder: '' },
    { icon: ListBulletIcon, label: 'Bullet List', syntax: '* ', placeholder: 'list item' },
    { icon: NumberedListIcon, label: 'Numbered List', syntax: '1. ', placeholder: 'list item' },
    { icon: DocumentTextIcon, label: 'Heading 1', syntax: '# ', placeholder: 'heading' },
    { icon: DocumentTextIcon, label: 'Heading 2', syntax: '## ', placeholder: 'heading' },
    { icon: DocumentTextIcon, label: 'Heading 3', syntax: '### ', placeholder: 'heading' },
  ]

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
        <button
          onClick={() => setActiveTab('content')}
          className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'content'
              ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
              : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
          }`}
        >
          Content
        </button>
        <button
          onClick={() => setActiveTab('meta')}
          className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'meta'
              ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
              : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
          }`}
        >
          SEO Meta
        </button>
      </div>

      {activeTab === 'content' && (
        <div className="space-y-4">
          {/* Title Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Title
            </label>
            <Input
              type="text"
              value={content.title}
              onChange={(e) => handleContentChange('title', e.target.value)}
              placeholder="Enter your content title..."
              className="text-lg font-medium"
            />
            {seoAnalysis && (
              <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Length: {content.title.length}/60 characters • 
                SEO Score: <span className={getKeywordDensityColor()}>{seoAnalysis.titleScore}%</span>
              </div>
            )}
          </div>

          {/* Content Editor */}
          <Card className="p-0 overflow-hidden">
            {/* Toolbar */}
            {showToolbar && (
              <div className="border-b border-gray-200 dark:border-gray-700 p-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-1">
                    {toolbarButtons.map((button, index) => (
                      <Button
                        key={index}
                        variant="ghost"
                        size="sm"
                        onClick={() => insertMarkdown(button.syntax, button.placeholder)}
                        title={button.label}
                        className="p-2"
                      >
                        <button.icon className="h-4 w-4" />
                      </Button>
                    ))}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowToolbar(false)}
                    className="text-gray-400"
                  >
                    Hide Toolbar
                  </Button>
                </div>
              </div>
            )}

            {!showToolbar && (
              <div className="border-b border-gray-200 dark:border-gray-700 p-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowToolbar(true)}
                  className="text-gray-400 text-xs"
                >
                  Show Toolbar
                </Button>
              </div>
            )}

            <div className="flex">
              {/* Editor */}
              <div className={`${showPreview ? 'w-1/2' : 'w-full'} border-r border-gray-200 dark:border-gray-700`}>
                <textarea
                  ref={textareaRef}
                  value={content.content}
                  onChange={(e) => {
                    handleContentChange('content', e.target.value)
                    setCursorPosition(e.target.selectionStart)
                  }}
                  placeholder="Start writing your content using Markdown...

Example:
# Main Heading
## Subheading
**Bold text** and *italic text*

* Bullet points
* Another point

[Link text](https://example.com)"
                  className="w-full h-96 p-4 border-0 resize-none focus:outline-none bg-transparent text-gray-900 dark:text-gray-100 font-mono text-sm leading-relaxed"
                  onSelect={(e) => setCursorPosition(e.currentTarget.selectionStart)}
                />
              </div>

              {/* Preview */}
              {showPreview && (
                <div className="w-1/2 p-4 bg-gray-50 dark:bg-gray-800 overflow-auto h-96">
                  <div className="prose prose-sm max-w-none dark:prose-invert">
                    {content.content ? (
                      <div
                        dangerouslySetInnerHTML={{
                          __html: highlightKeywords(formatMarkdown(content.content))
                        }}
                      />
                    ) : (
                      <p className="text-gray-500 dark:text-gray-400 italic">
                        Preview will appear here as you type...
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Status Bar */}
            <div className="border-t border-gray-200 dark:border-gray-700 px-4 py-2 bg-gray-50 dark:bg-gray-800">
              <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                <div className="flex items-center space-x-4">
                  <span>Words: {wordCount.toLocaleString()}</span>
                  <span>Characters: {content.content.length.toLocaleString()}</span>
                  <span>Position: {cursorPosition}</span>
                  {seoAnalysis && (
                    <span className={getKeywordDensityColor()}>
                      Keyword Density: {seoAnalysis.keywordDensity.toFixed(1)}%
                    </span>
                  )}
                </div>
                {isAnalyzing && (
                  <div className="flex items-center space-x-2">
                    <LoadingSpinner size="sm" />
                    <span>Analyzing SEO...</span>
                  </div>
                )}
              </div>
            </div>
          </Card>
        </div>
      )}

      {activeTab === 'meta' && (
        <div className="space-y-4">
          {/* Keyword Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Target Keyword
            </label>
            <Input
              type="text"
              value={content.keyword}
              onChange={(e) => handleContentChange('keyword', e.target.value)}
              placeholder="Enter your target keyword..."
            />
            {seoAnalysis && content.keyword && (
              <div className="mt-2 text-xs space-y-1">
                <div className="flex justify-between">
                  <span>In Title:</span>
                  <span className={seoAnalysis.keywordUsage.inTitle ? 'text-green-600' : 'text-red-600'}>
                    {seoAnalysis.keywordUsage.inTitle ? '✓' : '✗'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>In Meta Description:</span>
                  <span className={seoAnalysis.keywordUsage.inMeta ? 'text-green-600' : 'text-red-600'}>
                    {seoAnalysis.keywordUsage.inMeta ? '✓' : '✗'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>In First Paragraph:</span>
                  <span className={seoAnalysis.keywordUsage.inFirstParagraph ? 'text-green-600' : 'text-red-600'}>
                    {seoAnalysis.keywordUsage.inFirstParagraph ? '✓' : '✗'}
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Meta Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Meta Title
            </label>
            <Input
              type="text"
              value={content.metaTitle}
              onChange={(e) => handleContentChange('metaTitle', e.target.value)}
              placeholder="SEO-optimized title for search engines..."
            />
            <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Length: {content.metaTitle.length}/60 characters
              {content.metaTitle.length > 60 && (
                <span className="text-red-600 ml-2">Too long</span>
              )}
            </div>
          </div>

          {/* Meta Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Meta Description
            </label>
            <textarea
              value={content.metaDescription}
              onChange={(e) => handleContentChange('metaDescription', e.target.value)}
              placeholder="Brief description that appears in search results..."
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
            />
            <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Length: {content.metaDescription.length}/160 characters
              {content.metaDescription.length > 160 && (
                <span className="text-red-600 ml-2">Too long</span>
              )}
              {seoAnalysis && (
                <span className="ml-4">
                  SEO Score: <span className={getKeywordDensityColor()}>{seoAnalysis.metaScore}%</span>
                </span>
              )}
            </div>
          </div>

          {/* Content Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Content Type
            </label>
            <select
              value={content.contentType}
              onChange={(e) => handleContentChange('contentType', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
            >
              <option value="blog-post">Blog Post</option>
              <option value="product-page">Product Page</option>
              <option value="landing-page">Landing Page</option>
              <option value="category-page">Category Page</option>
              <option value="how-to-guide">How-to Guide</option>
              <option value="listicle">Listicle</option>
              <option value="review">Review</option>
              <option value="comparison">Comparison</option>
            </select>
          </div>
        </div>
      )}
    </div>
  )
}