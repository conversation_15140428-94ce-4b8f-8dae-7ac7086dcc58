/**
 * Content Generation E2E Tests
 * 
 * Tests for content generation workflows including project creation,
 * content generation, AI thinking engine, and content management.
 */

import { test, expect, Page } from '@playwright/test';
import { loginAsUser, existingUser } from './auth.spec';

// Test data
const testProject = {
  name: 'E2E Test Project',
  description: 'A project created for E2E testing',
  niche: 'Technology',
  targetAudience: 'Developers',
  contentType: 'Blog Posts',
  tone: 'Professional',
  keywords: ['testing', 'automation', 'playwright']
};

const testContent = {
  title: 'Test Article Generation',
  prompt: 'Write a comprehensive guide about automated testing best practices',
  expectedMinLength: 500,
  expectedSections: ['Introduction', 'Best Practices', 'Conclusion']
};

test.describe('Content Generation Workflows', () => {
  
  test.beforeEach(async ({ page }) => {
    // Login as existing user
    await loginAsUser(page, existingUser.email, existingUser.password);
  });

  test.describe('Project Management', () => {
    
    test('should create a new project successfully', async ({ page }) => {
      // Navigate to projects page
      await page.click('[data-testid="projects-nav"]');
      await expect(page).toHaveURL('**/dashboard/projects');
      
      // Click create new project
      await page.click('[data-testid="create-project-button"]');
      await expect(page).toHaveURL('**/dashboard/projects/new');
      
      // Fill project form
      await page.fill('[data-testid="project-name-input"]', testProject.name);
      await page.fill('[data-testid="project-description-input"]', testProject.description);
      await page.selectOption('[data-testid="niche-select"]', testProject.niche);
      await page.selectOption('[data-testid="target-audience-select"]', testProject.targetAudience);
      await page.selectOption('[data-testid="content-type-select"]', testProject.contentType);
      await page.selectOption('[data-testid="tone-select"]', testProject.tone);
      
      // Add keywords
      for (const keyword of testProject.keywords) {
        await page.fill('[data-testid="keyword-input"]', keyword);
        await page.click('[data-testid="add-keyword-button"]');
        await expect(page.locator(`[data-testid="keyword-tag-${keyword}"]`)).toBeVisible();
      }
      
      // Submit form
      await page.click('[data-testid="create-project-submit"]');
      
      // Should redirect to project dashboard
      await expect(page).toHaveURL('**/dashboard/projects/**');
      await expect(page.locator('[data-testid="project-name"]')).toContainText(testProject.name);
    });

    test('should validate required project fields', async ({ page }) => {
      await page.goto('/dashboard/projects/new');
      
      // Try to submit without filling required fields
      await page.click('[data-testid="create-project-submit"]');
      
      // Should show validation errors
      await expect(page.locator('[data-testid="project-name-error"]')).toContainText('Project name is required');
      await expect(page.locator('[data-testid="niche-error"]')).toContainText('Please select a niche');
    });

    test('should edit existing project', async ({ page }) => {
      // Go to projects page and select first project
      await page.goto('/dashboard/projects');
      await page.click('[data-testid="project-item"]:first-child');
      
      // Click edit button
      await page.click('[data-testid="edit-project-button"]');
      
      // Update project name
      await page.fill('[data-testid="project-name-input"]', 'Updated Project Name');
      await page.click('[data-testid="save-project-button"]');
      
      // Should show updated name
      await expect(page.locator('[data-testid="project-name"]')).toContainText('Updated Project Name');
    });

    test('should delete project with confirmation', async ({ page }) => {
      await page.goto('/dashboard/projects');
      
      // Click delete on first project
      await page.click('[data-testid="project-item"]:first-child [data-testid="delete-project-button"]');
      
      // Should show confirmation dialog
      await expect(page.locator('[data-testid="delete-confirmation-modal"]')).toBeVisible();
      
      // Cancel deletion
      await page.click('[data-testid="cancel-delete-button"]');
      await expect(page.locator('[data-testid="delete-confirmation-modal"]')).not.toBeVisible();
      
      // Try delete again and confirm
      await page.click('[data-testid="project-item"]:first-child [data-testid="delete-project-button"]');
      await page.click('[data-testid="confirm-delete-button"]');
      
      // Should remove project from list
      await expect(page.locator('[data-testid="project-deleted-message"]')).toBeVisible();
    });
  });

  test.describe('Content Generation', () => {
    
    test('should generate content successfully', async ({ page }) => {
      // Navigate to content generation page
      await page.click('[data-testid="content-generation-nav"]');
      await expect(page).toHaveURL('**/dashboard/content-generation');
      
      // Fill content generation form
      await page.fill('[data-testid="content-title-input"]', testContent.title);
      await page.fill('[data-testid="content-prompt-input"]', testContent.prompt);
      
      // Select content type and settings
      await page.selectOption('[data-testid="content-type-select"]', 'blog-post');
      await page.selectOption('[data-testid="content-length-select"]', 'medium');
      await page.selectOption('[data-testid="content-tone-select"]', 'professional');
      
      // Start generation
      await page.click('[data-testid="generate-content-button"]');
      
      // Should show loading state
      await expect(page.locator('[data-testid="generation-loading"]')).toBeVisible();
      await expect(page.locator('[data-testid="generate-content-button"]')).toBeDisabled();
      
      // Wait for generation to complete (increase timeout for AI generation)
      await expect(page.locator('[data-testid="generated-content"]')).toBeVisible({ timeout: 60000 });
      
      // Verify generated content
      const generatedContent = await page.locator('[data-testid="generated-content"]').textContent();
      expect(generatedContent).toBeTruthy();
      expect(generatedContent!.length).toBeGreaterThan(testContent.expectedMinLength);
      
      // Check if expected sections are present
      for (const section of testContent.expectedSections) {
        await expect(page.locator('[data-testid="generated-content"]')).toContainText(section);
      }
    });

    test('should show AI thinking process', async ({ page }) => {
      await page.goto('/dashboard/content-generation');
      
      // Fill form and generate content
      await page.fill('[data-testid="content-title-input"]', testContent.title);
      await page.fill('[data-testid="content-prompt-input"]', testContent.prompt);
      await page.click('[data-testid="generate-content-button"]');
      
      // Should show AI thinking stages
      await expect(page.locator('[data-testid="ai-thinking-stage-1"]')).toBeVisible();
      await expect(page.locator('[data-testid="ai-thinking-stage-1"]')).toContainText('Understanding');
      
      await expect(page.locator('[data-testid="ai-thinking-stage-2"]')).toBeVisible();
      await expect(page.locator('[data-testid="ai-thinking-stage-2"]')).toContainText('Research');
      
      await expect(page.locator('[data-testid="ai-thinking-stage-3"]')).toBeVisible();
      await expect(page.locator('[data-testid="ai-thinking-stage-3"]')).toContainText('Structuring');
      
      // Should show progress bar
      await expect(page.locator('[data-testid="generation-progress"]')).toBeVisible();
      
      // Progress should increase over time
      const initialProgress = await page.locator('[data-testid="generation-progress"]').getAttribute('value');
      await page.waitForTimeout(2000);
      const updatedProgress = await page.locator('[data-testid="generation-progress"]').getAttribute('value');
      expect(parseInt(updatedProgress!)).toBeGreaterThan(parseInt(initialProgress!));
    });

    test('should handle generation errors gracefully', async ({ page }) => {
      await page.goto('/dashboard/content-generation');
      
      // Fill form with problematic prompt
      await page.fill('[data-testid="content-title-input"]', 'Invalid Content');
      await page.fill('[data-testid="content-prompt-input"]', ''); // Empty prompt
      
      await page.click('[data-testid="generate-content-button"]');
      
      // Should show error message
      await expect(page.locator('[data-testid="generation-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="generation-error"]')).toContainText('Please provide a content prompt');
    });

    test('should save generated content', async ({ page }) => {
      await page.goto('/dashboard/content-generation');
      
      // Generate content (mock generation for speed)
      await page.fill('[data-testid="content-title-input"]', testContent.title);
      await page.fill('[data-testid="content-prompt-input"]', testContent.prompt);
      await page.click('[data-testid="generate-content-button"]');
      
      // Wait for generation to complete
      await expect(page.locator('[data-testid="generated-content"]')).toBeVisible({ timeout: 60000 });
      
      // Save content
      await page.click('[data-testid="save-content-button"]');
      
      // Should show save success message
      await expect(page.locator('[data-testid="save-success-message"]')).toBeVisible();
      await expect(page.locator('[data-testid="save-success-message"]')).toContainText('Content saved successfully');
      
      // Should navigate to content library
      await expect(page).toHaveURL('**/dashboard/content');
      
      // Content should appear in library
      await expect(page.locator(`[data-testid="content-item-${testContent.title}"]`)).toBeVisible();
    });

    test('should edit generated content before saving', async ({ page }) => {
      await page.goto('/dashboard/content-generation');
      
      // Generate content
      await page.fill('[data-testid="content-title-input"]', testContent.title);
      await page.fill('[data-testid="content-prompt-input"]', testContent.prompt);
      await page.click('[data-testid="generate-content-button"]');
      
      await expect(page.locator('[data-testid="generated-content"]')).toBeVisible({ timeout: 60000 });
      
      // Edit the generated content
      await page.click('[data-testid="edit-content-button"]');
      
      // Should show content editor
      await expect(page.locator('[data-testid="content-editor"]')).toBeVisible();
      
      // Make changes to content
      await page.fill('[data-testid="content-editor"]', 'This is edited content for testing purposes.');
      
      // Save changes
      await page.click('[data-testid="save-changes-button"]');
      
      // Should show updated content
      await expect(page.locator('[data-testid="generated-content"]')).toContainText('This is edited content for testing purposes.');
    });

    test('should regenerate content with different parameters', async ({ page }) => {
      await page.goto('/dashboard/content-generation');
      
      // Generate initial content
      await page.fill('[data-testid="content-title-input"]', testContent.title);
      await page.fill('[data-testid="content-prompt-input"]', testContent.prompt);
      await page.selectOption('[data-testid="content-tone-select"]', 'professional');
      await page.click('[data-testid="generate-content-button"]');
      
      await expect(page.locator('[data-testid="generated-content"]')).toBeVisible({ timeout: 60000 });
      const firstContent = await page.locator('[data-testid="generated-content"]').textContent();
      
      // Change parameters and regenerate
      await page.selectOption('[data-testid="content-tone-select"]', 'casual');
      await page.click('[data-testid="regenerate-content-button"]');
      
      // Should show loading again
      await expect(page.locator('[data-testid="generation-loading"]')).toBeVisible();
      
      // Wait for new generation
      await expect(page.locator('[data-testid="generated-content"]')).toBeVisible({ timeout: 60000 });
      const secondContent = await page.locator('[data-testid="generated-content"]').textContent();
      
      // Content should be different
      expect(secondContent).not.toBe(firstContent);
    });

    test('should handle multiple content generation requests', async ({ page }) => {
      await page.goto('/dashboard/content-generation');
      
      // Start first generation
      await page.fill('[data-testid="content-title-input"]', 'First Article');
      await page.fill('[data-testid="content-prompt-input"]', 'Write about first topic');
      await page.click('[data-testid="generate-content-button"]');
      
      // Should show loading
      await expect(page.locator('[data-testid="generation-loading"]')).toBeVisible();
      
      // Try to start another generation (should be prevented)
      await page.fill('[data-testid="content-title-input"]', 'Second Article');
      await page.fill('[data-testid="content-prompt-input"]', 'Write about second topic');
      
      // Generate button should be disabled
      await expect(page.locator('[data-testid="generate-content-button"]')).toBeDisabled();
      
      // Wait for first generation to complete
      await expect(page.locator('[data-testid="generated-content"]')).toBeVisible({ timeout: 60000 });
      
      // Now second generation should be possible
      await expect(page.locator('[data-testid="generate-content-button"]')).toBeEnabled();
    });
  });

  test.describe('Content Library', () => {
    
    test('should display content library', async ({ page }) => {
      await page.goto('/dashboard/content');
      
      // Should show content library
      await expect(page.locator('[data-testid="content-library"]')).toBeVisible();
      
      // Should show search and filter options
      await expect(page.locator('[data-testid="content-search"]')).toBeVisible();
      await expect(page.locator('[data-testid="content-filter"]')).toBeVisible();
      
      // Should show content items
      await expect(page.locator('[data-testid="content-item"]')).toHaveCount({ min: 1 });
    });

    test('should search content', async ({ page }) => {
      await page.goto('/dashboard/content');
      
      // Search for specific content
      await page.fill('[data-testid="content-search"]', 'test');
      await page.click('[data-testid="search-button"]');
      
      // Should filter results
      const searchResults = page.locator('[data-testid="content-item"]');
      await expect(searchResults).toHaveCount({ min: 1 });
      
      // Each result should contain search term
      const resultTexts = await searchResults.allTextContents();
      resultTexts.forEach(text => {
        expect(text.toLowerCase()).toContain('test');
      });
    });

    test('should filter content by type', async ({ page }) => {
      await page.goto('/dashboard/content');
      
      // Filter by blog posts
      await page.selectOption('[data-testid="content-type-filter"]', 'blog-post');
      
      // Should show only blog posts
      const blogPosts = page.locator('[data-testid="content-item"][data-type="blog-post"]');
      await expect(blogPosts).toHaveCount({ min: 1 });
      
      // Should not show other content types
      const otherTypes = page.locator('[data-testid="content-item"]:not([data-type="blog-post"])');
      await expect(otherTypes).toHaveCount(0);
    });

    test('should view content details', async ({ page }) => {
      await page.goto('/dashboard/content');
      
      // Click on first content item
      await page.click('[data-testid="content-item"]:first-child');
      
      // Should show content details
      await expect(page.locator('[data-testid="content-details"]')).toBeVisible();
      await expect(page.locator('[data-testid="content-title"]')).toBeVisible();
      await expect(page.locator('[data-testid="content-body"]')).toBeVisible();
      await expect(page.locator('[data-testid="content-metadata"]')).toBeVisible();
    });

    test('should export content', async ({ page }) => {
      await page.goto('/dashboard/content');
      
      // Select content item
      await page.click('[data-testid="content-item"]:first-child');
      
      // Click export button
      await page.click('[data-testid="export-content-button"]');
      
      // Should show export options
      await expect(page.locator('[data-testid="export-options"]')).toBeVisible();
      
      // Export as PDF
      const downloadPromise = page.waitForEvent('download');
      await page.click('[data-testid="export-pdf-button"]');
      const download = await downloadPromise;
      
      // Should download PDF file
      expect(download.suggestedFilename()).toContain('.pdf');
    });

    test('should delete content', async ({ page }) => {
      await page.goto('/dashboard/content');
      
      // Click delete on first content item
      await page.click('[data-testid="content-item"]:first-child [data-testid="delete-content-button"]');
      
      // Should show confirmation
      await expect(page.locator('[data-testid="delete-confirmation"]')).toBeVisible();
      
      // Confirm deletion
      await page.click('[data-testid="confirm-delete-button"]');
      
      // Should show success message
      await expect(page.locator('[data-testid="delete-success"]')).toBeVisible();
    });
  });

  test.describe('Performance and Responsiveness', () => {
    
    test('should handle large content generation', async ({ page }) => {
      await page.goto('/dashboard/content-generation');
      
      // Generate long content
      await page.fill('[data-testid="content-title-input"]', 'Large Article');
      await page.fill('[data-testid="content-prompt-input"]', 'Write a comprehensive 5000-word guide about software development');
      await page.selectOption('[data-testid="content-length-select"]', 'long');
      
      await page.click('[data-testid="generate-content-button"]');
      
      // Should handle long generation time
      await expect(page.locator('[data-testid="generated-content"]')).toBeVisible({ timeout: 120000 });
      
      // Content should be substantial
      const content = await page.locator('[data-testid="generated-content"]').textContent();
      expect(content!.length).toBeGreaterThan(2000);
    });

    test('should be responsive on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      await page.goto('/dashboard/content-generation');
      
      // Form should be usable on mobile
      await expect(page.locator('[data-testid="content-generation-form"]')).toBeVisible();
      
      // All form elements should be visible
      await expect(page.locator('[data-testid="content-title-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="content-prompt-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="generate-content-button"]')).toBeVisible();
    });
  });
});

// Helper function to create a test project
async function createTestProject(page: Page, project: typeof testProject) {
  await page.goto('/dashboard/projects/new');
  
  await page.fill('[data-testid="project-name-input"]', project.name);
  await page.fill('[data-testid="project-description-input"]', project.description);
  await page.selectOption('[data-testid="niche-select"]', project.niche);
  await page.selectOption('[data-testid="target-audience-select"]', project.targetAudience);
  await page.selectOption('[data-testid="content-type-select"]', project.contentType);
  await page.selectOption('[data-testid="tone-select"]', project.tone);
  
  await page.click('[data-testid="create-project-submit"]');
  
  await expect(page).toHaveURL('**/dashboard/projects/**');
}

// Export helper for use in other test files
export { createTestProject, testProject, testContent };