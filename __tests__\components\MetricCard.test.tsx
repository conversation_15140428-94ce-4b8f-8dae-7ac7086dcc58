import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import Metric<PERSON>ard from '@/components/UI/MetricCard';
import { DocumentTextIcon } from '@heroicons/react/24/outline';

describe('MetricCard', () => {
  const defaultProps = {
    title: 'Test Metric',
    value: 42,
    icon: DocumentTextIcon,
    loading: false,
  };

  it('renders the metric card with title and value', () => {
    render(<MetricCard {...defaultProps} />);

    expect(screen.getByText('Test Metric')).toBeInTheDocument();
    expect(screen.getByText('42')).toBeInTheDocument();
  });

  it('displays loading state correctly', () => {
    render(<MetricCard {...defaultProps} loading={true} />);

    // Check for loading skeletons
    const skeletons = screen.getAllByTestId('skeleton');
    expect(skeletons.length).toBeGreaterThan(0);
  });

  it('renders change information when provided', () => {
    const propsWithChange = {
      ...defaultProps,
      change: {
        value: 12.5,
        type: 'increase' as const,
        period: 'vs last month',
      },
    };

    render(<MetricCard {...propsWithChange} />);

    expect(screen.getByText('12.5%')).toBeInTheDocument();
    expect(screen.getByText('increase')).toBeInTheDocument();
    expect(screen.getByText('vs last month')).toBeInTheDocument();
  });

  it('handles different change types', () => {
    const propsWithDecrease = {
      ...defaultProps,
      change: {
        value: 5.2,
        type: 'decrease' as const,
      },
    };

    render(<MetricCard {...propsWithDecrease} />);

    expect(screen.getByText('5.2%')).toBeInTheDocument();
    expect(screen.getByText('decrease')).toBeInTheDocument();
  });

  it('renders icon correctly', () => {
    render(<MetricCard {...defaultProps} />);

    // Check if icon is rendered (it should be in the DOM)
    const iconElement = screen.getByTestId('metric-icon');
    expect(iconElement).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <MetricCard {...defaultProps} className="custom-class" />
    );

    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('handles string values', () => {
    const propsWithStringValue = {
      ...defaultProps,
      value: '#1',
    };

    render(<MetricCard {...propsWithStringValue} />);

    expect(screen.getByText('#1')).toBeInTheDocument();
  });

  it('handles large numbers correctly', () => {
    const propsWithLargeNumber = {
      ...defaultProps,
      value: 1234567,
    };

    render(<MetricCard {...propsWithLargeNumber} />);

    // Should format large numbers
    expect(screen.getByText('1,234,567')).toBeInTheDocument();
  });

  it('renders without change information', () => {
    render(<MetricCard {...defaultProps} />);

    // Should not show change indicators when no change prop is provided
    expect(screen.queryByText('%')).not.toBeInTheDocument();
    expect(screen.queryByText('increase')).not.toBeInTheDocument();
    expect(screen.queryByText('decrease')).not.toBeInTheDocument();
  });

  it('handles zero values', () => {
    const propsWithZero = {
      ...defaultProps,
      value: 0,
    };

    render(<MetricCard {...propsWithZero} />);

    expect(screen.getByText('0')).toBeInTheDocument();
  });

  it('handles negative values', () => {
    const propsWithNegative = {
      ...defaultProps,
      value: -15,
    };

    render(<MetricCard {...propsWithNegative} />);

    expect(screen.getByText('-15')).toBeInTheDocument();
  });

  it('has proper accessibility attributes', () => {
    render(<MetricCard {...defaultProps} />);

    const cardElement = screen.getByRole('article');
    expect(cardElement).toBeInTheDocument();
    expect(cardElement).toHaveAttribute('aria-label', 'Test Metric: 42');
  });

  it('renders with custom description', () => {
    const propsWithDescription = {
      ...defaultProps,
      description: 'This is a test metric description',
    };

    render(<MetricCard {...propsWithDescription} />);

    expect(screen.getByText('This is a test metric description')).toBeInTheDocument();
  });
});