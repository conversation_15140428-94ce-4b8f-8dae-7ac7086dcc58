/**
 * Content Structure Analyzer Component
 * Analyzes and visualizes content structure, heading hierarchy, and flow
 */

'use client'

import React, { useState, useMemo } from 'react'
import Card from '@/components/UI/Card'
import Button from '@/components/UI/Button'
import Badge from '@/components/UI/Badge'
import {
  XMarkIcon,
  DocumentTextIcon,
  Bars3Icon,
  ListBulletIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  EyeIcon,
  LightBulbIcon,
  ArrowDownIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline'

interface ContentStructureAnalyzerProps {
  content: string
  onClose: () => void
}

interface HeadingItem {
  level: number
  text: string
  line: number
  wordCount: number
}

interface StructureAnalysis {
  headings: HeadingItem[]
  paragraphs: string[]
  wordCount: number
  averageParagraphLength: number
  readingTime: number
  structureScore: number
  issues: string[]
  recommendations: string[]
}

export default function ContentStructureAnalyzer({
  content,
  onClose
}: ContentStructureAnalyzerProps) {
  const [activeTab, setActiveTab] = useState<'structure' | 'analysis' | 'recommendations'>('structure')

  const analysis = useMemo((): StructureAnalysis => {
    const lines = content.split('\n')
    const headings: HeadingItem[] = []
    const paragraphs: string[] = []
    
    lines.forEach((line, index) => {
      const headingMatch = line.match(/^(#{1,6})\s+(.+)$/)
      if (headingMatch) {
        headings.push({
          level: headingMatch[1].length,
          text: headingMatch[2],
          line: index + 1,
          wordCount: headingMatch[2].split(/\s+/).length
        })
      } else if (line.trim().length > 0 && !line.startsWith('#')) {
        paragraphs.push(line.trim())
      }
    })

    const wordCount = content.split(/\s+/).filter(w => w.length > 0).length
    const averageParagraphLength = paragraphs.length > 0 
      ? paragraphs.reduce((sum, p) => sum + p.split(/\s+/).length, 0) / paragraphs.length
      : 0
    const readingTime = Math.ceil(wordCount / 200) // 200 words per minute

    // Calculate structure score
    let structureScore = 100
    const issues: string[] = []
    const recommendations: string[] = []

    // Check heading hierarchy
    let prevLevel = 0
    let hasH1 = false
    headings.forEach((heading, index) => {
      if (heading.level === 1) {
        hasH1 = true
        if (index !== 0) {
          issues.push('H1 should be the first heading')
          structureScore -= 10
        }
      }
      
      if (heading.level > prevLevel + 1 && prevLevel > 0) {
        issues.push(`Heading level jumps from H${prevLevel} to H${heading.level}`)
        structureScore -= 5
      }
      
      if (heading.wordCount > 10) {
        issues.push(`Heading "${heading.text}" is too long (${heading.wordCount} words)`)
        structureScore -= 5
      }
      
      prevLevel = heading.level
    })

    if (!hasH1) {
      issues.push('Missing H1 heading')
      structureScore -= 15
    }

    if (headings.length < 3) {
      issues.push('Too few headings for content structure')
      structureScore -= 10
      recommendations.push('Add more headings to break up your content')
    }

    if (averageParagraphLength > 50) {
      issues.push('Paragraphs are too long on average')
      structureScore -= 10
      recommendations.push('Break up long paragraphs for better readability')
    }

    if (wordCount < 300) {
      recommendations.push('Consider adding more content (aim for at least 300 words)')
    }

    if (headings.length === 0) {
      recommendations.push('Add headings to structure your content')
    }

    if (paragraphs.length > 20 && headings.length < 5) {
      recommendations.push('Add more subheadings to break up long sections')
    }

    return {
      headings,
      paragraphs,
      wordCount,
      averageParagraphLength,
      readingTime,
      structureScore: Math.max(0, structureScore),
      issues,
      recommendations
    }
  }, [content])

  const getHeadingIcon = (level: number) => {
    const baseClasses = "h-4 w-4"
    switch (level) {
      case 1: return <div className={`${baseClasses} bg-blue-600 rounded`} />
      case 2: return <div className={`${baseClasses} bg-green-600 rounded`} />
      case 3: return <div className={`${baseClasses} bg-yellow-600 rounded`} />
      case 4: return <div className={`${baseClasses} bg-purple-600 rounded`} />
      case 5: return <div className={`${baseClasses} bg-pink-600 rounded`} />
      case 6: return <div className={`${baseClasses} bg-gray-600 rounded`} />
      default: return <DocumentTextIcon className={baseClasses} />
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 dark:text-green-400'
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400'
    return 'text-red-600 dark:text-red-400'
  }

  const renderHeadingTree = () => {
    return (
      <div className="space-y-2">
        {analysis.headings.map((heading, index) => {
          const indent = (heading.level - 1) * 20
          return (
            <div
              key={index}
              className="flex items-center p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              style={{ marginLeft: `${indent}px` }}
            >
              <div className="flex items-center space-x-3 flex-1">
                {getHeadingIcon(heading.level)}
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {heading.text}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    H{heading.level} • Line {heading.line} • {heading.wordCount} words
                  </div>
                </div>
              </div>
              {heading.wordCount > 10 && (
                <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500" title="Heading might be too long" />
              )}
            </div>
          )
        })}
        
        {analysis.headings.length === 0 && (
          <div className="text-center py-6 text-gray-500 dark:text-gray-400">
            <DocumentTextIcon className="h-8 w-8 mx-auto mb-2" />
            <p>No headings found in content</p>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        {/* Backdrop */}
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={onClose}
        />

        {/* Modal */}
        <div className="relative w-full max-w-4xl bg-white dark:bg-gray-900 rounded-xl shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Content Structure Analysis
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Analyze content hierarchy, flow, and readability
              </p>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <XMarkIcon className="h-5 w-5" />
            </Button>
          </div>

          {/* Stats Overview */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {analysis.wordCount.toLocaleString()}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">Words</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {analysis.headings.length}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">Headings</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {analysis.paragraphs.length}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">Paragraphs</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {analysis.readingTime}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">Min Read</div>
              </div>
              
              <div className="text-center">
                <div className={`text-2xl font-bold ${getScoreColor(analysis.structureScore)}`}>
                  {analysis.structureScore}%
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">Structure Score</div>
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="border-b border-gray-200 dark:border-gray-700">
            <div className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('structure')}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === 'structure'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <Bars3Icon className="h-4 w-4 inline mr-2" />
                Structure
              </button>
              <button
                onClick={() => setActiveTab('analysis')}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === 'analysis'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <ChartBarIcon className="h-4 w-4 inline mr-2" />
                Analysis
              </button>
              <button
                onClick={() => setActiveTab('recommendations')}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === 'recommendations'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <LightBulbIcon className="h-4 w-4 inline mr-2" />
                Recommendations
              </button>
            </div>
          </div>

          {/* Tab Content */}
          <div className="p-6 max-h-96 overflow-y-auto">
            {activeTab === 'structure' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                    Heading Hierarchy
                  </h3>
                  {renderHeadingTree()}
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                    Heading Distribution
                  </h3>
                  <div className="space-y-2">
                    {[1, 2, 3, 4, 5, 6].map(level => {
                      const count = analysis.headings.filter(h => h.level === level).length
                      const percentage = analysis.headings.length > 0 ? (count / analysis.headings.length) * 100 : 0
                      return (
                        <div key={level} className="flex items-center space-x-3">
                          <div className="flex items-center space-x-2 w-16">
                            {getHeadingIcon(level)}
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              H{level}
                            </span>
                          </div>
                          <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div
                              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${percentage}%` }}
                            />
                          </div>
                          <span className="text-sm text-gray-600 dark:text-gray-400 w-12">
                            {count}
                          </span>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'analysis' && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card className="p-4">
                    <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-3">
                      Readability Metrics
                    </h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Average paragraph length</span>
                        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {Math.round(analysis.averageParagraphLength)} words
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Reading time</span>
                        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {analysis.readingTime} minutes
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Words per heading</span>
                        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {analysis.headings.length > 0 ? Math.round(analysis.wordCount / analysis.headings.length) : 0}
                        </span>
                      </div>
                    </div>
                  </Card>

                  <Card className="p-4">
                    <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-3">
                      Structure Quality
                    </h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Overall score</span>
                        <Badge variant={analysis.structureScore >= 80 ? 'success' : analysis.structureScore >= 60 ? 'warning' : 'error'}>
                          {analysis.structureScore}%
                        </Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Has H1 heading</span>
                        {analysis.headings.some(h => h.level === 1) ? (
                          <CheckCircleIcon className="h-4 w-4 text-green-500" />
                        ) : (
                          <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />
                        )}
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Structure issues</span>
                        <span className="text-sm font-medium text-red-600 dark:text-red-400">
                          {analysis.issues.length}
                        </span>
                      </div>
                    </div>
                  </Card>
                </div>

                {analysis.issues.length > 0 && (
                  <div>
                    <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-3">
                      Structure Issues
                    </h4>
                    <div className="space-y-2">
                      {analysis.issues.map((issue, index) => (
                        <div key={index} className="flex items-start space-x-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                          <ExclamationTriangleIcon className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-red-700 dark:text-red-300">
                            {issue}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'recommendations' && (
              <div className="space-y-4">
                {analysis.recommendations.length > 0 ? (
                  <div className="space-y-3">
                    {analysis.recommendations.map((recommendation, index) => (
                      <div key={index} className="flex items-start space-x-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <LightBulbIcon className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-blue-700 dark:text-blue-300">
                          {recommendation}
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <CheckCircleIcon className="h-12 w-12 text-green-500 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                      Great Structure!
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Your content structure looks good. No immediate recommendations.
                    </p>
                  </div>
                )}

                <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    General Best Practices
                  </h4>
                  <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                    <li>• Use only one H1 heading per page</li>
                    <li>• Keep headings concise (under 60 characters)</li>
                    <li>• Maintain logical heading hierarchy (H1 → H2 → H3)</li>
                    <li>• Break up long paragraphs (aim for 3-4 sentences)</li>
                    <li>• Use headings every 200-300 words</li>
                  </ul>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end p-6 border-t border-gray-200 dark:border-gray-700">
            <Button onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}