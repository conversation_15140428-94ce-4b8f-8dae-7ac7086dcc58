/**
 * ProjectSettings Component
 * Enterprise SEO SAAS - Project configuration and settings management
 */

import { useState } from 'react'
import { Project, IndustryType, ProjectSettings as SettingsType, ContentType } from '@/types/project'
import { getIndustryInfo, validateProjectName, getDefaultProjectSettings } from '@/utils/projectHelpers'
import {
  CogIcon,
  DocumentTextIcon,
  GlobeAltIcon,
  TagIcon,
  UserGroupIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'

interface ProjectSettingsProps {
  project: Project
  onProjectUpdate: (project: Project) => void
}

export default function ProjectSettings({ project, onProjectUpdate }: ProjectSettingsProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    name: project.name,
    description: project.description || '',
    industry: project.industry,
    website: project.website || '',
    targetLocation: project.targetLocation || '',
    targetLanguage: project.targetLanguage,
    settings: { ...project.settings }
  })
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const [isSaving, setIsSaving] = useState(false)

  const industryOptions: IndustryType[] = [
    'technology', 'healthcare', 'finance', 'education', 'ecommerce',
    'real_estate', 'automotive', 'food_beverage', 'fashion', 'travel',
    'fitness', 'marketing', 'consulting', 'manufacturing', 'retail',
    'construction', 'legal', 'entertainment', 'sports', 'nonprofit', 'other'
  ]

  const languageOptions = [
    { value: 'en', label: 'English' },
    { value: 'es', label: 'Spanish' },
    { value: 'fr', label: 'French' },
    { value: 'de', label: 'German' },
    { value: 'it', label: 'Italian' },
    { value: 'pt', label: 'Portuguese' },
    { value: 'ru', label: 'Russian' },
    { value: 'ja', label: 'Japanese' },
    { value: 'ko', label: 'Korean' },
    { value: 'zh', label: 'Chinese' }
  ]

  const contentTypeOptions: ContentType[] = [
    'blog_post', 'product_description', 'landing_page', 
    'meta_description', 'social_media', 'email_marketing'
  ]

  const toneOptions = [
    { value: 'professional', label: 'Professional' },
    { value: 'casual', label: 'Casual' },
    { value: 'technical', label: 'Technical' },
    { value: 'friendly', label: 'Friendly' },
    { value: 'persuasive', label: 'Persuasive' }
  ]

  const handleInputChange = (field: string, value: any) => {
    if (field.startsWith('settings.')) {
      const settingKey = field.replace('settings.', '')
      setFormData(prev => ({
        ...prev,
        settings: { ...prev.settings, [settingKey]: value }
      }))
    } else {
      setFormData(prev => ({ ...prev, [field]: value }))
    }
    setValidationErrors([])
  }

  const validateForm = (): boolean => {
    const errors: string[] = []

    // Validate project name
    const nameValidation = validateProjectName(formData.name)
    if (!nameValidation.isValid) {
      errors.push(nameValidation.error!)
    }

    // Validate website if provided
    if (formData.website) {
      try {
        new URL(formData.website)
        
        // Check for demo patterns
        const demoPatterns = [
          /example\.com/i, /test\.com/i, /demo\.com/i, /sample\.com/i,
          /placeholder\.com/i, /fake\.com/i, /dummy\.com/i
        ]
        
        if (demoPatterns.some(pattern => pattern.test(formData.website))) {
          errors.push('Demo or test websites are not allowed. Please provide your real website URL.')
        }
      } catch {
        errors.push('Please provide a valid website URL')
      }
    }

    setValidationErrors(errors)
    return errors.length === 0
  }

  const handleSave = async () => {
    if (!validateForm()) return

    setIsSaving(true)
    
    try {
      const updatedProject: Project = {
        ...project,
        name: formData.name,
        description: formData.description,
        industry: formData.industry,
        website: formData.website,
        targetLocation: formData.targetLocation,
        targetLanguage: formData.targetLanguage,
        settings: formData.settings,
        updatedAt: new Date().toISOString()
      }

      // In real implementation, save to Supabase
      await new Promise(resolve => setTimeout(resolve, 1000))

      onProjectUpdate(updatedProject)
      setIsEditing(false)
    } catch (error) {
      console.error('Error saving project settings:', error)
      setValidationErrors(['Failed to save settings. Please try again.'])
    } finally {
      setIsSaving(false)
    }
  }

  const handleCancel = () => {
    setFormData({
      name: project.name,
      description: project.description || '',
      industry: project.industry,
      website: project.website || '',
      targetLocation: project.targetLocation || '',
      targetLanguage: project.targetLanguage,
      settings: { ...project.settings }
    })
    setValidationErrors([])
    setIsEditing(false)
  }

  const getContentTypeLabel = (type: ContentType) => {
    const labels: Record<ContentType, string> = {
      blog_post: 'Blog Post',
      product_description: 'Product Description',
      landing_page: 'Landing Page',
      meta_description: 'Meta Description',
      social_media: 'Social Media',
      email_marketing: 'Email Marketing'
    }
    return labels[type]
  }

  return (
    <div className="space-y-6">
      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-red-800 mb-1">
                Please fix the following errors:
              </h4>
              <ul className="text-sm text-red-700 space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Basic Information */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
            <CogIcon className="h-5 w-5 text-blue-600" />
            Basic Information
          </h3>
          {!isEditing && (
            <button
              onClick={() => setIsEditing(true)}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg"
            >
              Edit Settings
            </button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Project Name *
            </label>
            {isEditing ? (
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter project name (no demo data)"
              />
            ) : (
              <p className="text-gray-900">{project.name}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Industry *
            </label>
            {isEditing ? (
              <select
                value={formData.industry}
                onChange={(e) => handleInputChange('industry', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {industryOptions.map((industry) => {
                  const info = getIndustryInfo(industry)
                  return (
                    <option key={industry} value={industry}>
                      {info.icon} {info.label}
                    </option>
                  )
                })}
              </select>
            ) : (
              <p className="text-gray-900 flex items-center gap-2">
                <span>{getIndustryInfo(project.industry).icon}</span>
                {getIndustryInfo(project.industry).label}
              </p>
            )}
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            {isEditing ? (
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Describe your project goals and target audience"
              />
            ) : (
              <p className="text-gray-900">{project.description || 'No description provided'}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Website URL
            </label>
            {isEditing ? (
              <input
                type="url"
                value={formData.website}
                onChange={(e) => handleInputChange('website', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="https://yourwebsite.com"
              />
            ) : (
              <p className="text-gray-900">
                {project.website ? (
                  <a 
                    href={project.website} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 underline"
                  >
                    {project.website}
                  </a>
                ) : (
                  'No website provided'
                )}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Target Language
            </label>
            {isEditing ? (
              <select
                value={formData.targetLanguage}
                onChange={(e) => handleInputChange('targetLanguage', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {languageOptions.map((lang) => (
                  <option key={lang.value} value={lang.value}>
                    {lang.label}
                  </option>
                ))}
              </select>
            ) : (
              <p className="text-gray-900">
                {languageOptions.find(l => l.value === project.targetLanguage)?.label || 'English'}
              </p>
            )}
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Target Location
            </label>
            {isEditing ? (
              <input
                type="text"
                value={formData.targetLocation}
                onChange={(e) => handleInputChange('targetLocation', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., United States, New York, Global"
              />
            ) : (
              <p className="text-gray-900">{project.targetLocation || 'Global'}</p>
            )}
          </div>
        </div>
      </div>

      {/* Content Settings */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-6 flex items-center gap-2">
          <DocumentTextIcon className="h-5 w-5 text-blue-600" />
          Content Settings
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Default Content Type
            </label>
            {isEditing ? (
              <select
                value={formData.settings.defaultContentType}
                onChange={(e) => handleInputChange('settings.defaultContentType', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {contentTypeOptions.map((type) => (
                  <option key={type} value={type}>
                    {getContentTypeLabel(type)}
                  </option>
                ))}
              </select>
            ) : (
              <p className="text-gray-900">{getContentTypeLabel(project.settings.defaultContentType)}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Default Word Count
            </label>
            {isEditing ? (
              <input
                type="number"
                min="100"
                max="10000"
                step="100"
                value={formData.settings.defaultWordCount}
                onChange={(e) => handleInputChange('settings.defaultWordCount', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            ) : (
              <p className="text-gray-900">{project.settings.defaultWordCount.toLocaleString()} words</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Default Tone
            </label>
            {isEditing ? (
              <select
                value={formData.settings.defaultTone}
                onChange={(e) => handleInputChange('settings.defaultTone', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {toneOptions.map((tone) => (
                  <option key={tone.value} value={tone.value}>
                    {tone.label}
                  </option>
                ))}
              </select>
            ) : (
              <p className="text-gray-900 capitalize">{project.settings.defaultTone}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Target Audience
            </label>
            {isEditing ? (
              <input
                type="text"
                value={formData.settings.targetAudience || ''}
                onChange={(e) => handleInputChange('settings.targetAudience', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., Small business owners, Marketing professionals"
              />
            ) : (
              <p className="text-gray-900">{project.settings.targetAudience || 'Not specified'}</p>
            )}
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Brand Voice
            </label>
            {isEditing ? (
              <textarea
                value={formData.settings.brandVoice || ''}
                onChange={(e) => handleInputChange('settings.brandVoice', e.target.value)}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Describe your brand's voice and personality"
              />
            ) : (
              <p className="text-gray-900">{project.settings.brandVoice || 'Not specified'}</p>
            )}
          </div>
        </div>
      </div>

      {/* SEO Settings */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-6 flex items-center gap-2">
          <TagIcon className="h-5 w-5 text-blue-600" />
          SEO Settings
        </h3>

        <div className="space-y-4">
          {[
            { key: 'includeInternalLinks', label: 'Include Internal Links', description: 'Automatically add internal links to content' },
            { key: 'includeExternalLinks', label: 'Include External Links', description: 'Add authoritative external references' },
            { key: 'includeFAQs', label: 'Include FAQ Sections', description: 'Add FAQ sections for better user engagement' },
            { key: 'includeSchemaMarkup', label: 'Include Schema Markup', description: 'Add structured data for rich snippets' }
          ].map((setting) => (
            <div key={setting.key} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <h4 className="font-medium text-gray-900">{setting.label}</h4>
                <p className="text-sm text-gray-600">{setting.description}</p>
              </div>
              {isEditing ? (
                <input
                  type="checkbox"
                  checked={formData.settings[setting.key as keyof SettingsType] as boolean}
                  onChange={(e) => handleInputChange(`settings.${setting.key}`, e.target.checked)}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
              ) : (
                <div className={`
                  inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium
                  ${project.settings[setting.key as keyof SettingsType] 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                  }
                `}>
                  {project.settings[setting.key as keyof SettingsType] ? (
                    <>
                      <CheckCircleIcon className="h-3 w-3" />
                      Enabled
                    </>
                  ) : (
                    <>
                      <XMarkIcon className="h-3 w-3" />
                      Disabled
                    </>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Action Buttons */}
      {isEditing && (
        <div className="flex justify-end gap-3">
          <button
            onClick={handleCancel}
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            disabled={isSaving}
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium rounded-lg inline-flex items-center gap-2"
          >
            {isSaving && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            )}
            {isSaving ? 'Saving...' : 'Save Settings'}
          </button>
        </div>
      )}
    </div>
  )
}