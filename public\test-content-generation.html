<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Content Generation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 16px;
        }
        input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background: #45a049;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: rgba(255, 0, 0, 0.2);
            color: #ffcccc;
        }
        .loading {
            text-align: center;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Content Generation Test</h1>
        <p>Test the SEO content generation API directly</p>
        
        <form id="contentForm">
            <div class="form-group">
                <label for="keyword">Target Keyword:</label>
                <input type="text" id="keyword" placeholder="e.g., digital marketing strategies" required>
            </div>
            
            <div class="form-group">
                <label for="country">Target Country:</label>
                <select id="country">
                    <option value="United States">United States</option>
                    <option value="United Kingdom">United Kingdom</option>
                    <option value="Canada">Canada</option>
                    <option value="Australia">Australia</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="contentType">Content Type:</label>
                <select id="contentType">
                    <option value="blog-post">Blog Post</option>
                    <option value="article">Article</option>
                    <option value="guide">Guide</option>
                    <option value="tutorial">Tutorial</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="tone">Tone:</label>
                <select id="tone">
                    <option value="professional">Professional</option>
                    <option value="casual">Casual</option>
                    <option value="friendly">Friendly</option>
                    <option value="authoritative">Authoritative</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="length">Content Length:</label>
                <select id="length">
                    <option value="short">Short (800-1200 words)</option>
                    <option value="medium">Medium (1500-2500 words)</option>
                    <option value="long">Long (2500-4000 words)</option>
                </select>
            </div>
            
            <button type="submit" id="generateBtn">Generate Content</button>
        </form>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        document.getElementById('contentForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const generateBtn = document.getElementById('generateBtn');
            const resultDiv = document.getElementById('result');
            
            // Get form data
            const formData = {
                keyword: document.getElementById('keyword').value,
                target_country: document.getElementById('country').value,
                content_type: document.getElementById('contentType').value,
                tone: document.getElementById('tone').value,
                length: document.getElementById('length').value
            };
            
            // Validate
            if (!formData.keyword.trim()) {
                alert('Please enter a keyword');
                return;
            }
            
            // Show loading
            generateBtn.disabled = true;
            generateBtn.textContent = 'Generating...';
            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.textContent = '🤖 Generating content with AI... This may take 30-60 seconds.';
            
            try {
                console.log('Sending request to:', 'http://localhost:5000/api/seo/generate-content');
                console.log('Form data:', formData);
                
                const response = await fetch('http://localhost:5000/api/seo/generate-content', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });
                
                console.log('Response status:', response.status);
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                console.log('Success result:', result);
                
                // Display result
                resultDiv.className = 'result';
                resultDiv.innerHTML = `
                    <h3>✅ Content Generated Successfully!</h3>
                    <p><strong>Word Count:</strong> ${result.seo_analysis.actual_word_count}</p>
                    <p><strong>Keyword Density:</strong> ${result.seo_analysis.keyword_density}</p>
                    <p><strong>Target Market:</strong> ${result.seo_analysis.market_targeted}</p>
                    <hr>
                    <h4>Generated Content:</h4>
                    <div style="background: rgba(0,0,0,0.2); padding: 15px; border-radius: 5px; margin-top: 10px;">
                        ${result.content.body.replace(/\n/g, '<br>')}
                    </div>
                `;
                
            } catch (error) {
                console.error('Error:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ Error Generating Content</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p><strong>Troubleshooting:</strong></p>
                    <ul>
                        <li>Make sure the backend server is running on http://localhost:5000</li>
                        <li>Check if the OpenAI API key is configured</li>
                        <li>Try refreshing the page and trying again</li>
                    </ul>
                `;
            } finally {
                generateBtn.disabled = false;
                generateBtn.textContent = 'Generate Content';
            }
        });
    </script>
</body>
</html>
