import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration. Please check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Database Migration Script
 * Handles database schema updates and migrations
 */
async function migrateDatabase() {
  console.log('🔄 Running database migrations...');

  try {
    // Check current database version
    const currentVersion = await getCurrentVersion();
    console.log(`📊 Current database version: ${currentVersion}`);
    
    // Run migrations based on current version
    await runMigrations(currentVersion);
    
    console.log('✅ Database migrations completed successfully!');
    
  } catch (error) {
    console.error('❌ Database migration failed:', error);
    process.exit(1);
  }
}

/**
 * Get current database version
 */
async function getCurrentVersion() {
  try {
    const { data, error } = await supabase
      .from('schema_version')
      .select('version')
      .order('version', { ascending: false })
      .limit(1);

    if (error) {
      // If table doesn't exist, this is a fresh installation
      return 0;
    }

    return data && data.length > 0 ? data[0].version : 0;
  } catch (error) {
    return 0;
  }
}

/**
 * Run migrations based on current version
 */
async function runMigrations(currentVersion) {
  const migrations = [
    {
      version: 1,
      description: 'Initial database setup',
      up: async () => {
        console.log('🔧 Migration 1: Creating schema_version table...');
        await supabase.rpc('execute_sql', {
          query: `
            CREATE TABLE IF NOT EXISTS schema_version (
              version INTEGER PRIMARY KEY,
              description TEXT NOT NULL,
              applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
          `
        });
        
        await supabase.rpc('execute_sql', {
          query: `
            INSERT INTO schema_version (version, description) 
            VALUES (1, 'Initial database setup')
            ON CONFLICT (version) DO NOTHING
          `
        });
      }
    },
    {
      version: 2,
      description: 'Add content templates table',
      up: async () => {
        console.log('🔧 Migration 2: Creating content_templates table...');
        await supabase.rpc('execute_sql', {
          query: `
            CREATE TABLE IF NOT EXISTS content_templates (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              name VARCHAR(255) NOT NULL,
              description TEXT,
              template_content TEXT NOT NULL,
              template_type VARCHAR(50) NOT NULL,
              industry VARCHAR(100),
              created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
              updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
          `
        });
        
        await supabase.rpc('execute_sql', {
          query: `
            INSERT INTO schema_version (version, description) 
            VALUES (2, 'Add content templates table')
            ON CONFLICT (version) DO NOTHING
          `
        });
      }
    },
    {
      version: 3,
      description: 'Add bulk generation queue table',
      up: async () => {
        console.log('🔧 Migration 3: Creating bulk_generation_queue table...');
        await supabase.rpc('execute_sql', {
          query: `
            CREATE TABLE IF NOT EXISTS bulk_generation_queue (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
              project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
              keywords TEXT[] NOT NULL,
              status VARCHAR(20) DEFAULT 'pending',
              progress INTEGER DEFAULT 0,
              total_items INTEGER NOT NULL,
              settings JSONB,
              created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
              updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
              completed_at TIMESTAMP
            )
          `
        });
        
        await supabase.rpc('execute_sql', {
          query: `
            CREATE INDEX IF NOT EXISTS idx_bulk_generation_queue_user_id 
            ON bulk_generation_queue(user_id)
          `
        });
        
        await supabase.rpc('execute_sql', {
          query: `
            CREATE INDEX IF NOT EXISTS idx_bulk_generation_queue_status 
            ON bulk_generation_queue(status)
          `
        });
        
        await supabase.rpc('execute_sql', {
          query: `
            INSERT INTO schema_version (version, description) 
            VALUES (3, 'Add bulk generation queue table')
            ON CONFLICT (version) DO NOTHING
          `
        });
      }
    },
    {
      version: 4,
      description: 'Add authority links table',
      up: async () => {
        console.log('🔧 Migration 4: Creating authority_links table...');
        await supabase.rpc('execute_sql', {
          query: `
            CREATE TABLE IF NOT EXISTS authority_links (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              domain VARCHAR(255) NOT NULL,
              url TEXT NOT NULL,
              authority_score INTEGER,
              industry VARCHAR(100),
              keywords TEXT[],
              last_validated TIMESTAMP,
              is_active BOOLEAN DEFAULT true,
              created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
              updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
          `
        });
        
        await supabase.rpc('execute_sql', {
          query: `
            CREATE INDEX IF NOT EXISTS idx_authority_links_domain 
            ON authority_links(domain)
          `
        });
        
        await supabase.rpc('execute_sql', {
          query: `
            CREATE INDEX IF NOT EXISTS idx_authority_links_industry 
            ON authority_links(industry)
          `
        });
        
        await supabase.rpc('execute_sql', {
          query: `
            INSERT INTO schema_version (version, description) 
            VALUES (4, 'Add authority links table')
            ON CONFLICT (version) DO NOTHING
          `
        });
      }
    }
  ];

  // Run migrations sequentially
  for (const migration of migrations) {
    if (migration.version > currentVersion) {
      console.log(`📈 Running migration ${migration.version}: ${migration.description}`);
      await migration.up();
      console.log(`✅ Migration ${migration.version} completed`);
    } else {
      console.log(`⏭️  Skipping migration ${migration.version} (already applied)`);
    }
  }
}

// Run the migration
migrateDatabase();