/* 
 * SEO SAAS Layout System
 * Professional layout components and grid systems
 * Enterprise-grade responsive design
 */

/* ===========================
   CONTAINER SYSTEMS
   =========================== */

.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.container-sm {
  max-width: 640px;
}

.container-md {
  max-width: 768px;
}

.container-lg {
  max-width: 1024px;
}

.container-xl {
  max-width: 1280px;
}

.container-2xl {
  max-width: 1536px;
}

.container-fluid {
  max-width: none;
  width: 100%;
}

/* Section containers */
.section {
  padding: var(--space-16) 0;
}

.section-sm {
  padding: var(--space-8) 0;
}

.section-lg {
  padding: var(--space-24) 0;
}

/* ===========================
   PROFESSIONAL GRID SYSTEMS
   =========================== */

.grid {
  display: grid;
  gap: var(--space-6);
}

.grid-tight {
  gap: var(--space-4);
}

.grid-loose {
  gap: var(--space-8);
}

/* Grid columns */
.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
.grid-cols-12 { grid-template-columns: repeat(12, 1fr); }

/* Grid rows */
.grid-rows-1 { grid-template-rows: repeat(1, 1fr); }
.grid-rows-2 { grid-template-rows: repeat(2, 1fr); }
.grid-rows-3 { grid-template-rows: repeat(3, 1fr); }
.grid-rows-4 { grid-template-rows: repeat(4, 1fr); }

/* Grid spans */
.col-span-1 { grid-column: span 1 / span 1; }
.col-span-2 { grid-column: span 2 / span 2; }
.col-span-3 { grid-column: span 3 / span 3; }
.col-span-4 { grid-column: span 4 / span 4; }
.col-span-5 { grid-column: span 5 / span 5; }
.col-span-6 { grid-column: span 6 / span 6; }
.col-span-full { grid-column: 1 / -1; }

.row-span-1 { grid-row: span 1 / span 1; }
.row-span-2 { grid-row: span 2 / span 2; }
.row-span-3 { grid-row: span 3 / span 3; }
.row-span-4 { grid-row: span 4 / span 4; }
.row-span-full { grid-row: 1 / -1; }

/* ===========================
   FLEXBOX LAYOUT UTILITIES
   =========================== */

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

/* Flex direction */
.flex-row { flex-direction: row; }
.flex-row-reverse { flex-direction: row-reverse; }
.flex-col { flex-direction: column; }
.flex-col-reverse { flex-direction: column-reverse; }

/* Flex wrap */
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.flex-wrap-reverse { flex-wrap: wrap-reverse; }

/* Flex grow and shrink */
.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }

.grow { flex-grow: 1; }
.grow-0 { flex-grow: 0; }
.shrink { flex-shrink: 1; }
.shrink-0 { flex-shrink: 0; }

/* Justify content */
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

/* Align items */
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-baseline { align-items: baseline; }
.items-stretch { align-items: stretch; }

/* Align content */
.content-start { align-content: flex-start; }
.content-end { align-content: flex-end; }
.content-center { align-content: center; }
.content-between { align-content: space-between; }
.content-around { align-content: space-around; }
.content-evenly { align-content: space-evenly; }

/* Align self */
.self-auto { align-self: auto; }
.self-start { align-self: flex-start; }
.self-end { align-self: flex-end; }
.self-center { align-self: center; }
.self-stretch { align-self: stretch; }
.self-baseline { align-self: baseline; }

/* ===========================
   APPLICATION LAYOUT STRUCTURE
   =========================== */

.app-layout {
  display: flex;
  min-height: 100vh;
  background-color: var(--bg-secondary);
}

.app-layout-vertical {
  flex-direction: column;
}

.app-layout-horizontal {
  flex-direction: row;
}

/* Sidebar layout */
.sidebar {
  width: 256px;
  min-height: 100vh;
  background-color: white;
  border-right: 1px solid var(--gray-200);
  flex-shrink: 0;
  overflow-y: auto;
  transition: all 0.3s ease-in-out;
}

.sidebar.collapsed {
  width: 64px;
}

.sidebar-content {
  padding: var(--space-4);
  height: 100%;
}

/* Main content area */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-header {
  background-color: white;
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.content-body {
  flex: 1;
  padding: var(--space-6);
  overflow-y: auto;
  background-color: var(--bg-secondary);
}

/* Split layout */
.split-layout {
  display: flex;
  height: 100%;
  gap: var(--space-6);
}

.split-panel {
  flex: 1;
  min-width: 0;
}

.split-panel-sidebar {
  width: 320px;
  flex-shrink: 0;
}

.split-panel-main {
  flex: 1;
  min-width: 0;
}

/* ===========================
   PROFESSIONAL CARD LAYOUTS
   =========================== */

.card-grid {
  display: grid;
  gap: var(--space-6);
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.card-grid-fixed {
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
}

.card-stack {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.card-row {
  display: flex;
  gap: var(--space-4);
  align-items: stretch;
}

/* Masonry layout */
.masonry {
  column-count: 3;
  column-gap: var(--space-6);
  column-fill: balance;
}

.masonry-item {
  break-inside: avoid;
  margin-bottom: var(--space-6);
}

/* ===========================
   SPACING UTILITIES
   =========================== */

/* Margin utilities */
.m-auto { margin: auto; }
.mx-auto { margin-left: auto; margin-right: auto; }
.my-auto { margin-top: auto; margin-bottom: auto; }

.ml-auto { margin-left: auto; }
.mr-auto { margin-right: auto; }
.mt-auto { margin-top: auto; }
.mb-auto { margin-bottom: auto; }

/* Padding utilities */
.px-0 { padding-left: 0; padding-right: 0; }
.px-1 { padding-left: var(--space-1); padding-right: var(--space-1); }
.px-2 { padding-left: var(--space-2); padding-right: var(--space-2); }
.px-3 { padding-left: var(--space-3); padding-right: var(--space-3); }
.px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }
.px-6 { padding-left: var(--space-6); padding-right: var(--space-6); }
.px-8 { padding-left: var(--space-8); padding-right: var(--space-8); }

.py-0 { padding-top: 0; padding-bottom: 0; }
.py-1 { padding-top: var(--space-1); padding-bottom: var(--space-1); }
.py-2 { padding-top: var(--space-2); padding-bottom: var(--space-2); }
.py-3 { padding-top: var(--space-3); padding-bottom: var(--space-3); }
.py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }
.py-6 { padding-top: var(--space-6); padding-bottom: var(--space-6); }
.py-8 { padding-top: var(--space-8); padding-bottom: var(--space-8); }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: var(--space-1); }
.pl-2 { padding-left: var(--space-2); }
.pl-3 { padding-left: var(--space-3); }
.pl-4 { padding-left: var(--space-4); }
.pl-6 { padding-left: var(--space-6); }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: var(--space-1); }
.pr-2 { padding-right: var(--space-2); }
.pr-3 { padding-right: var(--space-3); }
.pr-4 { padding-right: var(--space-4); }
.pr-6 { padding-right: var(--space-6); }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: var(--space-1); }
.pt-2 { padding-top: var(--space-2); }
.pt-3 { padding-top: var(--space-3); }
.pt-4 { padding-top: var(--space-4); }
.pt-6 { padding-top: var(--space-6); }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: var(--space-1); }
.pb-2 { padding-bottom: var(--space-2); }
.pb-3 { padding-bottom: var(--space-3); }
.pb-4 { padding-bottom: var(--space-4); }
.pb-6 { padding-bottom: var(--space-6); }

/* ===========================
   POSITIONING UTILITIES
   =========================== */

.static { position: static; }
.fixed { position: fixed; }
.absolute { position: absolute; }
.relative { position: relative; }
.sticky { position: sticky; }

/* Positioning offsets */
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.inset-x-0 { right: 0; left: 0; }
.inset-y-0 { top: 0; bottom: 0; }

.top-0 { top: 0; }
.top-1 { top: var(--space-1); }
.top-2 { top: var(--space-2); }
.top-4 { top: var(--space-4); }

.right-0 { right: 0; }
.right-1 { right: var(--space-1); }
.right-2 { right: var(--space-2); }
.right-4 { right: var(--space-4); }

.bottom-0 { bottom: 0; }
.bottom-1 { bottom: var(--space-1); }
.bottom-2 { bottom: var(--space-2); }
.bottom-4 { bottom: var(--space-4); }

.left-0 { left: 0; }
.left-1 { left: var(--space-1); }
.left-2 { left: var(--space-2); }
.left-4 { left: var(--space-4); }

/* ===========================
   RESPONSIVE BREAKPOINTS
   =========================== */

/* Small devices (640px and up) */
@media (min-width: 640px) {
  .container { padding: 0 var(--space-6); }
  
  .sm\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .sm\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .sm\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  
  .sm\:flex { display: flex; }
  .sm\:flex-row { flex-direction: row; }
  .sm\:flex-col { flex-direction: column; }
  
  .sm\:col-span-1 { grid-column: span 1 / span 1; }
  .sm\:col-span-2 { grid-column: span 2 / span 2; }
  .sm\:col-span-3 { grid-column: span 3 / span 3; }
  .sm\:col-span-4 { grid-column: span 4 / span 4; }
}

/* Medium devices (768px and up) */
@media (min-width: 768px) {
  .md\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .md\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
  
  .md\:flex { display: flex; }
  .md\:flex-row { flex-direction: row; }
  .md\:flex-col { flex-direction: column; }
  
  .md\:col-span-1 { grid-column: span 1 / span 1; }
  .md\:col-span-2 { grid-column: span 2 / span 2; }
  .md\:col-span-3 { grid-column: span 3 / span 3; }
  .md\:col-span-4 { grid-column: span 4 / span 4; }
  .md\:col-span-6 { grid-column: span 6 / span 6; }
  
  .masonry { column-count: 2; }
}

/* Large devices (1024px and up) */
@media (min-width: 1024px) {
  .lg\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .lg\:grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
  .lg\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
  
  .lg\:flex { display: flex; }
  .lg\:flex-row { flex-direction: row; }
  .lg\:flex-col { flex-direction: column; }
  
  .lg\:col-span-1 { grid-column: span 1 / span 1; }
  .lg\:col-span-2 { grid-column: span 2 / span 2; }
  .lg\:col-span-3 { grid-column: span 3 / span 3; }
  .lg\:col-span-4 { grid-column: span 4 / span 4; }
  .lg\:col-span-5 { grid-column: span 5 / span 5; }
  .lg\:col-span-6 { grid-column: span 6 / span 6; }
  
  .masonry { column-count: 3; }
  
  .sidebar.auto-collapse {
    width: 64px;
  }
  
  .sidebar.auto-collapse:hover {
    width: 256px;
  }
}

/* Extra large devices (1280px and up) */
@media (min-width: 1280px) {
  .xl\:grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
  .xl\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .xl\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .xl\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .xl\:grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
  .xl\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
  
  .xl\:col-span-1 { grid-column: span 1 / span 1; }
  .xl\:col-span-2 { grid-column: span 2 / span 2; }
  .xl\:col-span-3 { grid-column: span 3 / span 3; }
  .xl\:col-span-4 { grid-column: span 4 / span 4; }
  .xl\:col-span-5 { grid-column: span 5 / span 5; }
  .xl\:col-span-6 { grid-column: span 6 / span 6; }
  
  .masonry { column-count: 4; }
}

/* 2X Large devices (1536px and up) */
@media (min-width: 1536px) {
  .container { max-width: 1536px; }
  
  .2xl\:grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
  .2xl\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
  
  .masonry { column-count: 5; }
}

/* ===========================
   MOBILE-SPECIFIC LAYOUTS
   =========================== */

@media (max-width: 768px) {
  .app-layout {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    min-height: auto;
    order: 2;
  }
  
  .main-content {
    order: 1;
  }
  
  .split-layout {
    flex-direction: column;
  }
  
  .split-panel-sidebar {
    width: 100%;
    order: 2;
  }
  
  .split-panel-main {
    order: 1;
  }
  
  .card-row {
    flex-direction: column;
  }
  
  .content-header {
    padding: var(--space-3) var(--space-4);
  }
  
  .content-body {
    padding: var(--space-4);
  }
}