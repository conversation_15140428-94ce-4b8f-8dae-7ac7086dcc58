# Dependencies
node_modules/
*/node_modules/
.pnp
.pnp.js

# Testing
coverage/
.nyc_output

# Production builds
.next/
out/
dist/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Logs
logs/
*.log
lerna-debug.log*

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# MacOS
.DS_Store

# Windows
Thumbs.db

# IDE
.vscode/
.idea/
*.swp
*.swo

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Backend specific
backend/uploads/*
backend/exports/*
backend/logs/*
!backend/uploads/.gitkeep
!backend/exports/.gitkeep
!backend/logs/.gitkeep

# AI Framework
ai-framework/models/
ai-framework/cache/
ai-framework/temp/

# Database
*.sqlite
*.db

# Cache
.cache/