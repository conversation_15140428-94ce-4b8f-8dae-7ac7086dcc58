// Search Autocomplete System - Enhanced suggestions and intelligent completion
class SearchAutocompleteSystem {
    constructor() {
        this.suggestions = {
            projects: [],
            keywords: [],
            actions: [],
            categories: []
        };
        this.recentQueries = [];
        this.popularQueries = [];
        this.contextualSuggestions = [];
        this.currentContext = null;
        this.debounceTimeout = null;
        this.minQueryLength = 2;
        
        this.init();
    }
    
    init() {
        this.loadSuggestionData();
        this.loadPopularQueries();
        this.detectContext();
    }
    
    // Generate autocomplete suggestions based on query
    generateSuggestions(query, context = null) {
        if (!query || query.length < this.minQueryLength) {
            return this.getDefaultSuggestions(context);
        }
        
        const lowerQuery = query.toLowerCase();
        const suggestions = [];
        
        // Add project suggestions
        const projectSuggestions = this.searchProjects(lowerQuery);
        suggestions.push(...projectSuggestions.map(item => ({
            type: 'project',
            text: item.name,
            description: `${item.keyword} • ${item.industry}`,
            icon: 'project',
            action: () => this.navigateToProject(item.id),
            score: this.calculateRelevanceScore(lowerQuery, item.name, item.keyword)
        })));
        
        // Add keyword suggestions
        const keywordSuggestions = this.searchKeywords(lowerQuery);
        suggestions.push(...keywordSuggestions.map(item => ({
            type: 'keyword',
            text: item.term,
            description: `Volume: ${item.volume} • ${item.industry}`,
            icon: 'keyword',
            action: () => this.searchKeyword(item.term),
            score: this.calculateRelevanceScore(lowerQuery, item.term, item.industry)
        })));
        
        // Add action suggestions
        const actionSuggestions = this.searchActions(lowerQuery);
        suggestions.push(...actionSuggestions.map(item => ({
            type: 'action',
            text: item.name,
            description: item.description,
            icon: item.icon,
            action: item.action,
            score: this.calculateRelevanceScore(lowerQuery, item.name, item.description)
        })));
        
        // Add contextual suggestions
        if (context) {
            const contextualSuggestions = this.getContextualSuggestions(lowerQuery, context);
            suggestions.push(...contextualSuggestions);
        }
        
        // Sort by relevance score and return top suggestions
        return suggestions
            .sort((a, b) => b.score - a.score)
            .slice(0, 8);
    }
    
    // Search projects with fuzzy matching
    searchProjects(query) {
        return this.suggestions.projects.filter(project => {
            const searchText = `${project.name} ${project.keyword} ${project.industry}`.toLowerCase();
            return this.fuzzyMatch(query, searchText);
        });
    }
    
    // Search keywords with intelligent matching
    searchKeywords(query) {
        return this.suggestions.keywords.filter(keyword => {
            const searchText = `${keyword.term} ${keyword.industry} ${keyword.category || ''}`.toLowerCase();
            return this.fuzzyMatch(query, searchText);
        });
    }
    
    // Search available actions
    searchActions(query) {
        return this.suggestions.actions.filter(action => {
            const searchText = `${action.name} ${action.description} ${action.keywords?.join(' ') || ''}`.toLowerCase();
            return this.fuzzyMatch(query, searchText);
        });
    }
    
    // Generate query completions
    generateCompletions(query) {
        if (!query || query.length < 1) return [];
        
        const completions = [];
        const lowerQuery = query.toLowerCase();
        
        // Find all items that start with the query
        const allSuggestions = [
            ...this.suggestions.projects.map(p => p.name),
            ...this.suggestions.projects.map(p => p.keyword),
            ...this.suggestions.keywords.map(k => k.term),
            ...this.popularQueries,
            ...this.recentQueries
        ];
        
        // Remove duplicates and filter
        const uniqueSuggestions = [...new Set(allSuggestions)]
            .filter(suggestion => suggestion.toLowerCase().startsWith(lowerQuery))
            .sort((a, b) => a.length - b.length)
            .slice(0, 6);
        
        return uniqueSuggestions.map(suggestion => ({
            type: 'completion',
            text: suggestion,
            query: suggestion,
            action: () => this.performSearch(suggestion)
        }));
    }
    
    // Get default suggestions when no query
    getDefaultSuggestions(context) {
        const suggestions = [];
        
        // Add recent searches
        if (this.recentQueries.length > 0) {
            suggestions.push({
                type: 'section',
                text: 'Recent Searches'
            });
            
            this.recentQueries.slice(0, 3).forEach(query => {
                suggestions.push({
                    type: 'recent',
                    text: query,
                    icon: 'clock',
                    action: () => this.performSearch(query)
                });
            });
        }
        
        // Add popular queries
        if (this.popularQueries.length > 0) {
            suggestions.push({
                type: 'section',
                text: 'Popular Searches'
            });
            
            this.popularQueries.slice(0, 3).forEach(query => {
                suggestions.push({
                    type: 'popular',
                    text: query,
                    icon: 'trending',
                    action: () => this.performSearch(query)
                });
            });
        }
        
        // Add quick actions
        suggestions.push({
            type: 'section',
            text: 'Quick Actions'
        });
        
        const quickActions = this.getQuickActions(context);
        suggestions.push(...quickActions.slice(0, 4));
        
        return suggestions;
    }
    
    // Get contextual suggestions based on current page/context
    getContextualSuggestions(query, context) {
        const suggestions = [];
        
        switch (context) {
            case 'analytics':
                suggestions.push(...this.getAnalyticsContextSuggestions(query));
                break;
            case 'projects':
                suggestions.push(...this.getProjectsContextSuggestions(query));
                break;
            case 'content-creator':
                suggestions.push(...this.getContentContextSuggestions(query));
                break;
            default:
                break;
        }
        
        return suggestions;
    }
    
    // Analytics page specific suggestions
    getAnalyticsContextSuggestions(query) {
        const analyticsSuggestions = [
            { name: 'View performance metrics', action: () => this.navigateToSection('metrics') },
            { name: 'Export report', action: () => this.exportReport() },
            { name: 'Compare competitors', action: () => this.navigateToSection('competitors') },
            { name: 'Filter by date range', action: () => this.openDateFilter() }
        ];
        
        return analyticsSuggestions
            .filter(item => item.name.toLowerCase().includes(query))
            .map(item => ({
                type: 'contextual',
                text: item.name,
                description: 'Analytics action',
                icon: 'analytics',
                action: item.action,
                score: this.calculateRelevanceScore(query, item.name, '')
            }));
    }
    
    // Projects page specific suggestions
    getProjectsContextSuggestions(query) {
        const projectsSuggestions = [
            { name: 'Create new project', action: () => this.createProject() },
            { name: 'Import project data', action: () => this.importProject() },
            { name: 'Filter by industry', action: () => this.filterByIndustry() },
            { name: 'Sort by score', action: () => this.sortByScore() }
        ];
        
        return projectsSuggestions
            .filter(item => item.name.toLowerCase().includes(query))
            .map(item => ({
                type: 'contextual',
                text: item.name,
                description: 'Project action',
                icon: 'project',
                action: item.action,
                score: this.calculateRelevanceScore(query, item.name, '')
            }));
    }
    
    // Content creator specific suggestions
    getContentContextSuggestions(query) {
        const contentSuggestions = [
            { name: 'Analyze competitors', action: () => this.analyzeCompetitors() },
            { name: 'Generate content', action: () => this.generateContent() },
            { name: 'Check keyword density', action: () => this.checkKeywordDensity() },
            { name: 'Optimize for SEO', action: () => this.optimizeForSEO() }
        ];
        
        return contentSuggestions
            .filter(item => item.name.toLowerCase().includes(query))
            .map(item => ({
                type: 'contextual',
                text: item.name,
                description: 'Content action',
                icon: 'content',
                action: item.action,
                score: this.calculateRelevanceScore(query, item.name, '')
            }));
    }
    
    // Get quick actions based on context
    getQuickActions(context) {
        const baseActions = [
            {
                type: 'action',
                text: 'Create New Project',
                description: 'Start a new SEO project',
                icon: 'plus',
                action: () => this.navigateTo('content-creator.html')
            },
            {
                type: 'action',
                text: 'View Analytics',
                description: 'Check performance metrics',
                icon: 'analytics',
                action: () => this.navigateTo('analytics.html')
            },
            {
                type: 'action',
                text: 'Generate Content',
                description: 'Create new SEO content',
                icon: 'content',
                action: () => this.navigateTo('content-creator.html')
            }
        ];
        
        // Add context-specific actions
        if (context === 'projects') {
            baseActions.unshift({
                type: 'action',
                text: 'Bulk Import Projects',
                description: 'Import multiple projects',
                icon: 'upload',
                action: () => this.openBulkImport()
            });
        }
        
        return baseActions;
    }
    
    // Fuzzy matching algorithm
    fuzzyMatch(query, text) {
        if (!query || !text) return false;
        
        // Exact match
        if (text.includes(query)) return true;
        
        // Word boundary match
        const words = text.split(/\s+/);
        if (words.some(word => word.startsWith(query))) return true;
        
        // Character sequence match
        let queryIndex = 0;
        for (let i = 0; i < text.length && queryIndex < query.length; i++) {
            if (text[i].toLowerCase() === query[queryIndex].toLowerCase()) {
                queryIndex++;
            }
        }
        
        return queryIndex === query.length;
    }
    
    // Calculate relevance score for sorting
    calculateRelevanceScore(query, text, description = '') {
        let score = 0;
        const lowerQuery = query.toLowerCase();
        const lowerText = text.toLowerCase();
        const lowerDesc = description.toLowerCase();
        
        // Exact match bonus
        if (lowerText === lowerQuery) score += 100;
        
        // Starts with bonus
        if (lowerText.startsWith(lowerQuery)) score += 50;
        
        // Contains bonus
        if (lowerText.includes(lowerQuery)) score += 25;
        
        // Word boundary bonus
        const words = lowerText.split(/\s+/);
        if (words.some(word => word.startsWith(lowerQuery))) score += 15;
        
        // Description match
        if (lowerDesc.includes(lowerQuery)) score += 10;
        
        // Length penalty (prefer shorter matches)
        score -= Math.floor(text.length / 10);
        
        return Math.max(0, score);
    }
    
    // Add query to search history
    addToHistory(query) {
        if (!query || query.trim().length === 0) return;
        
        // Remove if already exists
        this.recentQueries = this.recentQueries.filter(q => q !== query);
        
        // Add to beginning
        this.recentQueries.unshift(query);
        
        // Keep only last 10
        this.recentQueries = this.recentQueries.slice(0, 10);
        
        // Save to localStorage
        this.saveToStorage();
    }
    
    // Detect current page context
    detectContext() {
        const path = window.location.pathname;
        const page = path.split('/').pop().replace('.html', '');
        
        switch (page) {
            case 'analytics':
                this.currentContext = 'analytics';
                break;
            case 'projects':
                this.currentContext = 'projects';
                break;
            case 'content-creator':
            case 'content-editor':
                this.currentContext = 'content-creator';
                break;
            case 'dashboard':
                this.currentContext = 'dashboard';
                break;
            default:
                this.currentContext = 'general';
        }
    }
    
    // Load suggestion data
    loadSuggestionData() {
        // Sample data - would come from API in real implementation
        this.suggestions = {
            projects: [
                { id: '1', name: 'Digital Marketing Agency', keyword: 'digital marketing services', industry: 'Marketing' },
                { id: '2', name: 'Property Investment Hub', keyword: 'real estate investment', industry: 'Real Estate' },
                { id: '3', name: 'MedTech Solutions', keyword: 'healthcare technology', industry: 'Healthcare' },
                { id: '4', name: 'E-commerce Store', keyword: 'online shopping platform', industry: 'E-commerce' },
                { id: '5', name: 'Financial Advisor', keyword: 'financial planning services', industry: 'Finance' }
            ],
            keywords: [
                { term: 'digital marketing services', volume: '22,000', industry: 'Marketing', category: 'services' },
                { term: 'real estate investment', volume: '18,500', industry: 'Real Estate', category: 'investment' },
                { term: 'healthcare technology', volume: '15,300', industry: 'Healthcare', category: 'technology' },
                { term: 'SEO optimization', volume: '12,100', industry: 'Marketing', category: 'services' },
                { term: 'content marketing', volume: '9,900', industry: 'Marketing', category: 'strategy' }
            ],
            actions: [
                {
                    name: 'Create Project',
                    description: 'Start a new SEO project',
                    icon: 'plus',
                    keywords: ['create', 'new', 'project', 'start'],
                    action: () => this.navigateTo('content-creator.html')
                },
                {
                    name: 'Generate Content',
                    description: 'Create SEO-optimized content',
                    icon: 'content',
                    keywords: ['generate', 'create', 'content', 'write'],
                    action: () => this.navigateTo('content-creator.html')
                },
                {
                    name: 'Analyze Competitors',
                    description: 'Research competitor strategies',
                    icon: 'analyze',
                    keywords: ['analyze', 'competitor', 'research', 'study'],
                    action: () => this.openCompetitorAnalysis()
                },
                {
                    name: 'Export Data',
                    description: 'Download reports and analytics',
                    icon: 'download',
                    keywords: ['export', 'download', 'report', 'data'],
                    action: () => this.openExportDialog()
                }
            ]
        };
        
        // Load from localStorage if available
        this.loadFromStorage();
    }
    
    // Load popular queries
    loadPopularQueries() {
        this.popularQueries = [
            'digital marketing',
            'SEO optimization',
            'content strategy',
            'keyword research',
            'competitor analysis'
        ];
    }
    
    // Navigation helpers
    navigateTo(url) {
        window.location.href = url;
    }
    
    navigateToProject(projectId) {
        window.location.href = `project-details.html?id=${projectId}`;
    }
    
    navigateToSection(section) {
        const element = document.getElementById(section);
        if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
        }
    }
    
    performSearch(query) {
        // Trigger global search with query
        if (window.globalSearch) {
            window.globalSearch.searchInput.value = query;
            window.globalSearch.performSearch(query);
        }
    }
    
    // Action implementations
    createProject() {
        this.navigateTo('content-creator.html');
    }
    
    importProject() {
        console.log('Opening project import dialog...');
    }
    
    exportReport() {
        console.log('Exporting analytics report...');
    }
    
    analyzeCompetitors() {
        console.log('Starting competitor analysis...');
    }
    
    generateContent() {
        this.navigateTo('content-creator.html');
    }
    
    // Storage helpers
    saveToStorage() {
        try {
            localStorage.setItem('searchAutocomplete', JSON.stringify({
                recentQueries: this.recentQueries,
                popularQueries: this.popularQueries
            }));
        } catch (error) {
            console.error('Error saving autocomplete data:', error);
        }
    }
    
    loadFromStorage() {
        try {
            const data = localStorage.getItem('searchAutocomplete');
            if (data) {
                const parsed = JSON.parse(data);
                this.recentQueries = parsed.recentQueries || [];
                this.popularQueries = parsed.popularQueries || this.popularQueries;
            }
        } catch (error) {
            console.error('Error loading autocomplete data:', error);
        }
    }
}

// Initialize autocomplete system
let searchAutocomplete;
document.addEventListener('DOMContentLoaded', function() {
    searchAutocomplete = new SearchAutocompleteSystem();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SearchAutocompleteSystem;
}