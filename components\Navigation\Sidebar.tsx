'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useDemoAuth } from '@/hooks/useDemoAuth';
import {
  HomeIcon,
  ChartBarIcon,
  DocumentTextIcon,
  MagnifyingGlassIcon,
  FolderIcon,
  Cog6ToothIcon,
  PlusIcon,
  BoltIcon,
  UserGroupIcon,
  CreditCardIcon,
  KeyIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  XMarkIcon,
  MapIcon,
  LinkIcon
} from '@heroicons/react/24/outline';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  isMobile: boolean;
}

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  children?: NavigationItem[];
  requiresSubscription?: 'pro' | 'enterprise';
}

const navigation: NavigationItem[] = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: HomeIcon,
  },
  {
    name: 'Analytics',
    href: '/analytics',
    icon: ChartBarIcon,
    badge: 'New'
  },
  {
    name: 'Content Creation',
    href: '/content',
    icon: DocumentTextIcon,
    children: [
      {
        name: 'Create Content',
        href: '/content-generator',
        icon: PlusIcon,
      },
      {
        name: 'Content Editor',
        href: '/content/editor',
        icon: DocumentTextIcon,
      },
      {
        name: 'Bulk Generator',
        href: '/content/bulk',
        icon: BoltIcon,
        requiresSubscription: 'pro'
      },
      {
        name: 'Optimize Content',
        href: '/content/optimize',
        icon: ChartBarIcon,
      }
    ]
  },
  {
    name: 'Research & Analysis',
    href: '/research',
    icon: MagnifyingGlassIcon,
    children: [
      {
        name: 'Keyword Research',
        href: '/research/keywords',
        icon: MagnifyingGlassIcon,
      },
      {
        name: 'Competitor Analysis',
        href: '/research/competitors',
        icon: ChartBarIcon,
      },
      {
        name: 'SERP Analyzer',
        href: '/research/serp',
        icon: ChartBarIcon,
      },
      {
        name: 'Content Gaps',
        href: '/research/gaps',
        icon: DocumentTextIcon,
        requiresSubscription: 'pro'
      },
      {
        name: 'Sitemap Analysis',
        href: '/sitemap-analysis',
        icon: MapIcon,
        requiresSubscription: 'pro'
      },
      {
        name: 'Authority Links',
        href: '/authority-links',
        icon: LinkIcon,
        requiresSubscription: 'pro'
      }
    ]
  },
  {
    name: 'Projects',
    href: '/projects',
    icon: FolderIcon,
  },
  {
    name: 'Settings',
    href: '/settings',
    icon: Cog6ToothIcon,
    children: [
      {
        name: 'Account',
        href: '/settings/account',
        icon: Cog6ToothIcon,
      },
      {
        name: 'Billing',
        href: '/settings/billing',
        icon: CreditCardIcon,
      },
      {
        name: 'API Settings',
        href: '/settings/api',
        icon: KeyIcon,
      },
      {
        name: 'Team Management',
        href: '/settings/team',
        icon: UserGroupIcon,
        requiresSubscription: 'enterprise'
      }
    ]
  }
];

export default function Sidebar({ isOpen, onClose, isMobile }: SidebarProps) {
  const pathname = usePathname();
  const { userTier, usageCount, usageLimit, isSubscribed } = useDemoAuth();
  const [expandedSections, setExpandedSections] = useState<string[]>(['Content Creation', 'Research & Analysis']);

  const toggleSection = (sectionName: string) => {
    setExpandedSections(prev =>
      prev.includes(sectionName)
        ? prev.filter(name => name !== sectionName)
        : [...prev, sectionName]
    );
  };

  const isActive = (href: string) => {
    if (href === '/dashboard') {
      return pathname === '/dashboard' || pathname === '/';
    }
    return pathname.startsWith(href);
  };

  const canAccess = (item: NavigationItem) => {
    if (!item.requiresSubscription) return true;
    return isSubscribed(item.requiresSubscription);
  };

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    const active = isActive(item.href);
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedSections.includes(item.name);
    const accessible = canAccess(item);

    if (hasChildren) {
      return (
        <div key={item.name}>
          <button
            onClick={() => toggleSection(item.name)}
            className={`w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
              active
                ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700'
                : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
            }`}
          >
            <div className="flex items-center">
              <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
              <span>{item.name}</span>
              {item.badge && (
                <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                  {item.badge}
                </span>
              )}
            </div>
            {isExpanded ? (
              <ChevronDownIcon className="h-4 w-4" />
            ) : (
              <ChevronRightIcon className="h-4 w-4" />
            )}
          </button>

          {isExpanded && (
            <div className="ml-6 mt-1 space-y-1">
              {item.children.map(child => renderNavigationItem(child, level + 1))}
            </div>
          )}
        </div>
      );
    }

    return (
      <Link
        key={item.name}
        href={accessible ? item.href : '/pricing'}
        className={`group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
          level > 0 ? 'ml-0' : ''
        } ${
          active
            ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700'
            : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
        } ${!accessible ? 'opacity-60' : ''}`}
        onClick={isMobile ? onClose : undefined}
      >
        <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
        <span className="flex-1">{item.name}</span>
        {item.badge && (
          <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
            {item.badge}
          </span>
        )}
        {!accessible && (
          <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
            Pro
          </span>
        )}
      </Link>
    );
  };

  return (
    <>
      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        } ${isMobile ? 'lg:translate-x-0' : 'lg:translate-x-0'}`}
      >
        <div className="flex flex-col h-full">
          {/* Sidebar header */}
          <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">S</span>
              </div>
              <span className="ml-2 text-xl font-bold text-gray-900">SEO SAAS</span>
            </div>
            {isMobile && (
              <button
                onClick={onClose}
                className="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            )}
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {navigation.map(item => renderNavigationItem(item))}
          </nav>

          {/* Usage indicator */}
          <div className="p-4 border-t border-gray-200">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Usage This Month</span>
                <span className="text-sm text-gray-500">{userTier}</span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min((usageCount / usageLimit) * 100, 100)}%` }}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-500">
                  {usageCount} / {usageLimit} requests
                </span>
                {usageCount >= usageLimit * 0.8 && (
                  <Link
                    href="/pricing"
                    className="text-xs text-blue-600 hover:text-blue-800 font-medium"
                  >
                    Upgrade
                  </Link>
                )}
              </div>
            </div>

            {/* Upgrade button */}
            {userTier === 'free' && (
              <Link
                href="/pricing"
                className="w-full mt-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white text-center py-2 px-4 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center justify-center"
              >
                <BoltIcon className="h-4 w-4 mr-2" />
                Upgrade to Pro
              </Link>
            )}
          </div>
        </div>
      </div>
    </>
  );
}