/* Enhanced Sidebar Styles - Professional collapsible sections with animations */

/* ===========================
   ENHANCED SIDEBAR STRUCTURE
   =========================== */

.enhanced-nav-section {
    position: relative;
    margin-bottom: 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.enhanced-nav-section:hover {
    background-color: rgba(59, 130, 246, 0.02);
}

.enhanced-section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.2s ease;
    position: relative;
    user-select: none;
}

.enhanced-section-header:hover {
    background-color: rgba(59, 130, 246, 0.05);
}

.enhanced-section-header:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
    background-color: rgba(59, 130, 246, 0.1);
}

.section-header-focused {
    background-color: rgba(59, 130, 246, 0.08);
}

.nav-section-title {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    transition: color 0.2s ease;
}

.enhanced-section-header:hover .nav-section-title {
    color: #1f2937;
}

/* ===========================
   ENHANCED TOGGLE BUTTON
   =========================== */

.enhanced-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    padding: 0;
    background: none;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6b7280;
}

.enhanced-toggle:hover {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    transform: scale(1.1);
}

.enhanced-toggle:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 1px;
    background-color: rgba(59, 130, 246, 0.1);
}

.enhanced-toggle:active {
    transform: scale(0.95);
}

.toggle-icon {
    transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    transform-origin: center;
}

/* ===========================
   SECTION STATE STYLES
   =========================== */

.section-expanded .enhanced-section-header {
    border-bottom: 1px solid rgba(229, 231, 235, 0.6);
}

.section-collapsed .enhanced-section-header {
    border-bottom: none;
}

.section-collapsed .enhanced-toggle {
    color: #9ca3af;
}

.section-collapsed .nav-section-title {
    color: #6b7280;
}

.section-expanded .enhanced-section-header:hover {
    background-color: rgba(59, 130, 246, 0.08);
}

/* ===========================
   ENHANCED NAV ITEMS
   =========================== */

.enhanced-nav-items {
    overflow: hidden;
    transition: height 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.enhanced-nav-items .nav-item {
    transition: all 0.2s ease;
    border-radius: 6px;
    margin: 2px 8px;
}

.enhanced-nav-items .nav-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    position: relative;
    text-decoration: none;
    color: #6b7280;
    font-size: 14px;
    font-weight: 500;
}

.enhanced-nav-items .nav-link:hover {
    background-color: rgba(59, 130, 246, 0.08);
    color: #374151;
    transform: translateX(2px);
}

.enhanced-nav-items .nav-link:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 1px;
    background-color: rgba(59, 130, 246, 0.1);
}

.enhanced-nav-items .nav-item.active .nav-link {
    background-color: #3b82f6;
    color: white;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.enhanced-nav-items .nav-item.active .nav-link:hover {
    background-color: #2563eb;
    transform: translateX(0);
}

.enhanced-nav-items .nav-icon {
    flex-shrink: 0;
    transition: all 0.2s ease;
}

.enhanced-nav-items .nav-link:hover .nav-icon {
    transform: scale(1.1);
}

.enhanced-nav-items .nav-item.active .nav-icon {
    color: white;
}

/* ===========================
   SIDEBAR COLLAPSED STATE
   =========================== */

.sidebar-collapsed {
    transform: translateX(-100%);
}

.sidebar-collapsed .enhanced-section-header {
    justify-content: center;
    padding: 8px;
}

.sidebar-collapsed .nav-section-title {
    display: none;
}

.sidebar-collapsed .enhanced-toggle {
    display: none;
}

.sidebar-collapsed .enhanced-nav-items .nav-link span {
    display: none;
}

.sidebar-collapsed .enhanced-nav-items .nav-link {
    justify-content: center;
    padding: 8px;
    min-width: 40px;
}

/* Body adjustments when sidebar is collapsed */
body.sidebar-collapsed .main-content {
    margin-left: 60px;
}

body.sidebar-collapsed .dashboard-app {
    grid-template-columns: 60px 1fr;
}

/* ===========================
   HOVER EFFECTS AND MICRO-INTERACTIONS
   =========================== */

.enhanced-nav-section {
    position: relative;
}

.enhanced-nav-section::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(to bottom, #3b82f6, #1d4ed8);
    border-radius: 0 2px 2px 0;
    transform: scaleY(0);
    transition: transform 0.3s ease;
    opacity: 0;
}

.enhanced-nav-section:hover::before {
    transform: scaleY(1);
    opacity: 1;
}

.enhanced-nav-section.section-expanded::before {
    transform: scaleY(1);
    opacity: 0.6;
}

/* ===========================
   LOADING AND ANIMATION STATES
   =========================== */

.sidebar-loading .enhanced-nav-section {
    opacity: 0;
    transform: translateY(20px);
    animation: slideInUp 0.3s ease forwards;
}

.sidebar-loading .enhanced-nav-section:nth-child(1) { animation-delay: 0.1s; }
.sidebar-loading .enhanced-nav-section:nth-child(2) { animation-delay: 0.2s; }
.sidebar-loading .enhanced-nav-section:nth-child(3) { animation-delay: 0.3s; }
.sidebar-loading .enhanced-nav-section:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===========================
   RESPONSIVE DESIGN
   =========================== */

@media (max-width: 768px) {
    .sidebar-navigation {
        position: fixed;
        left: 0;
        top: 0;
        height: 100vh;
        z-index: 1000;
        background: white;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar-navigation:not(.sidebar-collapsed) {
        transform: translateX(0);
    }
    
    .enhanced-section-header {
        padding: 16px 20px;
    }
    
    .enhanced-nav-items .nav-link {
        padding: 12px 16px;
        font-size: 16px;
    }
    
    /* Mobile backdrop */
    .sidebar-navigation:not(.sidebar-collapsed)::before {
        content: '';
        position: fixed;
        top: 0;
        left: 100%;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.5);
        z-index: -1;
    }
}

@media (max-width: 480px) {
    .enhanced-section-header {
        padding: 14px 16px;
    }
    
    .enhanced-nav-items .nav-link {
        padding: 10px 16px;
    }
    
    .nav-section-title {
        font-size: 13px;
    }
}

/* ===========================
   DARK MODE SUPPORT
   =========================== */

@media (prefers-color-scheme: dark) {
    .enhanced-nav-section:hover {
        background-color: rgba(59, 130, 246, 0.1);
    }
    
    .enhanced-section-header:hover {
        background-color: rgba(59, 130, 246, 0.15);
    }
    
    .nav-section-title {
        color: #e5e7eb;
    }
    
    .enhanced-section-header:hover .nav-section-title {
        color: #f9fafb;
    }
    
    .enhanced-nav-items .nav-link {
        color: #d1d5db;
    }
    
    .enhanced-nav-items .nav-link:hover {
        background-color: rgba(59, 130, 246, 0.15);
        color: #f3f4f6;
    }
    
    .section-collapsed .nav-section-title {
        color: #9ca3af;
    }
    
    .enhanced-toggle {
        color: #9ca3af;
    }
    
    .enhanced-toggle:hover {
        background-color: rgba(59, 130, 246, 0.2);
        color: #60a5fa;
    }
}

/* ===========================
   HIGH CONTRAST MODE
   =========================== */

@media (prefers-contrast: high) {
    .enhanced-section-header:focus {
        outline: 3px solid #000;
        outline-offset: 2px;
    }
    
    .enhanced-nav-items .nav-link:focus {
        outline: 3px solid #000;
        outline-offset: 1px;
    }
    
    .enhanced-nav-items .nav-item.active .nav-link {
        background-color: #000;
        color: #fff;
    }
}

/* ===========================
   REDUCED MOTION SUPPORT
   =========================== */

@media (prefers-reduced-motion: reduce) {
    .enhanced-nav-section,
    .enhanced-section-header,
    .enhanced-toggle,
    .toggle-icon,
    .enhanced-nav-items,
    .enhanced-nav-items .nav-link,
    .enhanced-nav-items .nav-icon {
        transition: none;
        animation: none;
    }
    
    .sidebar-loading .enhanced-nav-section {
        animation: none;
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===========================
   PRINT STYLES
   =========================== */

@media print {
    .sidebar-navigation {
        display: none;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
}