/**
 * Competitor Intelligence Center
 * Advanced competitor analysis with visual insights and monitoring
 */

'use client'

import React, { useState, useEffect } from 'react'
import { useNotifications } from '@/components/Notifications'
import Card from '@/components/UI/Card'
import Button from '@/components/UI/Button'
import Input from '@/components/UI/Input'
import Badge from '@/components/UI/Badge'
import LoadingSpinner from '@/components/UI/LoadingSpinner'
import {
  UserGroupIcon,
  MagnifyingGlassIcon,
  ChartBarIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  EyeIcon,
  LinkIcon,
  GlobeAltIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  PlusIcon,
  ArrowTopRightOnSquareIcon,
  FunnelIcon,
  ArrowPathIcon,
  BellIcon,
  DocumentTextIcon,
  CursorArrowRaysIcon,
  ClockIcon,
  MapIcon,
  ShieldCheckIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline'

interface CompetitorIntelligenceCenterProps {
  onCompetitorSelect?: (competitor: CompetitorData) => void
}

interface CompetitorData {
  id: string
  name: string
  domain: string
  logo?: string
  category: string
  location: string
  metrics: {
    visibility: number
    totalKeywords: number
    organicTraffic: number
    paidTraffic: number
    backlinks: number
    domainRating: number
    contentPages: number
    socialFollowers: number
  }
  performance: {
    visibilityChange: number
    trafficChange: number
    keywordChange: number
    rankingTrend: 'up' | 'down' | 'stable'
  }
  keywordOverlap: {
    sharedKeywords: number
    competingKeywords: number
    uniqueKeywords: number
    overlapPercentage: number
  }
  contentStrategy: {
    publishingFrequency: number
    avgContentLength: number
    contentTypes: string[]
    topPerformingContent: Array<{
      title: string
      url: string
      traffic: number
      keywords: number
    }>
  }
  technicalSEO: {
    pageSpeed: number
    mobileScore: number
    coreWebVitals: 'good' | 'needs-improvement' | 'poor'
    securityScore: number
  }
  monitoringAlerts: Array<{
    type: 'keyword_gain' | 'keyword_loss' | 'content_publish' | 'backlink_gain'
    message: string
    timestamp: string
    severity: 'low' | 'medium' | 'high'
  }>
  lastUpdated: string
}

interface CompetitorLandscape {
  quadrant: 'leaders' | 'challengers' | 'visionaries' | 'niche'
  competitors: Array<{
    id: string
    name: string
    marketShare: number
    innovation: number
    x: number
    y: number
  }>
}

export default function CompetitorIntelligenceCenter({
  onCompetitorSelect
}: CompetitorIntelligenceCenterProps) {
  const [competitors, setCompetitors] = useState<CompetitorData[]>([])
  const [selectedCompetitor, setSelectedCompetitor] = useState<CompetitorData | null>(null)
  const [activeView, setActiveView] = useState<'overview' | 'analysis' | 'landscape' | 'monitoring'>('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [isLoading, setIsLoading] = useState(false)
  const [landscape, setLandscape] = useState<CompetitorLandscape[]>([])
  const [showAddCompetitor, setShowAddCompetitor] = useState(false)
  
  const { success: notifySuccess, error: notifyError } = useNotifications()

  // Mock data - in real app this would come from API
  useEffect(() => {
    loadCompetitorData()
  }, [])

  const loadCompetitorData = async () => {
    setIsLoading(true)
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      setCompetitors([
        {
          id: 'comp1',
          name: 'DigitalLeader',
          domain: 'digitalleader.com',
          category: 'Digital Marketing',
          location: 'United States',
          metrics: {
            visibility: 94.2,
            totalKeywords: 12450,
            organicTraffic: 890000,
            paidTraffic: 234000,
            backlinks: 156000,
            domainRating: 82,
            contentPages: 2340,
            socialFollowers: 125000
          },
          performance: {
            visibilityChange: 5.2,
            trafficChange: 12.8,
            keywordChange: 3.4,
            rankingTrend: 'up'
          },
          keywordOverlap: {
            sharedKeywords: 1240,
            competingKeywords: 890,
            uniqueKeywords: 8920,
            overlapPercentage: 12.3
          },
          contentStrategy: {
            publishingFrequency: 15,
            avgContentLength: 2100,
            contentTypes: ['Blog Posts', 'Case Studies', 'White Papers', 'Videos'],
            topPerformingContent: [
              {
                title: 'Ultimate Guide to Digital Marketing in 2024',
                url: '/guides/digital-marketing-2024',
                traffic: 45000,
                keywords: 340
              },
              {
                title: 'SEO Best Practices for Modern Websites',
                url: '/blog/seo-best-practices',
                traffic: 32000,
                keywords: 280
              }
            ]
          },
          technicalSEO: {
            pageSpeed: 85,
            mobileScore: 92,
            coreWebVitals: 'good',
            securityScore: 95
          },
          monitoringAlerts: [
            {
              type: 'keyword_gain',
              message: 'Gained ranking for "digital marketing strategy" (moved to position 3)',
              timestamp: '2024-01-15T10:30:00Z',
              severity: 'high'
            },
            {
              type: 'content_publish',
              message: 'Published new comprehensive guide on content marketing',
              timestamp: '2024-01-14T14:20:00Z',
              severity: 'medium'
            }
          ],
          lastUpdated: '2024-01-15T12:00:00Z'
        },
        {
          id: 'comp2',
          name: 'SEOPro',
          domain: 'seopro.com',
          category: 'SEO Services',
          location: 'United Kingdom',
          metrics: {
            visibility: 78.6,
            totalKeywords: 8940,
            organicTraffic: 520000,
            paidTraffic: 89000,
            backlinks: 89000,
            domainRating: 75,
            contentPages: 1560,
            socialFollowers: 67000
          },
          performance: {
            visibilityChange: -2.1,
            trafficChange: 8.4,
            keywordChange: -1.2,
            rankingTrend: 'down'
          },
          keywordOverlap: {
            sharedKeywords: 890,
            competingKeywords: 650,
            uniqueKeywords: 6200,
            overlapPercentage: 9.8
          },
          contentStrategy: {
            publishingFrequency: 8,
            avgContentLength: 1800,
            contentTypes: ['Technical Guides', 'Tutorials', 'Tools Reviews'],
            topPerformingContent: [
              {
                title: 'Technical SEO Audit Checklist',
                url: '/resources/technical-seo-audit',
                traffic: 28000,
                keywords: 190
              },
              {
                title: 'Link Building Strategies That Work',
                url: '/blog/link-building-strategies',
                traffic: 22000,
                keywords: 145
              }
            ]
          },
          technicalSEO: {
            pageSpeed: 78,
            mobileScore: 88,
            coreWebVitals: 'needs-improvement',
            securityScore: 89
          },
          monitoringAlerts: [
            {
              type: 'keyword_loss',
              message: 'Lost ranking for "seo tools" (dropped from position 2 to 5)',
              timestamp: '2024-01-13T09:15:00Z',
              severity: 'high'
            }
          ],
          lastUpdated: '2024-01-15T11:45:00Z'
        },
        {
          id: 'comp3',
          name: 'ContentKing',
          domain: 'contentking.com',
          category: 'Content Marketing',
          location: 'Canada',
          metrics: {
            visibility: 65.3,
            totalKeywords: 6720,
            organicTraffic: 340000,
            paidTraffic: 45000,
            backlinks: 67000,
            domainRating: 68,
            contentPages: 1890,
            socialFollowers: 89000
          },
          performance: {
            visibilityChange: 15.7,
            trafficChange: 22.1,
            keywordChange: 8.9,
            rankingTrend: 'up'
          },
          keywordOverlap: {
            sharedKeywords: 670,
            competingKeywords: 420,
            uniqueKeywords: 4890,
            overlapPercentage: 8.2
          },
          contentStrategy: {
            publishingFrequency: 12,
            avgContentLength: 2400,
            contentTypes: ['Long-form Articles', 'Infographics', 'Podcasts'],
            topPerformingContent: [
              {
                title: 'Content Marketing ROI: Complete Measurement Guide',
                url: '/guides/content-marketing-roi',
                traffic: 35000,
                keywords: 240
              }
            ]
          },
          technicalSEO: {
            pageSpeed: 91,
            mobileScore: 95,
            coreWebVitals: 'good',
            securityScore: 92
          },
          monitoringAlerts: [
            {
              type: 'backlink_gain',
              message: 'Gained 150 new high-quality backlinks from authority sites',
              timestamp: '2024-01-12T16:30:00Z',
              severity: 'medium'
            }
          ],
          lastUpdated: '2024-01-15T11:30:00Z'
        }
      ])

      // Mock competitive landscape data
      setLandscape([
        {
          quadrant: 'leaders',
          competitors: [
            { id: 'comp1', name: 'DigitalLeader', marketShare: 85, innovation: 90, x: 85, y: 90 },
            { id: 'industry1', name: 'IndustryGiant', marketShare: 92, innovation: 75, x: 92, y: 75 }
          ]
        },
        {
          quadrant: 'challengers',
          competitors: [
            { id: 'comp2', name: 'SEOPro', marketShare: 78, innovation: 65, x: 78, y: 65 },
            { id: 'rising1', name: 'RisingStart', marketShare: 68, innovation: 72, x: 68, y: 72 }
          ]
        },
        {
          quadrant: 'visionaries',
          competitors: [
            { id: 'comp3', name: 'ContentKing', marketShare: 45, innovation: 88, x: 45, y: 88 },
            { id: 'innovative1', name: 'TechInnovator', marketShare: 35, innovation: 95, x: 35, y: 95 }
          ]
        },
        {
          quadrant: 'niche',
          competitors: [
            { id: 'niche1', name: 'NichePlayer', marketShare: 25, innovation: 45, x: 25, y: 45 },
            { id: 'niche2', name: 'SpecialistSEO', marketShare: 30, innovation: 40, x: 30, y: 40 }
          ]
        }
      ])
    } catch (error) {
      notifyError('Failed to load competitor data')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCompetitorSelect = (competitor: CompetitorData) => {
    setSelectedCompetitor(competitor)
    if (onCompetitorSelect) {
      onCompetitorSelect(competitor)
    }
  }

  const filteredCompetitors = competitors.filter(competitor => {
    const matchesSearch = competitor.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         competitor.domain.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = categoryFilter === 'all' || competitor.category === categoryFilter
    return matchesSearch && matchesCategory
  })

  const getPerformanceIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUpIcon className="h-4 w-4 text-green-500" />
      case 'down': return <TrendingDownIcon className="h-4 w-4 text-red-500" />
      default: return <div className="h-4 w-4" />
    }
  }

  const getChangeColor = (change: number) => {
    if (change > 0) return 'text-green-600 dark:text-green-400'
    if (change < 0) return 'text-red-600 dark:text-red-400'
    return 'text-gray-600 dark:text-gray-400'
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
    return num.toString()
  }

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'keyword_gain': return <TrendingUpIcon className="h-4 w-4 text-green-500" />
      case 'keyword_loss': return <TrendingDownIcon className="h-4 w-4 text-red-500" />
      case 'content_publish': return <DocumentTextIcon className="h-4 w-4 text-blue-500" />
      case 'backlink_gain': return <LinkIcon className="h-4 w-4 text-purple-500" />
      default: return <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500" />
    }
  }

  const getQuadrantColor = (quadrant: string) => {
    switch (quadrant) {
      case 'leaders': return 'bg-green-100 dark:bg-green-900/20 border-green-300 dark:border-green-700'
      case 'challengers': return 'bg-blue-100 dark:bg-blue-900/20 border-blue-300 dark:border-blue-700'
      case 'visionaries': return 'bg-purple-100 dark:bg-purple-900/20 border-purple-300 dark:border-purple-700'
      case 'niche': return 'bg-yellow-100 dark:bg-yellow-900/20 border-yellow-300 dark:border-yellow-700'
      default: return 'bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-700'
    }
  }

  const CompetitorCard = ({ competitor }: { competitor: CompetitorData }) => (
    <Card 
      className={`p-4 cursor-pointer transition-all hover:shadow-md ${
        selectedCompetitor?.id === competitor.id ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : ''
      }`}
      onClick={() => handleCompetitorSelect(competitor)}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-medium text-sm">
              {competitor.name.substring(0, 2).toUpperCase()}
            </span>
          </div>
          <div>
            <h3 className="font-medium text-gray-900 dark:text-gray-100">
              {competitor.name}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {competitor.domain}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline">{competitor.category}</Badge>
          {getPerformanceIcon(competitor.performance.rankingTrend)}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Visibility</div>
          <div className="font-medium text-gray-900 dark:text-gray-100">
            {competitor.metrics.visibility}%
          </div>
          <div className={`text-xs ${getChangeColor(competitor.performance.visibilityChange)}`}>
            {competitor.performance.visibilityChange > 0 ? '+' : ''}
            {competitor.performance.visibilityChange.toFixed(1)}%
          </div>
        </div>
        <div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Traffic</div>
          <div className="font-medium text-gray-900 dark:text-gray-100">
            {formatNumber(competitor.metrics.organicTraffic)}
          </div>
          <div className={`text-xs ${getChangeColor(competitor.performance.trafficChange)}`}>
            {competitor.performance.trafficChange > 0 ? '+' : ''}
            {competitor.performance.trafficChange.toFixed(1)}%
          </div>
        </div>
        <div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Keywords</div>
          <div className="font-medium text-gray-900 dark:text-gray-100">
            {formatNumber(competitor.metrics.totalKeywords)}
          </div>
        </div>
        <div>
          <div className="text-sm text-gray-500 dark:text-gray-400">DR Score</div>
          <div className="font-medium text-gray-900 dark:text-gray-100">
            {competitor.metrics.domainRating}
          </div>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-xs text-gray-500 dark:text-gray-400">
          {competitor.keywordOverlap.overlapPercentage.toFixed(1)}% keyword overlap
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400">
          {competitor.location}
        </div>
      </div>
    </Card>
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading competitor intelligence...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Competitor Intelligence Center
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Advanced competitor analysis with monitoring and strategic insights
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            onClick={() => setShowAddCompetitor(true)}
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Competitor
          </Button>
          <Button
            variant="outline"
            onClick={() => loadCompetitorData()}
          >
            <ArrowPathIcon className="h-4 w-4 mr-2" />
            Refresh Data
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center">
            <UserGroupIcon className="h-8 w-8 text-blue-600 dark:text-blue-400 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Tracked Competitors</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{competitors.length}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center">
            <MagnifyingGlassIcon className="h-8 w-8 text-green-600 dark:text-green-400 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Shared Keywords</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {competitors.reduce((acc, c) => acc + c.keywordOverlap.sharedKeywords, 0)}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center">
            <BellIcon className="h-8 w-8 text-yellow-600 dark:text-yellow-400 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Active Alerts</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {competitors.reduce((acc, c) => acc + c.monitoringAlerts.length, 0)}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center">
            <TrendingUpIcon className="h-8 w-8 text-purple-600 dark:text-purple-400 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Avg Visibility</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {Math.round(competitors.reduce((acc, c) => acc + c.metrics.visibility, 0) / competitors.length)}%
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card className="p-4">
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              type="text"
              placeholder="Search competitors by name or domain..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          >
            <option value="all">All Categories</option>
            <option value="Digital Marketing">Digital Marketing</option>
            <option value="SEO Services">SEO Services</option>
            <option value="Content Marketing">Content Marketing</option>
          </select>
          <Badge variant="secondary">
            {filteredCompetitors.length} competitors
          </Badge>
        </div>
      </Card>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <div className="flex space-x-8">
          <button
            onClick={() => setActiveView('overview')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeView === 'overview'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <UserGroupIcon className="h-4 w-4 inline mr-2" />
            Overview
          </button>
          <button
            onClick={() => setActiveView('analysis')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeView === 'analysis'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <ChartBarIcon className="h-4 w-4 inline mr-2" />
            Deep Analysis
          </button>
          <button
            onClick={() => setActiveView('landscape')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeView === 'landscape'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <MapIcon className="h-4 w-4 inline mr-2" />
            Competitive Landscape
          </button>
          <button
            onClick={() => setActiveView('monitoring')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeView === 'monitoring'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <BellIcon className="h-4 w-4 inline mr-2" />
            Monitoring
          </button>
        </div>
      </div>

      {/* Tab Content */}
      {activeView === 'overview' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredCompetitors.map((competitor) => (
            <CompetitorCard key={competitor.id} competitor={competitor} />
          ))}
          
          {filteredCompetitors.length === 0 && (
            <div className="col-span-full">
              <Card className="p-8 text-center">
                <UserGroupIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  No competitors found
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {searchQuery ? 'Try adjusting your search terms' : 'Add competitors to start tracking'}
                </p>
                <Button onClick={() => setShowAddCompetitor(true)}>
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Add Competitor
                </Button>
              </Card>
            </div>
          )}
        </div>
      )}

      {activeView === 'analysis' && selectedCompetitor && (
        <div className="space-y-6">
          <Card className="p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-medium">
                    {selectedCompetitor.name.substring(0, 2).toUpperCase()}
                  </span>
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                    {selectedCompetitor.name} Analysis
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    {selectedCompetitor.domain}
                  </p>
                </div>
              </div>
              <Button
                variant="outline"
                onClick={() => window.open(`https://${selectedCompetitor.domain}`, '_blank')}
              >
                <ArrowTopRightOnSquareIcon className="h-4 w-4 mr-2" />
                Visit Site
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {selectedCompetitor.metrics.visibility}%
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Visibility Score</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {formatNumber(selectedCompetitor.metrics.organicTraffic)}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Organic Traffic</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {formatNumber(selectedCompetitor.metrics.totalKeywords)}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Total Keywords</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {selectedCompetitor.metrics.domainRating}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Domain Rating</div>
              </div>
            </div>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Keyword Overlap */}
            <Card className="p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Keyword Overlap Analysis
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Shared Keywords</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {formatNumber(selectedCompetitor.keywordOverlap.sharedKeywords)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Competing Keywords</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {formatNumber(selectedCompetitor.keywordOverlap.competingKeywords)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Unique Keywords</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {formatNumber(selectedCompetitor.keywordOverlap.uniqueKeywords)}
                  </span>
                </div>
                <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Overlap Percentage</span>
                    <span className="font-bold text-lg text-blue-600 dark:text-blue-400">
                      {selectedCompetitor.keywordOverlap.overlapPercentage.toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
            </Card>

            {/* Content Strategy */}
            <Card className="p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Content Strategy
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Publishing Frequency</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {selectedCompetitor.contentStrategy.publishingFrequency}/month
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Avg Content Length</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {formatNumber(selectedCompetitor.contentStrategy.avgContentLength)} words
                  </span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400 text-sm mb-2 block">Content Types</span>
                  <div className="flex flex-wrap gap-1">
                    {selectedCompetitor.contentStrategy.contentTypes.map((type, index) => (
                      <Badge key={index} variant="outline" size="sm">
                        {type}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </Card>

            {/* Technical SEO */}
            <Card className="p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Technical SEO Performance
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Page Speed</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-900 dark:text-gray-100">
                      {selectedCompetitor.technicalSEO.pageSpeed}
                    </span>
                    <Badge variant={selectedCompetitor.technicalSEO.pageSpeed >= 90 ? 'success' : 
                                   selectedCompetitor.technicalSEO.pageSpeed >= 70 ? 'warning' : 'error'}>
                      {selectedCompetitor.technicalSEO.pageSpeed >= 90 ? 'Good' : 
                       selectedCompetitor.technicalSEO.pageSpeed >= 70 ? 'Fair' : 'Poor'}
                    </Badge>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Mobile Score</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {selectedCompetitor.technicalSEO.mobileScore}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Core Web Vitals</span>
                  <Badge variant={selectedCompetitor.technicalSEO.coreWebVitals === 'good' ? 'success' : 
                                 selectedCompetitor.technicalSEO.coreWebVitals === 'needs-improvement' ? 'warning' : 'error'}>
                    {selectedCompetitor.technicalSEO.coreWebVitals.replace('-', ' ')}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Security Score</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {selectedCompetitor.technicalSEO.securityScore}
                  </span>
                </div>
              </div>
            </Card>

            {/* Top Performing Content */}
            <Card className="p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Top Performing Content
              </h3>
              <div className="space-y-3">
                {selectedCompetitor.contentStrategy.topPerformingContent.map((content, index) => (
                  <div key={index} className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                      {content.title}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      {content.url}
                    </div>
                    <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                      <span>{formatNumber(content.traffic)} traffic</span>
                      <span>{content.keywords} keywords</span>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        </div>
      )}

      {activeView === 'landscape' && (
        <div className="space-y-6">
          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">
              Competitive Landscape Matrix
            </h3>
            
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 h-96 relative">
              {/* Quadrant Labels */}
              <div className="absolute top-4 left-4 text-sm font-medium text-gray-600 dark:text-gray-400">
                Visionaries
              </div>
              <div className="absolute top-4 right-4 text-sm font-medium text-gray-600 dark:text-gray-400">
                Leaders
              </div>
              <div className="absolute bottom-4 left-4 text-sm font-medium text-gray-600 dark:text-gray-400">
                Niche Players
              </div>
              <div className="absolute bottom-4 right-4 text-sm font-medium text-gray-600 dark:text-gray-400">
                Challengers
              </div>
              
              {/* Axis Lines */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-px h-full bg-gray-300 dark:bg-gray-600" />
              </div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="h-px w-full bg-gray-300 dark:bg-gray-600" />
              </div>
              
              {/* Axis Labels */}
              <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-xs text-gray-500 dark:text-gray-400">
                Market Share →
              </div>
              <div className="absolute left-2 top-1/2 transform -translate-y-1/2 -rotate-90 text-xs text-gray-500 dark:text-gray-400">
                Innovation →
              </div>
              
              {/* Competitors positioned on matrix */}
              <div className="absolute inset-6">
                {landscape.map(quadrant => 
                  quadrant.competitors.map(comp => {
                    const competitor = competitors.find(c => c.id === comp.id)
                    return (
                      <div
                        key={comp.id}
                        className={`absolute w-3 h-3 rounded-full cursor-pointer transition-all hover:scale-125 ${
                          competitor ? 'bg-blue-500' : 'bg-gray-400'
                        }`}
                        style={{
                          left: `${comp.x}%`,
                          bottom: `${comp.y}%`,
                          transform: 'translate(-50%, 50%)'
                        }}
                        title={`${comp.name} - Market Share: ${comp.marketShare}%, Innovation: ${comp.innovation}%`}
                      />
                    )
                  })
                )}
              </div>
              
              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                <div className="text-center text-gray-500 dark:text-gray-400">
                  <MapIcon className="h-8 w-8 mx-auto mb-2" />
                  <p className="text-xs">Interactive competitive positioning</p>
                </div>
              </div>
            </div>
            
            <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
              {landscape.map((quadrant, index) => (
                <div key={index} className={`p-4 rounded-lg border-2 ${getQuadrantColor(quadrant.quadrant)}`}>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2 capitalize">
                    {quadrant.quadrant.replace('_', ' ')}
                  </h4>
                  <div className="space-y-1">
                    {quadrant.competitors.map((comp, compIndex) => (
                      <div key={compIndex} className="text-sm text-gray-600 dark:text-gray-400">
                        {comp.name}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>
      )}

      {activeView === 'monitoring' && (
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Competitor Monitoring Alerts
            </h3>
            <Button size="sm">
              <BellIcon className="h-4 w-4 mr-2" />
              Configure Alerts
            </Button>
          </div>
          
          <div className="space-y-4">
            {competitors.flatMap(competitor => 
              competitor.monitoringAlerts.map((alert, index) => (
                <div key={`${competitor.id}-${index}`} className="flex items-start space-x-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  {getAlertIcon(alert.type)}
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <div className="font-medium text-gray-900 dark:text-gray-100">
                        {competitor.name}
                      </div>
                      <Badge variant={alert.severity === 'high' ? 'error' : alert.severity === 'medium' ? 'warning' : 'secondary'}>
                        {alert.severity}
                      </Badge>
                    </div>
                    <div className="text-gray-700 dark:text-gray-300 mb-2">
                      {alert.message}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {new Date(alert.timestamp).toLocaleString()}
                    </div>
                  </div>
                </div>
              ))
            )}
            
            {competitors.every(c => c.monitoringAlerts.length === 0) && (
              <div className="text-center py-8">
                <BellIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  No alerts yet
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Set up monitoring to track competitor activities and changes
                </p>
              </div>
            )}
          </div>
        </Card>
      )}
    </div>
  )
}