/**
 * Report Generation E2E Tests
 * 
 * Tests for automated report generation, download functionality,
 * scheduled reports, and various report formats.
 */

import { test, expect, Page } from '@playwright/test';
import { loginAsUser, existingUser } from './auth.spec';

// Test data
const testReport = {
  name: 'Test Analytics Report',
  type: 'detailed_analytics',
  format: 'pdf',
  timeRange: 'month',
  includeSections: ['metrics', 'charts', 'insights'],
  recipients: ['<EMAIL>'],
  schedule: 'weekly'
};

test.describe('Report Generation', () => {
  
  test.beforeEach(async ({ page }) => {
    // Login as existing user
    await loginAsUser(page, existingUser.email, existingUser.password);
  });

  test.describe('Report Creation', () => {
    
    test('should navigate to reports page', async ({ page }) => {
      // Navigate to reports
      await page.click('[data-testid="reports-nav"]');
      await expect(page).toHaveURL('**/dashboard/reports');
      
      // Should show reports dashboard
      await expect(page.locator('[data-testid="reports-dashboard"]')).toBeVisible();
      await expect(page.locator('[data-testid="generate-report-button"]')).toBeVisible();
      await expect(page.locator('[data-testid="report-templates"]')).toBeVisible();
    });

    test('should create a new report successfully', async ({ page }) => {
      await page.goto('/dashboard/reports');
      
      // Click generate new report
      await page.click('[data-testid="generate-report-button"]');
      await expect(page).toHaveURL('**/dashboard/reports/new');
      
      // Fill report form
      await page.fill('[data-testid="report-name-input"]', testReport.name);
      await page.selectOption('[data-testid="report-template-select"]', testReport.type);
      await page.selectOption('[data-testid="report-format-select"]', testReport.format);
      await page.selectOption('[data-testid="time-range-select"]', testReport.timeRange);
      
      // Select report sections
      for (const section of testReport.includeSections) {
        await page.check(`[data-testid="section-${section}-checkbox"]`);
      }
      
      // Generate report
      await page.click('[data-testid="generate-report-submit"]');
      
      // Should show generation progress
      await expect(page.locator('[data-testid="report-generation-progress"]')).toBeVisible();
      await expect(page.locator('[data-testid="generation-status"]')).toContainText('Generating report...');
      
      // Should complete generation
      await expect(page.locator('[data-testid="report-ready-message"]')).toBeVisible({ timeout: 60000 });
      await expect(page.locator('[data-testid="download-report-button"]')).toBeVisible();
    });

    test('should validate report form fields', async ({ page }) => {
      await page.goto('/dashboard/reports/new');
      
      // Try to submit without required fields
      await page.click('[data-testid="generate-report-submit"]');
      
      // Should show validation errors
      await expect(page.locator('[data-testid="report-name-error"]')).toContainText('Report name is required');
      await expect(page.locator('[data-testid="template-error"]')).toContainText('Please select a template');
      await expect(page.locator('[data-testid="format-error"]')).toContainText('Please select a format');
    });

    test('should select from available templates', async ({ page }) => {
      await page.goto('/dashboard/reports/new');
      
      // Should show template options
      const templateSelect = page.locator('[data-testid="report-template-select"]');
      await expect(templateSelect).toBeVisible();
      
      // Should have executive summary template
      await expect(templateSelect.locator('option[value="executive_summary"]')).toBeVisible();
      
      // Should have detailed analytics template
      await expect(templateSelect.locator('option[value="detailed_analytics"]')).toBeVisible();
      
      // Select template and verify description
      await page.selectOption('[data-testid="report-template-select"]', 'executive_summary');
      await expect(page.locator('[data-testid="template-description"]')).toContainText('High-level overview report');
    });

    test('should support multiple report formats', async ({ page }) => {
      await page.goto('/dashboard/reports/new');
      
      const formats = ['pdf', 'html', 'csv', 'excel', 'json'];
      
      for (const format of formats) {
        // Select format
        await page.selectOption('[data-testid="report-format-select"]', format);
        
        // Should show format-specific options
        await expect(page.locator(`[data-testid="format-options-${format}"]`)).toBeVisible();
      }
    });

    test('should customize report sections', async ({ page }) => {
      await page.goto('/dashboard/reports/new');
      
      // Should show section options
      await expect(page.locator('[data-testid="report-sections"]')).toBeVisible();
      
      // Should have checkboxes for different sections
      await expect(page.locator('[data-testid="section-metrics-checkbox"]')).toBeVisible();
      await expect(page.locator('[data-testid="section-charts-checkbox"]')).toBeVisible();
      await expect(page.locator('[data-testid="section-insights-checkbox"]')).toBeVisible();
      await expect(page.locator('[data-testid="section-tables-checkbox"]')).toBeVisible();
      
      // Should be able to select/deselect sections
      await page.check('[data-testid="section-metrics-checkbox"]');
      await expect(page.locator('[data-testid="section-metrics-checkbox"]')).toBeChecked();
      
      await page.uncheck('[data-testid="section-metrics-checkbox"]');
      await expect(page.locator('[data-testid="section-metrics-checkbox"]')).not.toBeChecked();
    });
  });

  test.describe('Report Generation Process', () => {
    
    test('should show generation progress', async ({ page }) => {
      await page.goto('/dashboard/reports/new');
      
      // Fill and submit form
      await page.fill('[data-testid="report-name-input"]', testReport.name);
      await page.selectOption('[data-testid="report-template-select"]', testReport.type);
      await page.selectOption('[data-testid="report-format-select"]', testReport.format);
      await page.click('[data-testid="generate-report-submit"]');
      
      // Should show progress modal
      await expect(page.locator('[data-testid="generation-modal"]')).toBeVisible();
      
      // Should show progress bar
      await expect(page.locator('[data-testid="generation-progress-bar"]')).toBeVisible();
      
      // Should show generation stages
      await expect(page.locator('[data-testid="generation-stage-collecting"]')).toBeVisible();
      await expect(page.locator('[data-testid="generation-stage-processing"]')).toBeVisible();
      await expect(page.locator('[data-testid="generation-stage-formatting"]')).toBeVisible();
      
      // Progress should increase
      const initialProgress = await page.locator('[data-testid="generation-progress-bar"]').getAttribute('value');
      await page.waitForTimeout(2000);
      const updatedProgress = await page.locator('[data-testid="generation-progress-bar"]').getAttribute('value');
      expect(parseInt(updatedProgress!)).toBeGreaterThan(parseInt(initialProgress!));
    });

    test('should handle generation errors', async ({ page }) => {
      // Simulate error by intercepting API call
      await page.route('**/api/reports/generate', route => {
        route.fulfill({
          status: 500,
          body: JSON.stringify({ error: 'Generation failed' })
        });
      });
      
      await page.goto('/dashboard/reports/new');
      
      // Fill and submit form
      await page.fill('[data-testid="report-name-input"]', testReport.name);
      await page.selectOption('[data-testid="report-template-select"]', testReport.type);
      await page.selectOption('[data-testid="report-format-select"]', testReport.format);
      await page.click('[data-testid="generate-report-submit"]');
      
      // Should show error message
      await expect(page.locator('[data-testid="generation-error"]')).toBeVisible();
      await expect(page.locator('[data-testid="generation-error"]')).toContainText('Generation failed');
      
      // Should show retry button
      await expect(page.locator('[data-testid="retry-generation-button"]')).toBeVisible();
    });

    test('should allow cancelling generation', async ({ page }) => {
      await page.goto('/dashboard/reports/new');
      
      // Start generation
      await page.fill('[data-testid="report-name-input"]', testReport.name);
      await page.selectOption('[data-testid="report-template-select"]', testReport.type);
      await page.selectOption('[data-testid="report-format-select"]', testReport.format);
      await page.click('[data-testid="generate-report-submit"]');
      
      // Should show cancel button
      await expect(page.locator('[data-testid="cancel-generation-button"]')).toBeVisible();
      
      // Click cancel
      await page.click('[data-testid="cancel-generation-button"]');
      
      // Should show cancellation confirmation
      await expect(page.locator('[data-testid="generation-cancelled"]')).toBeVisible();
      
      // Should return to form
      await expect(page.locator('[data-testid="generate-report-submit"]')).toBeVisible();
    });
  });

  test.describe('Report Download', () => {
    
    test('should download PDF report', async ({ page }) => {
      await page.goto('/dashboard/reports/new');
      
      // Generate report
      await page.fill('[data-testid="report-name-input"]', testReport.name);
      await page.selectOption('[data-testid="report-template-select"]', testReport.type);
      await page.selectOption('[data-testid="report-format-select"]', 'pdf');
      await page.click('[data-testid="generate-report-submit"]');
      
      // Wait for generation to complete
      await expect(page.locator('[data-testid="download-report-button"]')).toBeVisible({ timeout: 60000 });
      
      // Start download
      const downloadPromise = page.waitForEvent('download');
      await page.click('[data-testid="download-report-button"]');
      const download = await downloadPromise;
      
      // Verify download
      expect(download.suggestedFilename()).toContain('.pdf');
      expect(download.suggestedFilename()).toContain(testReport.name);
    });

    test('should download HTML report', async ({ page }) => {
      await page.goto('/dashboard/reports/new');
      
      // Generate HTML report
      await page.fill('[data-testid="report-name-input"]', testReport.name);
      await page.selectOption('[data-testid="report-template-select"]', testReport.type);
      await page.selectOption('[data-testid="report-format-select"]', 'html');
      await page.click('[data-testid="generate-report-submit"]');
      
      await expect(page.locator('[data-testid="download-report-button"]')).toBeVisible({ timeout: 60000 });
      
      // Download HTML report
      const downloadPromise = page.waitForEvent('download');
      await page.click('[data-testid="download-report-button"]');
      const download = await downloadPromise;
      
      expect(download.suggestedFilename()).toContain('.html');
    });

    test('should download CSV report', async ({ page }) => {
      await page.goto('/dashboard/reports/new');
      
      // Generate CSV report
      await page.fill('[data-testid="report-name-input"]', testReport.name);
      await page.selectOption('[data-testid="report-template-select"]', testReport.type);
      await page.selectOption('[data-testid="report-format-select"]', 'csv');
      await page.click('[data-testid="generate-report-submit"]');
      
      await expect(page.locator('[data-testid="download-report-button"]')).toBeVisible({ timeout: 60000 });
      
      // Download CSV report
      const downloadPromise = page.waitForEvent('download');
      await page.click('[data-testid="download-report-button"]');
      const download = await downloadPromise;
      
      expect(download.suggestedFilename()).toContain('.csv');
    });

    test('should preview report before download', async ({ page }) => {
      await page.goto('/dashboard/reports/new');
      
      // Generate report
      await page.fill('[data-testid="report-name-input"]', testReport.name);
      await page.selectOption('[data-testid="report-template-select"]', testReport.type);
      await page.selectOption('[data-testid="report-format-select"]', 'html');
      await page.click('[data-testid="generate-report-submit"]');
      
      await expect(page.locator('[data-testid="preview-report-button"]')).toBeVisible({ timeout: 60000 });
      
      // Preview report
      await page.click('[data-testid="preview-report-button"]');
      
      // Should show preview modal
      await expect(page.locator('[data-testid="report-preview-modal"]')).toBeVisible();
      
      // Should show report content
      await expect(page.locator('[data-testid="report-preview-content"]')).toBeVisible();
      
      // Should have close button
      await expect(page.locator('[data-testid="close-preview-button"]')).toBeVisible();
    });

    test('should show download progress for large reports', async ({ page }) => {
      await page.goto('/dashboard/reports/new');
      
      // Generate large report
      await page.fill('[data-testid="report-name-input"]', 'Large Report');
      await page.selectOption('[data-testid="report-template-select"]', 'detailed_analytics');
      await page.selectOption('[data-testid="report-format-select"]', 'pdf');
      await page.selectOption('[data-testid="time-range-select"]', 'year');
      await page.click('[data-testid="generate-report-submit"]');
      
      await expect(page.locator('[data-testid="download-report-button"]')).toBeVisible({ timeout: 60000 });
      
      // Start download
      await page.click('[data-testid="download-report-button"]');
      
      // Should show download progress
      await expect(page.locator('[data-testid="download-progress"]')).toBeVisible();
      await expect(page.locator('[data-testid="download-progress-bar"]')).toBeVisible();
    });
  });

  test.describe('Report Management', () => {
    
    test('should list generated reports', async ({ page }) => {
      await page.goto('/dashboard/reports');
      
      // Should show reports list
      await expect(page.locator('[data-testid="reports-list"]')).toBeVisible();
      
      // Should show report items
      await expect(page.locator('[data-testid="report-item"]')).toHaveCount({ min: 1 });
      
      // Each report should show key information
      const reports = page.locator('[data-testid="report-item"]');
      const reportCount = await reports.count();
      
      for (let i = 0; i < reportCount; i++) {
        const report = reports.nth(i);
        await expect(report.locator('[data-testid="report-name"]')).toBeVisible();
        await expect(report.locator('[data-testid="report-format"]')).toBeVisible();
        await expect(report.locator('[data-testid="report-created-date"]')).toBeVisible();
        await expect(report.locator('[data-testid="report-size"]')).toBeVisible();
      }
    });

    test('should filter reports by format', async ({ page }) => {
      await page.goto('/dashboard/reports');
      
      // Filter by PDF
      await page.selectOption('[data-testid="report-format-filter"]', 'pdf');
      
      // Should show only PDF reports
      const pdfReports = page.locator('[data-testid="report-item"][data-format="pdf"]');
      await expect(pdfReports).toHaveCount({ min: 1 });
      
      // Should not show other formats
      const otherFormats = page.locator('[data-testid="report-item"]:not([data-format="pdf"])');
      await expect(otherFormats).toHaveCount(0);
    });

    test('should search reports by name', async ({ page }) => {
      await page.goto('/dashboard/reports');
      
      // Search for specific report
      await page.fill('[data-testid="report-search"]', 'Test');
      await page.click('[data-testid="search-button"]');
      
      // Should filter results
      const searchResults = page.locator('[data-testid="report-item"]');
      const resultTexts = await searchResults.allTextContents();
      
      resultTexts.forEach(text => {
        expect(text.toLowerCase()).toContain('test');
      });
    });

    test('should delete reports', async ({ page }) => {
      await page.goto('/dashboard/reports');
      
      // Click delete on first report
      await page.click('[data-testid="report-item"]:first-child [data-testid="delete-report-button"]');
      
      // Should show confirmation dialog
      await expect(page.locator('[data-testid="delete-confirmation-modal"]')).toBeVisible();
      
      // Confirm deletion
      await page.click('[data-testid="confirm-delete-button"]');
      
      // Should show success message
      await expect(page.locator('[data-testid="delete-success-message"]')).toBeVisible();
    });

    test('should show report details', async ({ page }) => {
      await page.goto('/dashboard/reports');
      
      // Click on first report
      await page.click('[data-testid="report-item"]:first-child [data-testid="view-report-button"]');
      
      // Should show report details page
      await expect(page).toHaveURL('**/dashboard/reports/**');
      
      // Should show report metadata
      await expect(page.locator('[data-testid="report-details"]')).toBeVisible();
      await expect(page.locator('[data-testid="report-title"]')).toBeVisible();
      await expect(page.locator('[data-testid="report-metadata"]')).toBeVisible();
      await expect(page.locator('[data-testid="report-actions"]')).toBeVisible();
    });
  });

  test.describe('Scheduled Reports', () => {
    
    test('should create scheduled report', async ({ page }) => {
      await page.goto('/dashboard/reports/new');
      
      // Fill report form
      await page.fill('[data-testid="report-name-input"]', 'Scheduled Weekly Report');
      await page.selectOption('[data-testid="report-template-select"]', testReport.type);
      await page.selectOption('[data-testid="report-format-select"]', testReport.format);
      
      // Enable scheduling
      await page.check('[data-testid="schedule-report-checkbox"]');
      
      // Should show scheduling options
      await expect(page.locator('[data-testid="schedule-options"]')).toBeVisible();
      
      // Set schedule
      await page.selectOption('[data-testid="schedule-frequency-select"]', 'weekly');
      await page.fill('[data-testid="schedule-recipients-input"]', testReport.recipients.join(', '));
      
      // Create scheduled report
      await page.click('[data-testid="create-schedule-button"]');
      
      // Should show success message
      await expect(page.locator('[data-testid="schedule-success-message"]')).toBeVisible();
      
      // Should navigate to schedules page
      await expect(page).toHaveURL('**/dashboard/reports/schedules');
    });

    test('should manage scheduled reports', async ({ page }) => {
      await page.goto('/dashboard/reports/schedules');
      
      // Should show schedules list
      await expect(page.locator('[data-testid="schedules-list"]')).toBeVisible();
      
      // Should show schedule items
      await expect(page.locator('[data-testid="schedule-item"]')).toHaveCount({ min: 1 });
      
      // Each schedule should show key information
      const schedules = page.locator('[data-testid="schedule-item"]');
      const scheduleCount = await schedules.count();
      
      for (let i = 0; i < scheduleCount; i++) {
        const schedule = schedules.nth(i);
        await expect(schedule.locator('[data-testid="schedule-name"]')).toBeVisible();
        await expect(schedule.locator('[data-testid="schedule-frequency"]')).toBeVisible();
        await expect(schedule.locator('[data-testid="schedule-next-run"]')).toBeVisible();
        await expect(schedule.locator('[data-testid="schedule-status"]')).toBeVisible();
      }
    });

    test('should enable/disable scheduled reports', async ({ page }) => {
      await page.goto('/dashboard/reports/schedules');
      
      // Toggle first schedule
      await page.click('[data-testid="schedule-item"]:first-child [data-testid="toggle-schedule-button"]');
      
      // Should show updated status
      await expect(page.locator('[data-testid="schedule-item"]:first-child [data-testid="schedule-status"]')).toContainText('Disabled');
      
      // Toggle again
      await page.click('[data-testid="schedule-item"]:first-child [data-testid="toggle-schedule-button"]');
      
      // Should show enabled status
      await expect(page.locator('[data-testid="schedule-item"]:first-child [data-testid="schedule-status"]')).toContainText('Enabled');
    });

    test('should edit scheduled report', async ({ page }) => {
      await page.goto('/dashboard/reports/schedules');
      
      // Click edit on first schedule
      await page.click('[data-testid="schedule-item"]:first-child [data-testid="edit-schedule-button"]');
      
      // Should show edit form
      await expect(page.locator('[data-testid="edit-schedule-form"]')).toBeVisible();
      
      // Update frequency
      await page.selectOption('[data-testid="schedule-frequency-select"]', 'monthly');
      
      // Save changes
      await page.click('[data-testid="save-schedule-button"]');
      
      // Should show success message
      await expect(page.locator('[data-testid="schedule-updated-message"]')).toBeVisible();
    });

    test('should delete scheduled report', async ({ page }) => {
      await page.goto('/dashboard/reports/schedules');
      
      // Click delete on first schedule
      await page.click('[data-testid="schedule-item"]:first-child [data-testid="delete-schedule-button"]');
      
      // Should show confirmation
      await expect(page.locator('[data-testid="delete-schedule-confirmation"]')).toBeVisible();
      
      // Confirm deletion
      await page.click('[data-testid="confirm-delete-schedule-button"]');
      
      // Should show success message
      await expect(page.locator('[data-testid="schedule-deleted-message"]')).toBeVisible();
    });
  });

  test.describe('Report Email Delivery', () => {
    
    test('should send report via email', async ({ page }) => {
      await page.goto('/dashboard/reports');
      
      // Click email on first report
      await page.click('[data-testid="report-item"]:first-child [data-testid="email-report-button"]');
      
      // Should show email form
      await expect(page.locator('[data-testid="email-form-modal"]')).toBeVisible();
      
      // Fill email form
      await page.fill('[data-testid="email-recipients-input"]', '<EMAIL>');
      await page.fill('[data-testid="email-subject-input"]', 'Analytics Report');
      await page.fill('[data-testid="email-message-input"]', 'Please find the attached report.');
      
      // Send email
      await page.click('[data-testid="send-email-button"]');
      
      // Should show success message
      await expect(page.locator('[data-testid="email-sent-message"]')).toBeVisible();
    });

    test('should validate email recipients', async ({ page }) => {
      await page.goto('/dashboard/reports');
      
      // Click email on first report
      await page.click('[data-testid="report-item"]:first-child [data-testid="email-report-button"]');
      
      // Try to send without recipients
      await page.click('[data-testid="send-email-button"]');
      
      // Should show validation error
      await expect(page.locator('[data-testid="recipients-error"]')).toContainText('Please enter at least one recipient');
      
      // Try with invalid email
      await page.fill('[data-testid="email-recipients-input"]', 'invalid-email');
      await page.click('[data-testid="send-email-button"]');
      
      // Should show validation error
      await expect(page.locator('[data-testid="recipients-error"]')).toContainText('Please enter valid email addresses');
    });
  });

  test.describe('Performance and Accessibility', () => {
    
    test('should generate reports within acceptable time', async ({ page }) => {
      await page.goto('/dashboard/reports/new');
      
      const startTime = Date.now();
      
      // Generate report
      await page.fill('[data-testid="report-name-input"]', testReport.name);
      await page.selectOption('[data-testid="report-template-select"]', testReport.type);
      await page.selectOption('[data-testid="report-format-select"]', testReport.format);
      await page.click('[data-testid="generate-report-submit"]');
      
      // Wait for completion
      await expect(page.locator('[data-testid="download-report-button"]')).toBeVisible({ timeout: 60000 });
      
      const generationTime = Date.now() - startTime;
      
      // Should complete within 60 seconds
      expect(generationTime).toBeLessThan(60000);
    });

    test('should be responsive on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      await page.goto('/dashboard/reports/new');
      
      // Form should be usable on mobile
      await expect(page.locator('[data-testid="report-form"]')).toBeVisible();
      
      // All form elements should be visible
      await expect(page.locator('[data-testid="report-name-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="report-template-select"]')).toBeVisible();
      await expect(page.locator('[data-testid="report-format-select"]')).toBeVisible();
      await expect(page.locator('[data-testid="generate-report-submit"]')).toBeVisible();
    });

    test('should be keyboard accessible', async ({ page }) => {
      await page.goto('/dashboard/reports/new');
      
      // Use keyboard to navigate form
      await page.keyboard.press('Tab'); // Report name
      await page.keyboard.type(testReport.name);
      
      await page.keyboard.press('Tab'); // Template select
      await page.keyboard.press('ArrowDown'); // Select option
      
      await page.keyboard.press('Tab'); // Format select
      await page.keyboard.press('ArrowDown'); // Select option
      
      await page.keyboard.press('Tab'); // Generate button
      await page.keyboard.press('Enter'); // Submit
      
      // Should start generation
      await expect(page.locator('[data-testid="generation-modal"]')).toBeVisible();
    });
  });
});

// Helper function to generate test report
async function generateTestReport(page: Page, reportConfig: typeof testReport) {
  await page.goto('/dashboard/reports/new');
  
  await page.fill('[data-testid="report-name-input"]', reportConfig.name);
  await page.selectOption('[data-testid="report-template-select"]', reportConfig.type);
  await page.selectOption('[data-testid="report-format-select"]', reportConfig.format);
  await page.click('[data-testid="generate-report-submit"]');
  
  await expect(page.locator('[data-testid="download-report-button"]')).toBeVisible({ timeout: 60000 });
}

// Export helper for use in other test files
export { generateTestReport, testReport };