import cors from 'cors';

// CORS middleware configuration
export const corsMiddleware = () => {
  const corsOptions = {
    origin: function (origin, callback) {
      // Allow requests with no origin (like mobile apps or curl requests)
      if (!origin) return callback(null, true);
      
      const allowedOrigins = process.env.NODE_ENV === 'production' 
        ? [process.env.FRONTEND_URL || 'https://your-frontend-domain.com']
        : [
            // Development ports
            'http://localhost:3000',    // Next.js default
            'http://localhost:3001',    // Backend port alternative
            'http://localhost:5000',    // Backend default
            'http://localhost:5555',    // Alternative frontend
            'http://localhost:8080',    // HTML frontend
            'http://127.0.0.1:3000',
            'http://127.0.0.1:3001',
            'http://127.0.0.1:5000',
            'http://127.0.0.1:5555',
            'http://127.0.0.1:8080',
            // Add any other development URLs as needed
          ];
      
      if (allowedOrigins.indexOf(origin) !== -1) {
        callback(null, true);
      } else {
        console.warn(`CORS: Origin ${origin} not allowed`);
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: [
      'Content-Type', 
      'Authorization', 
      'X-Requested-With',
      'Accept',
      'Origin',
      'X-Auth-Token',
      'X-Session-ID'
    ],
    exposedHeaders: [
      'X-Total-Count',
      'X-Page-Count',
      'X-Current-Page',
      'X-Per-Page'
    ],
    maxAge: 86400, // 24 hours
    optionsSuccessStatus: 200
  };

  return cors(corsOptions);
};

// Export default instance
export default corsMiddleware();