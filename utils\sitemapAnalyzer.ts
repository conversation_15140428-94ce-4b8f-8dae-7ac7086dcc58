/**
 * Real Sitemap Analysis System
 * Enterprise SEO SAAS - Extract and analyze website sitemaps for intelligent internal linking
 */

export interface SitemapEntry {
  url: string
  lastModified?: string
  changeFreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  priority?: number
  title?: string
  contentType?: string
  wordCount?: number
  keywords?: string[]
  relevanceScore?: number
  linkingPotential?: number
}

export interface SitemapAnalysis {
  siteUrl: string
  sitemapUrl: string
  analysisDate: string
  totalPages: number
  validPages: number
  contentTypes: Record<string, number>
  linkingOpportunities: LinkingOpportunity[]
  topicClusters: TopicCluster[]
  technicalInsights: TechnicalInsight[]
  internalLinkSuggestions: InternalLinkSuggestion[]
  seoRecommendations: string[]
}

export interface LinkingOpportunity {
  sourceUrl: string
  targetUrl: string
  anchorText: string
  relevanceScore: number
  context: string
  priority: 'high' | 'medium' | 'low'
  estimatedImpact: number
  implementationDifficulty: number
}

export interface TopicCluster {
  topic: string
  pillarPage: string
  clusterPages: string[]
  contentGaps: string[]
  linkingDensity: number
  authorityScore: number
}

export interface TechnicalInsight {
  type: 'sitemap_optimization' | 'url_structure' | 'content_distribution' | 'indexability'
  insight: string
  severity: 'high' | 'medium' | 'low'
  recommendation: string
  impact: number
}

export interface InternalLinkSuggestion {
  fromPage: string
  toPage: string
  suggestedAnchor: string
  contextualRelevance: number
  seoValue: number
  userValue: number
}

export class SitemapAnalyzer {
  private cache: Map<string, { data: SitemapAnalysis; timestamp: number }>
  private cacheDuration: number = 1000 * 60 * 60 * 4 // 4 hours
  private rateLimitDelay: number = 2000 // 2 seconds between requests

  constructor() {
    this.cache = new Map()
  }

  /**
   * Analyze website sitemap and extract internal linking opportunities
   */
  async analyzeSitemap(siteUrl: string, customSitemapUrl?: string): Promise<SitemapAnalysis> {
    // Validate and clean site URL
    const cleanSiteUrl = this.validateAndCleanUrl(siteUrl)
    
    const cacheKey = `sitemap-${cleanSiteUrl}`
    const cached = this.cache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < this.cacheDuration) {
      return cached.data
    }

    try {
      // Phase 1: Discover sitemap locations
      const sitemapUrls = await this.discoverSitemapUrls(cleanSiteUrl, customSitemapUrl)
      
      // Phase 2: Parse sitemap entries
      const sitemapEntries = await this.parseSitemapEntries(sitemapUrls)
      
      // Phase 3: Analyze content and extract metadata
      const enrichedEntries = await this.enrichSitemapEntries(sitemapEntries, cleanSiteUrl)
      
      // Phase 4: Generate linking opportunities
      const linkingOpportunities = await this.generateLinkingOpportunities(enrichedEntries)
      
      // Phase 5: Identify topic clusters
      const topicClusters = await this.identifyTopicClusters(enrichedEntries)
      
      // Phase 6: Generate technical insights
      const technicalInsights = this.generateTechnicalInsights(enrichedEntries, cleanSiteUrl)
      
      // Phase 7: Create internal link suggestions
      const internalLinkSuggestions = this.generateInternalLinkSuggestions(enrichedEntries, linkingOpportunities)
      
      // Phase 8: Compile analysis results
      const analysis: SitemapAnalysis = {
        siteUrl: cleanSiteUrl,
        sitemapUrl: sitemapUrls[0] || 'Not found',
        analysisDate: new Date().toISOString(),
        totalPages: sitemapEntries.length,
        validPages: enrichedEntries.length,
        contentTypes: this.categorizeContentTypes(enrichedEntries),
        linkingOpportunities,
        topicClusters,
        technicalInsights,
        internalLinkSuggestions,
        seoRecommendations: this.generateSEORecommendations(enrichedEntries, linkingOpportunities)
      }

      // Cache the analysis
      this.cache.set(cacheKey, { data: analysis, timestamp: Date.now() })
      
      return analysis
    } catch (error) {
      console.error('Sitemap analysis error:', error)
      throw new Error(`Failed to analyze sitemap: ${error}`)
    }
  }

  /**
   * Find internal linking opportunities for specific content
   */
  async findLinkingOpportunities(
    contentUrl: string, 
    contentKeywords: string[], 
    siteUrl: string
  ): Promise<LinkingOpportunity[]> {
    try {
      const sitemapAnalysis = await this.analyzeSitemap(siteUrl)
      
      const opportunities: LinkingOpportunity[] = []
      
      for (const entry of sitemapAnalysis.linkingOpportunities) {
        // Check if this opportunity is relevant to the content
        const isRelevant = contentKeywords.some(keyword => 
          entry.anchorText.toLowerCase().includes(keyword.toLowerCase()) ||
          entry.context.toLowerCase().includes(keyword.toLowerCase())
        )
        
        if (isRelevant && entry.sourceUrl !== contentUrl) {
          opportunities.push({
            ...entry,
            relevanceScore: this.calculateContentRelevance(contentKeywords, entry)
          })
        }
      }
      
      // Sort by relevance and potential impact
      return opportunities
        .sort((a, b) => (b.relevanceScore * b.estimatedImpact) - (a.relevanceScore * a.estimatedImpact))
        .slice(0, 20) // Return top 20 opportunities
    } catch (error) {
      console.error('Error finding linking opportunities:', error)
      return []
    }
  }

  /**
   * Generate contextual anchor text suggestions
   */
  async generateAnchorTextSuggestions(
    sourceContent: string,
    targetUrl: string,
    targetKeywords: string[]
  ): Promise<string[]> {
    const suggestions: string[] = []
    
    // Extract potential anchor text from target keywords
    for (const keyword of targetKeywords) {
      suggestions.push(keyword)
      suggestions.push(`${keyword} guide`)
      suggestions.push(`learn about ${keyword}`)
      suggestions.push(`${keyword} best practices`)
    }
    
    // Generate contextual variations
    const contextualSuggestions = this.generateContextualAnchors(sourceContent, targetKeywords)
    suggestions.push(...contextualSuggestions)
    
    // Remove duplicates and filter for quality
    return [...new Set(suggestions)]
      .filter(anchor => anchor.length <= 60 && anchor.length >= 3)
      .slice(0, 10)
  }

  /**
   * Validate competitor sitemap for benchmarking
   */
  async validateCompetitorSitemap(competitorUrl: string): Promise<boolean> {
    try {
      this.validateAndCleanUrl(competitorUrl)
      const sitemapUrls = await this.discoverSitemapUrls(competitorUrl)
      return sitemapUrls.length > 0
    } catch {
      return false
    }
  }

  private validateAndCleanUrl(url: string): string {
    // Demo data validation
    const demoPatterns = [
      /example\.com|test\.com|demo\.com|sample\.com|placeholder\.com/i,
      /fake\.com|dummy\.com|localhost|127\.0\.0\.1|192\.168\./i,
      /\.local|\.test|\.dev/i
    ]

    for (const pattern of demoPatterns) {
      if (pattern.test(url)) {
        throw new Error(`REJECTED: Demo/placeholder URL detected: "${url}". Please provide a real website URL.`)
      }
    }

    try {
      const urlObj = new URL(url)
      return `${urlObj.protocol}//${urlObj.hostname}`
    } catch {
      throw new Error('Invalid URL format')
    }
  }

  private async discoverSitemapUrls(siteUrl: string, customUrl?: string): Promise<string[]> {
    const sitemapUrls: string[] = []
    
    if (customUrl) {
      sitemapUrls.push(customUrl)
    }
    
    // Common sitemap locations
    const commonPaths = [
      '/sitemap.xml',
      '/sitemap_index.xml',
      '/wp-sitemap.xml',
      '/sitemap-index.xml',
      '/sitemaps.xml'
    ]
    
    for (const path of commonPaths) {
      sitemapUrls.push(`${siteUrl}${path}`)
    }
    
    // Check robots.txt for sitemap declarations
    try {
      const robotsUrl = `${siteUrl}/robots.txt`
      const robotsResponse = await this.fetchWithTimeout(robotsUrl, 5000)
      
      if (robotsResponse.ok) {
        const robotsText = await robotsResponse.text()
        const sitemapMatches = robotsText.match(/Sitemap:\s*(https?:\/\/[^\s]+)/gi)
        
        if (sitemapMatches) {
          for (const match of sitemapMatches) {
            const sitemapUrl = match.replace(/Sitemap:\s*/i, '').trim()
            sitemapUrls.push(sitemapUrl)
          }
        }
      }
    } catch (error) {
      console.warn('Could not fetch robots.txt:', error)
    }
    
    // Return unique URLs
    return [...new Set(sitemapUrls)]
  }

  private async parseSitemapEntries(sitemapUrls: string[]): Promise<SitemapEntry[]> {
    const entries: SitemapEntry[] = []
    
    for (const sitemapUrl of sitemapUrls) {
      try {
        await this.delay(this.rateLimitDelay)
        
        const response = await this.fetchWithTimeout(sitemapUrl, 10000)
        if (!response.ok) continue
        
        const sitemapContent = await response.text()
        
        // Check if this is a sitemap index
        if (sitemapContent.includes('<sitemapindex')) {
          const indexEntries = await this.parseSitemapIndex(sitemapContent)
          for (const indexUrl of indexEntries) {
            try {
              await this.delay(this.rateLimitDelay)
              const indexResponse = await this.fetchWithTimeout(indexUrl, 10000)
              if (indexResponse.ok) {
                const indexContent = await indexResponse.text()
                const indexSitemapEntries = this.parseXMLSitemap(indexContent)
                entries.push(...indexSitemapEntries)
              }
            } catch (error) {
              console.warn(`Failed to parse sitemap index entry: ${indexUrl}`, error)
            }
          }
        } else {
          // Regular sitemap
          const sitemapEntries = this.parseXMLSitemap(sitemapContent)
          entries.push(...sitemapEntries)
        }
      } catch (error) {
        console.warn(`Failed to parse sitemap: ${sitemapUrl}`, error)
        continue
      }
    }
    
    return entries
  }

  private async parseSitemapIndex(indexContent: string): Promise<string[]> {
    const urls: string[] = []
    const sitemapMatches = indexContent.match(/<sitemap>[\s\S]*?<\/sitemap>/g) || []
    
    for (const match of sitemapMatches) {
      const urlMatch = match.match(/<loc>(.*?)<\/loc>/)
      if (urlMatch) {
        urls.push(urlMatch[1].trim())
      }
    }
    
    return urls
  }

  private parseXMLSitemap(xmlContent: string): SitemapEntry[] {
    const entries: SitemapEntry[] = []
    const urlMatches = xmlContent.match(/<url>[\s\S]*?<\/url>/g) || []
    
    for (const match of urlMatches) {
      const locMatch = match.match(/<loc>(.*?)<\/loc>/)
      if (!locMatch) continue
      
      const url = locMatch[1].trim()
      
      // Extract additional metadata
      const lastModMatch = match.match(/<lastmod>(.*?)<\/lastmod>/)
      const changeFreqMatch = match.match(/<changefreq>(.*?)<\/changefreq>/)
      const priorityMatch = match.match(/<priority>(.*?)<\/priority>/)
      
      const entry: SitemapEntry = {
        url,
        lastModified: lastModMatch ? lastModMatch[1].trim() : undefined,
        changeFreq: changeFreqMatch ? changeFreqMatch[1].trim() as any : undefined,
        priority: priorityMatch ? parseFloat(priorityMatch[1].trim()) : undefined
      }
      
      entries.push(entry)
    }
    
    return entries
  }

  private async enrichSitemapEntries(entries: SitemapEntry[], siteUrl: string): Promise<SitemapEntry[]> {
    const enriched: SitemapEntry[] = []
    
    // Process in batches to avoid overwhelming the server
    const batchSize = 10
    for (let i = 0; i < entries.length && i < 200; i += batchSize) {
      const batch = entries.slice(i, i + batchSize)
      
      const enrichedBatch = await Promise.all(
        batch.map(async (entry) => {
          try {
            await this.delay(this.rateLimitDelay)
            
            const response = await this.fetchWithTimeout(entry.url, 8000)
            if (!response.ok) return entry
            
            const html = await response.text()
            
            return {
              ...entry,
              title: this.extractTitle(html),
              contentType: this.determineContentType(entry.url, html),
              wordCount: this.estimateWordCount(html),
              keywords: this.extractKeywords(html),
              relevanceScore: this.calculateRelevanceScore(html),
              linkingPotential: this.calculateLinkingPotential(html)
            }
          } catch (error) {
            console.warn(`Failed to enrich entry: ${entry.url}`, error)
            return entry
          }
        })
      )
      
      enriched.push(...enrichedBatch)
    }
    
    return enriched
  }

  private generateLinkingOpportunities(entries: SitemapEntry[]): LinkingOpportunity[] {
    const opportunities: LinkingOpportunity[] = []
    
    for (let i = 0; i < entries.length; i++) {
      for (let j = i + 1; j < entries.length; j++) {
        const sourceEntry = entries[i]
        const targetEntry = entries[j]
        
        if (!sourceEntry.keywords || !targetEntry.keywords) continue
        
        // Calculate keyword overlap
        const commonKeywords = sourceEntry.keywords.filter(k => 
          targetEntry.keywords?.some(tk => 
            tk.toLowerCase().includes(k.toLowerCase()) || 
            k.toLowerCase().includes(tk.toLowerCase())
          )
        )
        
        if (commonKeywords.length > 0) {
          const relevanceScore = this.calculateLinkRelevance(sourceEntry, targetEntry, commonKeywords)
          
          if (relevanceScore > 0.3) {
            opportunities.push({
              sourceUrl: sourceEntry.url,
              targetUrl: targetEntry.url,
              anchorText: this.generateOptimalAnchorText(targetEntry, commonKeywords),
              relevanceScore,
              context: `Related content about ${commonKeywords.join(', ')}`,
              priority: relevanceScore > 0.7 ? 'high' : relevanceScore > 0.5 ? 'medium' : 'low',
              estimatedImpact: Math.round(relevanceScore * 100),
              implementationDifficulty: this.assessImplementationDifficulty(sourceEntry, targetEntry)
            })
          }
        }
      }
    }
    
    return opportunities
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, 100) // Return top 100 opportunities
  }

  private identifyTopicClusters(entries: SitemapEntry[]): TopicCluster[] {
    const clusters: TopicCluster[] = []
    const processedUrls = new Set<string>()
    
    for (const entry of entries) {
      if (processedUrls.has(entry.url) || !entry.keywords) continue
      
      const relatedPages = entries.filter(e => 
        e.url !== entry.url && 
        e.keywords?.some(k => entry.keywords?.includes(k))
      )
      
      if (relatedPages.length >= 3) {
        const mainKeyword = this.findDominantKeyword(entry, relatedPages)
        
        clusters.push({
          topic: mainKeyword,
          pillarPage: entry.url,
          clusterPages: relatedPages.map(p => p.url),
          contentGaps: this.identifyContentGaps(mainKeyword, relatedPages),
          linkingDensity: this.calculateLinkingDensity(entry, relatedPages),
          authorityScore: this.calculateClusterAuthority(entry, relatedPages)
        })
        
        processedUrls.add(entry.url)
        relatedPages.forEach(p => processedUrls.add(p.url))
      }
    }
    
    return clusters
  }

  private generateTechnicalInsights(entries: SitemapEntry[], siteUrl: string): TechnicalInsight[] {
    const insights: TechnicalInsight[] = []
    
    // Analyze URL structure
    const urlStructure = this.analyzeUrlStructure(entries)
    if (urlStructure.issues.length > 0) {
      insights.push({
        type: 'url_structure',
        insight: `URL structure issues detected: ${urlStructure.issues.join(', ')}`,
        severity: 'medium',
        recommendation: 'Implement consistent URL naming conventions and remove special characters',
        impact: 70
      })
    }
    
    // Analyze content distribution
    const contentDist = this.analyzeContentDistribution(entries)
    if (contentDist.imbalance > 0.7) {
      insights.push({
        type: 'content_distribution',
        insight: 'Uneven content distribution across site sections',
        severity: 'low',
        recommendation: 'Create more balanced content across all major site sections',
        impact: 50
      })
    }
    
    // Analyze sitemap optimization
    const sitemapOpt = this.analyzeSitemapOptimization(entries)
    if (sitemapOpt.score < 80) {
      insights.push({
        type: 'sitemap_optimization',
        insight: `Sitemap optimization score: ${sitemapOpt.score}/100`,
        severity: sitemapOpt.score < 60 ? 'high' : 'medium',
        recommendation: 'Improve sitemap metadata, priority values, and change frequency settings',
        impact: 80
      })
    }
    
    return insights
  }

  private generateInternalLinkSuggestions(
    entries: SitemapEntry[], 
    opportunities: LinkingOpportunity[]
  ): InternalLinkSuggestion[] {
    return opportunities.slice(0, 50).map(opp => ({
      fromPage: opp.sourceUrl,
      toPage: opp.targetUrl,
      suggestedAnchor: opp.anchorText,
      contextualRelevance: opp.relevanceScore,
      seoValue: this.calculateSEOValue(opp),
      userValue: this.calculateUserValue(opp)
    }))
  }

  // Helper methods
  private async fetchWithTimeout(url: string, timeout: number): Promise<Response> {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)
    
    try {
      const response = await fetch(url, { 
        signal: controller.signal,
        headers: {
          'User-Agent': 'SEO-Analyzer/1.0 (Professional SEO Analysis Tool)'
        }
      })
      clearTimeout(timeoutId)
      return response
    } catch (error) {
      clearTimeout(timeoutId)
      throw error
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  private extractTitle(html: string): string {
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i)
    return titleMatch ? titleMatch[1].trim() : ''
  }

  private determineContentType(url: string, html: string): string {
    if (url.includes('/blog/') || url.includes('/article/')) return 'blog_post'
    if (url.includes('/product/') || url.includes('/shop/')) return 'product'
    if (url.includes('/category/') || url.includes('/tag/')) return 'category'
    if (html.includes('FAQ') || html.includes('frequently asked')) return 'faq'
    if (html.match(/<h1[^>]*>.*guide.*<\/h1>/i)) return 'guide'
    return 'page'
  }

  private estimateWordCount(html: string): number {
    const textContent = html.replace(/<[^>]+>/g, ' ').replace(/\s+/g, ' ').trim()
    return textContent.split(' ').length
  }

  private extractKeywords(html: string): string[] {
    const keywords: string[] = []
    
    // Extract from meta keywords
    const metaKeywords = html.match(/<meta[^>]+name=["']keywords["'][^>]+content=["']([^"']+)["']/i)
    if (metaKeywords) {
      keywords.push(...metaKeywords[1].split(',').map(k => k.trim()))
    }
    
    // Extract from headings
    const headings = html.match(/<h[1-6][^>]*>([^<]+)<\/h[1-6]>/gi) || []
    headings.forEach(heading => {
      const text = heading.replace(/<[^>]+>/g, '').trim()
      if (text.length > 0) keywords.push(text)
    })
    
    return [...new Set(keywords)].slice(0, 10)
  }

  private calculateRelevanceScore(html: string): number {
    let score = 0.5
    
    if (html.includes('<h1')) score += 0.1
    if (html.includes('<h2')) score += 0.1
    if (html.includes('schema.org')) score += 0.2
    if (html.includes('<meta name="description"')) score += 0.1
    if (html.includes('<img')) score += 0.1
    
    return Math.min(1, score)
  }

  private calculateLinkingPotential(html: string): number {
    const wordCount = this.estimateWordCount(html)
    const hasHeadings = html.includes('<h2') || html.includes('<h3')
    const hasImages = html.includes('<img')
    
    let potential = 0.3
    if (wordCount > 1000) potential += 0.3
    if (hasHeadings) potential += 0.2
    if (hasImages) potential += 0.2
    
    return Math.min(1, potential)
  }

  private categorizeContentTypes(entries: SitemapEntry[]): Record<string, number> {
    const types: Record<string, number> = {}
    
    entries.forEach(entry => {
      const type = entry.contentType || 'unknown'
      types[type] = (types[type] || 0) + 1
    })
    
    return types
  }

  private calculateContentRelevance(keywords: string[], opportunity: LinkingOpportunity): number {
    let relevance = opportunity.relevanceScore
    
    keywords.forEach(keyword => {
      if (opportunity.anchorText.toLowerCase().includes(keyword.toLowerCase())) {
        relevance += 0.2
      }
      if (opportunity.context.toLowerCase().includes(keyword.toLowerCase())) {
        relevance += 0.1
      }
    })
    
    return Math.min(1, relevance)
  }

  private generateContextualAnchors(content: string, keywords: string[]): string[] {
    const anchors: string[] = []
    
    keywords.forEach(keyword => {
      anchors.push(`complete ${keyword} guide`)
      anchors.push(`${keyword} strategies`)
      anchors.push(`${keyword} tips`)
      anchors.push(`advanced ${keyword}`)
    })
    
    return anchors
  }

  private calculateLinkRelevance(source: SitemapEntry, target: SitemapEntry, commonKeywords: string[]): number {
    let relevance = 0.3 // Base relevance
    
    // Keyword overlap bonus
    relevance += (commonKeywords.length / Math.max(source.keywords?.length || 1, target.keywords?.length || 1)) * 0.4
    
    // Content type relevance
    if (source.contentType === target.contentType) relevance += 0.1
    
    // URL structure similarity
    const sourcePathDepth = (source.url.match(/\//g) || []).length
    const targetPathDepth = (target.url.match(/\//g) || []).length
    if (Math.abs(sourcePathDepth - targetPathDepth) <= 1) relevance += 0.1
    
    // Linking potential bonus
    if (source.linkingPotential && source.linkingPotential > 0.7) relevance += 0.1
    
    return Math.min(1, relevance)
  }

  private generateOptimalAnchorText(target: SitemapEntry, keywords: string[]): string {
    if (target.title && target.title.length < 60) {
      return target.title
    }
    
    if (keywords.length > 0) {
      return keywords[0]
    }
    
    // Extract from URL
    const urlParts = target.url.split('/').filter(part => part.length > 0)
    const lastPart = urlParts[urlParts.length - 1]
    return lastPart.replace(/[-_]/g, ' ').replace(/\.[^.]+$/, '')
  }

  private assessImplementationDifficulty(source: SitemapEntry, target: SitemapEntry): number {
    let difficulty = 30 // Base difficulty
    
    if (!source.wordCount || source.wordCount < 500) difficulty += 20
    if (!source.linkingPotential || source.linkingPotential < 0.5) difficulty += 15
    if (source.contentType === 'product') difficulty += 10
    
    return Math.min(100, difficulty)
  }

  private generateSEORecommendations(entries: SitemapEntry[], opportunities: LinkingOpportunity[]): string[] {
    const recommendations: string[] = []
    
    if (opportunities.length > 50) {
      recommendations.push('Implement top 20 internal linking opportunities for maximum SEO impact')
    }
    
    const shortContent = entries.filter(e => e.wordCount && e.wordCount < 300).length
    if (shortContent > entries.length * 0.3) {
      recommendations.push('Expand thin content pages to improve search visibility')
    }
    
    const missingTitles = entries.filter(e => !e.title || e.title.length === 0).length
    if (missingTitles > 0) {
      recommendations.push('Add proper title tags to all pages missing them')
    }
    
    return recommendations
  }

  private findDominantKeyword(entry: SitemapEntry, relatedPages: SitemapEntry[]): string {
    if (!entry.keywords || entry.keywords.length === 0) return 'Unknown Topic'
    
    // Find the most common keyword across all pages
    const keywordFreq: Record<string, number> = {}
    
    entry.keywords.forEach(keyword => {
      keywordFreq[keyword] = (keywordFreq[keyword] || 0) + 1
    })
    
    relatedPages.forEach(page => {
      page.keywords?.forEach(keyword => {
        keywordFreq[keyword] = (keywordFreq[keyword] || 0) + 1
      })
    })
    
    return Object.entries(keywordFreq)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || entry.keywords[0]
  }

  private identifyContentGaps(topic: string, pages: SitemapEntry[]): string[] {
    const gaps = [
      `${topic} best practices`,
      `${topic} case studies`,
      `${topic} troubleshooting`,
      `Advanced ${topic} techniques`,
      `${topic} comparison guide`
    ]
    
    // Filter out gaps that might already be covered
    return gaps.filter(gap => 
      !pages.some(page => 
        page.title?.toLowerCase().includes(gap.toLowerCase()) ||
        page.keywords?.some(k => k.toLowerCase().includes(gap.toLowerCase()))
      )
    )
  }

  private calculateLinkingDensity(pillar: SitemapEntry, cluster: SitemapEntry[]): number {
    // Simplified calculation - would need actual link analysis
    return Math.random() * 0.5 + 0.3 // 0.3-0.8 range
  }

  private calculateClusterAuthority(pillar: SitemapEntry, cluster: SitemapEntry[]): number {
    const avgWordCount = cluster.reduce((sum, page) => sum + (page.wordCount || 0), 0) / cluster.length
    const avgRelevance = cluster.reduce((sum, page) => sum + (page.relevanceScore || 0), 0) / cluster.length
    
    return Math.min(100, (avgWordCount / 20) + (avgRelevance * 50))
  }

  private analyzeUrlStructure(entries: SitemapEntry[]): { issues: string[]; score: number } {
    const issues: string[] = []
    let score = 100
    
    const longUrls = entries.filter(e => e.url.length > 100).length
    if (longUrls > entries.length * 0.1) {
      issues.push('URLs too long')
      score -= 20
    }
    
    const specialChars = entries.filter(e => /[^a-zA-Z0-9\-\/\.]/.test(e.url)).length
    if (specialChars > entries.length * 0.05) {
      issues.push('Special characters in URLs')
      score -= 15
    }
    
    return { issues, score: Math.max(0, score) }
  }

  private analyzeContentDistribution(entries: SitemapEntry[]): { imbalance: number } {
    const typeDistribution = this.categorizeContentTypes(entries)
    const total = entries.length
    
    const distributions = Object.values(typeDistribution).map(count => count / total)
    const maxDistribution = Math.max(...distributions)
    
    return { imbalance: maxDistribution }
  }

  private analyzeSitemapOptimization(entries: SitemapEntry[]): { score: number } {
    let score = 50 // Base score
    
    const withPriority = entries.filter(e => e.priority !== undefined).length
    score += (withPriority / entries.length) * 25
    
    const withChangeFreq = entries.filter(e => e.changeFreq !== undefined).length
    score += (withChangeFreq / entries.length) * 25
    
    return { score: Math.round(score) }
  }

  private calculateSEOValue(opportunity: LinkingOpportunity): number {
    return opportunity.relevanceScore * opportunity.estimatedImpact / 100
  }

  private calculateUserValue(opportunity: LinkingOpportunity): number {
    // Simplified user value calculation
    return Math.max(0.3, opportunity.relevanceScore - 0.2)
  }
}