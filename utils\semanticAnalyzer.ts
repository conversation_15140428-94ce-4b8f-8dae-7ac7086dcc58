/**
 * Semantic Analysis Engine - Advanced LSI/Entity Extraction
 * Enterprise SEO SAAS - Deep semantic understanding for competitive content generation
 */

export interface SemanticAnalysis {
  lsiKeywords: LSIKeyword[]
  entities: ExtractedEntity[]
  keywordClusters: KeywordCluster[]
  topics: Topic[]
  semanticScore: number
  language: string
  recommendations: string[]
}

export interface LSIKeyword {
  keyword: string
  relevanceScore: number
  coOccurrenceCount: number
  semanticDistance: number
  category: 'primary' | 'secondary' | 'related'
}

export interface ExtractedEntity {
  text: string
  type: 'person' | 'organization' | 'location' | 'product' | 'event' | 'concept' | 'other'
  frequency: number
  confidence: number
  context: string[]
  variations: string[]
}

export interface KeywordCluster {
  id: string
  name: string
  keywords: string[]
  centroid: string
  coherenceScore: number
  importance: number
}

export interface Topic {
  id: string
  name: string
  keywords: string[]
  weight: number
  distribution: Record<string, number>
}

export interface KeywordDensityAnalysis {
  primaryKeyword: {
    keyword: string
    density: number
    frequency: number
    prominence: number // Based on position in title, headers, etc.
  }
  secondaryKeywords: Array<{
    keyword: string
    density: number
    frequency: number
  }>
  optimalDensityRange: {
    min: number
    max: number
    recommended: number
  }
  warnings: string[]
}

export class SemanticAnalyzer {
  private stopWords: Set<string>
  private entityPatterns: Record<string, RegExp[]>
  private arabicSupport: boolean = true

  constructor() {
    this.stopWords = this.initializeStopWords()
    this.entityPatterns = this.initializeEntityPatterns()
  }

  /**
   * Perform comprehensive semantic analysis on text
   */
  async analyzeSemantics(
    text: string,
    primaryKeyword: string,
    language: string = 'en',
    competitors?: string[]
  ): Promise<SemanticAnalysis> {
    // Detect language if not specified
    const detectedLanguage = this.detectLanguage(text) || language
    
    // Preprocess text
    const processedText = this.preprocessText(text, detectedLanguage)
    const words = this.tokenize(processedText, detectedLanguage)
    
    // Extract LSI keywords using advanced algorithms
    const lsiKeywords = this.extractAdvancedLSI(words, primaryKeyword, processedText)
    
    // Extract entities with NER
    const entities = this.extractAdvancedEntities(text, detectedLanguage)
    
    // Perform keyword clustering
    const keywordClusters = this.clusterKeywords(words, lsiKeywords)
    
    // Extract topics
    const topics = this.extractTopics(words, keywordClusters)
    
    // Calculate semantic score
    const semanticScore = this.calculateSemanticScore(lsiKeywords, entities, keywordClusters)
    
    // Generate recommendations
    const recommendations = this.generateSemanticRecommendations(
      lsiKeywords,
      entities,
      primaryKeyword,
      competitors
    )
    
    return {
      lsiKeywords,
      entities,
      keywordClusters,
      topics,
      semanticScore,
      language: detectedLanguage,
      recommendations
    }
  }

  /**
   * Analyze keyword density with advanced metrics
   */
  analyzeKeywordDensity(
    text: string,
    primaryKeyword: string,
    secondaryKeywords: string[] = []
  ): KeywordDensityAnalysis {
    const words = this.tokenize(text.toLowerCase(), 'en')
    const totalWords = words.length
    
    // Analyze primary keyword
    const primaryAnalysis = this.analyzeKeywordProminence(text, primaryKeyword, totalWords)
    
    // Analyze secondary keywords
    const secondaryAnalysis = secondaryKeywords.map(keyword => ({
      keyword,
      density: this.calculateDensity(words, keyword, totalWords),
      frequency: this.countKeywordOccurrences(words, keyword)
    }))
    
    // Determine optimal density range based on content length
    const optimalDensityRange = this.calculateOptimalDensityRange(totalWords, primaryKeyword)
    
    // Generate warnings
    const warnings = this.generateDensityWarnings(primaryAnalysis, secondaryAnalysis, optimalDensityRange)
    
    return {
      primaryKeyword: primaryAnalysis,
      secondaryKeywords: secondaryAnalysis,
      optimalDensityRange,
      warnings
    }
  }

  /**
   * Extract LSI keywords using co-occurrence matrix and semantic similarity
   */
  private extractAdvancedLSI(
    words: string[],
    primaryKeyword: string,
    fullText: string
  ): LSIKeyword[] {
    // Build co-occurrence matrix
    const coOccurrenceMatrix = this.buildCoOccurrenceMatrix(words)
    
    // Calculate semantic distances
    const keywordScores = new Map<string, {
      coOccurrence: number
      semanticDistance: number
      contextualRelevance: number
    }>()
    
    // Find words that frequently appear near the primary keyword
    const primaryWords = primaryKeyword.toLowerCase().split(/\s+/)
    const windowSize = 10 // Words within 10 positions
    
    words.forEach((word, index) => {
      if (this.stopWords.has(word) || word.length < 3) return
      
      let coOccurrenceScore = 0
      let contextualScore = 0
      
      // Check within window
      for (let i = Math.max(0, index - windowSize); i <= Math.min(words.length - 1, index + windowSize); i++) {
        if (i === index) continue
        
        if (primaryWords.includes(words[i])) {
          const distance = Math.abs(i - index)
          coOccurrenceScore += 1 / (distance + 1) // Closer words get higher scores
          contextualScore += this.getContextualWeight(words, i)
        }
      }
      
      if (coOccurrenceScore > 0) {
        const currentScore = keywordScores.get(word) || {
          coOccurrence: 0,
          semanticDistance: 1,
          contextualRelevance: 0
        }
        
        keywordScores.set(word, {
          coOccurrence: currentScore.coOccurrence + coOccurrenceScore,
          semanticDistance: this.calculateSemanticDistance(word, primaryKeyword),
          contextualRelevance: currentScore.contextualRelevance + contextualScore
        })
      }
    })
    
    // Convert to LSIKeyword array and sort by relevance
    const lsiKeywords: LSIKeyword[] = []
    
    keywordScores.forEach((scores, keyword) => {
      const relevanceScore = 
        (scores.coOccurrence * 0.4) +
        ((1 - scores.semanticDistance) * 0.3) +
        (scores.contextualRelevance * 0.3)
      
      lsiKeywords.push({
        keyword,
        relevanceScore,
        coOccurrenceCount: Math.round(scores.coOccurrence),
        semanticDistance: scores.semanticDistance,
        category: this.categorizeKeyword(relevanceScore)
      })
    })
    
    return lsiKeywords
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, 30) // Top 30 LSI keywords
  }

  /**
   * Extract entities with advanced NER patterns
   */
  private extractAdvancedEntities(text: string, language: string): ExtractedEntity[] {
    const entities = new Map<string, ExtractedEntity>()
    
    // Apply entity patterns
    Object.entries(this.entityPatterns).forEach(([type, patterns]) => {
      patterns.forEach(pattern => {
        const matches = text.matchAll(pattern)
        for (const match of matches) {
          const entityText = match[0].trim()
          if (entityText.length < 3) continue
          
          const existing = entities.get(entityText)
          if (existing) {
            existing.frequency++
            existing.context.push(this.extractContext(text, match.index!))
          } else {
            entities.set(entityText, {
              text: entityText,
              type: type as ExtractedEntity['type'],
              frequency: 1,
              confidence: this.calculateEntityConfidence(entityText, type, text),
              context: [this.extractContext(text, match.index!)],
              variations: this.findEntityVariations(entityText, text)
            })
          }
        }
      })
    })
    
    // Special handling for Arabic entities if detected
    if (language === 'ar' || this.containsArabic(text)) {
      this.extractArabicEntities(text, entities)
    }
    
    return Array.from(entities.values())
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, 50) // Top 50 entities
  }

  /**
   * Cluster keywords using k-means algorithm
   */
  private clusterKeywords(words: string[], lsiKeywords: LSIKeyword[]): KeywordCluster[] {
    const uniqueWords = Array.from(new Set(words.filter(w => !this.stopWords.has(w) && w.length > 2)))
    const vectors = this.createWordVectors(uniqueWords)
    
    // Simple k-means clustering
    const k = Math.min(5, Math.floor(uniqueWords.length / 10)) // 5 clusters or less
    if (k < 2) return []
    
    const clusters = this.kMeansClustering(vectors, k)
    
    return clusters.map((cluster, index) => ({
      id: `cluster-${index}`,
      name: this.generateClusterName(cluster.words),
      keywords: cluster.words,
      centroid: cluster.centroid,
      coherenceScore: this.calculateCoherence(cluster.words, words),
      importance: cluster.words.length / uniqueWords.length
    }))
  }

  /**
   * Extract topics using simple LDA-like approach
   */
  private extractTopics(words: string[], clusters: KeywordCluster[]): Topic[] {
    const topics: Topic[] = []
    
    clusters.forEach((cluster, index) => {
      const topicWords = cluster.keywords.slice(0, 10)
      const distribution: Record<string, number> = {}
      
      // Calculate word distribution for this topic
      topicWords.forEach(word => {
        distribution[word] = words.filter(w => w === word).length / words.length
      })
      
      topics.push({
        id: `topic-${index}`,
        name: cluster.name,
        keywords: topicWords,
        weight: cluster.importance,
        distribution
      })
    })
    
    return topics
  }

  /**
   * Helper methods
   */
  private initializeStopWords(): Set<string> {
    // English stop words
    const englishStopWords = new Set([
      'the', 'is', 'at', 'which', 'on', 'a', 'an', 'and', 'or', 'but',
      'in', 'with', 'to', 'for', 'of', 'as', 'by', 'that', 'this',
      'it', 'from', 'be', 'are', 'been', 'was', 'were', 'been',
      'i', 'you', 'he', 'she', 'we', 'they', 'them', 'their',
      'what', 'when', 'where', 'who', 'why', 'how',
      'all', 'each', 'every', 'some', 'any', 'many', 'more', 'most',
      'can', 'will', 'would', 'could', 'should', 'may', 'might',
      'must', 'shall', 'do', 'does', 'did', 'have', 'has', 'had'
    ])
    
    // Add Arabic stop words
    const arabicStopWords = new Set([
      'في', 'من', 'إلى', 'على', 'هذا', 'هذه', 'ذلك', 'التي', 'الذي',
      'هو', 'هي', 'هم', 'هن', 'نحن', 'أنت', 'أنا', 'كان', 'كانت',
      'مع', 'عن', 'بعد', 'قبل', 'عند', 'لكن', 'أو', 'و'
    ])
    
    return new Set([...englishStopWords, ...arabicStopWords])
  }

  private initializeEntityPatterns(): Record<string, RegExp[]> {
    return {
      person: [
        /\b(?:[A-Z][a-z]+ ){1,3}[A-Z][a-z]+\b/g, // English names
        /\b(?:Mr\.|Mrs\.|Ms\.|Dr\.|Prof\.) [A-Z][a-z]+(?: [A-Z][a-z]+)*\b/g,
        /\b(?:محمد|أحمد|علي|عبدالله|سارة|فاطمة) [^\s]+\b/gu // Arabic names
      ],
      organization: [
        /\b[A-Z][a-z]+(?:[A-Z][a-z]+)*(?: (?:Inc|LLC|Corp|Company|Co|Ltd|Group|International|Services|Solutions))\b/g,
        /\b(?:شركة|مؤسسة|مجموعة) [^\s]+(?: [^\s]+)*\b/gu // Arabic companies
      ],
      location: [
        /\b(?:Dubai|Abu Dhabi|Sharjah|Ajman|Fujairah|Ras Al Khaimah|Umm Al Quwain)\b/gi,
        /\b(?:UAE|United Arab Emirates|Saudi Arabia|Qatar|Kuwait|Bahrain|Oman)\b/gi,
        /\b[A-Z][a-z]+(?:,? [A-Z]{2,})\b/g, // City, State/Country
        /\b(?:دبي|أبوظبي|الشارقة|عجمان|الفجيرة|رأس الخيمة|أم القيوين)\b/gu // Arabic locations
      ],
      product: [
        /\b[A-Z][a-z]+(?:[A-Z][a-z]+)* (?:Pro|Plus|Premium|Enterprise|Suite|System|Platform)\b/g
      ],
      event: [
        /\b(?:Expo|Conference|Summit|Festival|Exhibition) \d{4}\b/gi,
        /\b[A-Z][a-z]+ (?:Expo|Conference|Summit|Festival|Exhibition)\b/g
      ],
      concept: [
        /\b(?:International|Professional|Digital|Global|Advanced|Premium) [A-Z][a-z]+\b/g
      ]
    }
  }

  private detectLanguage(text: string): string {
    // Simple language detection based on character patterns
    const arabicPattern = /[\u0600-\u06FF]/
    const hasArabic = arabicPattern.test(text)
    
    if (hasArabic) {
      const arabicRatio = (text.match(arabicPattern) || []).length / text.length
      return arabicRatio > 0.3 ? 'ar' : 'en' // If >30% Arabic, consider it Arabic
    }
    
    return 'en'
  }

  private containsArabic(text: string): boolean {
    return /[\u0600-\u06FF]/.test(text)
  }

  private preprocessText(text: string, language: string): string {
    // Remove HTML tags
    text = text.replace(/<[^>]*>/g, ' ')
    
    // Normalize whitespace
    text = text.replace(/\s+/g, ' ')
    
    // Keep punctuation for sentence boundaries but remove special chars
    text = text.replace(/[^\w\s\u0600-\u06FF.,!?;:]/g, ' ')
    
    return text.trim()
  }

  private tokenize(text: string, language: string): string[] {
    // Split by whitespace and punctuation
    const words = text.toLowerCase().split(/[\s.,!?;:]+/)
    
    // Filter out empty strings and very short words
    return words.filter(word => word.length > 1)
  }

  private buildCoOccurrenceMatrix(words: string[]): Map<string, Map<string, number>> {
    const matrix = new Map<string, Map<string, number>>()
    const windowSize = 5
    
    for (let i = 0; i < words.length; i++) {
      const word1 = words[i]
      if (this.stopWords.has(word1)) continue
      
      if (!matrix.has(word1)) {
        matrix.set(word1, new Map())
      }
      
      for (let j = Math.max(0, i - windowSize); j <= Math.min(words.length - 1, i + windowSize); j++) {
        if (i === j) continue
        
        const word2 = words[j]
        if (this.stopWords.has(word2)) continue
        
        const word1Map = matrix.get(word1)!
        word1Map.set(word2, (word1Map.get(word2) || 0) + 1)
      }
    }
    
    return matrix
  }

  private calculateSemanticDistance(word1: string, word2: string): number {
    // Simplified semantic distance (in production, use word embeddings)
    const w1 = word1.toLowerCase()
    const w2 = word2.toLowerCase()
    
    // Exact match
    if (w1 === w2) return 0
    
    // Substring match
    if (w1.includes(w2) || w2.includes(w1)) return 0.3
    
    // Common prefix
    const commonPrefix = this.getCommonPrefix(w1, w2)
    if (commonPrefix.length > 3) return 0.5
    
    // Default distance
    return 1
  }

  private getCommonPrefix(str1: string, str2: string): string {
    let i = 0
    while (i < str1.length && i < str2.length && str1[i] === str2[i]) {
      i++
    }
    return str1.substring(0, i)
  }

  private getContextualWeight(words: string[], position: number): number {
    // Higher weight for words in important positions
    const totalWords = words.length
    const relativePosition = position / totalWords
    
    // Beginning and end of content get higher weights
    if (relativePosition < 0.1 || relativePosition > 0.9) return 1.5
    if (relativePosition < 0.2 || relativePosition > 0.8) return 1.2
    
    return 1.0
  }

  private categorizeKeyword(relevanceScore: number): 'primary' | 'secondary' | 'related' {
    if (relevanceScore > 0.7) return 'primary'
    if (relevanceScore > 0.4) return 'secondary'
    return 'related'
  }

  private calculateEntityConfidence(entity: string, type: string, text: string): number {
    let confidence = 0.5 // Base confidence
    
    // Increase confidence for capitalized words
    if (entity[0] === entity[0].toUpperCase()) confidence += 0.2
    
    // Increase confidence for multi-word entities
    if (entity.split(/\s+/).length > 1) confidence += 0.1
    
    // Increase confidence based on frequency
    const frequency = (text.match(new RegExp(entity, 'gi')) || []).length
    confidence += Math.min(0.2, frequency * 0.02)
    
    return Math.min(1, confidence)
  }

  private extractContext(text: string, position: number, windowSize: number = 50): string {
    const start = Math.max(0, position - windowSize)
    const end = Math.min(text.length, position + windowSize)
    return text.substring(start, end).trim()
  }

  private findEntityVariations(entity: string, text: string): string[] {
    const variations = new Set<string>()
    const entityWords = entity.split(/\s+/)
    
    // Look for acronyms
    if (entityWords.length > 1) {
      const acronym = entityWords.map(w => w[0]).join('').toUpperCase()
      if (text.includes(acronym)) {
        variations.add(acronym)
      }
    }
    
    // Look for partial matches
    const partialPattern = new RegExp(`\\b${entityWords[0]}[^\\s]*\\b`, 'gi')
    const partialMatches = text.match(partialPattern) || []
    partialMatches.forEach(match => {
      if (match !== entity && match.length < entity.length + 5) {
        variations.add(match)
      }
    })
    
    return Array.from(variations).slice(0, 5)
  }

  private extractArabicEntities(text: string, entities: Map<string, ExtractedEntity>): void {
    // Extract Arabic-specific entities
    const arabicLocationPattern = /\b(?:دبي|أبوظبي|الشارقة|الإمارات|السعودية|قطر|الكويت|البحرين|عمان)\b/gu
    const matches = text.matchAll(arabicLocationPattern)
    
    for (const match of matches) {
      const entityText = match[0]
      const existing = entities.get(entityText)
      
      if (existing) {
        existing.frequency++
      } else {
        entities.set(entityText, {
          text: entityText,
          type: 'location',
          frequency: 1,
          confidence: 0.9, // High confidence for known Arabic locations
          context: [this.extractContext(text, match.index!)],
          variations: []
        })
      }
    }
  }

  private createWordVectors(words: string[]): Array<{ word: string; vector: number[] }> {
    // Simple TF-IDF like vectors
    const vectors = words.map(word => {
      const vector = words.map(w => w === word ? 1 : 0)
      return { word, vector }
    })
    
    return vectors
  }

  private kMeansClustering(
    vectors: Array<{ word: string; vector: number[] }>,
    k: number
  ): Array<{ words: string[]; centroid: string }> {
    // Simplified k-means implementation
    const clusters: Array<{ words: string[]; centroid: string }> = []
    
    // Initialize clusters with random centroids
    for (let i = 0; i < k; i++) {
      clusters.push({
        words: [],
        centroid: vectors[Math.floor(Math.random() * vectors.length)].word
      })
    }
    
    // Assign words to nearest centroid
    vectors.forEach(({ word }) => {
      const randomCluster = Math.floor(Math.random() * k)
      clusters[randomCluster].words.push(word)
    })
    
    return clusters
  }

  private generateClusterName(words: string[]): string {
    // Use the most frequent word as cluster name
    const frequency: Record<string, number> = {}
    words.forEach(word => {
      frequency[word] = (frequency[word] || 0) + 1
    })
    
    const topWord = Object.entries(frequency)
      .sort(([,a], [,b]) => b - a)[0]
    
    return topWord ? topWord[0].charAt(0).toUpperCase() + topWord[0].slice(1) : 'Unknown'
  }

  private calculateCoherence(words: string[], allWords: string[]): number {
    // Simple coherence score based on how often cluster words appear together
    let coOccurrences = 0
    const windowSize = 10
    
    for (let i = 0; i < allWords.length; i++) {
      if (words.includes(allWords[i])) {
        for (let j = Math.max(0, i - windowSize); j <= Math.min(allWords.length - 1, i + windowSize); j++) {
          if (i !== j && words.includes(allWords[j])) {
            coOccurrences++
          }
        }
      }
    }
    
    // Normalize by cluster size
    const maxPossible = words.length * words.length * (allWords.length / words.length)
    return Math.min(1, coOccurrences / maxPossible)
  }

  private analyzeKeywordProminence(
    text: string,
    keyword: string,
    totalWords: number
  ): KeywordDensityAnalysis['primaryKeyword'] {
    const lowerText = text.toLowerCase()
    const lowerKeyword = keyword.toLowerCase()
    const words = lowerText.split(/\s+/)
    
    const frequency = this.countKeywordOccurrences(words, keyword)
    const density = this.calculateDensity(words, keyword, totalWords)
    
    // Calculate prominence based on position
    let prominence = 0
    
    // Check title (assuming first line is title)
    const lines = text.split('\n')
    if (lines[0].toLowerCase().includes(lowerKeyword)) prominence += 3
    
    // Check first paragraph
    const firstParagraph = text.substring(0, 200).toLowerCase()
    if (firstParagraph.includes(lowerKeyword)) prominence += 2
    
    // Check headings (simple pattern)
    const headingPattern = /^#{1,6}\s+(.+)$/gm
    const headings = text.match(headingPattern) || []
    headings.forEach(heading => {
      if (heading.toLowerCase().includes(lowerKeyword)) prominence += 1.5
    })
    
    return {
      keyword,
      density,
      frequency,
      prominence
    }
  }

  private calculateDensity(words: string[], keyword: string, totalWords: number): number {
    const keywordWords = keyword.toLowerCase().split(/\s+/)
    let occurrences = 0
    
    for (let i = 0; i <= words.length - keywordWords.length; i++) {
      let match = true
      for (let j = 0; j < keywordWords.length; j++) {
        if (words[i + j] !== keywordWords[j]) {
          match = false
          break
        }
      }
      if (match) occurrences++
    }
    
    return (occurrences * keywordWords.length / totalWords) * 100
  }

  private countKeywordOccurrences(words: string[], keyword: string): number {
    const keywordWords = keyword.toLowerCase().split(/\s+/)
    let count = 0
    
    for (let i = 0; i <= words.length - keywordWords.length; i++) {
      let match = true
      for (let j = 0; j < keywordWords.length; j++) {
        if (words[i + j] !== keywordWords[j]) {
          match = false
          break
        }
      }
      if (match) count++
    }
    
    return count
  }

  private calculateOptimalDensityRange(
    totalWords: number,
    keyword: string
  ): KeywordDensityAnalysis['optimalDensityRange'] {
    // Optimal density varies by content length
    let min, max, recommended
    
    if (totalWords < 500) {
      min = 2.5
      max = 4.0
      recommended = 3.0
    } else if (totalWords < 1500) {
      min = 1.5
      max = 3.0
      recommended = 2.0
    } else if (totalWords < 3000) {
      min = 1.0
      max = 2.5
      recommended = 1.5
    } else {
      min = 0.5
      max = 2.0
      recommended = 1.0
    }
    
    // Adjust for multi-word keywords
    const keywordWords = keyword.split(/\s+/).length
    if (keywordWords > 1) {
      min *= 0.8
      max *= 0.8
      recommended *= 0.8
    }
    
    return { min, max, recommended }
  }

  private generateDensityWarnings(
    primary: KeywordDensityAnalysis['primaryKeyword'],
    secondary: KeywordDensityAnalysis['secondaryKeywords'],
    optimal: KeywordDensityAnalysis['optimalDensityRange']
  ): string[] {
    const warnings: string[] = []
    
    // Check primary keyword density
    if (primary.density < optimal.min) {
      warnings.push(`Primary keyword density (${primary.density.toFixed(2)}%) is below optimal range (${optimal.min}%-${optimal.max}%)`)
    } else if (primary.density > optimal.max) {
      warnings.push(`Primary keyword density (${primary.density.toFixed(2)}%) exceeds optimal range - risk of keyword stuffing`)
    }
    
    // Check if primary keyword appears in important positions
    if (primary.prominence < 3) {
      warnings.push('Primary keyword should appear in title and first paragraph for better SEO')
    }
    
    // Check secondary keywords
    const highDensitySecondary = secondary.filter(kw => kw.density > optimal.max * 0.7)
    if (highDensitySecondary.length > 0) {
      warnings.push(`Secondary keywords with high density: ${highDensitySecondary.map(kw => kw.keyword).join(', ')}`)
    }
    
    // Check keyword diversity
    const totalKeywordDensity = primary.density + secondary.reduce((sum, kw) => sum + kw.density, 0)
    if (totalKeywordDensity > optimal.max * 3) {
      warnings.push('Overall keyword density is too high - content may appear over-optimized')
    }
    
    return warnings
  }

  private calculateSemanticScore(
    lsiKeywords: LSIKeyword[],
    entities: ExtractedEntity[],
    clusters: KeywordCluster[]
  ): number {
    let score = 0
    
    // LSI keyword diversity (max 30 points)
    const lsiDiversity = Math.min(30, lsiKeywords.length)
    score += lsiDiversity
    
    // Entity richness (max 20 points)
    const entityScore = Math.min(20, entities.length * 2)
    score += entityScore
    
    // Cluster coherence (max 20 points)
    const avgCoherence = clusters.reduce((sum, c) => sum + c.coherenceScore, 0) / clusters.length
    score += avgCoherence * 20
    
    // High relevance LSI keywords (max 30 points)
    const highRelevanceLSI = lsiKeywords.filter(k => k.relevanceScore > 0.7).length
    score += Math.min(30, highRelevanceLSI * 5)
    
    return Math.round(score)
  }

  private generateSemanticRecommendations(
    lsiKeywords: LSIKeyword[],
    entities: ExtractedEntity[],
    primaryKeyword: string,
    competitors?: string[]
  ): string[] {
    const recommendations: string[] = []
    
    // LSI keyword recommendations
    const topLSI = lsiKeywords.slice(0, 10).map(k => k.keyword)
    recommendations.push(`Include these LSI keywords naturally: ${topLSI.join(', ')}`)
    
    // Entity recommendations
    const locationEntities = entities.filter(e => e.type === 'location')
    if (locationEntities.length === 0) {
      recommendations.push('Add location-specific entities to improve local relevance')
    }
    
    const orgEntities = entities.filter(e => e.type === 'organization')
    if (orgEntities.length < 3) {
      recommendations.push('Mention more organizations/companies to build authority')
    }
    
    // Keyword clustering recommendations
    const primaryLSI = lsiKeywords.filter(k => k.category === 'primary')
    if (primaryLSI.length < 5) {
      recommendations.push('Strengthen content by adding more primary LSI keywords')
    }
    
    // Competitor-based recommendations
    if (competitors && competitors.length > 0) {
      recommendations.push('Analyze competitor content to identify missing semantic elements')
    }
    
    return recommendations
  }
}

// Export singleton instance
export const semanticAnalyzer = new SemanticAnalyzer()