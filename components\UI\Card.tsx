'use client';

import React from 'react';
import { ComponentProps } from 'react';
import { componentTokens } from '@/lib/design-system';

interface CardProps extends ComponentProps<'div'> {
  variant?: 'default' | 'elevated' | 'outlined' | 'flat';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  hover?: boolean;
}

const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  padding = 'md',
  hover = false,
  className = '',
  ...props
}) => {
  
  // Base card classes
  const baseClasses = [
    'rounded-lg transition-all duration-200',
    hover ? 'cursor-pointer' : ''
  ].filter(Boolean).join(' ');

  // Variant styles
  const variantClasses = {
    default: [
      'bg-white dark:bg-gray-800',
      'border border-gray-200 dark:border-gray-700',
      'shadow-sm',
      hover ? 'hover:shadow-md hover:border-gray-300 dark:hover:border-gray-600' : ''
    ].join(' '),
    
    elevated: [
      'bg-white dark:bg-gray-800',
      'shadow-lg border border-gray-100 dark:border-gray-700',
      hover ? 'hover:shadow-xl hover:scale-[1.02]' : ''
    ].join(' '),
    
    outlined: [
      'bg-transparent',
      'border-2 border-gray-200 dark:border-gray-600',
      hover ? 'hover:border-gray-300 dark:hover:border-gray-500 hover:bg-gray-50 dark:hover:bg-gray-800' : ''
    ].join(' '),
    
    flat: [
      'bg-gray-50 dark:bg-gray-700',
      'border-0',
      hover ? 'hover:bg-gray-100 dark:hover:bg-gray-600' : ''
    ].join(' ')
  };

  // Padding classes
  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-10'
  };

  const finalClassName = [
    baseClasses,
    variantClasses[variant],
    paddingClasses[padding],
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={finalClassName} {...props}>
      {children}
    </div>
  );
};

// Card Header component
interface CardHeaderProps extends ComponentProps<'div'> {
  title?: string;
  subtitle?: string;
  action?: React.ReactNode;
}

export const CardHeader: React.FC<CardHeaderProps> = ({
  title,
  subtitle,
  action,
  children,
  className = '',
  ...props
}) => {
  return (
    <div 
      className={`flex items-start justify-between border-b border-gray-200 dark:border-gray-700 pb-4 mb-6 ${className}`}
      {...props}
    >
      <div className="flex-1">
        {title && (
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {title}
          </h3>
        )}
        {subtitle && (
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {subtitle}
          </p>
        )}
        {children}
      </div>
      {action && (
        <div className="flex-shrink-0 ml-4">
          {action}
        </div>
      )}
    </div>
  );
};

// Card Content component
interface CardContentProps extends ComponentProps<'div'> {}

export const CardContent: React.FC<CardContentProps> = ({
  children,
  className = '',
  ...props
}) => {
  return (
    <div className={`text-gray-700 dark:text-gray-300 ${className}`} {...props}>
      {children}
    </div>
  );
};

// Card Footer component
interface CardFooterProps extends ComponentProps<'div'> {
  align?: 'left' | 'center' | 'right' | 'between';
}

export const CardFooter: React.FC<CardFooterProps> = ({
  children,
  align = 'right',
  className = '',
  ...props
}) => {
  const alignClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end',
    between: 'justify-between'
  };

  return (
    <div 
      className={`flex items-center ${alignClasses[align]} border-t border-gray-200 dark:border-gray-700 pt-4 mt-6 ${className}`}
      {...props}
    >
      {children}
    </div>
  );
};

export default Card;