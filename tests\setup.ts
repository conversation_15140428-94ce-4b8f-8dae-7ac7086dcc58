import { expect, afterEach, vi } from 'vitest'
import { cleanup } from '@testing-library/react'
import * as matchers from '@testing-library/jest-dom/matchers'

// Extend Vitest's expect with jest-dom matchers
expect.extend(matchers)

// Cleanup after each test case
afterEach(() => {
  cleanup()
})

// Global test utilities
global.ResizeObserver = vi.fn(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
}))

global.IntersectionObserver = vi.fn(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
}))

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn()
  }))
})

// Mock window.scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: vi.fn()
})

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock
})

// Mock fetch
global.fetch = vi.fn()

// Mock next/router
vi.mock('next/router', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    back: vi.fn(),
    reload: vi.fn(),
    route: '/',
    pathname: '/',
    query: {},
    asPath: '/'
  })
}))

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    back: vi.fn(),
    refresh: vi.fn()
  }),
  usePathname: () => '/',
  useSearchParams: () => new URLSearchParams()
}))

// Mock environment variables
process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co'
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key'
process.env.NEXT_PUBLIC_API_URL = 'http://localhost:3001'
process.env.NEXT_PUBLIC_DEMO_MODE = 'true'

// Console warning suppression for tests
const originalConsoleWarn = console.warn
console.warn = (...args: any[]) => {
  // Suppress specific warnings that are expected in tests
  const message = args[0]
  if (
    typeof message === 'string' &&
    (
      message.includes('Warning: ReactDOM.render is no longer supported') ||
      message.includes('Warning: validateDOMNesting') ||
      message.includes('Warning: Each child in a list should have a unique "key" prop')
    )
  ) {
    return
  }
  originalConsoleWarn(...args)
}

// Custom test utilities
export const createMockUser = (overrides = {}) => ({
  id: 'test-user-id',
  email: '<EMAIL>',
  name: 'Test User',
  role: 'user',
  createdAt: Date.now(),
  ...overrides
})

export const createMockProject = (overrides = {}) => ({
  id: 'test-project-id',
  name: 'Test Project',
  description: 'Test project description',
  status: 'active',
  createdAt: Date.now(),
  updatedAt: Date.now(),
  userId: 'test-user-id',
  settings: {
    language: 'en',
    targetCountry: 'US',
    keywords: ['test', 'seo']
  },
  ...overrides
})

export const createMockContent = (overrides = {}) => ({
  id: 'test-content-id',
  title: 'Test Content',
  content: 'Test content body',
  type: 'article',
  status: 'draft',
  keywords: ['test'],
  targetCountry: 'US',
  seoScore: 85,
  readabilityScore: 90,
  createdAt: Date.now(),
  updatedAt: Date.now(),
  userId: 'test-user-id',
  projectId: 'test-project-id',
  ...overrides
})

export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// Mock API responses
export const mockApiResponse = (data: any, status = 200) => ({
  ok: status >= 200 && status < 300,
  status,
  json: () => Promise.resolve(data),
  text: () => Promise.resolve(JSON.stringify(data))
})

// Test wrapper components
export const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  return children
}

// Assertion helpers
export const expectToBeInDocument = (element: any) => {
  expect(element).toBeInTheDocument()
}

export const expectNotToBeInDocument = (element: any) => {
  expect(element).not.toBeInTheDocument()
}

export const expectToHaveClass = (element: any, className: string) => {
  expect(element).toHaveClass(className)
}

export const expectToHaveAttribute = (element: any, attr: string, value?: string) => {
  if (value !== undefined) {
    expect(element).toHaveAttribute(attr, value)
  } else {
    expect(element).toHaveAttribute(attr)
  }
}