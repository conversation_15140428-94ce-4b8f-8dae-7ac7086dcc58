/**
 * ProjectCard Component
 * Enterprise SEO SAAS - Project display card for grid and list views
 */

import { useState } from 'react'
import Link from 'next/link'
import { Project } from '@/types/project'
import { formatProjectStatus, getIndustryInfo, calculateProjectMetrics } from '@/utils/projectHelpers'
import {
  EllipsisVerticalIcon,
  CalendarIcon,
  ChartBarIcon,
  DocumentTextIcon,
  TagIcon,
  GlobeAltIcon,
  UsersIcon
} from '@heroicons/react/24/outline'

interface ProjectCardProps {
  project: Project
  viewMode: 'grid' | 'list'
  onProjectUpdate: (project: Project) => void
}

export default function ProjectCard({ project, viewMode, onProjectUpdate }: ProjectCardProps) {
  const [showDropdown, setShowDropdown] = useState(false)
  
  const statusInfo = formatProjectStatus(project.status)
  const industryInfo = getIndustryInfo(project.industry)
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const handleMenuAction = (action: string) => {
    setShowDropdown(false)
    
    switch (action) {
      case 'edit':
        // Handle edit project
        break
      case 'duplicate':
        // Handle duplicate project
        break
      case 'archive':
        // Handle archive project
        break
      case 'delete':
        // Handle delete project
        break
    }
  }

  if (viewMode === 'list') {
    return (
      <div className="bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-4">
                {/* Project Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-3 mb-2">
                    <Link
                      href={`/projects/${project.id}`}
                      className="text-lg font-semibold text-gray-900 hover:text-blue-600 truncate"
                    >
                      {project.name}
                    </Link>
                    <span className={`
                      inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                      ${statusInfo.color} ${statusInfo.bgColor}
                    `}>
                      {statusInfo.label}
                    </span>
                  </div>
                  
                  {project.description && (
                    <p className="text-sm text-gray-600 mb-2 line-clamp-1">
                      {project.description}
                    </p>
                  )}
                  
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <div className="flex items-center gap-1">
                      <span className="text-lg">{industryInfo.icon}</span>
                      <span>{industryInfo.label}</span>
                    </div>
                    
                    <div className="flex items-center gap-1">
                      <TagIcon className="h-4 w-4" />
                      <span>{project.keywords.length} keywords</span>
                    </div>
                    
                    <div className="flex items-center gap-1">
                      <UsersIcon className="h-4 w-4" />
                      <span>{project.competitors.length} competitors</span>
                    </div>
                    
                    <div className="flex items-center gap-1">
                      <DocumentTextIcon className="h-4 w-4" />
                      <span>{project.metrics.totalContent} content</span>
                    </div>
                  </div>
                </div>

                {/* Metrics */}
                <div className="hidden sm:flex items-center gap-6 text-sm">
                  <div className="text-center">
                    <div className="font-semibold text-gray-900">{project.metrics.averageSeoScore.toFixed(1)}</div>
                    <div className="text-gray-500">SEO Score</div>
                  </div>
                  <div className="text-center">
                    <div className="font-semibold text-gray-900">{project.metrics.averageWordCount}</div>
                    <div className="text-gray-500">Avg Words</div>
                  </div>
                  <div className="text-center">
                    <div className="font-semibold text-gray-900">{formatDate(project.updatedAt)}</div>
                    <div className="text-gray-500">Updated</div>
                  </div>
                </div>

                {/* Actions */}
                <div className="relative">
                  <button
                    onClick={() => setShowDropdown(!showDropdown)}
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-50"
                  >
                    <EllipsisVerticalIcon className="h-5 w-5" />
                  </button>
                  
                  {showDropdown && (
                    <div className="absolute right-0 top-full mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10">
                      <button
                        onClick={() => handleMenuAction('edit')}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      >
                        Edit Project
                      </button>
                      <button
                        onClick={() => handleMenuAction('duplicate')}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      >
                        Duplicate
                      </button>
                      <hr className="my-1" />
                      <button
                        onClick={() => handleMenuAction('archive')}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                      >
                        Archive
                      </button>
                      <button
                        onClick={() => handleMenuAction('delete')}
                        className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                      >
                        Delete
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Grid view
  return (
    <div className="bg-white border border-gray-200 rounded-lg hover:shadow-lg transition-shadow group">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-2">
            <span className="text-2xl">{industryInfo.icon}</span>
            <span className={`
              inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
              ${statusInfo.color} ${statusInfo.bgColor}
            `}>
              {statusInfo.label}
            </span>
          </div>
          
          <div className="relative">
            <button
              onClick={() => setShowDropdown(!showDropdown)}
              className="p-1 text-gray-400 hover:text-gray-600 rounded opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <EllipsisVerticalIcon className="h-5 w-5" />
            </button>
            
            {showDropdown && (
              <div className="absolute right-0 top-full mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10">
                <button
                  onClick={() => handleMenuAction('edit')}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  Edit Project
                </button>
                <button
                  onClick={() => handleMenuAction('duplicate')}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  Duplicate
                </button>
                <hr className="my-1" />
                <button
                  onClick={() => handleMenuAction('archive')}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  Archive
                </button>
                <button
                  onClick={() => handleMenuAction('delete')}
                  className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                >
                  Delete
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Project Title */}
        <div className="mb-3">
          <Link
            href={`/projects/${project.id}`}
            className="text-xl font-semibold text-gray-900 hover:text-blue-600 line-clamp-1"
          >
            {project.name}
          </Link>
          {project.description && (
            <p className="text-sm text-gray-600 mt-1 line-clamp-2">
              {project.description}
            </p>
          )}
        </div>

        {/* Industry */}
        <div className="mb-4">
          <span className={`inline-flex items-center gap-1 text-sm ${industryInfo.color}`}>
            <span>{industryInfo.label}</span>
          </span>
        </div>

        {/* Metrics Grid */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-semibold text-gray-900">{project.keywords.length}</div>
            <div className="text-xs text-gray-500">Keywords</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-semibold text-gray-900">{project.competitors.length}</div>
            <div className="text-xs text-gray-500">Competitors</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-semibold text-gray-900">{project.metrics.totalContent}</div>
            <div className="text-xs text-gray-500">Content</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-lg font-semibold text-gray-900">{project.metrics.averageSeoScore.toFixed(1)}</div>
            <div className="text-xs text-gray-500">SEO Score</div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between text-sm text-gray-500">
          <div className="flex items-center gap-1">
            <CalendarIcon className="h-4 w-4" />
            <span>Updated {formatDate(project.updatedAt)}</span>
          </div>
          
          {project.website && (
            <div className="flex items-center gap-1">
              <GlobeAltIcon className="h-4 w-4" />
              <span className="truncate max-w-20">{new URL(project.website).hostname}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}