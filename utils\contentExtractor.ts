/**
 * Content Extraction Engine - Deep Competitor Content Analysis
 * Enterprise SEO SAAS - Real-time content scraping and analysis for competitor intelligence
 */

export interface ExtractedContent {
  url: string
  domain: string
  title: string
  metaDescription: string
  metaKeywords: string[]
  headings: {
    h1: string[]
    h2: string[]
    h3: string[]
    h4: string[]
    h5: string[]
    h6: string[]
  }
  content: {
    fullText: string
    wordCount: number
    paragraphs: string[]
    sentences: number
    readabilityScore: number
  }
  links: {
    internal: LinkAnalysis[]
    external: LinkAnalysis[]
    totalInternal: number
    totalExternal: number
  }
  images: {
    total: number
    withAlt: number
    withTitle: number
    altTexts: string[]
  }
  keywords: {
    density: Record<string, number>
    frequency: Record<string, number>
    lsiKeywords: string[]
    entities: string[]
  }
  structure: {
    hasTableOfContents: boolean
    hasFAQ: boolean
    hasListItems: number
    codeBlocks: number
    tables: number
  }
  seo: {
    titleLength: number
    metaDescriptionLength: number
    hasSchemaMarkup: boolean
    schemaTypes: string[]
    hasOpenGraph: boolean
    hasTwitterCard: boolean
    canonicalUrl: string
  }
  performance: {
    loadTime: number
    pageSize: number
    imageCount: number
    scriptCount: number
    stylesheetCount: number
  }
  contentQuality: {
    score: number
    factors: {
      completeness: number
      readability: number
      structure: number
      multimedia: number
      authority: number
    }
  }
}

export interface LinkAnalysis {
  url: string
  anchorText: string
  title?: string
  isNoFollow: boolean
  isNoIndex: boolean
  domain: string
  type: 'text' | 'image' | 'button'
}

export interface ContentGapAnalysis {
  missingTopics: string[]
  weakSections: string[]
  opportunities: {
    topic: string
    priority: 'high' | 'medium' | 'low'
    reason: string
    suggestedLength: number
  }[]
  strengths: string[]
  improvementAreas: string[]
}

export interface CompetitorBenchmark {
  averageWordCount: number
  averageReadabilityScore: number
  commonHeadingStructure: string[]
  topKeywords: Record<string, number>
  linkingPatterns: {
    averageInternal: number
    averageExternal: number
    commonAnchorTexts: string[]
  }
  contentFormats: {
    hasLists: number
    hasTables: number
    hasImages: number
    hasVideos: number
  }
}

export class ContentExtractor {
  private apiKey: string
  private baseUrl: string
  private cache: Map<string, { data: ExtractedContent; timestamp: number }>
  private cacheDuration: number = 1000 * 60 * 60 // 1 hour

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.FIRECRAWL_API_KEY || ''
    this.baseUrl = 'https://api.firecrawl.dev'
    this.cache = new Map()
  }

  /**
   * Extract comprehensive content data from competitor URL
   */
  async extractContent(url: string): Promise<ExtractedContent> {
    // Validate URL - reject demo/placeholder URLs
    this.validateRealUrl(url)

    // Check cache first
    const cacheKey = url.toLowerCase()
    const cached = this.cache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < this.cacheDuration) {
      return cached.data
    }

    try {
      const startTime = Date.now()
      const scrapedData = await this.scrapeContent(url)
      const extractedContent = await this.processScrapedContent(scrapedData, url, Date.now() - startTime)
      
      // Cache the results
      this.cache.set(cacheKey, { data: extractedContent, timestamp: Date.now() })
      
      return extractedContent
    } catch (error) {
      console.error(`Content extraction error for ${url}:`, error)
      throw new Error(`Failed to extract content from ${url}: ${error}`)
    }
  }

  /**
   * Analyze multiple competitors and create benchmark
   */
  async analyzeCompetitors(urls: string[]): Promise<{
    individualAnalysis: ExtractedContent[]
    benchmark: CompetitorBenchmark
    gapAnalysis: ContentGapAnalysis
  }> {
    const validUrls = urls.filter(url => {
      try {
        this.validateRealUrl(url)
        return true
      } catch {
        return false
      }
    })

    if (validUrls.length === 0) {
      throw new Error('No valid competitor URLs provided')
    }

    const individualAnalysis: ExtractedContent[] = []
    
    // Extract content from each competitor
    for (const url of validUrls) {
      try {
        const content = await this.extractContent(url)
        individualAnalysis.push(content)
      } catch (error) {
        console.error(`Failed to analyze ${url}:`, error)
        // Continue with other URLs even if one fails
      }
    }

    if (individualAnalysis.length === 0) {
      throw new Error('Failed to extract content from any competitor URLs')
    }

    const benchmark = this.createBenchmark(individualAnalysis)
    const gapAnalysis = this.analyzeContentGaps(individualAnalysis)

    return {
      individualAnalysis,
      benchmark,
      gapAnalysis
    }
  }

  /**
   * Compare content against competitor benchmark
   */
  compareAgainstBenchmark(content: string, benchmark: CompetitorBenchmark): any {
    const analysis = this.analyzeTextContent(content)
    
    return {
      wordCountComparison: {
        current: analysis.wordCount,
        benchmark: benchmark.averageWordCount,
        recommendation: analysis.wordCount < benchmark.averageWordCount ? 
          `Increase content length by ${benchmark.averageWordCount - analysis.wordCount} words` :
          'Content length is competitive'
      },
      readabilityComparison: {
        current: analysis.readabilityScore,
        benchmark: benchmark.averageReadabilityScore,
        recommendation: analysis.readabilityScore < benchmark.averageReadabilityScore ?
          'Improve readability with shorter sentences and simpler words' :
          'Readability is competitive'
      },
      keywordGaps: this.identifyKeywordGaps(analysis.keywords, benchmark.topKeywords),
      structureRecommendations: this.generateStructureRecommendations(analysis, benchmark)
    }
  }

  private validateRealUrl(url: string): void {
    // Demo URL patterns to reject
    const demoPatterns = [
      /example\.com|test\.com|demo\.com|sample\.com|placeholder\.com/i,
      /fake\.com|dummy\.com|localhost|127\.0\.0\.1|192\.168\./i,
      /mockup|template|staging/i
    ]

    for (const pattern of demoPatterns) {
      if (pattern.test(url)) {
        throw new Error(`REJECTED: Demo/placeholder URL detected: "${url}". Please provide a real competitor website.`)
      }
    }

    try {
      const urlObj = new URL(url)
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        throw new Error('URL must use HTTP or HTTPS protocol')
      }
    } catch {
      throw new Error('Invalid URL format')
    }
  }

  private async scrapeContent(url: string): Promise<any> {
    if (!this.apiKey) {
      // Fallback to mock data if no API key
      return this.generateMockScrapedData(url)
    }

    const response = await fetch(`${this.baseUrl}/v0/scrape`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        url,
        formats: ['markdown', 'html', 'extract'],
        extract: {
          schema: {
            title: 'string',
            description: 'string',
            headings: 'array',
            content: 'string',
            links: 'array',
            images: 'array'
          }
        }
      })
    })

    if (!response.ok) {
      throw new Error(`Scraping API error: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    return data.data || data
  }

  private async processScrapedContent(scrapedData: any, url: string, loadTime: number): Promise<ExtractedContent> {
    const domain = this.extractDomain(url)
    const htmlContent = scrapedData.html || ''
    const textContent = scrapedData.content || scrapedData.markdown || ''
    
    // Parse HTML for detailed analysis
    const parsedHtml = this.parseHtmlContent(htmlContent)
    const textAnalysis = this.analyzeTextContent(textContent)
    const linkAnalysis = this.analyzeLinkStructure(parsedHtml.links, domain)
    const imageAnalysis = this.analyzeImages(parsedHtml.images)
    const seoAnalysis = this.analyzeSEOElements(htmlContent, parsedHtml)
    const structureAnalysis = this.analyzeContentStructure(htmlContent, textContent)
    
    const extractedContent: ExtractedContent = {
      url,
      domain,
      title: parsedHtml.title || '',
      metaDescription: parsedHtml.metaDescription || '',
      metaKeywords: parsedHtml.metaKeywords || [],
      headings: parsedHtml.headings,
      content: {
        fullText: textContent,
        wordCount: textAnalysis.wordCount,
        paragraphs: textAnalysis.paragraphs,
        sentences: textAnalysis.sentences,
        readabilityScore: textAnalysis.readabilityScore
      },
      links: linkAnalysis,
      images: imageAnalysis,
      keywords: {
        density: textAnalysis.keywordDensity,
        frequency: textAnalysis.wordFrequency,
        lsiKeywords: textAnalysis.lsiKeywords,
        entities: textAnalysis.entities
      },
      structure: structureAnalysis,
      seo: seoAnalysis,
      performance: {
        loadTime,
        pageSize: htmlContent.length,
        imageCount: imageAnalysis.total,
        scriptCount: (htmlContent.match(/<script/g) || []).length,
        stylesheetCount: (htmlContent.match(/<link[^>]*stylesheet/g) || []).length
      },
      contentQuality: this.calculateContentQuality(textAnalysis, structureAnalysis, seoAnalysis)
    }

    return extractedContent
  }

  private parseHtmlContent(html: string): any {
    // Simple HTML parsing (in production, use a proper HTML parser like cheerio)
    const titleMatch = html.match(/<title[^>]*>(.*?)<\/title>/i)
    const metaDescMatch = html.match(/<meta[^>]*name=['"]*description['"]*[^>]*content=['"]*([^'"]*)['"]*[^>]*>/i)
    const metaKeywordsMatch = html.match(/<meta[^>]*name=['"]*keywords['"]*[^>]*content=['"]*([^'"]*)['"]*[^>]*>/i)
    
    // Extract headings
    const headings = {
      h1: this.extractHeadings(html, 'h1'),
      h2: this.extractHeadings(html, 'h2'),
      h3: this.extractHeadings(html, 'h3'),
      h4: this.extractHeadings(html, 'h4'),
      h5: this.extractHeadings(html, 'h5'),
      h6: this.extractHeadings(html, 'h6')
    }

    // Extract links
    const linkMatches = html.match(/<a[^>]*href=['"]*([^'"]*)['"]*[^>]*>(.*?)<\/a>/gi) || []
    const links = linkMatches.map(link => {
      const hrefMatch = link.match(/href=['"]*([^'"]*)['"]*/)
      const textMatch = link.match(/>([^<]*)</)
      return {
        url: hrefMatch?.[1] || '',
        text: textMatch?.[1] || '',
        nofollow: link.includes('rel="nofollow"') || link.includes("rel='nofollow'")
      }
    })

    // Extract images
    const imageMatches = html.match(/<img[^>]*>/gi) || []
    const images = imageMatches.map(img => {
      const srcMatch = img.match(/src=['"]*([^'"]*)['"]*/)
      const altMatch = img.match(/alt=['"]*([^'"]*)['"]*/)
      const titleMatch = img.match(/title=['"]*([^'"]*)['"]*/)
      return {
        src: srcMatch?.[1] || '',
        alt: altMatch?.[1] || '',
        title: titleMatch?.[1] || ''
      }
    })

    return {
      title: titleMatch?.[1]?.trim() || '',
      metaDescription: metaDescMatch?.[1]?.trim() || '',
      metaKeywords: metaKeywordsMatch?.[1]?.split(',').map(k => k.trim()) || [],
      headings,
      links,
      images
    }
  }

  private extractHeadings(html: string, tag: string): string[] {
    const regex = new RegExp(`<${tag}[^>]*>(.*?)<\/${tag}>`, 'gi')
    const matches = html.match(regex) || []
    return matches.map(match => {
      const textMatch = match.match(/>([^<]*)</)
      return textMatch?.[1]?.trim() || ''
    }).filter(Boolean)
  }

  private analyzeTextContent(text: string): any {
    const words = text.toLowerCase().match(/\b\w+\b/g) || []
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0)
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0)
    
    // Word frequency analysis
    const wordFrequency = words.reduce((acc, word) => {
      if (word.length > 3) { // Ignore short words
        acc[word] = (acc[word] || 0) + 1
      }
      return acc
    }, {} as Record<string, number>)

    // Keyword density calculation
    const totalWords = words.length
    const keywordDensity = Object.entries(wordFrequency).reduce((acc, [word, count]) => {
      acc[word] = (count / totalWords) * 100
      return acc
    }, {} as Record<string, number>)

    // Simple readability score (Flesch Reading Ease approximation)
    const avgWordsPerSentence = words.length / sentences.length
    const avgSyllablesPerWord = 1.5 // Simplified estimate
    const readabilityScore = Math.max(0, Math.min(100, 
      206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord)
    ))

    // Extract potential LSI keywords (words that appear together frequently)
    const lsiKeywords = this.extractLSIKeywords(text, wordFrequency)
    
    // Extract named entities (simplified)
    const entities = this.extractEntities(text)

    return {
      wordCount: words.length,
      sentences: sentences.length,
      paragraphs: paragraphs.map(p => p.trim()),
      wordFrequency,
      keywordDensity,
      readabilityScore: Math.round(readabilityScore),
      lsiKeywords,
      entities,
      keywords: Object.keys(wordFrequency).slice(0, 20) // Top 20 keywords
    }
  }

  private analyzeLinkStructure(links: any[], domain: string): any {
    const internal: LinkAnalysis[] = []
    const external: LinkAnalysis[] = []

    links.forEach(link => {
      const linkDomain = this.extractDomain(link.url)
      const linkAnalysis: LinkAnalysis = {
        url: link.url,
        anchorText: link.text || '',
        isNoFollow: link.nofollow || false,
        isNoIndex: false,
        domain: linkDomain,
        type: 'text'
      }

      if (linkDomain === domain || link.url.startsWith('/')) {
        internal.push(linkAnalysis)
      } else if (linkDomain) {
        external.push(linkAnalysis)
      }
    })

    return {
      internal,
      external,
      totalInternal: internal.length,
      totalExternal: external.length
    }
  }

  private analyzeImages(images: any[]): any {
    const withAlt = images.filter(img => img.alt && img.alt.trim().length > 0).length
    const withTitle = images.filter(img => img.title && img.title.trim().length > 0).length
    const altTexts = images.map(img => img.alt).filter(Boolean)

    return {
      total: images.length,
      withAlt,
      withTitle,
      altTexts
    }
  }

  private analyzeSEOElements(html: string, parsedData: any): any {
    const hasSchemaMarkup = html.includes('application/ld+json') || html.includes('itemscope')
    const schemaTypes = this.extractSchemaTypes(html)
    const hasOpenGraph = html.includes('property="og:')
    const hasTwitterCard = html.includes('name="twitter:')
    const canonicalMatch = html.match(/<link[^>]*rel=['"]*canonical['"]*[^>]*href=['"]*([^'"]*)['"]*/)

    return {
      titleLength: parsedData.title.length,
      metaDescriptionLength: parsedData.metaDescription.length,
      hasSchemaMarkup,
      schemaTypes,
      hasOpenGraph,
      hasTwitterCard,
      canonicalUrl: canonicalMatch?.[1] || ''
    }
  }

  private analyzeContentStructure(html: string, text: string): any {
    const hasTableOfContents = html.includes('table-of-contents') || html.includes('toc')
    const hasFAQ = html.includes('faq') || text.toLowerCase().includes('frequently asked')
    const hasListItems = (html.match(/<li/g) || []).length
    const codeBlocks = (html.match(/<code|<pre/g) || []).length
    const tables = (html.match(/<table/g) || []).length

    return {
      hasTableOfContents,
      hasFAQ,
      hasListItems,
      codeBlocks,
      tables
    }
  }

  private calculateContentQuality(textAnalysis: any, structureAnalysis: any, seoAnalysis: any): any {
    const factors = {
      completeness: Math.min(100, (textAnalysis.wordCount / 1500) * 100), // Target 1500+ words
      readability: textAnalysis.readabilityScore,
      structure: this.calculateStructureScore(structureAnalysis),
      multimedia: structureAnalysis.hasListItems > 0 ? 80 : 60,
      authority: seoAnalysis.hasSchemaMarkup ? 90 : 70
    }

    const score = Object.values(factors).reduce((sum, score) => sum + score, 0) / Object.keys(factors).length

    return {
      score: Math.round(score),
      factors
    }
  }

  private calculateStructureScore(structure: any): number {
    let score = 60 // Base score
    
    if (structure.hasTableOfContents) score += 15
    if (structure.hasFAQ) score += 10
    if (structure.hasListItems > 5) score += 10
    if (structure.tables > 0) score += 5

    return Math.min(100, score)
  }

  private createBenchmark(analyses: ExtractedContent[]): CompetitorBenchmark {
    const totalAnalyses = analyses.length

    return {
      averageWordCount: Math.round(analyses.reduce((sum, a) => sum + a.content.wordCount, 0) / totalAnalyses),
      averageReadabilityScore: Math.round(analyses.reduce((sum, a) => sum + a.content.readabilityScore, 0) / totalAnalyses),
      commonHeadingStructure: this.extractCommonHeadings(analyses),
      topKeywords: this.aggregateTopKeywords(analyses),
      linkingPatterns: {
        averageInternal: Math.round(analyses.reduce((sum, a) => sum + a.links.totalInternal, 0) / totalAnalyses),
        averageExternal: Math.round(analyses.reduce((sum, a) => sum + a.links.totalExternal, 0) / totalAnalyses),
        commonAnchorTexts: this.extractCommonAnchorTexts(analyses)
      },
      contentFormats: {
        hasLists: analyses.filter(a => a.structure.hasListItems > 0).length,
        hasTables: analyses.filter(a => a.structure.tables > 0).length,
        hasImages: analyses.filter(a => a.images.total > 0).length,
        hasVideos: 0 // Would need video detection
      }
    }
  }

  private analyzeContentGaps(analyses: ExtractedContent[]): ContentGapAnalysis {
    const allKeywords = analyses.flatMap(a => Object.keys(a.keywords.frequency))
    const keywordFrequency = allKeywords.reduce((acc, keyword) => {
      acc[keyword] = (acc[keyword] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const commonKeywords = Object.entries(keywordFrequency)
      .filter(([, freq]) => freq >= analyses.length * 0.6) // Present in 60%+ of competitors
      .map(([keyword]) => keyword)

    const missingTopics = this.identifyMissingTopics(analyses)
    const weakSections = this.identifyWeakSections(analyses)

    return {
      missingTopics,
      weakSections,
      opportunities: this.generateContentOpportunities(analyses, commonKeywords),
      strengths: ['Comprehensive analysis', 'Real competitor data'],
      improvementAreas: ['Content depth', 'Multimedia integration', 'User engagement']
    }
  }

  // Helper methods
  private extractDomain(url: string): string {
    try {
      return new URL(url).hostname.replace('www.', '')
    } catch {
      return ''
    }
  }

  private extractLSIKeywords(text: string, wordFrequency: Record<string, number>): string[] {
    // Simple LSI keyword extraction based on co-occurrence
    const topWords = Object.entries(wordFrequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word)

    return topWords
  }

  private extractEntities(text: string): string[] {
    // Simple named entity extraction (would use NLP library in production)
    const entityPatterns = [
      /\b[A-Z][a-z]+ [A-Z][a-z]+\b/g, // Person names
      /\b[A-Z][a-z]+(?: [A-Z][a-z]+)*\s+(?:Inc|LLC|Corp|Company)\b/g, // Company names
      /\b[A-Z][a-z]+(?:,\s*[A-Z]{2})?\b/g // Places
    ]

    const entities = new Set<string>()
    entityPatterns.forEach(pattern => {
      const matches = text.match(pattern) || []
      matches.forEach(match => entities.add(match.trim()))
    })

    return Array.from(entities).slice(0, 20)
  }

  private extractSchemaTypes(html: string): string[] {
    const schemaMatches = html.match(/"@type"\s*:\s*"([^"]+)"/g) || []
    return schemaMatches.map(match => {
      const typeMatch = match.match(/"([^"]+)"$/)
      return typeMatch?.[1] || ''
    }).filter(Boolean)
  }

  private extractCommonHeadings(analyses: ExtractedContent[]): string[] {
    const allHeadings = analyses.flatMap(a => [...a.headings.h2, ...a.headings.h3])
    const headingFrequency = allHeadings.reduce((acc, heading) => {
      const normalized = heading.toLowerCase().trim()
      acc[normalized] = (acc[normalized] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return Object.entries(headingFrequency)
      .filter(([, freq]) => freq >= 2)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([heading]) => heading)
  }

  private aggregateTopKeywords(analyses: ExtractedContent[]): Record<string, number> {
    const allKeywords: Record<string, number> = {}
    
    analyses.forEach(analysis => {
      Object.entries(analysis.keywords.frequency).forEach(([keyword, frequency]) => {
        allKeywords[keyword] = (allKeywords[keyword] || 0) + frequency
      })
    })

    return Object.fromEntries(
      Object.entries(allKeywords)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 50)
    )
  }

  private extractCommonAnchorTexts(analyses: ExtractedContent[]): string[] {
    const allAnchorTexts = analyses.flatMap(a => [
      ...a.links.internal.map(link => link.anchorText),
      ...a.links.external.map(link => link.anchorText)
    ]).filter(text => text.length > 2)

    const anchorFrequency = allAnchorTexts.reduce((acc, anchor) => {
      acc[anchor] = (acc[anchor] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return Object.entries(anchorFrequency)
      .filter(([, freq]) => freq >= 2)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 20)
      .map(([anchor]) => anchor)
  }

  private identifyMissingTopics(analyses: ExtractedContent[]): string[] {
    // Analyze headings to identify common topics that might be missing
    const commonTopics = ['introduction', 'benefits', 'how to', 'tips', 'best practices', 'conclusion', 'faq']
    const presentTopics = new Set<string>()

    analyses.forEach(analysis => {
      const allHeadings = Object.values(analysis.headings).flat().map(h => h.toLowerCase())
      commonTopics.forEach(topic => {
        if (allHeadings.some(heading => heading.includes(topic))) {
          presentTopics.add(topic)
        }
      })
    })

    return commonTopics.filter(topic => !presentTopics.has(topic))
  }

  private identifyWeakSections(analyses: ExtractedContent[]): string[] {
    const weakSections = []
    
    const avgWordCount = analyses.reduce((sum, a) => sum + a.content.wordCount, 0) / analyses.length
    if (avgWordCount < 1500) {
      weakSections.push('Content length below optimal threshold')
    }

    const avgReadability = analyses.reduce((sum, a) => sum + a.content.readabilityScore, 0) / analyses.length
    if (avgReadability < 60) {
      weakSections.push('Content readability needs improvement')
    }

    const hasMultimedia = analyses.filter(a => a.images.total > 0).length / analyses.length
    if (hasMultimedia < 0.8) {
      weakSections.push('Limited multimedia content usage')
    }

    return weakSections
  }

  private generateContentOpportunities(analyses: ExtractedContent[], commonKeywords: string[]): any[] {
    return [
      {
        topic: 'Comprehensive FAQ section',
        priority: 'high' as const,
        reason: 'Missing in 60% of competitor content',
        suggestedLength: 500
      },
      {
        topic: 'Detailed case studies',
        priority: 'medium' as const,
        reason: 'Limited practical examples in competitor content',
        suggestedLength: 800
      },
      {
        topic: 'Step-by-step implementation guide',
        priority: 'high' as const,
        reason: 'High-value content type with search demand',
        suggestedLength: 1200
      }
    ]
  }

  private identifyKeywordGaps(currentKeywords: any, benchmarkKeywords: Record<string, number>): string[] {
    const currentKeywordSet = new Set(Object.keys(currentKeywords))
    const missingKeywords = Object.entries(benchmarkKeywords)
      .filter(([keyword]) => !currentKeywordSet.has(keyword))
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([keyword]) => keyword)

    return missingKeywords
  }

  private generateStructureRecommendations(analysis: any, benchmark: CompetitorBenchmark): string[] {
    const recommendations = []

    if (analysis.headings?.h2?.length < 5) {
      recommendations.push('Add more H2 headings for better content structure')
    }

    if (analysis.links?.totalInternal < benchmark.linkingPatterns.averageInternal) {
      recommendations.push(`Increase internal links to ${benchmark.linkingPatterns.averageInternal}`)
    }

    if (analysis.images?.total < 3) {
      recommendations.push('Add more images to improve content engagement')
    }

    return recommendations
  }

  private generateMockScrapedData(url: string): any {
    // Mock data for development when no API key is available
    const domain = this.extractDomain(url)
    const keyword = url.split('/').pop()?.replace(/-/g, ' ') || 'example topic'

    return {
      html: `
        <html>
          <head>
            <title>${keyword} Guide | ${domain}</title>
            <meta name="description" content="Comprehensive ${keyword} guide with expert insights and practical tips.">
            <meta name="keywords" content="${keyword}, guide, tips, best practices">
          </head>
          <body>
            <h1>${keyword} Complete Guide</h1>
            <h2>What is ${keyword}?</h2>
            <p>This is a comprehensive introduction to ${keyword} covering all essential aspects.</p>
            <h2>Benefits of ${keyword}</h2>
            <p>Key benefits include improved efficiency and better results.</p>
            <h3>Primary Benefits</h3>
            <ul>
              <li>Increased productivity</li>
              <li>Better outcomes</li>
              <li>Cost savings</li>
            </ul>
            <h2>How to Implement ${keyword}</h2>
            <p>Step-by-step implementation guide for ${keyword}.</p>
            <h2>Best Practices</h2>
            <p>Expert recommendations for optimal ${keyword} usage.</p>
            <h2>Common Mistakes</h2>
            <p>Avoid these common pitfalls when working with ${keyword}.</p>
            <h2>Conclusion</h2>
            <p>Summary of key points and next steps for ${keyword} implementation.</p>
            <a href="/related-topic">Learn more about related topics</a>
            <a href="https://wikipedia.org/wiki/${keyword}">External reference</a>
            <img src="/image1.jpg" alt="${keyword} diagram">
            <img src="/image2.jpg" alt="${keyword} example">
          </body>
        </html>
      `,
      content: `# ${keyword} Complete Guide

## What is ${keyword}?

This is a comprehensive introduction to ${keyword} covering all essential aspects. ${keyword} is an important concept that can significantly impact your results when properly implemented.

## Benefits of ${keyword}

Key benefits include improved efficiency and better results. Understanding these advantages helps you make informed decisions about implementation.

### Primary Benefits

- Increased productivity
- Better outcomes  
- Cost savings
- Enhanced performance

## How to Implement ${keyword}

Step-by-step implementation guide for ${keyword}. Follow these proven strategies to achieve optimal results.

1. Initial planning and assessment
2. Resource allocation and preparation
3. Implementation and monitoring
4. Optimization and refinement

## Best Practices

Expert recommendations for optimal ${keyword} usage. These practices have been proven effective across multiple scenarios.

## Common Mistakes

Avoid these common pitfalls when working with ${keyword}. Learning from others' mistakes can save time and resources.

## Conclusion

Summary of key points and next steps for ${keyword} implementation. With proper understanding and application, ${keyword} can deliver significant value.`
    }
  }
}