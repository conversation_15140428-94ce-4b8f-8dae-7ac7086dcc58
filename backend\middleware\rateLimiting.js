import { RateLimiterMemory, RateLimiterRedis } from 'rate-limiter-flexible';
import NodeCache from 'node-cache';
import express from 'express';

/**
 * Enhanced Rate Limiting and Caching System
 * Enterprise-grade protection with intelligent throttling
 */

// Initialize cache with 1 hour TTL
const cache = new NodeCache({ stdTTL: 3600, checkperiod: 600 });

/**
 * Rate Limiter Configurations
 */
const rateLimiters = {
  // General API requests
  general: new RateLimiterMemory({
    keyPrefix: 'general',
    points: 100, // 100 requests
    duration: 900, // Per 15 minutes
  }),

  // Content generation (more restrictive)
  contentGeneration: new RateLimiterMemory({
    keyPrefix: 'content_gen',
    points: 10, // 10 generations
    duration: 3600, // Per hour
  }),

  // Authority link discovery
  authorityLinks: new RateLimiterMemory({
    keyPrefix: 'authority',
    points: 50, // 50 requests
    duration: 3600, // Per hour
  }),

  // Input validation (less restrictive)
  validation: new RateLimiterMemory({
    keyPrefix: 'validation',
    points: 200, // 200 validations
    duration: 900, // Per 15 minutes
  }),

  // Demo data attempts (very restrictive)
  demoData: new RateLimiterMemory({
    keyPrefix: 'demo_attempts',
    points: 5, // 5 demo attempts
    duration: 3600, // Per hour
    blockDuration: 3600, // Block for 1 hour
  })
};

/**
 * Create rate limiting middleware
 */
export const createRateLimit = (limiterName, options = {}) => {
  const limiter = rateLimiters[limiterName];
  
  if (!limiter) {
    console.error(`Rate limiter '${limiterName}' not found`);
    return (req, res, next) => next();
  }

  return async (req, res, next) => {
    try {
      const key = options.keyGenerator ? options.keyGenerator(req) : req.ip;
      
      await limiter.consume(key);
      next();
    } catch (rejRes) {
      const remainingPoints = rejRes.remainingPoints || 0;
      const msBeforeNext = rejRes.msBeforeNext || 0;
      
      // Special handling for demo data attempts
      if (limiterName === 'demoData') {
        console.warn(`Demo data attempt blocked for IP: ${req.ip}`);
        return res.status(429).json({
          success: false,
          error: 'Too many demo data attempts. Please use real data.',
          code: 'DEMO_DATA_RATE_LIMITED',
          retryAfter: Math.round(msBeforeNext / 1000),
          message: 'This system only accepts real data. Demo/placeholder data is not allowed.'
        });
      }
      
      res.status(429).json({
        success: false,
        error: 'Too many requests',
        code: 'RATE_LIMITED',
        retryAfter: Math.round(msBeforeNext / 1000),
        remainingPoints: remainingPoints
      });
    }
  };
};

/**
 * Demo data detection rate limiting
 */
export const demoDataRateLimit = createRateLimit('demoData', {
  keyGenerator: (req) => {
    // Track by IP for demo data attempts
    return `demo_${req.ip}`;
  }
});

/**
 * Content generation rate limiting
 */
export const contentGenerationRateLimit = createRateLimit('contentGeneration', {
  keyGenerator: (req) => {
    // Track by user ID if available, otherwise IP
    return req.user?.id || req.ip;
  }
});

/**
 * Authority link rate limiting
 */
export const authorityLinkRateLimit = createRateLimit('authorityLinks');

/**
 * Validation rate limiting
 */
export const validationRateLimit = createRateLimit('validation');

/**
 * General API rate limiting
 */
export const generalRateLimit = createRateLimit('general');

/**
 * Caching Middleware
 */
export class CacheManager {
  constructor() {
    this.cache = cache;
  }

  /**
   * Cache middleware for GET requests
   */
  createCacheMiddleware(ttl = 3600) {
    return (req, res, next) => {
      // Only cache GET requests
      if (req.method !== 'GET') {
        return next();
      }

      const key = this.generateCacheKey(req);
      const cachedResponse = this.cache.get(key);

      if (cachedResponse) {
        console.log(`Cache hit for: ${key}`);
        return res.json(cachedResponse);
      }

      // Store original json method
      const originalJson = res.json;
      
      // Override json method to cache response
      res.json = (data) => {
        if (res.statusCode === 200) {
          this.cache.set(key, data, ttl);
          console.log(`Cache set for: ${key}`);
        }
        return originalJson.call(res, data);
      };

      next();
    };
  }

  /**
   * Generate cache key from request
   */
  generateCacheKey(req) {
    const { method, originalUrl, query, user } = req;
    const userKey = user?.id || 'anonymous';
    
    return `${method}:${originalUrl}:${JSON.stringify(query)}:${userKey}`;
  }

  /**
   * Cache content generation results
   */
  cacheContentGeneration(params, result) {
    const key = `content_gen:${JSON.stringify(params)}`;
    this.cache.set(key, result, 7200); // 2 hours cache
    console.log(`Content generation cached: ${key}`);
  }

  /**
   * Get cached content generation
   */
  getCachedContentGeneration(params) {
    const key = `content_gen:${JSON.stringify(params)}`;
    return this.cache.get(key);
  }

  /**
   * Cache authority link validation
   */
  cacheAuthorityLinkValidation(links, result) {
    const key = `authority_validation:${JSON.stringify(links)}`;
    this.cache.set(key, result, 3600); // 1 hour cache
    console.log(`Authority link validation cached: ${key}`);
  }

  /**
   * Get cached authority link validation
   */
  getCachedAuthorityLinkValidation(links) {
    const key = `authority_validation:${JSON.stringify(links)}`;
    return this.cache.get(key);
  }

  /**
   * Clear cache by pattern
   */
  clearCacheByPattern(pattern) {
    const keys = this.cache.keys();
    const matchingKeys = keys.filter(key => key.includes(pattern));
    
    matchingKeys.forEach(key => {
      this.cache.del(key);
    });
    
    console.log(`Cleared ${matchingKeys.length} cache entries matching: ${pattern}`);
    return matchingKeys.length;
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      keys: this.cache.keys().length,
      stats: this.cache.getStats(),
      memoryUsage: process.memoryUsage()
    };
  }
}

// Initialize cache manager
export const cacheManager = new CacheManager();

/**
 * Smart caching middleware for content generation
 */
export const smartContentCache = (req, res, next) => {
  if (req.method !== 'POST') {
    return next();
  }

  // Check for cached content generation
  const cachedResult = cacheManager.getCachedContentGeneration(req.body);
  if (cachedResult) {
    console.log('Returning cached content generation result');
    return res.json({
      success: true,
      data: cachedResult,
      cached: true,
      message: 'Content retrieved from cache'
    });
  }

  // Store original json method
  const originalJson = res.json;
  
  // Override json method to cache successful responses
  res.json = (data) => {
    if (res.statusCode === 200 && data.success && data.data) {
      cacheManager.cacheContentGeneration(req.body, data.data);
    }
    return originalJson.call(res, data);
  };

  next();
};

/**
 * Smart caching middleware for authority link validation
 */
export const smartAuthorityLinkCache = (req, res, next) => {
  if (req.method !== 'POST' || !req.body.links) {
    return next();
  }

  // Check for cached validation
  const cachedResult = cacheManager.getCachedAuthorityLinkValidation(req.body.links);
  if (cachedResult) {
    console.log('Returning cached authority link validation result');
    return res.json({
      success: true,
      data: cachedResult,
      cached: true,
      message: 'Validation retrieved from cache'
    });
  }

  // Store original json method
  const originalJson = res.json;
  
  // Override json method to cache successful responses
  res.json = (data) => {
    if (res.statusCode === 200 && data.success && data.data) {
      cacheManager.cacheAuthorityLinkValidation(req.body.links, data.data);
    }
    return originalJson.call(res, data);
  };

  next();
};

/**
 * Middleware to track demo data attempts
 */
export const trackDemoDataAttempt = async (req, res, next) => {
  try {
    // Check if request contains demo data patterns
    const requestData = JSON.stringify(req.body).toLowerCase();
    const demoPatterns = [
      'example.com',
      'test.com',
      'demo.com',
      'placeholder',
      'lorem ipsum',
      'sample',
      'dummy',
      'mock'
    ];

    const containsDemoData = demoPatterns.some(pattern => 
      requestData.includes(pattern)
    );

    if (containsDemoData) {
      // Apply demo data rate limiting
      await rateLimiters.demoData.consume(`demo_${req.ip}`);
      console.warn(`Demo data attempt from IP: ${req.ip}`);
    }

    next();
  } catch (rejRes) {
    // Demo data rate limit exceeded
    return res.status(429).json({
      success: false,
      error: 'Too many demo data attempts. This system only accepts real data.',
      code: 'DEMO_DATA_BLOCKED',
      retryAfter: Math.round((rejRes.msBeforeNext || 3600000) / 1000),
      message: 'Please provide real business data instead of demo/placeholder content.'
    });
  }
};

/**
 * Rate limiting middleware factory
 */
export const createSmartRateLimit = (config = {}) => {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutes
    max = 100, // 100 requests
    message = 'Too many requests',
    skipSuccessfulRequests = false,
    skipFailedRequests = false
  } = config;

  const limiter = new RateLimiterMemory({
    keyPrefix: 'smart_rate_limit',
    points: max,
    duration: Math.floor(windowMs / 1000),
  });

  return async (req, res, next) => {
    try {
      const key = req.user?.id || req.ip;
      await limiter.consume(key);
      next();
    } catch (rejRes) {
      res.status(429).json({
        success: false,
        error: message,
        code: 'RATE_LIMITED',
        retryAfter: Math.round((rejRes.msBeforeNext || 0) / 1000)
      });
    }
  };
};

export default {
  createRateLimit,
  cacheManager,
  smartContentCache,
  smartAuthorityLinkCache,
  trackDemoDataAttempt,
  createSmartRateLimit,
  contentGenerationRateLimit,
  authorityLinkRateLimit,
  validationRateLimit,
  generalRateLimit,
  demoDataRateLimit
};
