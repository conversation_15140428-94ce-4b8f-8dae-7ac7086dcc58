# 🔄 DYNAMIC ROUTING SYSTEM
# SEO SAAS HTML - Professional SPA Routing & Navigation

## 🎯 **ROUTING SYSTEM OVERVIEW**

Implement a professional dynamic routing system that provides seamless navigation, URL management, and state handling for a modern single-page application experience.

## 🛣️ **ROUTING ARCHITECTURE**

### **Client-Side Router Implementation**
```javascript
class ProfessionalRouter {
  constructor() {
    this.routes = new Map();
    this.middlewares = [];
    this.currentRoute = null;
    this.history = [];
    this.guards = new Map();
    this.loadingStates = new Map();
    
    // Initialize router
    this.init();
  }
  
  init() {
    // Handle browser navigation
    window.addEventListener('popstate', (event) => {
      this.handlePopState(event);
    });
    
    // Handle initial page load
    this.handleInitialRoute();
    
    // Intercept link clicks
    this.interceptLinks();
  }
  
  // Route Definition System
  defineRoutes() {
    const routes = [
      // Dashboard Routes
      { path: '/', component: 'DashboardOverview', title: 'Dashboard - SEO SAAS' },
      { path: '/dashboard', component: 'DashboardOverview', title: 'Dashboard - SEO SAAS' },
      { path: '/analytics', component: 'Analytics', title: 'Analytics - SEO SAAS' },
      
      // Content Creation Routes
      { path: '/content/create', component: 'ContentCreator', title: 'Create Content - SEO SAAS' },
      { path: '/content/optimize', component: 'ContentOptimizer', title: 'Optimize Content - SEO SAAS' },
      { path: '/content/editor', component: 'ContentEditor', title: 'Content Editor - SEO SAAS' },
      { path: '/content/bulk', component: 'BulkGenerator', title: 'Bulk Generator - SEO SAAS' },
      
      // Research Routes
      { path: '/research/keywords', component: 'KeywordResearch', title: 'Keyword Research - SEO SAAS' },
      { path: '/research/competitors', component: 'CompetitorAnalysis', title: 'Competitor Analysis - SEO SAAS' },
      { path: '/research/serp', component: 'SerpAnalyzer', title: 'SERP Analyzer - SEO SAAS' },
      { path: '/research/gaps', component: 'ContentGaps', title: 'Content Gaps - SEO SAAS' },
      
      // Project Routes
      { path: '/projects', component: 'ProjectsList', title: 'Projects - SEO SAAS' },
      { path: '/projects/:id', component: 'ProjectDetails', title: 'Project Details - SEO SAAS' },
      { path: '/projects/:id/edit', component: 'ProjectEdit', title: 'Edit Project - SEO SAAS' },
      
      // Settings Routes
      { path: '/settings/account', component: 'AccountSettings', title: 'Account Settings - SEO SAAS' },
      { path: '/settings/billing', component: 'BillingSettings', title: 'Billing - SEO SAAS' },
      { path: '/settings/api', component: 'ApiSettings', title: 'API Settings - SEO SAAS' },
      { path: '/settings/team', component: 'TeamManagement', title: 'Team Management - SEO SAAS' },
      
      // Authentication Routes
      { path: '/login', component: 'Login', title: 'Login - SEO SAAS', public: true },
      { path: '/register', component: 'Register', title: 'Register - SEO SAAS', public: true },
      { path: '/forgot-password', component: 'ForgotPassword', title: 'Reset Password - SEO SAAS', public: true },
      
      // Error Routes
      { path: '/404', component: 'NotFound', title: 'Page Not Found - SEO SAAS' },
      { path: '/500', component: 'ServerError', title: 'Server Error - SEO SAAS' }
    ];
    
    routes.forEach(route => this.addRoute(route));
  }
  
  addRoute(route) {
    const routePattern = this.createRoutePattern(route.path);
    this.routes.set(routePattern, {
      ...route,
      pattern: routePattern,
      params: this.extractParamNames(route.path)
    });
  }
  
  createRoutePattern(path) {
    // Convert route path to regex pattern
    return new RegExp('^' + path.replace(/:\w+/g, '([^/]+)') + '$');
  }
  
  extractParamNames(path) {
    const matches = path.match(/:(\w+)/g);
    return matches ? matches.map(match => match.substring(1)) : [];
  }
  
  // Navigation Methods
  navigate(path, options = {}) {
    const { replace = false, state = null } = options;
    
    // Check route guards
    if (!this.checkRouteGuards(path)) {
      return false;
    }
    
    // Update browser history
    if (replace) {
      history.replaceState(state, '', path);
    } else {
      history.pushState(state, '', path);
    }
    
    // Handle route change
    this.handleRouteChange(path, state);
    
    return true;
  }
  
  async handleRouteChange(path, state = null) {
    try {
      // Show loading state
      this.showLoadingState();
      
      // Find matching route
      const route = this.findRoute(path);
      
      if (!route) {
        this.navigate('/404', { replace: true });
        return;
      }
      
      // Extract route parameters
      const params = this.extractParams(path, route);
      
      // Run middleware
      await this.runMiddleware(route, params);
      
      // Load and render component
      await this.loadComponent(route, params, state);
      
      // Update navigation state
      this.updateNavigationState(route, path);
      
      // Update page title
      document.title = route.title;
      
      // Hide loading state
      this.hideLoadingState();
      
    } catch (error) {
      console.error('Route change error:', error);
      this.navigate('/500', { replace: true });
    }
  }
  
  findRoute(path) {
    for (const [pattern, route] of this.routes) {
      if (pattern.test(path)) {
        return route;
      }
    }
    return null;
  }
  
  extractParams(path, route) {
    const matches = path.match(route.pattern);
    const params = {};
    
    if (matches && route.params) {
      route.params.forEach((param, index) => {
        params[param] = matches[index + 1];
      });
    }
    
    return params;
  }
  
  // Component Loading System
  async loadComponent(route, params, state) {
    const componentName = route.component;
    
    try {
      // Dynamic import for code splitting
      const module = await import(`./components/${componentName}.js`);
      const Component = module.default;
      
      // Create component instance
      const componentInstance = new Component({
        params,
        state,
        router: this
      });
      
      // Render component
      await this.renderComponent(componentInstance);
      
      // Store current route
      this.currentRoute = {
        route,
        params,
        component: componentInstance
      };
      
    } catch (error) {
      console.error(`Failed to load component ${componentName}:`, error);
      throw error;
    }
  }
  
  async renderComponent(component) {
    const mainContent = document.getElementById('mainContent');
    
    if (!mainContent) {
      throw new Error('Main content container not found');
    }
    
    // Clear previous content
    mainContent.innerHTML = '';
    
    // Render new component
    const componentHTML = await component.render();
    mainContent.innerHTML = componentHTML;
    
    // Initialize component
    if (component.init) {
      await component.init();
    }
    
    // Bind event listeners
    if (component.bindEvents) {
      component.bindEvents();
    }
  }
  
  // Route Guards System
  addGuard(path, guardFunction) {
    this.guards.set(path, guardFunction);
  }
  
  checkRouteGuards(path) {
    for (const [guardPath, guardFunction] of this.guards) {
      if (path.startsWith(guardPath)) {
        if (!guardFunction()) {
          return false;
        }
      }
    }
    return true;
  }
  
  // Middleware System
  addMiddleware(middleware) {
    this.middlewares.push(middleware);
  }
  
  async runMiddleware(route, params) {
    for (const middleware of this.middlewares) {
      await middleware(route, params);
    }
  }
  
  // Navigation State Management
  updateNavigationState(route, path) {
    // Update active navigation items
    this.updateActiveNavigation(path);
    
    // Update breadcrumbs
    this.updateBreadcrumbs(route, path);
    
    // Update page header
    this.updatePageHeader(route);
  }
  
  updateActiveNavigation(path) {
    // Remove active class from all nav items
    document.querySelectorAll('.nav-link.active').forEach(link => {
      link.classList.remove('active');
    });
    
    // Add active class to current nav item
    const currentNavLink = document.querySelector(`[href="${path}"]`);
    if (currentNavLink) {
      currentNavLink.classList.add('active');
    }
  }
  
  updateBreadcrumbs(route, path) {
    const breadcrumbNav = document.querySelector('.breadcrumb');
    if (!breadcrumbNav) return;
    
    const pathSegments = path.split('/').filter(segment => segment);
    const breadcrumbs = this.generateBreadcrumbs(pathSegments);
    
    breadcrumbNav.innerHTML = breadcrumbs.map((crumb, index) => {
      const isLast = index === breadcrumbs.length - 1;
      return `
        <li class="breadcrumb-item ${isLast ? 'active' : ''}">
          ${isLast ? crumb.title : `<a href="${crumb.path}">${crumb.title}</a>`}
        </li>
      `;
    }).join('');
  }
  
  generateBreadcrumbs(pathSegments) {
    const breadcrumbs = [{ title: 'Dashboard', path: '/dashboard' }];
    
    let currentPath = '';
    pathSegments.forEach(segment => {
      currentPath += `/${segment}`;
      const route = this.findRoute(currentPath);
      if (route) {
        breadcrumbs.push({
          title: this.formatBreadcrumbTitle(segment),
          path: currentPath
        });
      }
    });
    
    return breadcrumbs;
  }
  
  formatBreadcrumbTitle(segment) {
    return segment.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  }
  
  // Loading States
  showLoadingState() {
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
      loadingIndicator.style.display = 'block';
    }
  }
  
  hideLoadingState() {
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
      loadingIndicator.style.display = 'none';
    }
  }
  
  // Link Interception
  interceptLinks() {
    document.addEventListener('click', (event) => {
      const link = event.target.closest('a[href]');
      
      if (link && this.shouldInterceptLink(link)) {
        event.preventDefault();
        this.navigate(link.getAttribute('href'));
      }
    });
  }
  
  shouldInterceptLink(link) {
    const href = link.getAttribute('href');
    
    // Don't intercept external links
    if (href.startsWith('http') || href.startsWith('mailto:')) {
      return false;
    }
    
    // Don't intercept links with target="_blank"
    if (link.getAttribute('target') === '_blank') {
      return false;
    }
    
    // Don't intercept download links
    if (link.hasAttribute('download')) {
      return false;
    }
    
    return true;
  }
}

// Initialize Router
const router = new ProfessionalRouter();

// Define all routes
router.defineRoutes();

// Add authentication guard
router.addGuard('/', () => {
  return AuthService.isAuthenticated();
});

// Add loading middleware
router.addMiddleware(async (route, params) => {
  // Track page views
  if (window.gtag) {
    gtag('config', 'GA_MEASUREMENT_ID', {
      page_title: route.title,
      page_location: window.location.href
    });
  }
});

// Export router instance
window.router = router;
```

## 🔒 **ROUTE PROTECTION SYSTEM**

### **Authentication Guards**
```javascript
class RouteGuards {
  static authenticationGuard() {
    return AuthService.isAuthenticated();
  }
  
  static adminGuard() {
    return AuthService.hasRole('admin');
  }
  
  static subscriptionGuard() {
    return SubscriptionService.hasActiveSubscription();
  }
  
  static setupGuards(router) {
    // Protect all dashboard routes
    router.addGuard('/dashboard', RouteGuards.authenticationGuard);
    router.addGuard('/content', RouteGuards.authenticationGuard);
    router.addGuard('/research', RouteGuards.authenticationGuard);
    router.addGuard('/projects', RouteGuards.authenticationGuard);
    router.addGuard('/analytics', RouteGuards.authenticationGuard);
    
    // Protect admin routes
    router.addGuard('/settings/team', RouteGuards.adminGuard);
    
    // Protect premium features
    router.addGuard('/content/bulk', RouteGuards.subscriptionGuard);
  }
}
```

This dynamic routing system provides professional SPA navigation with URL management, component loading, route protection, and seamless user experience.
