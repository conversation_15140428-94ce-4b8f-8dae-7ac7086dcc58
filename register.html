<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Create your SEO SAAS account and start generating AI-powered content for any niche.">
    <title>Sign Up - SEO SAAS</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/forms.css">
    <link rel="stylesheet" href="css/animations.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center py-12">
    <div class="container max-w-md mx-auto p-4">
        <!-- <PERSON><PERSON> and Back Link -->
        <div class="text-center mb-8 animate-fade-in-down">
            <a href="index.html" class="inline-flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-6 transition-colors">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to home
            </a>
            
            <div class="flex justify-center items-center gap-3 mb-2">
                <svg class="w-12 h-12 text-primary-600" viewBox="0 0 40 40" fill="currentColor">
                    <path d="M20 5L5 15v10l15 10 15-10V15L20 5z"/>
                    <path d="M20 15l-10 6v8l10 6 10-6v-8l-10-6z" fill="white" opacity="0.5"/>
                </svg>
                <h1 class="text-2xl font-bold text-gray-900">SEO SAAS</h1>
            </div>
            <p class="text-gray-600">Create your account and start dominating any niche</p>
        </div>
        
        <!-- Plan Selection -->
        <div class="mb-6 animate-fade-in" id="planSelector">
            <div class="text-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Choose Your Plan</h3>
                <p class="text-sm text-gray-600">Start with 14 days free, cancel anytime</p>
            </div>
            
            <div class="grid grid-cols-2 gap-3">
                <label class="plan-option relative cursor-pointer">
                    <input type="radio" name="plan" value="starter" class="sr-only" checked>
                    <div class="border-2 border-gray-200 rounded-lg p-4 transition-all hover:border-primary-300">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-gray-900">$49</div>
                            <div class="text-sm text-gray-600 mb-2">per month</div>
                            <div class="font-medium text-gray-900">Starter</div>
                            <div class="text-xs text-gray-500">50 generations/month</div>
                        </div>
                    </div>
                </label>
                
                <label class="plan-option relative cursor-pointer">
                    <input type="radio" name="plan" value="professional" class="sr-only">
                    <div class="border-2 border-gray-200 rounded-lg p-4 transition-all hover:border-primary-300 relative">
                        <div class="absolute -top-2 left-1/2 transform -translate-x-1/2">
                            <span class="badge badge-primary text-xs">Popular</span>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary-600">$149</div>
                            <div class="text-sm text-gray-600 mb-2">per month</div>
                            <div class="font-medium text-gray-900">Professional</div>
                            <div class="text-xs text-gray-500">500 generations/month</div>
                        </div>
                    </div>
                </label>
            </div>
        </div>
        
        <!-- Registration Form -->
        <div class="form-container animate-fade-in-up">
            <form id="registerForm" class="space-y-6">
                <!-- Name Fields -->
                <div class="grid grid-cols-2 gap-4">
                    <div class="form-group">
                        <label for="firstName" class="form-label">First Name</label>
                        <input 
                            type="text" 
                            id="firstName" 
                            name="firstName"
                            class="form-input"
                            placeholder="John"
                            required
                            autofocus
                        >
                    </div>
                    <div class="form-group">
                        <label for="lastName" class="form-label">Last Name</label>
                        <input 
                            type="text" 
                            id="lastName" 
                            name="lastName"
                            class="form-input"
                            placeholder="Doe"
                            required
                        >
                    </div>
                </div>
                
                <!-- Email Field -->
                <div class="form-group">
                    <label for="email" class="form-label">Email Address</label>
                    <div class="input-with-icon">
                        <svg class="input-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        <input 
                            type="email" 
                            id="email" 
                            name="email"
                            class="form-input"
                            placeholder="<EMAIL>"
                            required
                        >
                    </div>
                </div>
                
                <!-- Password Field -->
                <div class="form-group">
                    <label for="password" class="form-label">Password</label>
                    <div class="input-with-icon">
                        <svg class="input-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                        <input 
                            type="password" 
                            id="password" 
                            name="password"
                            class="form-input"
                            placeholder="••••••••"
                            required
                            minlength="8"
                        >
                    </div>
                    <div class="form-help">
                        Must be at least 8 characters with uppercase, lowercase, and numbers
                    </div>
                </div>
                
                <!-- Confirm Password -->
                <div class="form-group">
                    <label for="confirmPassword" class="form-label">Confirm Password</label>
                    <div class="input-with-icon">
                        <svg class="input-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                        <input 
                            type="password" 
                            id="confirmPassword" 
                            name="confirmPassword"
                            class="form-input"
                            placeholder="••••••••"
                            required
                        >
                    </div>
                </div>
                
                <!-- Company (Optional) -->
                <div class="form-group">
                    <label for="company" class="form-label">Company <span class="text-gray-400">(Optional)</span></label>
                    <div class="input-with-icon">
                        <svg class="input-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        <input 
                            type="text" 
                            id="company" 
                            name="company"
                            class="form-input"
                            placeholder="Your Company"
                        >
                    </div>
                </div>
                
                <!-- How did you hear about us? -->
                <div class="form-group">
                    <label for="referralSource" class="form-label">How did you hear about us?</label>
                    <select id="referralSource" name="referralSource" class="form-select">
                        <option value="">Please select</option>
                        <option value="google">Google Search</option>
                        <option value="social">Social Media</option>
                        <option value="referral">Friend/Colleague</option>
                        <option value="blog">Blog/Article</option>
                        <option value="youtube">YouTube</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                
                <!-- Terms and Privacy -->
                <div class="space-y-3">
                    <label class="form-checkbox">
                        <input type="checkbox" name="terms" id="terms" required>
                        <span class="checkbox-indicator"></span>
                        <span class="checkbox-label text-sm">
                            I agree to the <a href="terms.html" class="text-primary-600 hover:text-primary-700" target="_blank">Terms of Service</a> 
                            and <a href="privacy.html" class="text-primary-600 hover:text-primary-700" target="_blank">Privacy Policy</a>
                        </span>
                    </label>
                    
                    <label class="form-checkbox">
                        <input type="checkbox" name="marketing" id="marketing">
                        <span class="checkbox-indicator"></span>
                        <span class="checkbox-label text-sm">
                            Send me product updates and marketing emails (you can unsubscribe anytime)
                        </span>
                    </label>
                </div>
                
                <!-- Submit Button -->
                <button type="submit" class="btn btn-primary w-full btn-lg">
                    <span>Create Account</span>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                    </svg>
                </button>
                
                <!-- Divider -->
                <div class="relative my-6">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-500">Or sign up with</span>
                    </div>
                </div>
                
                <!-- Social Registration -->
                <div class="grid grid-cols-2 gap-3">
                    <button type="button" class="btn btn-secondary">
                        <svg class="w-5 h-5" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        <span>Google</span>
                    </button>
                    
                    <button type="button" class="btn btn-secondary">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z"/>
                        </svg>
                        <span>GitHub</span>
                    </button>
                </div>
            </form>
            
            <!-- Sign In Link -->
            <p class="text-center text-sm text-gray-600 mt-6">
                Already have an account? 
                <a href="login.html" class="text-primary-600 hover:text-primary-700 font-medium">
                    Sign in
                </a>
            </p>
        </div>
        
        <!-- Trial Benefits -->
        <div class="mt-8 p-4 bg-primary-50 border border-primary-200 rounded-lg">
            <h4 class="font-semibold text-primary-900 mb-2">14-Day Free Trial Includes:</h4>
            <ul class="text-sm text-primary-700 space-y-1">
                <li class="flex items-center gap-2">
                    <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Full access to all features
                </li>
                <li class="flex items-center gap-2">
                    <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Generate content for any niche
                </li>
                <li class="flex items-center gap-2">
                    <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    No credit card required
                </li>
                <li class="flex items-center gap-2">
                    <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Cancel anytime
                </li>
            </ul>
        </div>
        
        <!-- Security Notice -->
        <div class="mt-6 text-center">
            <p class="text-xs text-gray-500 flex items-center justify-center gap-2">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
                Your data is protected with enterprise-grade security
            </p>
        </div>
    </div>
    
    <!-- Background Pattern -->
    <div class="fixed inset-0 -z-10 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-primary-100 rounded-full opacity-20 blur-3xl"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-100 rounded-full opacity-20 blur-3xl"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-green-100 rounded-full opacity-10 blur-3xl"></div>
    </div>
    
    <!-- Scripts -->
    <script src="js/forms.js"></script>
    <script>
        // Plan selection handling
        document.querySelectorAll('input[name="plan"]').forEach(radio => {
            radio.addEventListener('change', function() {
                document.querySelectorAll('.plan-option div').forEach(div => {
                    div.classList.remove('border-primary-500', 'bg-primary-50');
                    div.classList.add('border-gray-200');
                });
                
                this.closest('.plan-option').querySelector('div').classList.remove('border-gray-200');
                this.closest('.plan-option').querySelector('div').classList.add('border-primary-500', 'bg-primary-50');
            });
        });
        
        // Set initial state
        document.querySelector('input[name="plan"]:checked').dispatchEvent(new Event('change'));
    </script>
</body>
</html>