/**
 * Authentication E2E Tests
 * 
 * Tests for user registration, login, logout, and authentication flows
 * including error handling and security validations.
 */

import { test, expect, Page } from '@playwright/test';

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'SecurePassword123!',
  name: 'New Test User'
};

const existingUser = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

test.describe('Authentication Flows', () => {
  
  test.beforeEach(async ({ page }) => {
    // Start from the home page
    await page.goto('/');
  });

  test.describe('User Registration', () => {
    
    test('should register a new user successfully', async ({ page }) => {
      // Navigate to registration page
      await page.click('[data-testid="register-link"]');
      await expect(page).toHaveURL('**/register');
      
      // Fill registration form
      await page.fill('[data-testid="name-input"]', testUser.name);
      await page.fill('[data-testid="email-input"]', testUser.email);
      await page.fill('[data-testid="password-input"]', testUser.password);
      await page.fill('[data-testid="confirm-password-input"]', testUser.password);
      
      // Accept terms and conditions
      await page.check('[data-testid="terms-checkbox"]');
      
      // Submit registration
      await page.click('[data-testid="register-button"]');
      
      // Should redirect to email verification page
      await expect(page).toHaveURL('**/verify-email');
      await expect(page.locator('[data-testid="verification-message"]')).toContainText('Please check your email');
    });

    test('should show validation errors for invalid input', async ({ page }) => {
      await page.click('[data-testid="register-link"]');
      
      // Try to register with invalid email
      await page.fill('[data-testid="email-input"]', 'invalid-email');
      await page.fill('[data-testid="password-input"]', '123');
      await page.click('[data-testid="register-button"]');
      
      // Should show validation errors
      await expect(page.locator('[data-testid="email-error"]')).toContainText('Please enter a valid email');
      await expect(page.locator('[data-testid="password-error"]')).toContainText('Password must be at least');
    });

    test('should prevent registration with existing email', async ({ page }) => {
      await page.click('[data-testid="register-link"]');
      
      // Try to register with existing email
      await page.fill('[data-testid="name-input"]', 'Test User');
      await page.fill('[data-testid="email-input"]', existingUser.email);
      await page.fill('[data-testid="password-input"]', testUser.password);
      await page.fill('[data-testid="confirm-password-input"]', testUser.password);
      await page.check('[data-testid="terms-checkbox"]');
      
      await page.click('[data-testid="register-button"]');
      
      // Should show error message
      await expect(page.locator('[data-testid="registration-error"]')).toContainText('already registered');
    });

    test('should require terms acceptance', async ({ page }) => {
      await page.click('[data-testid="register-link"]');
      
      // Fill form without checking terms
      await page.fill('[data-testid="name-input"]', testUser.name);
      await page.fill('[data-testid="email-input"]', testUser.email);
      await page.fill('[data-testid="password-input"]', testUser.password);
      await page.fill('[data-testid="confirm-password-input"]', testUser.password);
      
      // Register button should be disabled
      await expect(page.locator('[data-testid="register-button"]')).toBeDisabled();
    });

    test('should validate password confirmation', async ({ page }) => {
      await page.click('[data-testid="register-link"]');
      
      // Fill form with mismatched passwords
      await page.fill('[data-testid="password-input"]', testUser.password);
      await page.fill('[data-testid="confirm-password-input"]', 'DifferentPassword123!');
      
      await page.click('[data-testid="register-button"]');
      
      // Should show password mismatch error
      await expect(page.locator('[data-testid="password-confirm-error"]')).toContainText('Passwords do not match');
    });
  });

  test.describe('User Login', () => {
    
    test('should login with valid credentials', async ({ page }) => {
      // Navigate to login page
      await page.click('[data-testid="login-link"]');
      await expect(page).toHaveURL('**/login');
      
      // Fill login form
      await page.fill('[data-testid="email-input"]', existingUser.email);
      await page.fill('[data-testid="password-input"]', existingUser.password);
      
      // Submit login
      await page.click('[data-testid="login-button"]');
      
      // Should redirect to dashboard
      await expect(page).toHaveURL('**/dashboard');
      await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
    });

    test('should show error for invalid credentials', async ({ page }) => {
      await page.click('[data-testid="login-link"]');
      
      // Try to login with invalid credentials
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'wrongpassword');
      
      await page.click('[data-testid="login-button"]');
      
      // Should show error message
      await expect(page.locator('[data-testid="login-error"]')).toContainText('Invalid credentials');
    });

    test('should validate required fields', async ({ page }) => {
      await page.click('[data-testid="login-link"]');
      
      // Try to login with empty fields
      await page.click('[data-testid="login-button"]');
      
      // Should show validation errors
      await expect(page.locator('[data-testid="email-error"]')).toContainText('Email is required');
      await expect(page.locator('[data-testid="password-error"]')).toContainText('Password is required');
    });

    test('should handle "Remember Me" functionality', async ({ page }) => {
      await page.click('[data-testid="login-link"]');
      
      // Fill login form and check remember me
      await page.fill('[data-testid="email-input"]', existingUser.email);
      await page.fill('[data-testid="password-input"]', existingUser.password);
      await page.check('[data-testid="remember-me-checkbox"]');
      
      await page.click('[data-testid="login-button"]');
      
      // Should redirect to dashboard
      await expect(page).toHaveURL('**/dashboard');
      
      // Check that session persists after page reload
      await page.reload();
      await expect(page).toHaveURL('**/dashboard');
    });

    test('should redirect to intended page after login', async ({ page }) => {
      // Try to access protected page without authentication
      await page.goto('/dashboard/projects');
      
      // Should redirect to login
      await expect(page).toHaveURL('**/login');
      
      // Login
      await page.fill('[data-testid="email-input"]', existingUser.email);
      await page.fill('[data-testid="password-input"]', existingUser.password);
      await page.click('[data-testid="login-button"]');
      
      // Should redirect to originally requested page
      await expect(page).toHaveURL('**/dashboard/projects');
    });
  });

  test.describe('Password Reset', () => {
    
    test('should initiate password reset flow', async ({ page }) => {
      await page.click('[data-testid="login-link"]');
      
      // Click forgot password link
      await page.click('[data-testid="forgot-password-link"]');
      await expect(page).toHaveURL('**/forgot-password');
      
      // Fill email and submit
      await page.fill('[data-testid="email-input"]', existingUser.email);
      await page.click('[data-testid="reset-password-button"]');
      
      // Should show success message
      await expect(page.locator('[data-testid="reset-success-message"]')).toContainText('Password reset email sent');
    });

    test('should validate email for password reset', async ({ page }) => {
      await page.goto('/forgot-password');
      
      // Try with invalid email
      await page.fill('[data-testid="email-input"]', 'invalid-email');
      await page.click('[data-testid="reset-password-button"]');
      
      // Should show validation error
      await expect(page.locator('[data-testid="email-error"]')).toContainText('Please enter a valid email');
    });
  });

  test.describe('User Logout', () => {
    
    test('should logout successfully', async ({ page }) => {
      // Login first
      await page.goto('/login');
      await page.fill('[data-testid="email-input"]', existingUser.email);
      await page.fill('[data-testid="password-input"]', existingUser.password);
      await page.click('[data-testid="login-button"]');
      
      await expect(page).toHaveURL('**/dashboard');
      
      // Logout
      await page.click('[data-testid="user-menu"]');
      await page.click('[data-testid="logout-button"]');
      
      // Should redirect to home page
      await expect(page).toHaveURL('/');
      
      // Should not be able to access protected routes
      await page.goto('/dashboard');
      await expect(page).toHaveURL('**/login');
    });
  });

  test.describe('Session Management', () => {
    
    test('should handle session expiration', async ({ page }) => {
      // Login first
      await page.goto('/login');
      await page.fill('[data-testid="email-input"]', existingUser.email);
      await page.fill('[data-testid="password-input"]', existingUser.password);
      await page.click('[data-testid="login-button"]');
      
      await expect(page).toHaveURL('**/dashboard');
      
      // Simulate session expiration by clearing cookies
      await page.context().clearCookies();
      
      // Try to access protected page
      await page.goto('/dashboard/projects');
      
      // Should redirect to login
      await expect(page).toHaveURL('**/login');
    });

    test('should maintain session across tabs', async ({ context }) => {
      // Create first tab and login
      const page1 = await context.newPage();
      await page1.goto('/login');
      await page1.fill('[data-testid="email-input"]', existingUser.email);
      await page1.fill('[data-testid="password-input"]', existingUser.password);
      await page1.click('[data-testid="login-button"]');
      
      await expect(page1).toHaveURL('**/dashboard');
      
      // Create second tab
      const page2 = await context.newPage();
      await page2.goto('/dashboard');
      
      // Should be authenticated in second tab
      await expect(page2).toHaveURL('**/dashboard');
      await expect(page2.locator('[data-testid="user-menu"]')).toBeVisible();
    });
  });

  test.describe('Authentication UI/UX', () => {
    
    test('should show loading states during authentication', async ({ page }) => {
      await page.goto('/login');
      
      // Fill form
      await page.fill('[data-testid="email-input"]', existingUser.email);
      await page.fill('[data-testid="password-input"]', existingUser.password);
      
      // Click login and check loading state
      await page.click('[data-testid="login-button"]');
      
      // Should show loading spinner
      await expect(page.locator('[data-testid="login-loading"]')).toBeVisible();
      
      // Button should be disabled during loading
      await expect(page.locator('[data-testid="login-button"]')).toBeDisabled();
    });

    test('should be accessible with keyboard navigation', async ({ page }) => {
      await page.goto('/login');
      
      // Use keyboard to navigate form
      await page.keyboard.press('Tab'); // Email input
      await page.keyboard.type(existingUser.email);
      
      await page.keyboard.press('Tab'); // Password input
      await page.keyboard.type(existingUser.password);
      
      await page.keyboard.press('Tab'); // Remember me checkbox
      await page.keyboard.press('Space'); // Check the box
      
      await page.keyboard.press('Tab'); // Login button
      await page.keyboard.press('Enter'); // Submit
      
      // Should login successfully
      await expect(page).toHaveURL('**/dashboard');
    });

    test('should be responsive on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      await page.goto('/login');
      
      // Form should be visible and usable
      await expect(page.locator('[data-testid="login-form"]')).toBeVisible();
      await expect(page.locator('[data-testid="email-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="password-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="login-button"]')).toBeVisible();
      
      // Form should be properly sized
      const form = page.locator('[data-testid="login-form"]');
      const formBox = await form.boundingBox();
      expect(formBox?.width).toBeLessThanOrEqual(375);
    });
  });

  test.describe('Security Features', () => {
    
    test('should implement rate limiting on login attempts', async ({ page }) => {
      await page.goto('/login');
      
      // Make multiple failed login attempts
      for (let i = 0; i < 5; i++) {
        await page.fill('[data-testid="email-input"]', '<EMAIL>');
        await page.fill('[data-testid="password-input"]', 'wrongpassword');
        await page.click('[data-testid="login-button"]');
        
        if (i < 4) {
          await expect(page.locator('[data-testid="login-error"]')).toContainText('Invalid credentials');
        }
      }
      
      // Should show rate limit message
      await expect(page.locator('[data-testid="rate-limit-error"]')).toContainText('Too many attempts');
    });

    test('should prevent password field from being auto-filled by browser', async ({ page }) => {
      await page.goto('/login');
      
      // Password field should have autocomplete="off" or "new-password"
      const passwordInput = page.locator('[data-testid="password-input"]');
      const autocomplete = await passwordInput.getAttribute('autocomplete');
      expect(['off', 'new-password', 'current-password']).toContain(autocomplete);
    });

    test('should hide password by default and allow toggle', async ({ page }) => {
      await page.goto('/login');
      
      // Password should be hidden by default
      await expect(page.locator('[data-testid="password-input"]')).toHaveAttribute('type', 'password');
      
      // Click show password button
      await page.click('[data-testid="show-password-button"]');
      
      // Password should be visible
      await expect(page.locator('[data-testid="password-input"]')).toHaveAttribute('type', 'text');
      
      // Click hide password button
      await page.click('[data-testid="hide-password-button"]');
      
      // Password should be hidden again
      await expect(page.locator('[data-testid="password-input"]')).toHaveAttribute('type', 'password');
    });
  });
});

// Helper function to login programmatically
async function loginAsUser(page: Page, email: string, password: string) {
  await page.goto('/login');
  await page.fill('[data-testid="email-input"]', email);
  await page.fill('[data-testid="password-input"]', password);
  await page.click('[data-testid="login-button"]');
  await expect(page).toHaveURL('**/dashboard');
}

// Export helper for use in other test files
export { loginAsUser, testUser, existingUser };