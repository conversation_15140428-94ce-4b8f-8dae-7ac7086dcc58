/**
 * Error Boundary Component
 * Comprehensive error handling with recovery options and error reporting
 */

'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'
import Button from '@/components/UI/Button'
import Card from '@/components/UI/Card'
import { 
  ExclamationTriangleIcon, 
  ArrowPathIcon,
  ClipboardDocumentIcon,
  BugAntIcon,
  HomeIcon,
  ChevronDownIcon,
  ChevronUpIcon
} from '@heroicons/react/24/outline'

interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  errorId: string
  showDetails: boolean
  retryCount: number
}

interface ErrorBoundaryProps {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo, errorId: string) => void
  enableRetry?: boolean
  maxRetries?: number
  showDetails?: boolean
  isolate?: boolean
  level?: 'page' | 'component' | 'critical'
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private retryTimeoutId: NodeJS.Timeout | null = null

  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      showDetails: false,
      retryCount: 0
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    return {
      hasError: true,
      error,
      errorId
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const errorId = this.state.errorId

    this.setState({
      errorInfo
    })

    // Log error details
    this.logError(error, errorInfo, errorId)

    // Call custom error handler
    this.props.onError?.(error, errorInfo, errorId)

    // Report error to monitoring service
    this.reportError(error, errorInfo, errorId)
  }

  private logError(error: Error, errorInfo: ErrorInfo, errorId: string) {
    console.group(`🚨 Error Boundary Caught Error [${errorId}]`)
    console.error('Error:', error)
    console.error('Error Message:', error.message)
    console.error('Stack Trace:', error.stack)
    console.error('Component Stack:', errorInfo.componentStack)
    console.error('Props:', this.props)
    console.groupEnd()
  }

  private async reportError(error: Error, errorInfo: ErrorInfo, errorId: string) {
    try {
      const errorReport = {
        id: errorId,
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: new Date().toISOString(),
        level: this.props.level || 'component',
        retryCount: this.state.retryCount,
        props: this.props.isolate ? undefined : this.props
      }

      // Send to error reporting service
      await fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(errorReport)
      }).catch(() => {
        // Silently handle reporting failures
        console.warn('Failed to report error to server')
      })
    } catch (reportingError) {
      console.warn('Error reporting failed:', reportingError)
    }
  }

  private handleRetry = () => {
    const { maxRetries = 3 } = this.props
    
    if (this.state.retryCount >= maxRetries) {
      console.warn('Max retry attempts reached')
      return
    }

    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1
    }))

    // Add a small delay before retry
    this.retryTimeoutId = setTimeout(() => {
      // Force re-render
      this.forceUpdate()
    }, 100)
  }

  private handleReload = () => {
    window.location.reload()
  }

  private handleGoHome = () => {
    window.location.href = '/'
  }

  private handleCopyError = async () => {
    const { error, errorInfo, errorId } = this.state
    
    const errorText = `
Error ID: ${errorId}
Error: ${error?.message}
Stack: ${error?.stack}
Component Stack: ${errorInfo?.componentStack}
URL: ${window.location.href}
Timestamp: ${new Date().toISOString()}
    `.trim()

    try {
      await navigator.clipboard.writeText(errorText)
      // Could show a toast notification here
      console.log('Error details copied to clipboard')
    } catch (err) {
      console.warn('Failed to copy to clipboard:', err)
      // Fallback: select text in a textarea
      const textarea = document.createElement('textarea')
      textarea.value = errorText
      document.body.appendChild(textarea)
      textarea.select()
      document.execCommand('copy')
      document.body.removeChild(textarea)
    }
  }

  private toggleDetails = () => {
    this.setState(prevState => ({
      showDetails: !prevState.showDetails
    }))
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId)
    }
  }

  render() {
    if (this.state.hasError) {
      const { 
        fallback, 
        enableRetry = true, 
        maxRetries = 3, 
        showDetails = true,
        level = 'component'
      } = this.props
      const { error, errorInfo, errorId, retryCount } = this.state

      if (fallback) {
        return fallback
      }

      const canRetry = enableRetry && retryCount < maxRetries
      const isCritical = level === 'critical' || level === 'page'

      return (
        <div className={`error-boundary ${isCritical ? 'error-boundary-critical' : 'error-boundary-component'}`}>
          <div className="min-h-[400px] flex items-center justify-center p-4">
            <Card className="max-w-2xl w-full p-6">
              <div className="text-center">
                {/* Error Icon */}
                <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900/20 mb-4">
                  {isCritical ? (
                    <ExclamationTriangleIcon className="h-8 w-8 text-red-600 dark:text-red-400" />
                  ) : (
                    <BugAntIcon className="h-8 w-8 text-red-600 dark:text-red-400" />
                  )}
                </div>

                {/* Error Title */}
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  {isCritical ? 'Something went wrong' : 'Component Error'}
                </h2>

                {/* Error Description */}
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  {isCritical 
                    ? 'We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists.'
                    : 'This component encountered an error and could not render properly.'}
                </p>

                {/* Error ID */}
                <div className="mb-6">
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Error ID: <code className="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-xs font-mono">{errorId}</code>
                  </p>
                  {retryCount > 0 && (
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                      Retry attempts: {retryCount}/{maxRetries}
                    </p>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3 justify-center mb-6">
                  {canRetry && (
                    <Button
                      variant="primary"
                      onClick={this.handleRetry}
                      leftIcon={<ArrowPathIcon className="h-4 w-4" />}
                    >
                      Try Again
                    </Button>
                  )}
                  
                  {isCritical && (
                    <Button
                      variant="outline"
                      onClick={this.handleReload}
                      leftIcon={<ArrowPathIcon className="h-4 w-4" />}
                    >
                      Reload Page
                    </Button>
                  )}
                  
                  <Button
                    variant="ghost"
                    onClick={this.handleGoHome}
                    leftIcon={<HomeIcon className="h-4 w-4" />}
                  >
                    Go Home
                  </Button>

                  <Button
                    variant="ghost"
                    onClick={this.handleCopyError}
                    leftIcon={<ClipboardDocumentIcon className="h-4 w-4" />}
                  >
                    Copy Error
                  </Button>
                </div>

                {/* Error Details Toggle */}
                {showDetails && (error || errorInfo) && (
                  <div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={this.toggleDetails}
                      leftIcon={
                        this.state.showDetails ? (
                          <ChevronUpIcon className="h-4 w-4" />
                        ) : (
                          <ChevronDownIcon className="h-4 w-4" />
                        )
                      }
                    >
                      {this.state.showDetails ? 'Hide' : 'Show'} Error Details
                    </Button>

                    {this.state.showDetails && (
                      <div className="mt-4 text-left">
                        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border">
                          {error && (
                            <div className="mb-4">
                              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                                Error Message:
                              </h4>
                              <pre className="text-xs text-red-600 dark:text-red-400 whitespace-pre-wrap break-words">
                                {error.message}
                              </pre>
                            </div>
                          )}

                          {error?.stack && (
                            <div className="mb-4">
                              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                                Stack Trace:
                              </h4>
                              <pre className="text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap break-words max-h-40 overflow-y-auto">
                                {error.stack}
                              </pre>
                            </div>
                          )}

                          {errorInfo?.componentStack && (
                            <div>
                              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                                Component Stack:
                              </h4>
                              <pre className="text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap break-words max-h-40 overflow-y-auto">
                                {errorInfo.componentStack}
                              </pre>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </Card>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// Higher-order component for wrapping components with error boundaries
export const withErrorBoundary = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) => {
  const WithErrorBoundaryComponent = (props: P) => {
    return (
      <ErrorBoundary {...errorBoundaryProps}>
        <WrappedComponent {...props} />
      </ErrorBoundary>
    )
  }

  WithErrorBoundaryComponent.displayName = `withErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`
  
  return WithErrorBoundaryComponent
}

// Hook for error handling in functional components
export const useErrorHandler = () => {
  const [error, setError] = React.useState<Error | null>(null)

  const resetError = React.useCallback(() => {
    setError(null)
  }, [])

  const reportError = React.useCallback((error: Error, errorInfo?: any) => {
    setError(error)
    
    // Log error
    console.error('Error caught by useErrorHandler:', error)
    if (errorInfo) {
      console.error('Error info:', errorInfo)
    }

    // Report to monitoring service
    const errorReport = {
      id: `hook_error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      message: error.message,
      stack: error.stack,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      level: 'hook',
      errorInfo
    }

    fetch('/api/errors', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(errorReport)
    }).catch(() => {
      console.warn('Failed to report error to server')
    })
  }, [])

  React.useEffect(() => {
    if (error) {
      // You might want to show a toast notification here
      console.warn('Component error state updated:', error.message)
    }
  }, [error])

  return {
    error,
    reportError,
    resetError,
    hasError: error !== null
  }
}

// Global error handler initialization
const initializeGlobalErrorHandlers = () => {
  if (typeof window === 'undefined') return;

  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    
    const errorReport = {
      id: `unhandled_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      message: event.reason?.message || 'Unhandled promise rejection',
      stack: event.reason?.stack,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      level: 'global',
      type: 'unhandledrejection'
    }

    fetch('/api/errors', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(errorReport)
    }).catch(() => {
      // Silently handle reporting failures
    })
  })

  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error)
    
    const errorReport = {
      id: `global_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      message: event.error?.message || event.message,
      stack: event.error?.stack,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      level: 'global',
      type: 'javascript'
    }

    fetch('/api/errors', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(errorReport)
    }).catch(() => {
      // Silently handle reporting failures
    })
  })
}

// Initialize global error handlers
initializeGlobalErrorHandlers()

// Specialized error boundaries for different contexts
export const PageErrorBoundary = ({ children }: { children: ReactNode }) => (
  <ErrorBoundary
    level="page"
    enableRetry={true}
    maxRetries={2}
    showDetails={true}
  >
    {children}
  </ErrorBoundary>
)

export const ComponentErrorBoundary = ({ children }: { children: ReactNode }) => (
  <ErrorBoundary
    level="component"
    enableRetry={true}
    maxRetries={3}
    showDetails={false}
  >
    {children}
  </ErrorBoundary>
)

export const CriticalErrorBoundary = ({ children }: { children: ReactNode }) => (
  <ErrorBoundary
    level="critical"
    enableRetry={false}
    showDetails={true}
  >
    {children}
  </ErrorBoundary>
)

export default ErrorBoundary;