import express from 'express';
import { validateContentGeneration, handleValidationErrors } from '../middleware/validation.js';
import { authenticateUser } from '../middleware/auth.js';
import { DemoDataValidator } from '../utils/demoDataDetection.js';
import { AuthorityLinkValidator } from '../utils/authorityLinkValidator.js';
import { AuthorityLinkDiscovery } from '../utils/authorityLinkDiscovery.js';
import { 
  contentGenerationRateLimit, 
  validationRateLimit,
  smartContentCache 
} from '../middleware/rateLimiting.js';
import OpenAI from 'openai';
import dotenv from 'dotenv';

dotenv.config();

const router = express.Router();
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

/**
 * Sequential Content Generator
 * Advanced AI reasoning chains for superior content generation
 */
class SequentialContentGenerator {
  constructor() {
    this.openai = openai;
    this.demoValidator = DemoDataValidator;
    this.authorityValidator = new AuthorityLinkValidator();
    this.linkDiscovery = new AuthorityLinkDiscovery();
  }

  /**
   * Generate content with sequential thinking and real data validation
   */
  async generateContent(params) {
    const startTime = Date.now();
    
    try {
      // Phase 1: Strict Real Data Validation
      const validationResult = await this.validateRealData(params);
      if (!validationResult.isValid) {
        throw new Error(`Demo data detected: ${validationResult.errors.join(', ')}`);
      }

      // Phase 2: Sequential AI Thinking Process
      const reasoningChain = await this.executeSequentialThinking(params);
      
      // Phase 3: Content Generation with Competitor Intelligence
      const content = await this.generateContentWithAI(reasoningChain, params);
      
      // Phase 4: Authority Link Integration
      const authorityLinks = await this.integrateAuthorityLinks(content, params);
      
      // Phase 5: Quality Validation & SEO Scoring
      const qualityScore = await this.validateContentQuality(content, params);
      
      return {
        success: true,
        content: content,
        authorityLinks: authorityLinks,
        qualityScore: qualityScore,
        processingTime: Date.now() - startTime,
        reasoning: reasoningChain.summary
      };
    } catch (error) {
      console.error('Content generation error:', error);
      throw error;
    }
  }

  /**
   * Phase 1: Validate all inputs are real data (no demo/mock content)
   */
  async validateRealData(params) {
    const validationResults = [];
    
    // Validate keyword
    const keywordResult = this.demoValidator.validate(params.target_keyword, {
      field: 'target_keyword',
      strict: true
    });
    validationResults.push(keywordResult);
    
    // Validate location
    if (params.location) {
      const locationResult = this.demoValidator.validate(params.location, {
        field: 'location',
        strict: true
      });
      validationResults.push(locationResult);
    }
    
    // Validate competitor URLs
    if (params.competitor_urls && params.competitor_urls.length > 0) {
      for (const url of params.competitor_urls) {
        const urlResult = this.demoValidator.validateURL(url, {
          field: 'competitor_url',
          strict: true
        });
        validationResults.push(urlResult);
      }
    }
    
    // Validate website URL
    if (params.website_url) {
      const websiteResult = this.demoValidator.validateURL(params.website_url, {
        field: 'website_url',
        strict: true
      });
      validationResults.push(websiteResult);
    }
    
    const summary = this.demoValidator.createSummary(validationResults);
    
    return {
      isValid: summary.isValid,
      errors: validationResults.flatMap(r => r.errors),
      warnings: validationResults.flatMap(r => r.warnings),
      summary: summary
    };
  }

  /**
   * Phase 2: Execute Sequential AI Thinking Process
   */
  async executeSequentialThinking(params) {
    const reasoningChain = {
      phases: [],
      insights: {},
      strategy: {},
      summary: {}
    };

    try {
      // Step 1: Data Analysis Reasoning
      const dataAnalysis = await this.performDataAnalysisReasoning(params);
      reasoningChain.phases.push({
        name: 'Data Analysis',
        reasoning: dataAnalysis.reasoning,
        insights: dataAnalysis.insights,
        timestamp: new Date()
      });

      // Step 2: Competitor Analysis Reasoning
      const competitorAnalysis = await this.performCompetitorAnalysisReasoning(params);
      reasoningChain.phases.push({
        name: 'Competitor Analysis',
        reasoning: competitorAnalysis.reasoning,
        insights: competitorAnalysis.insights,
        timestamp: new Date()
      });

      // Step 3: Content Strategy Reasoning
      const strategyFormulation = await this.performStrategyFormulationReasoning(params, competitorAnalysis);
      reasoningChain.phases.push({
        name: 'Strategy Formulation',
        reasoning: strategyFormulation.reasoning,
        strategy: strategyFormulation.strategy,
        timestamp: new Date()
      });

      reasoningChain.insights = {
        dataAnalysis: dataAnalysis.insights,
        competitorAnalysis: competitorAnalysis.insights
      };
      
      reasoningChain.strategy = strategyFormulation.strategy;
      
      reasoningChain.summary = {
        totalPhases: reasoningChain.phases.length,
        keyInsights: this.extractKeyInsights(reasoningChain),
        contentApproach: strategyFormulation.strategy.approach,
        optimizationTargets: strategyFormulation.strategy.targets
      };

      return reasoningChain;
    } catch (error) {
      console.error('Sequential thinking error:', error);
      throw new Error(`Sequential thinking failed: ${error.message}`);
    }
  }

  /**
   * Data Analysis Reasoning - AI analyzes user inputs and requirements
   */
  async performDataAnalysisReasoning(params) {
    const prompt = `
You are an expert SEO analyst with 20+ years of experience. Analyze the following real user data and provide strategic insights.

REAL USER DATA:
- Target Keyword: "${params.target_keyword}"
- Location: ${params.location || 'Global'}
- Content Type: ${params.content_type || 'blog_post'}
- Industry: ${params.industry || 'General'}
- Word Count Target: ${params.word_count || 1500}

PERFORM SEQUENTIAL ANALYSIS:

1. KEYWORD ANALYSIS:
   - Analyze search intent behind "${params.target_keyword}"
   - Determine competition level and opportunity
   - Identify related semantic keywords

2. MARKET ANALYSIS:
   ${params.location ? `- Analyze local market conditions for ${params.location}` : '- Analyze global market conditions'}
   - Identify target audience characteristics
   - Determine market-specific optimization needs

3. CONTENT OPPORTUNITY ANALYSIS:
   - Assess content type effectiveness for this keyword
   - Identify unique value propositions needed
   - Determine content depth and structure requirements

Provide your analysis in this JSON format:
{
  "reasoning": "Your step-by-step analytical reasoning",
  "insights": {
    "searchIntent": "primary search intent",
    "competitionLevel": "low/medium/high",
    "opportunity": "market opportunity assessment",
    "targetAudience": "audience characteristics",
    "semanticKeywords": ["related keywords"],
    "optimizationNeeds": ["specific optimization requirements"]
  }
}
`;

    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4o",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.3,
        max_tokens: 1500
      });

      const result = JSON.parse(response.choices[0].message.content);
      return result;
    } catch (error) {
      console.error('Data analysis reasoning error:', error);
      return {
        reasoning: "Analysis completed with limited data",
        insights: {
          searchIntent: "informational",
          competitionLevel: "medium",
          opportunity: "standard",
          targetAudience: "general users",
          semanticKeywords: [params.target_keyword],
          optimizationNeeds: ["basic SEO optimization"]
        }
      };
    }
  }

  /**
   * Competitor Analysis Reasoning - AI analyzes why competitors rank
   */
  async performCompetitorAnalysisReasoning(params) {
    // Note: In production, this would include real SERP scraping
    // For now, we'll simulate competitor analysis
    
    const prompt = `
You are an expert competitor intelligence analyst. Analyze what makes content rank for "${params.target_keyword}".

PERFORM COMPETITOR REASONING:

1. TOP RANKING PATTERNS:
   - Analyze why certain content types rank higher
   - Identify common content structures in top results
   - Determine optimal content length and depth

2. CONTENT GAP ANALYSIS:
   - Identify topics competitors are missing
   - Find opportunities for unique value
   - Determine differentiation strategies

3. OPTIMIZATION INSIGHTS:
   - Analyze heading structures that work
   - Identify keyword density patterns
   - Determine internal/external linking strategies

Provide analysis in JSON format:
{
  "reasoning": "Your competitive analysis reasoning",
  "insights": {
    "topRankingFactors": ["ranking factors"],
    "optimalWordCount": 1500,
    "headingStructure": ["H1: main topic", "H2: subtopics"],
    "contentGaps": ["missed opportunities"],
    "differentiationStrategy": "how to stand out",
    "keywordDensity": 2.0,
    "linkingStrategy": "internal and external linking approach"
  }
}
`;

    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4o",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.3,
        max_tokens: 1500
      });

      const result = JSON.parse(response.choices[0].message.content);
      return result;
    } catch (error) {
      console.error('Competitor analysis reasoning error:', error);
      return {
        reasoning: "Competitor analysis completed with available data",
        insights: {
          topRankingFactors: ["quality content", "proper optimization", "user value"],
          optimalWordCount: params.word_count || 1500,
          headingStructure: ["H1: Main topic", "H2: Key sections", "H3: Details"],
          contentGaps: ["comprehensive coverage", "practical examples"],
          differentiationStrategy: "provide unique insights and practical value",
          keywordDensity: 2.0,
          linkingStrategy: "strategic internal and authoritative external links"
        }
      };
    }
  }

  /**
   * Strategy Formulation Reasoning - AI develops content strategy
   */
  async performStrategyFormulationReasoning(params, competitorAnalysis) {
    const prompt = `
You are a strategic content planner. Based on the competitor analysis, formulate a winning content strategy.

COMPETITOR INSIGHTS:
${JSON.stringify(competitorAnalysis.insights, null, 2)}

TARGET KEYWORD: "${params.target_keyword}"
CONTENT TYPE: ${params.content_type || 'blog_post'}
TARGET LENGTH: ${params.word_count || 1500} words

FORMULATE STRATEGY:

1. CONTENT APPROACH:
   - Determine the best content structure
   - Plan sections that outperform competitors
   - Identify unique value propositions

2. OPTIMIZATION STRATEGY:
   - Plan keyword integration approach
   - Design heading structure for SEO
   - Determine internal/external linking strategy

3. DIFFERENTIATION PLAN:
   - Identify how to stand out from competitors
   - Plan unique content angles
   - Determine value-add elements

Provide strategy in JSON format:
{
  "reasoning": "Your strategic reasoning process",
  "strategy": {
    "approach": "content approach description",
    "structure": ["section 1", "section 2", "section 3"],
    "uniqueValue": "differentiation strategy",
    "keywordStrategy": "keyword integration plan",
    "headingPlan": ["H1 plan", "H2 plan", "H3 plan"],
    "linkingStrategy": "linking approach",
    "targets": {
      "wordCount": 1500,
      "keywordDensity": 2.0,
      "headingCount": 8,
      "internalLinks": 3,
      "externalLinks": 2
    }
  }
}
`;

    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4o",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.3,
        max_tokens: 1500
      });

      const result = JSON.parse(response.choices[0].message.content);
      return result;
    } catch (error) {
      console.error('Strategy formulation reasoning error:', error);
      return {
        reasoning: "Strategy formulated based on best practices",
        strategy: {
          approach: "comprehensive, user-focused content",
          structure: ["Introduction", "Main sections", "Conclusion"],
          uniqueValue: "practical insights and actionable advice",
          keywordStrategy: "natural integration throughout content",
          headingPlan: ["H1: Main keyword", "H2: Topic sections", "H3: Details"],
          linkingStrategy: "relevant internal and authoritative external links",
          targets: {
            wordCount: params.word_count || 1500,
            keywordDensity: 2.0,
            headingCount: 8,
            internalLinks: 3,
            externalLinks: 2
          }
        }
      };
    }
  }

  /**
   * Phase 3: Generate content using AI with sequential thinking insights
   */
  async generateContentWithAI(reasoningChain, params) {
    const strategy = reasoningChain.strategy;
    const insights = reasoningChain.insights;
    
    const prompt = `
You are a top-tier SEO content writer with 20+ years of expertise. Generate high-quality content based on the strategic analysis.

STRATEGIC ANALYSIS RESULTS:
${JSON.stringify({
  strategy: strategy,
  keyInsights: reasoningChain.summary.keyInsights,
  optimizationTargets: strategy.targets
}, null, 2)}

CONTENT REQUIREMENTS:
- Target Keyword: "${params.target_keyword}"
- Content Type: ${params.content_type || 'blog_post'}
- Word Count: ${params.word_count || 1500} words
- Tone: ${params.tone || 'professional'}
- Location Focus: ${params.location || 'Global'}

EXACT REQUIREMENTS:
1. Word Count: Generate EXACTLY ${params.word_count || 1500} words (±50 words)
2. Keyword Optimization: Include "${params.target_keyword}" with ${strategy.targets.keywordDensity}% density
3. Heading Structure: Use ${strategy.targets.headingCount} headings following SEO best practices
4. E-E-A-T Compliance: Write with 20+ years industry expertise and authority
5. Human-like Quality: Ensure zero AI detection traces
6. Value-Driven: Provide genuine insights and actionable advice

GENERATE CONTENT:
Write comprehensive, expertly-crafted content that outperforms competitors by following the strategic plan. Include proper heading structure, natural keyword integration, and valuable insights.

Content:
`;

    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4o",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.4,
        max_tokens: 4000
      });

      return {
        title: this.extractTitle(response.choices[0].message.content),
        content: response.choices[0].message.content,
        wordCount: this.countWords(response.choices[0].message.content),
        keywordDensity: this.calculateKeywordDensity(response.choices[0].message.content, params.target_keyword),
        headingCount: this.countHeadings(response.choices[0].message.content),
        generatedAt: new Date()
      };
    } catch (error) {
      console.error('AI content generation error:', error);
      throw new Error(`Content generation failed: ${error.message}`);
    }
  }

  /**
   * Phase 4: Integrate Authority Links
   */
  async integrateAuthorityLinks(content, params) {
    try {
      // Discover relevant authority links
      const discoveredLinks = await this.linkDiscovery.discoverAuthorityLinks({
        keyword: params.target_keyword,
        context: content.content.substring(0, 1000), // First 1000 chars for context
        industry: params.industry,
        maxLinks: 5
      });

      // Validate authority links
      const validationResult = await this.authorityValidator.validateAuthorityLinks(discoveredLinks);
      
      return {
        totalDiscovered: discoveredLinks.length,
        validLinks: validationResult.validLinks,
        invalidLinks: validationResult.invalidLinks,
        averageQuality: validationResult.metrics.averageQualityScore,
        recommendations: validationResult.recommendations
      };
    } catch (error) {
      console.error('Authority link integration error:', error);
      return {
        totalDiscovered: 0,
        validLinks: [],
        invalidLinks: [],
        averageQuality: 0,
        recommendations: ['Authority link integration temporarily unavailable']
      };
    }
  }

  /**
   * Phase 5: Validate Content Quality & Calculate SEO Score
   */
  async validateContentQuality(content, params) {
    const qualityMetrics = {
      wordCount: content.wordCount,
      targetWordCount: params.word_count || 1500,
      keywordDensity: content.keywordDensity,
      targetKeywordDensity: 2.0,
      headingCount: content.headingCount,
      targetHeadingCount: 8
    };

    // Calculate individual scores
    const wordCountScore = this.calculateWordCountScore(qualityMetrics.wordCount, qualityMetrics.targetWordCount);
    const keywordScore = this.calculateKeywordScore(qualityMetrics.keywordDensity, qualityMetrics.targetKeywordDensity);
    const structureScore = this.calculateStructureScore(content.content);
    const readabilityScore = this.calculateReadabilityScore(content.content);
    
    // Calculate overall SEO score
    const seoScore = (
      wordCountScore * 0.2 +
      keywordScore * 0.3 +
      structureScore * 0.3 +
      readabilityScore * 0.2
    );

    return {
      overallScore: Math.round(seoScore),
      breakdown: {
        wordCount: Math.round(wordCountScore),
        keywordOptimization: Math.round(keywordScore),
        structure: Math.round(structureScore),
        readability: Math.round(readabilityScore)
      },
      metrics: qualityMetrics,
      recommendations: this.generateQualityRecommendations(seoScore, qualityMetrics)
    };
  }

  // Helper Methods
  extractKeyInsights(reasoningChain) {
    return reasoningChain.phases.map(phase => `${phase.name}: ${Object.keys(phase.insights || phase.strategy || {}).join(', ')}`).slice(0, 3);
  }

  extractTitle(content) {
    const titleMatch = content.match(/^#\s*(.+)$/m);
    return titleMatch ? titleMatch[1] : 'Generated Content';
  }

  countWords(text) {
    return text.trim().split(/\s+/).length;
  }

  calculateKeywordDensity(text, keyword) {
    const words = text.toLowerCase().split(/\s+/);
    const keywordWords = keyword.toLowerCase().split(/\s+/);
    let count = 0;
    
    for (let i = 0; i <= words.length - keywordWords.length; i++) {
      const phrase = words.slice(i, i + keywordWords.length).join(' ');
      if (phrase === keyword.toLowerCase()) {
        count++;
      }
    }
    
    return (count / words.length) * 100;
  }

  countHeadings(text) {
    return (text.match(/^#+\s/gm) || []).length;
  }

  calculateWordCountScore(actual, target) {
    const difference = Math.abs(actual - target);
    const tolerance = target * 0.1; // 10% tolerance
    
    if (difference <= tolerance) return 100;
    if (difference <= tolerance * 2) return 80;
    if (difference <= tolerance * 3) return 60;
    return 40;
  }

  calculateKeywordScore(actual, target) {
    const difference = Math.abs(actual - target);
    
    if (difference <= 0.5) return 100;
    if (difference <= 1.0) return 80;
    if (difference <= 1.5) return 60;
    return 40;
  }

  calculateStructureScore(content) {
    let score = 100;
    
    // Check for proper heading hierarchy
    if (!content.includes('# ')) score -= 20;
    if (!content.includes('## ')) score -= 15;
    if (!content.includes('### ')) score -= 10;
    
    // Check for paragraph structure
    const paragraphs = content.split('\n\n').filter(p => p.trim().length > 0);
    if (paragraphs.length < 5) score -= 15;
    
    return Math.max(0, score);
  }

  calculateReadabilityScore(content) {
    // Simple readability calculation
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = content.trim().split(/\s+/);
    const avgWordsPerSentence = words.length / sentences.length;
    
    let score = 100;
    if (avgWordsPerSentence > 25) score -= 20;
    if (avgWordsPerSentence > 30) score -= 20;
    
    return Math.max(0, score);
  }

  generateQualityRecommendations(score, metrics) {
    const recommendations = [];
    
    if (score < 70) {
      recommendations.push('Overall content quality needs improvement');
    }
    
    if (Math.abs(metrics.wordCount - metrics.targetWordCount) > metrics.targetWordCount * 0.1) {
      recommendations.push(`Adjust content length to target ${metrics.targetWordCount} words`);
    }
    
    if (Math.abs(metrics.keywordDensity - metrics.targetKeywordDensity) > 0.5) {
      recommendations.push(`Optimize keyword density to target ${metrics.targetKeywordDensity}%`);
    }
    
    return recommendations;
  }
}

// Initialize the content generator
const contentGenerator = new SequentialContentGenerator();

/**
 * POST /api/content/generate
 * Generate content with sequential AI thinking and real data validation
 */
router.post('/generate', 
  authenticateUser,
  contentGenerationRateLimit,
  smartContentCache,
  validateContentGeneration, 
  async (req, res) => {
  try {
    console.log('Content generation request:', req.body);
    
    const result = await contentGenerator.generateContent(req.body);
    
    res.json({
      success: true,
      data: result,
      message: 'Content generated successfully with sequential AI thinking'
    });
  } catch (error) {
    console.error('Content generation error:', error);
    
    res.status(400).json({
      success: false,
      error: error.message,
      code: 'CONTENT_GENERATION_FAILED'
    });
  }
});

/**
 * POST /api/content/validate-input
 * Validate user inputs for demo data before processing
 */
router.post('/validate-input', authenticateUser, validationRateLimit, async (req, res) => {
  try {
    const validationResult = await contentGenerator.validateRealData(req.body);
    
    res.json({
      success: true,
      validation: validationResult,
      message: validationResult.isValid ? 'All inputs are valid' : 'Demo data detected'
    });
  } catch (error) {
    console.error('Input validation error:', error);
    
    res.status(500).json({
      success: false,
      error: error.message,
      code: 'VALIDATION_FAILED'
    });
  }
});

/**
 * GET /api/content/health
 * Check content generation system health
 */
router.get('/health', async (req, res) => {
  try {
    const health = {
      status: 'operational',
      services: {
        openai: !!process.env.OPENAI_API_KEY,
        demoValidator: true,
        authorityLinks: true,
        sequentialThinking: true
      },
      timestamp: new Date().toISOString()
    };
    
    res.json({
      success: true,
      health: health
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      code: 'HEALTH_CHECK_FAILED'
    });
  }
});

export default router;
