'use client';

import React from 'react';
import Link from 'next/link';

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900">
      {/* Header */}
      <header className="fixed top-0 w-full z-50 glass-effect">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="text-2xl font-bold text-white">
              SEO SAAS
            </div>
            <nav className="hidden md:flex space-x-8">
              <Link href="#features" className="text-white hover:text-blue-300 transition-colors">
                Features
              </Link>
              <Link href="#pricing" className="text-white hover:text-blue-300 transition-colors">
                Pricing
              </Link>
              <Link href="/auth/login" className="text-white hover:text-blue-300 transition-colors">
                Login
              </Link>
              <Link href="/auth/register" className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                Get Started
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="pt-32 pb-20 px-6">
        <div className="container mx-auto text-center">
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-8 animate-fade-in">
            World's Most Advanced
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent block">
              AI-Powered SEO Platform
            </span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-12 max-w-4xl mx-auto animate-slide-up">
            Dominate ANY niche with AI-generated content based on deep competitor research and real-time analysis. 
            Works for ANY keyword in ANY industry with ZERO demo data - only genuine competitor intelligence.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center animate-slide-up">
            <Link href="/dashboard" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors">
              Start Generating Content
            </Link>
            <Link href="#features" className="border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-4 rounded-lg text-lg font-semibold transition-colors">
              See Features
            </Link>
          </div>
        </div>
      </section>

      {/* Key Features Section */}
      <section id="features" className="py-20 px-6">
        <div className="container mx-auto">
          <h2 className="text-4xl font-bold text-center text-white mb-16">
            Revolutionary Features That Dominate Competition
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="glass-effect p-8 rounded-xl text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl">🎯</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">Universal Niche Adaptation</h3>
              <p className="text-gray-300">
                Works for ANY keyword in ANY industry with deep competitor research and real-time SERP analysis.
              </p>
            </div>
            <div className="glass-effect p-8 rounded-xl text-center">
              <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl">🧠</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">Sequential AI Thinking</h3>
              <p className="text-gray-300">
                Advanced reasoning chains that provide AI with step-by-step analytical intelligence for superior content.
              </p>
            </div>
            <div className="glass-effect p-8 rounded-xl text-center">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl">🔒</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">100% Real Data Only</h3>
              <p className="text-gray-300">
                ZERO tolerance for demo/mock data. Only genuine competitor analysis and user-provided information.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-6">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-8">
            Ready to Dominate Your Niche?
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Join thousands of businesses using our AI-powered platform to outrank competitors with superior content.
          </p>
          <Link href="/auth/register" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-12 py-4 rounded-lg text-xl font-semibold transition-all transform hover:scale-105">
            Start Your Free Trial
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-6 border-t border-gray-800">
        <div className="container mx-auto text-center">
          <div className="text-2xl font-bold text-white mb-4">SEO SAAS</div>
          <p className="text-gray-400">
            © 2024 SEO SAAS. World's most advanced AI-powered SEO content generation platform.
          </p>
        </div>
      </footer>
    </div>
  );
}