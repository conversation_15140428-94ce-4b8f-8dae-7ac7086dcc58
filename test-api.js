// Test script for SEO SAAS API
import fetch from 'node-fetch';

async function testContentGeneration() {
  try {
    console.log('🧪 Testing SEO Content Generation API...');
    
    const response = await fetch('http://localhost:5000/api/seo/generate-content', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        keyword: 'digital marketing strategies',
        target_country: 'United States',
        content_type: 'blog-post',
        tone: 'professional',
        length: 'medium'
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error:', response.status, errorText);
      return;
    }

    const result = await response.json();
    
    console.log('✅ Content Generation Successful!');
    console.log('📊 Metadata:', result.seo_analysis);
    console.log('📝 Content Preview:', result.content.body.substring(0, 500) + '...');
    console.log('🎯 Keyword Density:', result.seo_analysis.keyword_density);
    console.log('📏 Word Count:', result.seo_analysis.actual_word_count);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

async function testHealthCheck() {
  try {
    console.log('🏥 Testing Health Check...');
    
    const response = await fetch('http://localhost:5000/health');
    const result = await response.json();
    
    console.log('✅ Backend Health:', result.status);
    console.log('🕐 Timestamp:', result.timestamp);
    
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
  }
}

// Run tests
async function runTests() {
  await testHealthCheck();
  console.log('\n' + '='.repeat(50) + '\n');
  await testContentGeneration();
}

runTests();
