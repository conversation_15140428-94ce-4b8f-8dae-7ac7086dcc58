'use client';

import React, { useState, useEffect } from 'react';
import { ProtectedRoute, useAuth } from '@/hooks/useAuth';
import { AuthenticatedLayout } from '@/components/Layout/DashboardLayout';
import {
  UserIcon,
  CameraIcon,
  EnvelopeIcon,
  CalendarIcon,
  CreditCardIcon,
  ChartBarIcon,
  CogIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

interface UserStats {
  contentGenerated: number;
  wordsGenerated: number;
  averageSeoScore: number;
  projectsCount: number;
  lastActivity: string;
}

export default function ProfilePage() {
  const { user, userProfile, userTier, usageCount, usageLimit, updateProfile, refreshUserData } = useAuth();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  
  // Form state
  const [formData, setFormData] = useState({
    full_name: '',
    email: '',
    avatar_url: ''
  });

  useEffect(() => {
    if (userProfile) {
      setFormData({
        full_name: userProfile.full_name || '',
        email: user?.email || '',
        avatar_url: userProfile.avatar_url || ''
      });
    }
  }, [userProfile, user]);

  useEffect(() => {
    loadUserStats();
  }, []);

  const loadUserStats = async () => {
    try {
      // Import analytics API client
      const { analyticsAPI } = await import('@/lib/api-client');
      
      // Fetch user dashboard data
      const response = await analyticsAPI.getDashboardData();
      
      if (response.success) {
        const data = response.data;
        setUserStats({
          contentGenerated: data.totalContent,
          wordsGenerated: data.totalWords,
          averageSeoScore: data.averageSeoScore,
          projectsCount: data.totalProjects,
          lastActivity: new Date().toISOString() // TODO: Get from actual last activity
        });
      }
    } catch (error) {
      console.error('Error loading user stats:', error);
      // Set fallback stats
      setUserStats({
        contentGenerated: 0,
        wordsGenerated: 0,
        averageSeoScore: 0,
        projectsCount: 0,
        lastActivity: new Date().toISOString()
      });
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const result = await updateProfile(formData);
      
      if (result.error) {
        setError(result.error.message);
      } else {
        setSuccess('Profile updated successfully!');
        await refreshUserData();
      }
    } catch (error) {
      setError('Failed to update profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'enterprise':
        return 'bg-purple-100 text-purple-800';
      case 'pro':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getUsageColor = (usage: number, limit: number) => {
    const percentage = (usage / limit) * 100;
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <ProtectedRoute>
      <AuthenticatedLayout>
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Page Header */}
          <div className="border-b border-gray-200 pb-5">
            <h1 className="text-2xl font-bold text-gray-900">Profile Settings</h1>
            <p className="mt-1 text-sm text-gray-500">
              Manage your account information and preferences
            </p>
          </div>

          {/* Success/Error Messages */}
          {success && (
            <div className="rounded-md bg-green-50 p-4">
              <div className="flex">
                <CheckCircleIcon className="h-5 w-5 text-green-400" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-green-800">{success}</p>
                </div>
              </div>
            </div>
          )}

          {error && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="flex">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-red-800">{error}</p>
                </div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Profile Form */}
            <div className="lg:col-span-2 space-y-6">
              {/* Personal Information */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                    Personal Information
                  </h3>
                  
                  <form onSubmit={handleSubmit} className="space-y-4">
                    {/* Avatar */}
                    <div className="flex items-center space-x-4">
                      <div className="relative">
                        {formData.avatar_url ? (
                          <img
                            className="h-20 w-20 rounded-full object-cover"
                            src={formData.avatar_url}
                            alt="Profile"
                          />
                        ) : (
                          <div className="h-20 w-20 rounded-full bg-gray-300 flex items-center justify-center">
                            <UserIcon className="h-10 w-10 text-gray-600" />
                          </div>
                        )}
                        <button
                          type="button"
                          className="absolute bottom-0 right-0 bg-blue-600 rounded-full p-1 text-white hover:bg-blue-700"
                        >
                          <CameraIcon className="h-4 w-4" />
                        </button>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">Profile Photo</h4>
                        <p className="text-xs text-gray-500">
                          Upload a photo to personalize your account
                        </p>
                      </div>
                    </div>

                    {/* Full Name */}
                    <div>
                      <label htmlFor="full_name" className="block text-sm font-medium text-gray-700">
                        Full Name
                      </label>
                      <input
                        type="text"
                        id="full_name"
                        name="full_name"
                        value={formData.full_name}
                        onChange={handleInputChange}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="Enter your full name"
                      />
                    </div>

                    {/* Email */}
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                        Email Address
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        disabled
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500 sm:text-sm"
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        Email cannot be changed. Contact support if needed.
                      </p>
                    </div>

                    {/* Submit Button */}
                    <div className="pt-4">
                      <button
                        type="submit"
                        disabled={loading}
                        className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                      >
                        {loading ? 'Updating...' : 'Update Profile'}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Account Summary */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                    Account Summary
                  </h3>
                  
                  <div className="space-y-4">
                    {/* Subscription Tier */}
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">Subscription</span>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full capitalize ${getTierColor(userTier)}`}>
                        {userTier}
                      </span>
                    </div>

                    {/* Usage */}
                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm text-gray-500">Usage This Month</span>
                        <span className="text-sm font-medium text-gray-900">
                          {usageCount} / {usageLimit}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${getUsageColor(usageCount, usageLimit)}`}
                          style={{ width: `${Math.min((usageCount / usageLimit) * 100, 100)}%` }}
                        ></div>
                      </div>
                    </div>

                    {/* Member Since */}
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">Member Since</span>
                      <span className="text-sm font-medium text-gray-900">
                        {userProfile?.created_at 
                          ? new Date(userProfile.created_at).toLocaleDateString()
                          : 'N/A'
                        }
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Stats Overview */}
              {userStats && (
                <div className="bg-white shadow rounded-lg">
                  <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                      Your Statistics
                    </h3>
                    
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Content Generated</span>
                        <span className="text-sm font-medium text-gray-900">
                          {userStats.contentGenerated}
                        </span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Words Written</span>
                        <span className="text-sm font-medium text-gray-900">
                          {userStats.wordsGenerated.toLocaleString()}
                        </span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Avg SEO Score</span>
                        <span className="text-sm font-medium text-gray-900">
                          {userStats.averageSeoScore}
                        </span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Active Projects</span>
                        <span className="text-sm font-medium text-gray-900">
                          {userStats.projectsCount}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Quick Actions */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                    Quick Actions
                  </h3>
                  
                  <div className="space-y-2">
                    <button className="w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">
                      <CogIcon className="h-4 w-4 mr-2" />
                      Account Settings
                    </button>
                    
                    <button className="w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">
                      <CreditCardIcon className="h-4 w-4 mr-2" />
                      Billing & Subscription
                    </button>
                    
                    <button className="w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">
                      <ChartBarIcon className="h-4 w-4 mr-2" />
                      View Analytics
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </AuthenticatedLayout>
    </ProtectedRoute>
  );
}