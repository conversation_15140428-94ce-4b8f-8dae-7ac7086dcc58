'use client';

import React, { useState, useEffect } from 'react';
import { EnhancedProtectedRoute, useUnifiedAuth } from '@/hooks/useUnifiedAuth';
import { AuthenticatedLayout } from '@/components/Layout/DashboardLayout';
import { <PERSON><PERSON>, <PERSON>, CardHeader, CardContent, Badge } from '@/components/UI';
import { useNotifications } from '@/components/Notifications';
import {
  UserIcon,
  CameraIcon,
  EnvelopeIcon,
  CalendarIcon,
  CreditCardIcon,
  ChartBarIcon,
  CogIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  CloudArrowUpIcon,
  PhotoIcon,
  TrophyIcon,
  FireIcon,
  BoltIcon,
  FolderIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';

interface UserStats {
  contentGenerated: number;
  wordsGenerated: number;
  averageSeoScore: number;
  projectsCount: number;
  lastActivity: string;
}

export default function ProfilePage() {
  const { 
    user, 
    userProfile, 
    userTier, 
    usageCount, 
    usageLimit, 
    updateProfile, 
    refreshUserData,
    isDemoMode 
  } = useUnifiedAuth();
  const { success: notifySuccess, error: notifyError } = useNotifications();
  const [loading, setLoading] = useState(false);
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [uploadingAvatar, setUploadingAvatar] = useState(false);
  
  // Form state
  const [formData, setFormData] = useState({
    full_name: '',
    email: '',
    avatar_url: ''
  });

  useEffect(() => {
    if (userProfile) {
      setFormData({
        full_name: userProfile.full_name || '',
        email: user?.email || '',
        avatar_url: userProfile.avatar_url || ''
      });
    }
  }, [userProfile, user]);

  useEffect(() => {
    loadUserStats();
  }, []);

  const loadUserStats = async () => {
    try {
      // Import analytics API client
      const { analyticsAPI } = await import('@/lib/api-client');
      
      // Fetch user dashboard data
      const response = await analyticsAPI.getDashboardData();
      
      if (response.success) {
        const data = response.data;
        setUserStats({
          contentGenerated: data.totalContent,
          wordsGenerated: data.totalWords,
          averageSeoScore: data.averageSeoScore,
          projectsCount: data.totalProjects,
          lastActivity: new Date().toISOString() // TODO: Get from actual last activity
        });
      }
    } catch (error) {
      console.error('Error loading user stats:', error);
      // Set fallback stats
      setUserStats({
        contentGenerated: 0,
        wordsGenerated: 0,
        averageSeoScore: 0,
        projectsCount: 0,
        lastActivity: new Date().toISOString()
      });
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const result = await updateProfile(formData);
      
      if (result.error) {
        notifyError('Profile Update Failed', result.error.message);
      } else {
        notifySuccess('Profile Updated', 'Your profile has been updated successfully!');
        await refreshUserData();
      }
    } catch (error) {
      notifyError('Update Error', 'Failed to update profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type and size
    if (!file.type.startsWith('image/')) {
      notifyError('Invalid File', 'Please select an image file.');
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      notifyError('File Too Large', 'Please select an image smaller than 5MB.');
      return;
    }

    setUploadingAvatar(true);

    try {
      if (isDemoMode) {
        // Demo mode: Just use a local URL
        const reader = new FileReader();
        reader.onload = async (e) => {
          const avatar_url = e.target?.result as string;
          const result = await updateProfile({ avatar_url });
          
          if (result.error) {
            notifyError('Upload Failed', 'Failed to update avatar.');
          } else {
            setFormData(prev => ({ ...prev, avatar_url }));
            notifySuccess('Avatar Updated', 'Your profile picture has been updated!');
          }
        };
        reader.readAsDataURL(file);
      } else {
        // Production mode: Upload to storage
        // TODO: Implement actual file upload to Supabase storage
        notifyError('Feature Unavailable', 'Avatar upload is not yet implemented in production mode.');
      }
    } catch (error) {
      notifyError('Upload Error', 'Failed to upload avatar. Please try again.');
    } finally {
      setUploadingAvatar(false);
    }
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'enterprise':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200';
      case 'pro':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const getTierIcon = (tier: string) => {
    switch (tier) {
      case 'enterprise':
        return <TrophyIcon className="h-4 w-4" />;
      case 'pro':
        return <BoltIcon className="h-4 w-4" />;
      default:
        return <UserIcon className="h-4 w-4" />;
    }
  };

  const getUsageColor = (usage: number, limit: number) => {
    const percentage = (usage / limit) * 100;
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <EnhancedProtectedRoute>
      <AuthenticatedLayout>
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Page Header */}
          <div className="border-b border-gray-200 dark:border-gray-700 pb-5">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Profile Settings</h1>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Manage your account information and preferences
                </p>
              </div>
              {isDemoMode && (
                <Badge variant="info" icon={<FireIcon className="h-3 w-3" />}>
                  Demo Mode
                </Badge>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Profile Form */}
            <div className="lg:col-span-2 space-y-6">
              {/* Personal Information */}
              <Card>
                <CardHeader
                  title="Personal Information"
                  subtitle="Update your personal details and profile picture"
                />
                
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Avatar */}
                    <div className="flex items-center space-x-6">
                      <div className="relative group">
                        {formData.avatar_url ? (
                          <img
                            className="h-24 w-24 rounded-full object-cover ring-4 ring-gray-100 dark:ring-gray-700"
                            src={formData.avatar_url}
                            alt="Profile"
                          />
                        ) : (
                          <div className="h-24 w-24 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center ring-4 ring-gray-100 dark:ring-gray-700">
                            <UserIcon className="h-12 w-12 text-gray-500 dark:text-gray-400" />
                          </div>
                        )}
                        
                        <label className="absolute bottom-0 right-0 bg-blue-600 rounded-full p-2 text-white hover:bg-blue-700 cursor-pointer transition-colors group-hover:scale-105">
                          <input
                            type="file"
                            accept="image/*"
                            onChange={handleAvatarUpload}
                            className="hidden"
                            disabled={uploadingAvatar}
                          />
                          {uploadingAvatar ? (
                            <CloudArrowUpIcon className="h-4 w-4 animate-pulse" />
                          ) : (
                            <CameraIcon className="h-4 w-4" />
                          )}
                        </label>
                      </div>
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">Profile Photo</h4>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          Upload a photo to personalize your account. JPG, PNG up to 5MB.
                        </p>
                        {uploadingAvatar && (
                          <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                            Uploading...
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Full Name */}
                    <div>
                      <label htmlFor="full_name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Full Name
                      </label>
                      <input
                        type="text"
                        id="full_name"
                        name="full_name"
                        value={formData.full_name}
                        onChange={handleInputChange}
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="Enter your full name"
                      />
                    </div>

                    {/* Email */}
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Email Address
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        disabled
                        className="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-50 dark:bg-gray-600 text-gray-500 dark:text-gray-400 sm:text-sm"
                      />
                      <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        Email cannot be changed. Contact support if needed.
                      </p>
                    </div>

                    {/* Submit Button */}
                    <div className="pt-4">
                      <Button
                        type="submit"
                        loading={loading}
                        variant="primary"
                        className="w-full sm:w-auto"
                      >
                        Update Profile
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Account Summary */}
              <Card>
                <CardHeader
                  title="Account Summary"
                  subtitle="Your subscription and usage overview"
                />
                
                <CardContent>
                  <div className="space-y-4">
                    {/* Subscription Tier */}
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Subscription</span>
                      <Badge 
                        variant={userTier === 'enterprise' ? 'secondary' : userTier === 'pro' ? 'primary' : 'default'}
                        icon={getTierIcon(userTier)}
                      >
                        {userTier}
                      </Badge>
                    </div>

                    {/* Usage */}
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-gray-500 dark:text-gray-400">Usage This Month</span>
                        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {usageCount} / {usageLimit}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full transition-all duration-300 ${getUsageColor(usageCount, usageLimit)}`}
                          style={{ width: `${Math.min((usageCount / usageLimit) * 100, 100)}%` }}
                        ></div>
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {Math.round((usageCount / usageLimit) * 100)}% of limit used
                      </p>
                    </div>

                    {/* Member Since */}
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Member Since</span>
                      <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {userProfile?.created_at 
                          ? new Date(userProfile.created_at).toLocaleDateString()
                          : 'N/A'
                        }
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Stats Overview */}
              {userStats && (
                <Card>
                  <CardHeader
                    title="Your Statistics"
                    subtitle="Performance metrics and achievements"
                  />
                  
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <DocumentTextIcon className="h-4 w-4 text-blue-500" />
                          <span className="text-sm text-gray-500 dark:text-gray-400">Content Generated</span>
                        </div>
                        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {userStats.contentGenerated}
                        </span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <ChartBarIcon className="h-4 w-4 text-green-500" />
                          <span className="text-sm text-gray-500 dark:text-gray-400">Words Written</span>
                        </div>
                        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {userStats.wordsGenerated.toLocaleString()}
                        </span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <TrophyIcon className="h-4 w-4 text-yellow-500" />
                          <span className="text-sm text-gray-500 dark:text-gray-400">Avg SEO Score</span>
                        </div>
                        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {userStats.averageSeoScore}
                        </span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <FolderIcon className="h-4 w-4 text-purple-500" />
                          <span className="text-sm text-gray-500 dark:text-gray-400">Active Projects</span>
                        </div>
                        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {userStats.projectsCount}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Quick Actions */}
              <Card>
                <CardHeader
                  title="Quick Actions"
                  subtitle="Common tasks and shortcuts"
                />
                
                <CardContent>
                  <div className="space-y-2">
                    <Button
                      variant="ghost"
                      fullWidth
                      leftIcon={<CogIcon className="h-4 w-4" />}
                      className="justify-start"
                      onClick={() => window.location.href = '/settings'}
                    >
                      Account Settings
                    </Button>
                    
                    <Button
                      variant="ghost"
                      fullWidth
                      leftIcon={<CreditCardIcon className="h-4 w-4" />}
                      className="justify-start"
                      onClick={() => window.location.href = '/settings/billing'}
                    >
                      Billing & Subscription
                    </Button>
                    
                    <Button
                      variant="ghost"
                      fullWidth
                      leftIcon={<ChartBarIcon className="h-4 w-4" />}
                      className="justify-start"
                      onClick={() => window.location.href = '/analytics'}
                    >
                      View Analytics
                    </Button>

                    <Button
                      variant="ghost"
                      fullWidth
                      leftIcon={<DocumentTextIcon className="h-4 w-4" />}
                      className="justify-start"
                      onClick={() => window.location.href = '/content-generator'}
                    >
                      Generate Content
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </AuthenticatedLayout>
    </EnhancedProtectedRoute>
  );
}