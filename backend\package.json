{"name": "seo-saas-backend", "version": "1.0.0", "description": "Backend for SEO SAAS HTML - AI-Powered SEO Content Generation Platform", "main": "server.js", "type": "module", "scripts": {"dev": "nodemon server.js", "start": "node server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@mendable/firecrawl-js": "^1.29.1", "@supabase/supabase-js": "^2.39.3", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cheerio": "^1.0.0-rc.12", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-validator": "^7.0.1", "helmet": "^7.1.0", "iconv-lite": "^0.6.3", "isomorphic-dompurify": "^2.26.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "node-cache": "^5.1.2", "openai": "^4.104.0", "rate-limiter-flexible": "^7.1.1", "uuid": "^9.0.1", "validator": "^13.11.0", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["seo", "content-generation", "ai", "backend", "express", "nodejs"], "author": "SEO SAAS Team", "license": "MIT"}