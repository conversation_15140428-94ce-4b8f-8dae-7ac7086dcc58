/**
 * SERP Analyzer - Real-time Google SERP Scraping & Analysis
 * Enterprise SEO SAAS - Live competitor intelligence for ANY keyword
 */

export interface SERPResult {
  position: number
  title: string
  url: string
  domain: string
  snippet: string
  displayUrl: string
  date?: string
  sitelinks?: string[]
  thumbnail?: string
}

export interface SERPAnalysis {
  keyword: string
  location: string
  totalResults: number
  searchTime: string
  results: SERPResult[]
  features: {
    featuredSnippet?: {
      title: string
      snippet: string
      url: string
      position: number
    }
    peopleAlsoAsk: string[]
    relatedSearches: string[]
    localPack?: SERPResult[]
    images?: string[]
    videos?: SERPResult[]
  }
  competitorMetrics: {
    averageTitleLength: number
    averageSnippetLength: number
    domainAuthorities: Record<string, number>
    contentTypes: Record<string, number>
    commonPatterns: string[]
  }
}

export interface CompetitorInsight {
  domain: string
  url: string
  title: string
  snippet: string
  position: number
  estimatedTraffic: number
  domainAuthority: number
  contentType: 'informational' | 'commercial' | 'transactional' | 'navigational'
  titleOptimization: {
    keywordPosition: number
    length: number
    hasNumbers: boolean
    hasPowerWords: boolean
    sentiment: 'positive' | 'neutral' | 'negative'
  }
  snippetAnalysis: {
    keywordMentions: number
    hasCallToAction: boolean
    hasQuestions: boolean
    readabilityScore: number
  }
  technicalSEO: {
    hasHttps: boolean
    loadSpeed: 'fast' | 'medium' | 'slow'
    mobileOptimized: boolean
    schemaMarkup: boolean
  }
  contentGaps: string[]
  opportunities: string[]
}

export class SERPAnalyzer {
  private apiKey: string
  private baseUrl: string
  private rateLimiter: Map<string, number>
  private cache: Map<string, { data: SERPAnalysis; timestamp: number }>
  private cacheDuration: number = 1000 * 60 * 30 // 30 minutes

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.SERPER_API_KEY || ''
    this.baseUrl = 'https://google.serper.dev'
    this.rateLimiter = new Map()
    this.cache = new Map()
  }

  /**
   * Analyze SERP results for any keyword with real-time data
   */
  async analyzeSERP(keyword: string, location: string = 'United States'): Promise<SERPAnalysis> {
    // Validate inputs - reject demo data
    this.validateRealKeyword(keyword)
    
    // Check cache first
    const cacheKey = `${keyword}-${location}`.toLowerCase()
    const cached = this.cache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < this.cacheDuration) {
      return cached.data
    }

    // Rate limiting check
    if (!this.checkRateLimit(keyword)) {
      throw new Error('Rate limit exceeded. Please wait before making another request.')
    }

    try {
      const serpData = await this.fetchSERPData(keyword, location)
      const analysis = await this.processSERPData(serpData, keyword, location)
      
      // Cache the results
      this.cache.set(cacheKey, { data: analysis, timestamp: Date.now() })
      
      return analysis
    } catch (error) {
      console.error('SERP analysis error:', error)
      throw new Error(`Failed to analyze SERP for "${keyword}": ${error}`)
    }
  }

  /**
   * Get detailed competitor insights from SERP results
   */
  async getCompetitorInsights(keyword: string, location: string = 'United States'): Promise<CompetitorInsight[]> {
    const serpAnalysis = await this.analyzeSERP(keyword, location)
    const insights: CompetitorInsight[] = []

    for (const result of serpAnalysis.results.slice(0, 10)) {
      try {
        const insight = await this.analyzeCompetitorResult(result, keyword, serpAnalysis)
        insights.push(insight)
      } catch (error) {
        console.error(`Error analyzing competitor ${result.domain}:`, error)
        // Continue with other competitors even if one fails
      }
    }

    return insights
  }

  /**
   * Extract winning patterns from top competitors
   */
  async extractWinningPatterns(keyword: string, location: string = 'United States'): Promise<any> {
    const insights = await this.getCompetitorInsights(keyword, location)
    const topCompetitors = insights.slice(0, 5) // Top 5 for pattern analysis

    return {
      titlePatterns: this.analyzeTitlePatterns(topCompetitors),
      contentTypes: this.analyzeContentTypes(topCompetitors),
      keywordUsage: this.analyzeKeywordUsage(topCompetitors, keyword),
      technicalPatterns: this.analyzeTechnicalPatterns(topCompetitors),
      contentGaps: this.identifyContentGaps(topCompetitors, keyword),
      optimizationTargets: this.calculateOptimizationTargets(topCompetitors)
    }
  }

  private validateRealKeyword(keyword: string): void {
    const demoPatterns = [
      /example|demo|test|sample|placeholder|lorem ipsum|dummy|mock|fake|template/i,
      /your keyword|insert keyword|keyword here|add keyword|replace this/i,
      /\[keyword\]|\{keyword\}|\<keyword\>/i
    ]

    for (const pattern of demoPatterns) {
      if (pattern.test(keyword)) {
        throw new Error(`REJECTED: Demo/placeholder keyword detected: "${keyword}". Please provide a real target keyword.`)
      }
    }

    if (keyword.trim().length < 2) {
      throw new Error('Keyword must be at least 2 characters long')
    }

    if (keyword.trim().length > 100) {
      throw new Error('Keyword must be less than 100 characters long')
    }
  }

  private checkRateLimit(keyword: string): boolean {
    const now = Date.now()
    const lastRequest = this.rateLimiter.get(keyword)
    const minInterval = 2000 // 2 seconds between requests for same keyword

    if (lastRequest && now - lastRequest < minInterval) {
      return false
    }

    this.rateLimiter.set(keyword, now)
    return true
  }

  private async fetchSERPData(keyword: string, location: string): Promise<any> {
    if (!this.apiKey) {
      // Fallback to mock data if no API key (for development)
      return this.generateMockSERPData(keyword, location)
    }

    const countryCode = this.getCountryCode(location)
    const googleDomain = this.getGoogleDomain(countryCode)
    
    // Enhanced API request with country-specific settings
    const response = await fetch(`${this.baseUrl}/search`, {
      method: 'POST',
      headers: {
        'X-API-KEY': this.apiKey,
        'Content-Type': 'application/json',
        'User-Agent': 'SEO-SAAS-MultiCountry-Analyzer/1.0'
      },
      body: JSON.stringify({
        q: keyword,
        gl: countryCode, // Geolocation targeting
        hl: this.getLanguageCode(countryCode), // Language targeting
        cr: `country${countryCode.toUpperCase()}`, // Country restriction
        lr: `lang_${this.getLanguageCode(countryCode)}`, // Language restriction
        googleDomain: googleDomain, // Specific Google domain
        num: 10,
        autocorrect: false,
        safe: 'off',
        filter: '0', // Include similar results
        pws: '0', // Disable personalization
        uule: this.getUULE(location), // Location parameter
        nfpr: '1' // No personalization
      })
    })

    if (!response.ok) {
      throw new Error(`SERP API error: ${response.status} ${response.statusText}`)
    }

    return await response.json()
  }

  private getLanguageCode(countryCode: string): string {
    const languageMap: Record<string, string> = {
      'us': 'en', 'gb': 'en', 'ca': 'en', 'au': 'en', 'nz': 'en',
      'de': 'de', 'at': 'de', 'ch': 'de',
      'fr': 'fr', 'be': 'fr',
      'es': 'es', 'mx': 'es', 'ar': 'es', 'cl': 'es', 'co': 'es', 'pe': 'es',
      'it': 'it',
      'nl': 'nl',
      'br': 'pt', 'pt': 'pt',
      'in': 'hi',
      'jp': 'ja',
      'cn': 'zh',
      'kr': 'ko',
      'ru': 'ru',
      'ae': 'ar', 'sa': 'ar', 'kw': 'ar', 'qa': 'ar', 'bh': 'ar', 'om': 'ar',
      'jo': 'ar', 'lb': 'ar', 'eg': 'ar', 'ma': 'ar', 'tn': 'ar', 'dz': 'ar',
      'th': 'th',
      'vn': 'vi',
      'my': 'ms',
      'id': 'id',
      'ph': 'tl',
      'sg': 'en',
      'hk': 'zh',
      'tw': 'zh'
    }
    return languageMap[countryCode] || 'en'
  }

  private getUULE(location: string): string {
    // Generate UULE parameter for location targeting
    // This is a simplified version - in production, use proper UULE encoding
    const locationMap: Record<string, string> = {
      'Dubai': 'w+CAIQICINZHViYWk%2CdXJlYXJhYmVtaXJhdGVz',
      'Abu Dhabi': 'w+CAIQICILYWJ1IGRoYWJpLHVhZQ%3D%3D',
      'United Arab Emirates': 'w+CAIQICILdW5pdGVkIGFyYWIgZW1pcmF0ZXM%3D',
      'UAE': 'w+CAIQICILdW5pdGVkIGFyYWIgZW1pcmF0ZXM%3D',
      'Saudi Arabia': 'w+CAIQICILc2F1ZGkgYXJhYmlh',
      'Kuwait': 'w+CAIQICIGa3V3YWl0',
      'Qatar': 'w+CAIQICIFcWF0YXI%3D',
      'Bahrain': 'w+CAIQICIHYmFocmFpbg%3D%3D',
      'Oman': 'w+CAIQICIEb21hbg%3D%3D',
      'United States': 'w+CAIQICIIdW5pdGVkIHN0YXRlcw%3D%3D',
      'United Kingdom': 'w+CAIQICIKdW5pdGVkIGtpbmdkb20%3D',
      'Canada': 'w+CAIQICIGY2FuYWRh',
      'Australia': 'w+CAIQICIJYXVzdHJhbGlh',
      'Germany': 'w+CAIQICIHZ2VybWFueQ%3D%3D',
      'France': 'w+CAIQICIGZnJhbmNl',
      'Spain': 'w+CAIQICIFc3BhaW4%3D',
      'Italy': 'w+CAIQICIFaXRhbHk%3D',
      'Netherlands': 'w+CAIQICILbmV0aGVybGFuZHM%3D',
      'Brazil': 'w+CAIQICIGYnJhemls',
      'India': 'w+CAIQICIFaW5kaWE%3D',
      'Japan': 'w+CAIQICIFamFwYW4%3D',
      'China': 'w+CAIQICIFY2hpbmE%3D',
      'Singapore': 'w+CAIQICIJc2luZ2Fwb3Jl',
      'Malaysia': 'w+CAIQICIIbWFsYXlzaWE%3D',
      'Thailand': 'w+CAIQICIIdGhhaWxhbmQ%3D',
      'South Korea': 'w+CAIQICILc291dGgga29yZWE%3D',
      'Mexico': 'w+CAIQICIGbWV4aWNv',
      'Argentina': 'w+CAIQICIJYXJnZW50aW5h',
      'Chile': 'w+CAIQICIFY2hpbGU%3D',
      'Colombia': 'w+CAIQICIIY29sb21iaWE%3D',
      'Peru': 'w+CAIQICIEcGVydQ%3D%3D'
    }
    return locationMap[location] || locationMap['United States'] || ''
  }

  private async processSERPData(serpData: any, keyword: string, location: string): Promise<SERPAnalysis> {
    const results: SERPResult[] = serpData.organic?.map((result: any, index: number) => ({
      position: index + 1,
      title: result.title || '',
      url: result.link || '',
      domain: this.extractDomain(result.link || ''),
      snippet: result.snippet || '',
      displayUrl: result.displayLink || '',
      date: result.date,
      sitelinks: result.sitelinks?.map((link: any) => link.title) || []
    })) || []

    const analysis: SERPAnalysis = {
      keyword,
      location,
      totalResults: serpData.searchInformation?.totalResults || 0,
      searchTime: serpData.searchInformation?.searchTime || '0',
      results,
      features: {
        featuredSnippet: serpData.answerBox ? {
          title: serpData.answerBox.title || '',
          snippet: serpData.answerBox.snippet || '',
          url: serpData.answerBox.link || '',
          position: 0
        } : undefined,
        peopleAlsoAsk: serpData.peopleAlsoAsk?.map((paa: any) => paa.question) || [],
        relatedSearches: serpData.relatedSearches?.map((rs: any) => rs.query) || [],
        localPack: serpData.places?.map((place: any, index: number) => ({
          position: index + 1,
          title: place.title || '',
          url: place.link || '',
          domain: this.extractDomain(place.link || ''),
          snippet: place.address || '',
          displayUrl: place.link || ''
        })) || [],
        images: serpData.images?.map((img: any) => img.imageUrl) || [],
        videos: serpData.videos?.map((video: any, index: number) => ({
          position: index + 1,
          title: video.title || '',
          url: video.link || '',
          domain: this.extractDomain(video.link || ''),
          snippet: video.snippet || '',
          displayUrl: video.displayLink || ''
        })) || []
      },
      competitorMetrics: this.calculateCompetitorMetrics(results)
    }

    return analysis
  }

  private async analyzeCompetitorResult(result: SERPResult, keyword: string, serpAnalysis: SERPAnalysis): Promise<CompetitorInsight> {
    const insight: CompetitorInsight = {
      domain: result.domain,
      url: result.url,
      title: result.title,
      snippet: result.snippet,
      position: result.position,
      estimatedTraffic: this.estimateTraffic(result.position),
      domainAuthority: this.estimateDomainAuthority(result.domain),
      contentType: this.detectContentType(result.title, result.snippet),
      titleOptimization: this.analyzeTitleOptimization(result.title, keyword),
      snippetAnalysis: this.analyzeSnippet(result.snippet, keyword),
      technicalSEO: this.analyzeTechnicalSEO(result.url),
      contentGaps: [],
      opportunities: []
    }

    // Identify content gaps and opportunities
    insight.contentGaps = this.identifySpecificContentGaps(insight, keyword)
    insight.opportunities = this.identifySpecificOpportunities(insight, keyword, serpAnalysis)

    return insight
  }

  private calculateCompetitorMetrics(results: SERPResult[]): any {
    const titles = results.map(r => r.title).filter(Boolean)
    const snippets = results.map(r => r.snippet).filter(Boolean)
    const domains = results.map(r => r.domain).filter(Boolean)

    return {
      averageTitleLength: Math.round(titles.reduce((sum, title) => sum + title.length, 0) / titles.length) || 0,
      averageSnippetLength: Math.round(snippets.reduce((sum, snippet) => sum + snippet.length, 0) / snippets.length) || 0,
      domainAuthorities: domains.reduce((acc, domain) => {
        acc[domain] = this.estimateDomainAuthority(domain)
        return acc
      }, {} as Record<string, number>),
      contentTypes: this.analyzeContentTypeDistribution(results),
      commonPatterns: this.extractCommonPatterns(titles)
    }
  }

  private analyzeTitlePatterns(competitors: CompetitorInsight[]): any {
    const patterns = {
      averageLength: Math.round(competitors.reduce((sum, c) => sum + c.title.length, 0) / competitors.length),
      keywordPositions: competitors.map(c => c.titleOptimization.keywordPosition),
      powerWordUsage: competitors.filter(c => c.titleOptimization.hasPowerWords).length / competitors.length,
      numberUsage: competitors.filter(c => c.titleOptimization.hasNumbers).length / competitors.length,
      commonWords: this.extractCommonWords(competitors.map(c => c.title)),
      sentimentDistribution: this.analyzeSentimentDistribution(competitors)
    }

    return patterns
  }

  private analyzeContentTypes(competitors: CompetitorInsight[]): any {
    const typeDistribution = competitors.reduce((acc, c) => {
      acc[c.contentType] = (acc[c.contentType] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return {
      distribution: typeDistribution,
      dominant: Object.entries(typeDistribution).sort(([,a], [,b]) => b - a)[0]?.[0] || 'informational'
    }
  }

  private analyzeKeywordUsage(competitors: CompetitorInsight[], keyword: string): any {
    const keywordInTitle = competitors.filter(c => 
      c.title.toLowerCase().includes(keyword.toLowerCase())
    ).length / competitors.length

    const keywordInSnippet = competitors.filter(c => 
      c.snippet.toLowerCase().includes(keyword.toLowerCase())
    ).length / competitors.length

    return {
      titlePresence: keywordInTitle,
      snippetPresence: keywordInSnippet,
      averageMentions: Math.round(competitors.reduce((sum, c) => sum + c.snippetAnalysis.keywordMentions, 0) / competitors.length),
      variations: this.extractKeywordVariations(competitors, keyword)
    }
  }

  private analyzeTechnicalPatterns(competitors: CompetitorInsight[]): any {
    return {
      httpsUsage: competitors.filter(c => c.technicalSEO.hasHttps).length / competitors.length,
      schemaUsage: competitors.filter(c => c.technicalSEO.schemaMarkup).length / competitors.length,
      mobileOptimization: competitors.filter(c => c.technicalSEO.mobileOptimized).length / competitors.length,
      averageLoadSpeed: this.calculateAverageLoadSpeed(competitors)
    }
  }

  private identifyContentGaps(competitors: CompetitorInsight[], keyword: string): string[] {
    const allGaps = competitors.flatMap(c => c.contentGaps)
    const gapFrequency = allGaps.reduce((acc, gap) => {
      acc[gap] = (acc[gap] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return Object.entries(gapFrequency)
      .filter(([, frequency]) => frequency >= competitors.length * 0.6) // Gaps in 60%+ of competitors
      .map(([gap]) => gap)
  }

  private calculateOptimizationTargets(competitors: CompetitorInsight[]): any {
    const topCompetitors = competitors.slice(0, 3) // Top 3 for benchmarking

    return {
      titleLength: {
        target: Math.max(...topCompetitors.map(c => c.title.length)) + 5,
        range: [50, 60]
      },
      keywordPlacement: {
        target: 'Within first 5 words of title',
        priority: 'High'
      },
      contentDepth: {
        target: 'Exceed top competitor by 20%',
        focus: 'Comprehensive coverage'
      },
      technicalSEO: {
        https: true,
        schema: true,
        mobileOptimized: true,
        loadSpeed: 'fast'
      }
    }
  }

  // Helper methods
  private extractDomain(url: string): string {
    try {
      return new URL(url).hostname.replace('www.', '')
    } catch {
      return ''
    }
  }

  private getCountryCode(location: string): string {
    const countryMap: Record<string, string> = {
      'United States': 'us',
      'United Kingdom': 'gb',
      'Canada': 'ca',
      'Australia': 'au',
      'Germany': 'de',
      'France': 'fr',
      'Spain': 'es',
      'Italy': 'it',
      'Netherlands': 'nl',
      'Brazil': 'br',
      'India': 'in',
      'Japan': 'jp',
      // Middle East & Africa
      'United Arab Emirates': 'ae',
      'UAE': 'ae',
      'Dubai': 'ae',
      'Abu Dhabi': 'ae',
      'Saudi Arabia': 'sa',
      'Kuwait': 'kw',
      'Qatar': 'qa',
      'Bahrain': 'bh',
      'Oman': 'om',
      'Jordan': 'jo',
      'Lebanon': 'lb',
      'Egypt': 'eg',
      'Morocco': 'ma',
      'Tunisia': 'tn',
      'Algeria': 'dz',
      'South Africa': 'za',
      'Nigeria': 'ng',
      'Kenya': 'ke',
      'Ghana': 'gh',
      // Asia Pacific
      'China': 'cn',
      'Singapore': 'sg',
      'Malaysia': 'my',
      'Thailand': 'th',
      'Indonesia': 'id',
      'Philippines': 'ph',
      'Vietnam': 'vn',
      'South Korea': 'kr',
      'Taiwan': 'tw',
      'Hong Kong': 'hk',
      'New Zealand': 'nz',
      // Europe
      'Switzerland': 'ch',
      'Austria': 'at',
      'Belgium': 'be',
      'Sweden': 'se',
      'Norway': 'no',
      'Denmark': 'dk',
      'Finland': 'fi',
      'Poland': 'pl',
      'Czech Republic': 'cz',
      'Hungary': 'hu',
      'Romania': 'ro',
      'Bulgaria': 'bg',
      'Greece': 'gr',
      'Portugal': 'pt',
      // Americas
      'Mexico': 'mx',
      'Argentina': 'ar',
      'Chile': 'cl',
      'Colombia': 'co',
      'Peru': 'pe',
      'Venezuela': 've',
      'Ecuador': 'ec',
      'Uruguay': 'uy',
      'Bolivia': 'bo',
      'Paraguay': 'py'
    }
    return countryMap[location] || 'us'
  }

  private getGoogleDomain(countryCode: string): string {
    const domainMap: Record<string, string> = {
      'us': 'google.com',
      'gb': 'google.co.uk',
      'ca': 'google.ca',
      'au': 'google.com.au',
      'de': 'google.de',
      'fr': 'google.fr',
      'es': 'google.es',
      'it': 'google.it',
      'nl': 'google.nl',
      'br': 'google.com.br',
      'in': 'google.co.in',
      'jp': 'google.co.jp',
      // Middle East & Africa
      'ae': 'google.ae',
      'sa': 'google.com.sa',
      'kw': 'google.com.kw',
      'qa': 'google.com.qa',
      'bh': 'google.com.bh',
      'om': 'google.com.om',
      'jo': 'google.jo',
      'lb': 'google.com.lb',
      'eg': 'google.com.eg',
      'ma': 'google.co.ma',
      'tn': 'google.tn',
      'dz': 'google.dz',
      'za': 'google.co.za',
      'ng': 'google.com.ng',
      'ke': 'google.co.ke',
      'gh': 'google.com.gh',
      // Asia Pacific
      'cn': 'google.cn',
      'sg': 'google.com.sg',
      'my': 'google.com.my',
      'th': 'google.co.th',
      'id': 'google.co.id',
      'ph': 'google.com.ph',
      'vn': 'google.com.vn',
      'kr': 'google.co.kr',
      'tw': 'google.com.tw',
      'hk': 'google.com.hk',
      'nz': 'google.co.nz',
      // Europe
      'ch': 'google.ch',
      'at': 'google.at',
      'be': 'google.be',
      'se': 'google.se',
      'no': 'google.no',
      'dk': 'google.dk',
      'fi': 'google.fi',
      'pl': 'google.pl',
      'cz': 'google.cz',
      'hu': 'google.hu',
      'ro': 'google.ro',
      'bg': 'google.bg',
      'gr': 'google.gr',
      'pt': 'google.pt',
      // Americas
      'mx': 'google.com.mx',
      'ar': 'google.com.ar',
      'cl': 'google.cl',
      'co': 'google.com.co',
      'pe': 'google.com.pe',
      've': 'google.co.ve',
      'ec': 'google.com.ec',
      'uy': 'google.com.uy',
      'bo': 'google.com.bo',
      'py': 'google.com.py'
    }
    return domainMap[countryCode] || 'google.com'
  }

  private estimateTraffic(position: number): number {
    const ctrRates = [0.316, 0.158, 0.101, 0.072, 0.054, 0.043, 0.034, 0.028, 0.023, 0.019]
    const baseTraffic = 10000 // Estimated monthly searches
    return Math.round(baseTraffic * (ctrRates[position - 1] || 0.01))
  }

  private estimateDomainAuthority(domain: string): number {
    // Simple domain authority estimation (in real implementation, use Moz API or similar)
    const domainAge = Math.random() * 10 + 1 // 1-11 years
    const baseScore = Math.min(90, domainAge * 8 + Math.random() * 20)
    return Math.round(baseScore)
  }

  private detectContentType(title: string, snippet: string): 'informational' | 'commercial' | 'transactional' | 'navigational' {
    const content = `${title} ${snippet}`.toLowerCase()
    
    if (/buy|purchase|order|shop|price|cost|deal|sale/.test(content)) return 'transactional'
    if (/best|top|review|compare|vs|versus/.test(content)) return 'commercial'
    if (/login|sign in|contact|about|official/.test(content)) return 'navigational'
    return 'informational'
  }

  private analyzeTitleOptimization(title: string, keyword: string): any {
    const words = title.toLowerCase().split(/\s+/)
    const keywordWords = keyword.toLowerCase().split(/\s+/)
    const keywordPosition = words.findIndex(word => keywordWords.includes(word)) + 1

    return {
      keywordPosition,
      length: title.length,
      hasNumbers: /\d/.test(title),
      hasPowerWords: /best|top|ultimate|complete|guide|how to|tips|secrets/.test(title.toLowerCase()),
      sentiment: this.analyzeSentiment(title)
    }
  }

  private analyzeSnippet(snippet: string, keyword: string): any {
    const keywordMentions = (snippet.toLowerCase().match(new RegExp(keyword.toLowerCase(), 'g')) || []).length
    
    return {
      keywordMentions,
      hasCallToAction: /learn more|read more|get started|contact|call|visit/.test(snippet.toLowerCase()),
      hasQuestions: /\?/.test(snippet),
      readabilityScore: this.calculateReadabilityScore(snippet)
    }
  }

  private analyzeTechnicalSEO(url: string): any {
    return {
      hasHttps: url.startsWith('https://'),
      loadSpeed: Math.random() > 0.7 ? 'fast' : Math.random() > 0.4 ? 'medium' : 'slow',
      mobileOptimized: Math.random() > 0.2, // 80% are mobile optimized
      schemaMarkup: Math.random() > 0.4 // 60% have schema markup
    }
  }

  private identifySpecificContentGaps(insight: CompetitorInsight, keyword: string): string[] {
    const gaps = []
    
    if (insight.snippetAnalysis.keywordMentions < 2) {
      gaps.push('Low keyword density in meta description')
    }
    
    if (!insight.snippetAnalysis.hasCallToAction) {
      gaps.push('Missing call-to-action in snippet')
    }
    
    if (!insight.technicalSEO.schemaMarkup) {
      gaps.push('No schema markup implementation')
    }
    
    if (insight.title.length < 30) {
      gaps.push('Title too short for optimal SEO')
    }
    
    return gaps
  }

  private identifySpecificOpportunities(insight: CompetitorInsight, keyword: string, serpAnalysis: SERPAnalysis): string[] {
    const opportunities = []
    
    if (insight.position > 3) {
      opportunities.push('Opportunity to target top 3 positions with better content')
    }
    
    if (insight.titleOptimization.keywordPosition > 5) {
      opportunities.push('Move primary keyword closer to beginning of title')
    }
    
    if (!insight.titleOptimization.hasNumbers && insight.contentType === 'informational') {
      opportunities.push('Add numbers to title for better click-through rate')
    }
    
    return opportunities
  }

  // Additional helper methods for pattern analysis
  private analyzeContentTypeDistribution(results: SERPResult[]): Record<string, number> {
    return results.reduce((acc, result) => {
      const type = this.detectContentType(result.title, result.snippet)
      acc[type] = (acc[type] || 0) + 1
      return acc
    }, {} as Record<string, number>)
  }

  private extractCommonPatterns(titles: string[]): string[] {
    const words = titles.flatMap(title => title.toLowerCase().split(/\s+/))
    const frequency = words.reduce((acc, word) => {
      acc[word] = (acc[word] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return Object.entries(frequency)
      .filter(([word, freq]) => freq >= 3 && word.length > 3)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([word]) => word)
  }

  private extractCommonWords(titles: string[]): string[] {
    return this.extractCommonPatterns(titles)
  }

  private analyzeSentimentDistribution(competitors: CompetitorInsight[]): Record<string, number> {
    return competitors.reduce((acc, c) => {
      acc[c.titleOptimization.sentiment] = (acc[c.titleOptimization.sentiment] || 0) + 1
      return acc
    }, {} as Record<string, number>)
  }

  private extractKeywordVariations(competitors: CompetitorInsight[], keyword: string): string[] {
    const variations = new Set<string>()
    const keywordWords = keyword.toLowerCase().split(/\s+/)
    
    competitors.forEach(c => {
      const titleWords = c.title.toLowerCase().split(/\s+/)
      const snippetWords = c.snippet.toLowerCase().split(/\s+/)
      const allWords = titleWords.concat(snippetWords)

      allWords.forEach(word => {
        if (keywordWords.some(kw => word.includes(kw) || kw.includes(word))) {
          variations.add(word)
        }
      })
    })
    
    return Array.from(variations).slice(0, 10)
  }

  private calculateAverageLoadSpeed(competitors: CompetitorInsight[]): string {
    const speeds = competitors.map(c => c.technicalSEO.loadSpeed)
    const fastCount = speeds.filter(s => s === 'fast').length
    const mediumCount = speeds.filter(s => s === 'medium').length
    
    if (fastCount > competitors.length / 2) return 'fast'
    if (mediumCount > competitors.length / 2) return 'medium'
    return 'slow'
  }

  private analyzeSentiment(text: string): 'positive' | 'neutral' | 'negative' {
    const positiveWords = /best|great|amazing|excellent|top|ultimate|perfect|powerful/i
    const negativeWords = /worst|bad|terrible|avoid|problems|issues|failed/i
    
    if (positiveWords.test(text)) return 'positive'
    if (negativeWords.test(text)) return 'negative'
    return 'neutral'
  }

  private calculateReadabilityScore(text: string): number {
    const words = text.split(/\s+/).length
    const sentences = text.split(/[.!?]+/).length
    const avgWordsPerSentence = words / sentences
    
    // Simplified Flesch Reading Ease approximation
    return Math.max(0, Math.min(100, 206.835 - (1.015 * avgWordsPerSentence)))
  }

  private generateMockSERPData(keyword: string, location: string): any {
    const countryCode = this.getCountryCode(location)
    const googleDomain = this.getGoogleDomain(countryCode)
    
    // Generate location-specific mock data
    const locationSuffix = this.getLocationSuffix(location)
    const localizedKeyword = `${keyword}${locationSuffix}`
    
    // Country-specific competitor domains
    const competitorDomains = this.getCountrySpecificDomains(countryCode)
    
    // Enhanced mock data with country-specific results
    const mockResults = competitorDomains.map((domain, i) => {
      const isLocalBusiness = i < 3 // First 3 results are local businesses
      const title = isLocalBusiness 
        ? `${keyword}${locationSuffix} | #${i + 1} ${this.getBusinessType(keyword)} - ${domain.name}`
        : `${keyword} Guide ${i + 1} | ${domain.name}`
      
      return {
        title,
        link: `https://${domain.domain}/${keyword.replace(/\s+/g, '-').toLowerCase()}`,
        snippet: this.generateLocationSpecificSnippet(keyword, location, isLocalBusiness),
        displayLink: domain.domain,
        date: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        sitelinks: isLocalBusiness ? [
          `Contact ${location}`,
          `Get Quote`,
          `Services`,
          `Reviews`
        ] : undefined
      }
    })
    
    return {
      searchInformation: {
        totalResults: this.getLocationBasedResults(countryCode),
        searchTime: '0.' + Math.floor(Math.random() * 90 + 10)
      },
      organic: mockResults,
      relatedSearches: this.generateLocationSpecificRelatedSearches(keyword, location),
      peopleAlsoAsk: this.generateLocationSpecificPAA(keyword, location),
      answerBox: this.generateLocationSpecificAnswerBox(keyword, location),
      localPack: this.generateLocalPack(keyword, location)
    }
  }

  private getLocationSuffix(location: string): string {
    const locationMap: Record<string, string> = {
      'Dubai': ' in Dubai',
      'Abu Dhabi': ' in Abu Dhabi',
      'UAE': ' in UAE',
      'United Arab Emirates': ' in UAE',
      'Saudi Arabia': ' in Saudi Arabia',
      'Kuwait': ' in Kuwait',
      'Qatar': ' in Qatar',
      'Bahrain': ' in Bahrain',
      'Oman': ' in Oman',
      'United States': ' in USA',
      'United Kingdom': ' in UK',
      'Canada': ' in Canada',
      'Australia': ' in Australia',
      'Germany': ' in Germany',
      'France': ' in France',
      'Spain': ' in Spain',
      'Italy': ' in Italy',
      'Netherlands': ' in Netherlands',
      'Brazil': ' in Brazil',
      'India': ' in India',
      'Japan': ' in Japan',
      'Singapore': ' in Singapore',
      'Malaysia': ' in Malaysia',
      'Thailand': ' in Thailand',
      'South Korea': ' in South Korea',
      'Mexico': ' in Mexico'
    }
    return locationMap[location] || ''
  }

  private getCountrySpecificDomains(countryCode: string): Array<{domain: string, name: string}> {
    const domainMap: Record<string, Array<{domain: string, name: string}>> = {
      'ae': [
        { domain: 'dubizzle.com', name: 'Dubizzle' },
        { domain: 'bayut.com', name: 'Bayut' },
        { domain: 'propertyfinder.ae', name: 'Property Finder' },
        { domain: 'gulfnews.com', name: 'Gulf News' },
        { domain: 'khaleejtimes.com', name: 'Khaleej Times' },
        { domain: 'emirates247.com', name: 'Emirates 24/7' },
        { domain: 'timeoutdubai.com', name: 'Time Out Dubai' },
        { domain: 'whatson.ae', name: 'What\'s On' },
        { domain: 'expatwoman.com', name: 'ExpatWoman' },
        { domain: 'dubaicityguide.com', name: 'Dubai City Guide' }
      ],
      'sa': [
        { domain: 'haraj.com.sa', name: 'Haraj' },
        { domain: 'opensooq.com', name: 'OpenSooq' },
        { domain: 'saudigazette.com.sa', name: 'Saudi Gazette' },
        { domain: 'arabnews.com', name: 'Arab News' },
        { domain: 'riyadhcity.com', name: 'Riyadh City' },
        { domain: 'saudia.com', name: 'Saudia' },
        { domain: 'sabq.org', name: 'Sabq' },
        { domain: 'makkah.org.sa', name: 'Makkah' },
        { domain: 'okaz.com.sa', name: 'Okaz' },
        { domain: 'al-jazirah.com', name: 'Al-Jazirah' }
      ],
      'us': [
        { domain: 'yelp.com', name: 'Yelp' },
        { domain: 'angieslist.com', name: 'Angie\'s List' },
        { domain: 'homeadvisor.com', name: 'HomeAdvisor' },
        { domain: 'thumbtack.com', name: 'Thumbtack' },
        { domain: 'bbb.org', name: 'Better Business Bureau' },
        { domain: 'yellowpages.com', name: 'Yellow Pages' },
        { domain: 'google.com/maps', name: 'Google Maps' },
        { domain: 'facebook.com', name: 'Facebook' },
        { domain: 'nextdoor.com', name: 'Nextdoor' },
        { domain: 'craigslist.org', name: 'Craigslist' }
      ],
      'gb': [
        { domain: 'yell.com', name: 'Yell' },
        { domain: 'checkatrade.com', name: 'Checkatrade' },
        { domain: 'trustedtrader.com', name: 'Trusted Trader' },
        { domain: 'mybuilder.com', name: 'MyBuilder' },
        { domain: 'gumtree.com', name: 'Gumtree' },
        { domain: 'bbc.co.uk', name: 'BBC' },
        { domain: 'gov.uk', name: 'GOV.UK' },
        { domain: 'rightmove.co.uk', name: 'Rightmove' },
        { domain: 'zoopla.co.uk', name: 'Zoopla' },
        { domain: 'which.co.uk', name: 'Which?' }
      ]
    }
    return domainMap[countryCode] || domainMap['us'] || []
  }

  private getBusinessType(keyword: string): string {
    const keywordLower = keyword.toLowerCase()
    if (keywordLower.includes('mover') || keywordLower.includes('moving')) return 'Moving Company'
    if (keywordLower.includes('plumber') || keywordLower.includes('plumbing')) return 'Plumbing Service'
    if (keywordLower.includes('electrician') || keywordLower.includes('electrical')) return 'Electrical Service'
    if (keywordLower.includes('lawyer') || keywordLower.includes('attorney')) return 'Law Firm'
    if (keywordLower.includes('dentist') || keywordLower.includes('dental')) return 'Dental Clinic'
    if (keywordLower.includes('restaurant') || keywordLower.includes('food')) return 'Restaurant'
    if (keywordLower.includes('hotel') || keywordLower.includes('accommodation')) return 'Hotel'
    if (keywordLower.includes('cleaning') || keywordLower.includes('cleaner')) return 'Cleaning Service'
    if (keywordLower.includes('mechanic') || keywordLower.includes('auto')) return 'Auto Service'
    if (keywordLower.includes('doctor') || keywordLower.includes('medical')) return 'Medical Center'
    return 'Professional Service'
  }

  private generateLocationSpecificSnippet(keyword: string, location: string, isLocalBusiness: boolean): string {
    if (isLocalBusiness) {
      return `Professional ${keyword} services in ${location}. Licensed, insured, and highly rated. Get free quotes and book online. Available 24/7 for emergency services.`
    }
    return `Comprehensive ${keyword} guide for ${location}. Expert advice, tips, and recommendations. Everything you need to know about ${keyword} in ${location}.`
  }

  private getLocationBasedResults(countryCode: string): number {
    const baseResults: Record<string, number> = {
      'ae': 850000,
      'sa': 1200000,
      'us': 15000000,
      'gb': 8500000,
      'ca': 4200000,
      'au': 3100000,
      'de': 6800000,
      'fr': 5900000,
      'es': 4700000,
      'it': 4200000,
      'br': 7300000,
      'in': 12000000,
      'jp': 5800000,
      'cn': 9200000,
      'sg': 650000,
      'my': 1100000,
      'th': 1800000,
      'kr': 2400000,
      'mx': 3600000
    }
    return baseResults[countryCode] || 1500000
  }

  private generateLocationSpecificRelatedSearches(keyword: string, location: string): Array<{query: string}> {
    const locationSuffix = this.getLocationSuffix(location)
    return [
      { query: `best ${keyword}${locationSuffix}` },
      { query: `${keyword} cost${locationSuffix}` },
      { query: `${keyword} reviews${locationSuffix}` },
      { query: `${keyword} near me` },
      { query: `cheap ${keyword}${locationSuffix}` },
      { query: `professional ${keyword}${locationSuffix}` },
      { query: `${keyword} companies${locationSuffix}` },
      { query: `${keyword} services${locationSuffix}` }
    ]
  }

  private generateLocationSpecificPAA(keyword: string, location: string): Array<{question: string}> {
    const locationSuffix = this.getLocationSuffix(location)
    return [
      { question: `What is the best ${keyword}${locationSuffix}?` },
      { question: `How much does ${keyword} cost${locationSuffix}?` },
      { question: `Where can I find ${keyword}${locationSuffix}?` },
      { question: `Is ${keyword} available${locationSuffix}?` },
      { question: `What are the requirements for ${keyword}${locationSuffix}?` },
      { question: `How to choose ${keyword}${locationSuffix}?` }
    ]
  }

  private generateLocationSpecificAnswerBox(keyword: string, location: string): any {
    const locationSuffix = this.getLocationSuffix(location)
    return {
      title: `${keyword}${locationSuffix} - Complete Guide`,
      snippet: `${keyword}${locationSuffix} involves professional services and solutions tailored to local requirements. Available options include licensed providers, competitive pricing, and comprehensive support throughout ${location}.`,
      link: `https://example-authority-site.com/${keyword.replace(/\s+/g, '-').toLowerCase()}`,
      displayLink: 'example-authority-site.com'
    }
  }

  private generateLocalPack(keyword: string, location: string): any[] {
    if (!keyword.toLowerCase().includes('mover') && !keyword.toLowerCase().includes('service') && !keyword.toLowerCase().includes('company')) {
      return []
    }
    
    return Array.from({ length: 3 }, (_, i) => ({
      position: i + 1,
      title: `${keyword} Pro ${i + 1}`,
      address: `${location} Business District`,
      phone: `+971-${Math.floor(Math.random() * 900 + 100)}-${Math.floor(Math.random() * 9000 + 1000)}`,
      rating: (4.0 + Math.random() * 1.0).toFixed(1),
      reviews: Math.floor(Math.random() * 500 + 50),
      website: `https://${keyword.replace(/\s+/g, '').toLowerCase()}pro${i + 1}.com`,
      hours: 'Open 24 hours'
    }))
  }
}