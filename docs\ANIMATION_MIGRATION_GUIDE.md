# Animation Migration Guide

This guide provides step-by-step instructions for migrating from the existing Tailwind CSS animations to the new enterprise animation system.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Migration Checklist](#migration-checklist)
3. [Component Migration](#component-migration)
4. [Testing Migration](#testing-migration)
5. [Common Patterns](#common-patterns)
6. [Troubleshooting](#troubleshooting)

## Quick Start

### 1. Install Dependencies

The animation system is built into the codebase, no additional dependencies required.

### 2. Import Animation Components

```typescript
// Old approach
<div className="animate-fade-in">Content</div>

// New approach
import { AnimationWrapper, AnimationType } from '@/components/animations';

<AnimationWrapper animation={AnimationType.FADE_IN}>
  <div>Content</div>
</AnimationWrapper>
```

### 3. Update Global Styles

Add to your global CSS:

```css
/* Ensure smooth animations */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Animation performance optimizations */
.will-change-auto {
  will-change: auto;
}
```

## Migration Checklist

- [ ] Audit existing animations using the migration tool
- [ ] Update component imports
- [ ] Replace Tailwind animation classes
- [ ] Add accessibility attributes
- [ ] Test with reduced motion preference
- [ ] Verify performance metrics
- [ ] Update documentation

## Component Migration

### Basic Animation Migration

#### Before (Tailwind)
```tsx
const Card = () => (
  <div className="animate-fade-in p-4 bg-white rounded-lg shadow">
    <h2 className="text-xl font-bold">Title</h2>
    <p>Content</p>
  </div>
);
```

#### After (Enterprise System)
```tsx
import { AnimationWrapper, AnimationType } from '@/components/animations';

const Card = () => (
  <AnimationWrapper animation={AnimationType.FADE_IN}>
    <div className="p-4 bg-white rounded-lg shadow">
      <h2 className="text-xl font-bold">Title</h2>
      <p>Content</p>
    </div>
  </AnimationWrapper>
);
```

### Scroll-Triggered Animation

#### Before
```tsx
const Section = () => (
  <section className="animate-slide-in opacity-0">
    <h2>Section Title</h2>
    <p>Content that appears on scroll</p>
  </section>
);
```

#### After
```tsx
import { ScrollReveal, AnimationType } from '@/components/animations';

const Section = () => (
  <ScrollReveal animation={AnimationType.SLIDE_UP}>
    <section>
      <h2>Section Title</h2>
      <p>Content that appears on scroll</p>
    </section>
  </ScrollReveal>
);
```

### Loading States

#### Before
```tsx
const Loading = () => (
  <div className="animate-spin h-8 w-8 border-2 border-primary-600 border-t-transparent rounded-full" />
);
```

#### After
```tsx
import { Spinner } from '@/components/animations';

const Loading = () => <Spinner size="md" />;
```

### Button with Interactions

#### Before
```tsx
const Button = ({ children, onClick }) => (
  <button 
    className="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors"
    onClick={onClick}
  >
    {children}
  </button>
);
```

#### After
```tsx
import { AnimatedButton } from '@/components/animations';

const Button = ({ children, onClick }) => (
  <AnimatedButton 
    variant="primary"
    onClick={onClick}
    ripple
    scaleOnPress
  >
    {children}
  </AnimatedButton>
);
```

## Testing Migration

### 1. Performance Testing

```typescript
import { measureAnimationPerformance, testAnimationBudget } from '@/lib/animations/testing';

describe('Animation Performance', () => {
  it('should maintain 60fps', async () => {
    const metrics = await measureAnimationPerformance('.animated-element', 1000);
    expect(metrics.averageFPS).toBeGreaterThanOrEqual(59);
  });

  it('should stay within budget', () => {
    const budget = testAnimationBudget();
    expect(budget.passed).toBe(true);
  });
});
```

### 2. Accessibility Testing

```typescript
import { testAccessibility } from '@/lib/animations/testing';
import { render } from '@testing-library/react';

test('animations are accessible', () => {
  const { container } = render(<AnimatedComponent />);
  const element = container.firstChild as HTMLElement;
  
  const result = testAccessibility(element);
  expect(result.passed).toBe(true);
});
```

### 3. Visual Regression Testing

```typescript
import { test, expect } from '@playwright/test';

test('animation snapshots', async ({ page }) => {
  await page.goto('/animations');
  
  // Disable animations for consistent snapshots
  await page.addStyleTag({
    content: '*, *::before, *::after { animation-duration: 0s !important; }'
  });
  
  await expect(page).toHaveScreenshot('animations.png');
});
```

## Common Patterns

### Staggered List Animation

```tsx
import { ScrollReveal } from '@/components/animations';

const List = ({ items }) => (
  <ScrollReveal stagger staggerDelay="NORMAL">
    {items.map((item, index) => (
      <div key={index} className="p-4 bg-white rounded shadow mb-2">
        {item}
      </div>
    ))}
  </ScrollReveal>
);
```

### Page Transitions

```tsx
// In _app.tsx
import { TransitionLayout } from '@/components/animations';

function MyApp({ Component, pageProps }) {
  return (
    <TransitionLayout>
      <Component {...pageProps} />
    </TransitionLayout>
  );
}
```

### Conditional Animations

```tsx
import { AnimationWrapper, AnimationType } from '@/components/animations';

const ConditionalAnimation = ({ shouldAnimate, children }) => {
  if (!shouldAnimate) return children;
  
  return (
    <AnimationWrapper animation={AnimationType.FADE_IN}>
      {children}
    </AnimationWrapper>
  );
};
```

### Custom Animation Sequences

```tsx
import { useAnimationTimeline } from '@/lib/animations';

const SequentialAnimation = () => {
  const { addToTimeline, play } = useAnimationTimeline();
  const ref1 = useRef<HTMLDivElement>(null);
  const ref2 = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (ref1.current && ref2.current) {
      addToTimeline(ref1.current, AnimationType.FADE_IN, 0);
      addToTimeline(ref2.current, AnimationType.SLIDE_UP, 200);
      play();
    }
  }, []);
  
  return (
    <>
      <div ref={ref1}>First element</div>
      <div ref={ref2}>Second element</div>
    </>
  );
};
```

## Troubleshooting

### Animation Not Playing

1. Check if `prefers-reduced-motion` is enabled
2. Verify element is in the DOM
3. Check console for errors
4. Ensure animation type is imported correctly

### Performance Issues

1. Use Performance Monitor:
```typescript
import { animationMonitor } from '@/lib/animations';

// Start monitoring
animationMonitor.start();

// Get report
const report = animationMonitor.getReport();
console.log(report);
```

2. Reduce concurrent animations
3. Use `will-change` sparingly
4. Optimize keyframe properties

### Accessibility Issues

1. Add ARIA labels:
```tsx
<AnimationWrapper 
  animation={AnimationType.SPINNER}
  essential
>
  <div role="status" aria-label="Loading">
    <Spinner />
  </div>
</AnimationWrapper>
```

2. Provide controls for long animations
3. Test with screen readers

### Migration Script

Run the migration audit:

```typescript
import { auditLegacyAnimations, generateMigrationReport } from '@/lib/animations/migration';

async function runMigration() {
  const audit = await auditLegacyAnimations();
  const report = generateMigrationReport(audit);
  console.log(report);
}
```

## Next Steps

1. Review the [Animation Guidelines](./ANIMATION_GUIDELINES.md)
2. Explore animation examples in Storybook
3. Join the #animations Slack channel for support
4. Submit feedback and suggestions

Remember: The goal is to create delightful, performant, and accessible animations that enhance the user experience without compromising functionality.