/**
 * Bulk Actions Component
 * Actions that can be performed on multiple selected content items
 */

'use client'

import React, { useState } from 'react'
import Card from '@/components/UI/Card'
import Button from '@/components/UI/Button'
import {
  TrashIcon,
  ArrowDownTrayIcon,
  DocumentDuplicateIcon,
  FolderIcon,
  ShareIcon,
  XMarkIcon,
  CheckIcon
} from '@heroicons/react/24/outline'

interface BulkActionsProps {
  selectedCount: number
  onDelete: () => void
  onExport: (format: 'json' | 'csv' | 'markdown') => void
  onClearSelection: () => void
}

export default function BulkActions({
  selectedCount,
  onDelete,
  onExport,
  onClearSelection
}: BulkActionsProps) {
  const [showExportMenu, setShowExportMenu] = useState(false)

  const handleExport = (format: 'json' | 'csv' | 'markdown') => {
    onExport(format)
    setShowExportMenu(false)
  }

  return (
    <Card className="p-4 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <CheckIcon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
              {selectedCount} item{selectedCount !== 1 ? 's' : ''} selected
            </span>
          </div>

          <div className="h-4 w-px bg-blue-200 dark:bg-blue-700" />

          <div className="flex items-center space-x-2">
            {/* Export Actions */}
            <div className="relative">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowExportMenu(!showExportMenu)}
                className="border-blue-300 text-blue-700 hover:bg-blue-100 dark:border-blue-600 dark:text-blue-300 dark:hover:bg-blue-800"
              >
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                Export
              </Button>

              {showExportMenu && (
                <div className="absolute top-full left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-10">
                  <div className="py-1">
                    <button
                      onClick={() => handleExport('json')}
                      className="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <div className="flex items-center">
                        <DocumentDuplicateIcon className="h-4 w-4 mr-2" />
                        Export as JSON
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 ml-6">
                        Structured data format
                      </div>
                    </button>
                    <button
                      onClick={() => handleExport('csv')}
                      className="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <div className="flex items-center">
                        <DocumentDuplicateIcon className="h-4 w-4 mr-2" />
                        Export as CSV
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 ml-6">
                        Spreadsheet format
                      </div>
                    </button>
                    <button
                      onClick={() => handleExport('markdown')}
                      className="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <div className="flex items-center">
                        <DocumentDuplicateIcon className="h-4 w-4 mr-2" />
                        Export as Markdown
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 ml-6">
                        Documentation format
                      </div>
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Move to Project */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // TODO: Implement move to project functionality
                console.log('Move to project')
              }}
              className="border-blue-300 text-blue-700 hover:bg-blue-100 dark:border-blue-600 dark:text-blue-300 dark:hover:bg-blue-800"
            >
              <FolderIcon className="h-4 w-4 mr-2" />
              Move to Project
            </Button>

            {/* Duplicate */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // TODO: Implement bulk duplicate functionality
                console.log('Duplicate selected items')
              }}
              className="border-blue-300 text-blue-700 hover:bg-blue-100 dark:border-blue-600 dark:text-blue-300 dark:hover:bg-blue-800"
            >
              <DocumentDuplicateIcon className="h-4 w-4 mr-2" />
              Duplicate
            </Button>

            {/* Share */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // TODO: Implement bulk share functionality
                console.log('Share selected items')
              }}
              className="border-blue-300 text-blue-700 hover:bg-blue-100 dark:border-blue-600 dark:text-blue-300 dark:hover:bg-blue-800"
            >
              <ShareIcon className="h-4 w-4 mr-2" />
              Share
            </Button>

            {/* Delete */}
            <Button
              variant="outline"
              size="sm"
              onClick={onDelete}
              className="border-red-300 text-red-700 hover:bg-red-100 dark:border-red-600 dark:text-red-300 dark:hover:bg-red-800"
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>

        <Button
          variant="ghost"
          size="sm"
          onClick={onClearSelection}
          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
        >
          <XMarkIcon className="h-4 w-4 mr-1" />
          Clear Selection
        </Button>
      </div>

      {/* Quick Actions Info */}
      <div className="mt-3 pt-3 border-t border-blue-200 dark:border-blue-700">
        <div className="flex items-center justify-between text-xs text-blue-700 dark:text-blue-300">
          <div className="flex items-center space-x-4">
            <span>💡 Tip: Use Shift+Click to select ranges</span>
            <span>⌘+A to select all visible items</span>
          </div>
          <div className="flex items-center space-x-2">
            <span>Keyboard shortcuts:</span>
            <kbd className="px-2 py-1 bg-blue-100 dark:bg-blue-800 rounded text-xs">Del</kbd>
            <span>delete</span>
            <kbd className="px-2 py-1 bg-blue-100 dark:bg-blue-800 rounded text-xs">⌘+E</kbd>
            <span>export</span>
          </div>
        </div>
      </div>
    </Card>
  )
}