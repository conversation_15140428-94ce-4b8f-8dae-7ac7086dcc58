import jwt from 'jsonwebtoken';
import { createClient } from '@supabase/supabase-js';
import rateLimit from 'express-rate-limit';

// Initialize Supabase client with service role (optional for development)
const supabase = (process.env.SUPABASE_URL && 
                  process.env.SUPABASE_SERVICE_ROLE_KEY && 
                  process.env.SUPABASE_URL !== 'https://demo.supabase.co' &&
                  process.env.SUPABASE_SERVICE_ROLE_KEY !== 'demo-service-role-key') 
  ? createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY)
  : null;

/**
 * Enterprise Authentication Middleware
 * Validates JWT tokens and enforces user session security
 */
export const authenticateUser = async (req, res, next) => {
  try {
    // Skip authentication if Supabase is not configured (development mode)
    if (!supabase) {
      console.log('⚠️  Authentication skipped - Supabase not configured');
      req.user = {
        id: 'dev-user',
        email: 'dev@localhost',
        subscription_tier: 'enterprise',
        usage_count: 0,
        usage_limit: 1000
      };
      return next();
    }

    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ 
        error: 'Authentication required',
        code: 'MISSING_TOKEN'
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify token with Supabase
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      return res.status(401).json({ 
        error: 'Invalid or expired token',
        code: 'INVALID_TOKEN'
      });
    }

    // Check if user exists in our database
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single();

    if (userError || !userData) {
      return res.status(401).json({ 
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    // Attach user data to request
    req.user = {
      id: user.id,
      email: user.email,
      subscription_tier: userData.subscription_tier,
      usage_count: userData.usage_count,
      usage_limit: userData.usage_limit
    };

    // Log authentication event
    await logUserActivity(user.id, 'authentication', 'user', null, {
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(500).json({ 
      error: 'Authentication service error',
      code: 'AUTH_SERVICE_ERROR'
    });
  }
};

/**
 * Role-based Authorization Middleware
 * Enforces subscription tier access controls
 */
export const requireSubscription = (requiredTier) => {
  const tierLevels = {
    'free': 1,
    'pro': 2,
    'enterprise': 3
  };

  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        code: 'AUTHENTICATION_REQUIRED'
      });
    }

    const userTierLevel = tierLevels[req.user.subscription_tier] || 0;
    const requiredTierLevel = tierLevels[requiredTier] || 0;

    if (userTierLevel < requiredTierLevel) {
      return res.status(403).json({ 
        error: 'Insufficient subscription tier',
        code: 'INSUFFICIENT_TIER',
        required: requiredTier,
        current: req.user.subscription_tier
      });
    }

    next();
  };
};

/**
 * Usage Limit Enforcement Middleware
 * Checks and enforces API usage limits based on subscription
 */
export const enforceUsageLimit = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        code: 'AUTHENTICATION_REQUIRED'
      });
    }

    // Check if user has exceeded usage limit
    if (req.user.usage_count >= req.user.usage_limit) {
      return res.status(429).json({ 
        error: 'Usage limit exceeded',
        code: 'USAGE_LIMIT_EXCEEDED',
        usage_count: req.user.usage_count,
        usage_limit: req.user.usage_limit
      });
    }

    next();
  } catch (error) {
    console.error('Usage limit check error:', error);
    return res.status(500).json({ 
      error: 'Usage limit service error',
      code: 'USAGE_LIMIT_SERVICE_ERROR'
    });
  }
};

/**
 * Advanced Rate Limiting Configuration
 * Protects against abuse and DDoS attacks
 */
export const createRateLimiter = (options = {}) => {
  const defaultOptions = {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: {
      error: 'Too many requests from this IP',
      code: 'RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // Skip rate limiting for health checks
      return req.path === '/api/health';
    },
    keyGenerator: (req) => {
      // Use user ID if authenticated, otherwise IP address
      return req.user?.id || req.ip;
    }
  };

  return rateLimit({
    ...defaultOptions,
    ...options
  });
};

/**
 * API Key Authentication Middleware
 * For external integrations and webhooks
 */
export const authenticateApiKey = async (req, res, next) => {
  try {
    // Skip API key authentication if Supabase is not configured
    if (!supabase) {
      console.log('⚠️  API key authentication skipped - Supabase not configured');
      req.user = {
        id: 'dev-user',
        email: 'dev@localhost',
        subscription_tier: 'enterprise',
        usage_count: 0,
        usage_limit: 1000
      };
      return next();
    }

    const apiKey = req.headers['x-api-key'];
    
    if (!apiKey) {
      return res.status(401).json({ 
        error: 'API key required',
        code: 'MISSING_API_KEY'
      });
    }

    // Validate API key against database
    const { data: keyData, error } = await supabase
      .from('api_keys')
      .select('user_id, is_active')
      .eq('api_key_encrypted', apiKey) // In production, use proper encryption
      .eq('is_active', true)
      .single();

    if (error || !keyData) {
      return res.status(401).json({ 
        error: 'Invalid API key',
        code: 'INVALID_API_KEY'
      });
    }

    // Get user data
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', keyData.user_id)
      .single();

    if (userError || !userData) {
      return res.status(401).json({ 
        error: 'API key user not found',
        code: 'API_KEY_USER_NOT_FOUND'
      });
    }

    // Attach user data to request
    req.user = {
      id: userData.id,
      email: userData.email,
      subscription_tier: userData.subscription_tier,
      usage_count: userData.usage_count,
      usage_limit: userData.usage_limit
    };

    // Update last used timestamp
    await supabase
      .from('api_keys')
      .update({ last_used_at: new Date().toISOString() })
      .eq('api_key_encrypted', apiKey);

    next();
  } catch (error) {
    console.error('API key authentication error:', error);
    return res.status(500).json({ 
      error: 'API key service error',
      code: 'API_KEY_SERVICE_ERROR'
    });
  }
};

/**
 * Session Security Middleware
 * Validates session integrity and handles timeouts
 */
export const validateSession = async (req, res, next) => {
  try {
    if (!req.user) {
      return next();
    }

    // Check for suspicious activity patterns
    const suspiciousPatterns = await checkSuspiciousActivity(req.user.id, {
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    if (suspiciousPatterns.isBlocked) {
      return res.status(403).json({ 
        error: 'Account temporarily blocked due to suspicious activity',
        code: 'ACCOUNT_BLOCKED',
        reason: suspiciousPatterns.reason
      });
    }

    next();
  } catch (error) {
    console.error('Session validation error:', error);
    next(); // Continue even if session validation fails
  }
};

/**
 * Log user activity for security monitoring
 */
const logUserActivity = async (userId, action, resourceType, resourceId, metadata) => {
  try {
    if (!supabase) {
      console.log(`📊 Activity Log (dev mode): ${action} by ${userId}`);
      return;
    }
    
    await supabase
      .from('usage_tracking')
      .insert({
        user_id: userId,
        action,
        resource_type: resourceType,
        resource_id: resourceId,
        metadata,
        ip_address: metadata.ip_address,
        user_agent: metadata.user_agent
      });
  } catch (error) {
    console.error('Activity logging error:', error);
  }
};

/**
 * Check for suspicious activity patterns
 */
const checkSuspiciousActivity = async (userId, metadata) => {
  try {
    if (!supabase) {
      // Skip suspicious activity checks in development mode
      return { isBlocked: false };
    }
    
    // Get recent activity for this user
    const { data: recentActivity } = await supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', userId)
      .gte('created_at', new Date(Date.now() - 60 * 60 * 1000).toISOString()) // Last hour
      .order('created_at', { ascending: false });

    if (!recentActivity || recentActivity.length === 0) {
      return { isBlocked: false };
    }

    // Check for rapid requests from different IPs
    const uniqueIPs = new Set(recentActivity.map(activity => activity.ip_address));
    if (uniqueIPs.size > 5) {
      return { 
        isBlocked: true, 
        reason: 'Multiple IP addresses detected' 
      };
    }

    // Check for excessive request rate
    if (recentActivity.length > 200) {
      return { 
        isBlocked: true, 
        reason: 'Excessive request rate detected' 
      };
    }

    return { isBlocked: false };
  } catch (error) {
    console.error('Suspicious activity check error:', error);
    return { isBlocked: false };
  }
};

export default {
  authenticateUser,
  requireSubscription,
  enforceUsageLimit,
  createRateLimiter,
  authenticateApiKey,
  validateSession
};