/**
 * ProjectMetrics Component
 * Enterprise SEO SAAS - Comprehensive project analytics and performance tracking
 */

import { Project } from '@/types/project'
import { 
  ChartBarIcon, 
  TrendingUpIcon, 
  TrendingDownIcon,
  DocumentTextIcon,
  TagIcon,
  UsersIcon,
  EyeIcon,
  CursorArrowRaysIcon
} from '@heroicons/react/24/outline'

interface ProjectMetricsProps {
  project: Project
}

export default function ProjectMetrics({ project }: ProjectMetricsProps) {
  const { metrics } = project

  // Mock performance data (in real app, this would come from analytics API)
  const performanceData = {
    rankingChanges: Math.floor(Math.random() * 20) - 10, // -10 to +10
    trafficChange: Math.floor(Math.random() * 40) - 20, // -20% to +20%
    avgPosition: Math.floor(Math.random() * 50) + 1, // 1 to 50
    clickThroughRate: (Math.random() * 5 + 1).toFixed(1), // 1.0% to 6.0%
    topRankingKeywords: Math.floor(Math.random() * 10) + 1,
    avgSeoScore: metrics.averageSeoScore || 0
  }

  const formatTrend = (value: number, isPercentage = false) => {
    const isPositive = value > 0
    const TrendIcon = isPositive ? TrendingUpIcon : TrendingDownIcon
    const colorClass = isPositive ? 'text-green-600' : value < 0 ? 'text-red-600' : 'text-gray-600'
    
    return (
      <div className={`flex items-center gap-1 ${colorClass}`}>
        <TrendIcon className="h-4 w-4" />
        <span className="text-sm font-medium">
          {isPositive ? '+' : ''}{value}{isPercentage ? '%' : ''}
        </span>
      </div>
    )
  }

  const MetricCard = ({ 
    title, 
    value, 
    subtitle, 
    icon: Icon, 
    trend, 
    trendType = 'number' 
  }: {
    title: string
    value: string | number
    subtitle: string
    icon: any
    trend?: number
    trendType?: 'number' | 'percentage'
  }) => (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-50 rounded-lg">
            <Icon className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500">{title}</h3>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
          </div>
        </div>
        {trend !== undefined && formatTrend(trend, trendType === 'percentage')}
      </div>
      <p className="text-sm text-gray-600">{subtitle}</p>
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Main Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Total Content"
          value={metrics.totalContent}
          subtitle="Published content pieces"
          icon={DocumentTextIcon}
          trend={Math.floor(Math.random() * 10) - 5}
        />
        
        <MetricCard
          title="Keywords Tracked"
          value={metrics.keywordsTracked}
          subtitle="Active keyword monitoring"
          icon={TagIcon}
          trend={Math.floor(Math.random() * 8) - 4}
        />
        
        <MetricCard
          title="Competitors Analyzed"
          value={metrics.competitorsAnalyzed}
          subtitle="Active competitor tracking"
          icon={UsersIcon}
        />
        
        <MetricCard
          title="Average SEO Score"
          value={`${performanceData.avgSeoScore.toFixed(1)}/10`}
          subtitle="Content optimization score"
          icon={ChartBarIcon}
          trend={Math.floor(Math.random() * 3) - 1}
        />
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Search Performance */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
            <ChartBarIcon className="h-5 w-5 text-blue-600" />
            Search Performance
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between py-3 border-b border-gray-100">
              <div>
                <p className="text-sm font-medium text-gray-900">Average Position</p>
                <p className="text-xs text-gray-500">SERP ranking position</p>
              </div>
              <div className="text-right">
                <p className="text-lg font-semibold text-gray-900">#{performanceData.avgPosition}</p>
                {formatTrend(performanceData.rankingChanges)}
              </div>
            </div>
            
            <div className="flex items-center justify-between py-3 border-b border-gray-100">
              <div>
                <p className="text-sm font-medium text-gray-900">Top 10 Rankings</p>
                <p className="text-xs text-gray-500">Keywords in top 10</p>
              </div>
              <div className="text-right">
                <p className="text-lg font-semibold text-gray-900">{performanceData.topRankingKeywords}</p>
                <p className="text-xs text-gray-500">out of {metrics.keywordsTracked}</p>
              </div>
            </div>
            
            <div className="flex items-center justify-between py-3">
              <div>
                <p className="text-sm font-medium text-gray-900">Click-Through Rate</p>
                <p className="text-xs text-gray-500">Average CTR</p>
              </div>
              <div className="text-right">
                <p className="text-lg font-semibold text-gray-900">{performanceData.clickThroughRate}%</p>
                {formatTrend(Math.floor(Math.random() * 2) - 1, true)}
              </div>
            </div>
          </div>
        </div>

        {/* Content Performance */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
            <DocumentTextIcon className="h-5 w-5 text-blue-600" />
            Content Analytics
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between py-3 border-b border-gray-100">
              <div>
                <p className="text-sm font-medium text-gray-900">Total Words</p>
                <p className="text-xs text-gray-500">All published content</p>
              </div>
              <div className="text-right">
                <p className="text-lg font-semibold text-gray-900">{metrics.totalWords.toLocaleString()}</p>
                {formatTrend(Math.floor(Math.random() * 1000) - 500)}
              </div>
            </div>
            
            <div className="flex items-center justify-between py-3 border-b border-gray-100">
              <div>
                <p className="text-sm font-medium text-gray-900">Average Length</p>
                <p className="text-xs text-gray-500">Words per content</p>
              </div>
              <div className="text-right">
                <p className="text-lg font-semibold text-gray-900">{metrics.averageWordCount}</p>
                <p className="text-xs text-gray-500">words</p>
              </div>
            </div>
            
            <div className="flex items-center justify-between py-3">
              <div>
                <p className="text-sm font-medium text-gray-900">Content Types</p>
                <p className="text-xs text-gray-500">Active formats</p>
              </div>
              <div className="text-right">
                <p className="text-lg font-semibold text-gray-900">
                  {Object.values(metrics.contentByType).filter(count => count > 0).length}
                </p>
                <p className="text-xs text-gray-500">formats</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content Type Breakdown */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Content Distribution</h3>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {Object.entries(metrics.contentByType).map(([type, count]) => {
            const typeLabels: Record<string, string> = {
              blog_post: 'Blog Posts',
              product_description: 'Products',
              landing_page: 'Landing Pages',
              meta_description: 'Meta Descriptions',
              social_media: 'Social Media',
              email_marketing: 'Email Marketing'
            }
            
            return (
              <div key={type} className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-gray-900">{count}</div>
                <div className="text-xs text-gray-500 mt-1">{typeLabels[type]}</div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Performance Insights */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
          <EyeIcon className="h-5 w-5 text-blue-600" />
          Performance Insights
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">Content Opportunities</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• {Math.floor(Math.random() * 20) + 5} keywords need content optimization</li>
              <li>• {Math.floor(Math.random() * 10) + 2} competitors have new content</li>
              <li>• {Math.floor(Math.random() * 15) + 3} ranking improvements possible</li>
            </ul>
          </div>
          
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">Next Actions</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Update {Math.floor(Math.random() * 5) + 1} underperforming articles</li>
              <li>• Create content for {Math.floor(Math.random() * 8) + 2} new keywords</li>
              <li>• Analyze {Math.floor(Math.random() * 3) + 1} new competitors</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}