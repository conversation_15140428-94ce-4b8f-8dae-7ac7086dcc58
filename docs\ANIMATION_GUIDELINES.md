# Enterprise Animation Guidelines for SEO SAAS Platform

## Executive Summary

This document establishes comprehensive animation standards for the SEO SAAS platform, ensuring performance, accessibility, and consistency across all user interfaces. These guidelines build upon the existing Tailwind CSS animation system while introducing enterprise-grade features for scalability and maintainability.

## Table of Contents

1. [Animation Philosophy](#animation-philosophy)
2. [Performance Standards](#performance-standards)
3. [Accessibility Compliance](#accessibility-compliance)
4. [Animation Taxonomy](#animation-taxonomy)
5. [Implementation Standards](#implementation-standards)
6. [Testing Requirements](#testing-requirements)
7. [Migration Strategy](#migration-strategy)
8. [Code Examples](#code-examples)
9. [Best Practices](#best-practices)
10. [Troubleshooting](#troubleshooting)

## Animation Philosophy

### Core Principles

1. **Purpose-Driven**: Every animation must have a clear purpose
2. **Performance-First**: Maintain 60fps across all devices
3. **Accessibility-By-Default**: Respect user preferences and WCAG 2.1 standards
4. **Consistency**: Uniform animation behavior across the platform
5. **Subtlety**: Enhance, don't distract from the user experience

### Animation Value Proposition

- **Guide Attention**: Direct users to important UI changes
- **Show Relationships**: Demonstrate connections between elements
- **Provide Feedback**: Confirm user actions immediately
- **Create Delight**: Add personality without compromising functionality

## Performance Standards

### Hardware Acceleration

```css
/* Always use GPU-accelerated properties */
.performant-animation {
  /* DO: Use transform and opacity */
  transform: translateX(100px);
  opacity: 0.8;
  
  /* DON'T: Avoid layout-triggering properties */
  /* left: 100px; */
  /* width: 200px; */
}
```

### Performance Metrics

| Metric | Target | Maximum |
|--------|---------|---------|
| Frame Rate | 60 FPS | 60 FPS |
| Paint Time | < 10ms | < 16ms |
| Composite Time | < 2ms | < 4ms |
| Total Frame Time | < 12ms | < 16.67ms |

### will-change Property Usage

```typescript
// utils/animation-performance.ts
export const optimizeAnimation = (element: HTMLElement, property: string) => {
  // Set will-change before animation
  element.style.willChange = property;
  
  // Remove after animation completes
  element.addEventListener('transitionend', () => {
    element.style.willChange = 'auto';
  }, { once: true });
};
```

### Animation Budget

```typescript
// config/animation-budget.ts
export const ANIMATION_BUDGET = {
  // Maximum concurrent animations
  MAX_CONCURRENT: 3,
  
  // Maximum animation duration
  MAX_DURATION: 1000, // 1 second
  
  // Maximum elements animated simultaneously
  MAX_ELEMENTS: 20,
  
  // Performance thresholds
  FRAME_BUDGET: 16.67, // 60fps
  INTERACTION_BUDGET: 100, // 100ms for user feedback
};
```

## Accessibility Compliance

### WCAG 2.1 Requirements

#### Level A Compliance
- Provide pause, stop, or hide controls for animations > 5 seconds
- Ensure animations don't flash more than 3 times per second

#### Level AA Compliance
- Respect prefers-reduced-motion settings
- Provide alternative ways to access animated content

### Implementation

```typescript
// hooks/useMotionPreference.ts
import { useEffect, useState } from 'react';

export const useMotionPreference = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
};
```

### Reduced Motion Styles

```css
/* styles/motion-safe.css */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  /* Preserve essential animations */
  .motion-essential {
    animation-duration: 200ms !important;
    transition-duration: 200ms !important;
  }
}
```

### Focus Management

```typescript
// utils/focus-management.ts
export const animateWithFocusPreservation = async (
  element: HTMLElement,
  animation: Animation
) => {
  const activeElement = document.activeElement as HTMLElement;
  
  await animation.finished;
  
  // Restore focus if it was lost during animation
  if (activeElement && !document.activeElement) {
    activeElement.focus();
  }
};
```

## Animation Taxonomy

### Duration Scale

```typescript
// config/animation-tokens.ts
export const ANIMATION_DURATION = {
  instant: 0,        // No animation
  fast: 150,         // Micro-interactions
  normal: 250,       // Standard transitions
  slow: 350,         // Complex animations
  slower: 500,       // Page transitions
  slowest: 1000      // Special effects
} as const;
```

### Easing Functions

```typescript
export const ANIMATION_EASING = {
  // Natural easing (default)
  ease: 'cubic-bezier(0.4, 0, 0.2, 1)',
  
  // Acceleration curves
  easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
  easeInQuad: 'cubic-bezier(0.55, 0.085, 0.68, 0.53)',
  easeInCubic: 'cubic-bezier(0.55, 0.055, 0.675, 0.19)',
  easeInQuart: 'cubic-bezier(0.895, 0.03, 0.685, 0.22)',
  
  // Deceleration curves
  easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
  easeOutQuad: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  easeOutCubic: 'cubic-bezier(0.215, 0.61, 0.355, 1)',
  easeOutQuart: 'cubic-bezier(0.165, 0.84, 0.44, 1)',
  
  // Acceleration-deceleration curves
  easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
  easeInOutQuad: 'cubic-bezier(0.455, 0.03, 0.515, 0.955)',
  easeInOutCubic: 'cubic-bezier(0.645, 0.045, 0.355, 1)',
  
  // Special effects
  spring: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  elastic: 'cubic-bezier(0.175, 0.885, 0.32, 2.0)'
} as const;
```

### Animation Categories

#### 1. Entrance Animations
```typescript
export const ENTRANCE_ANIMATIONS = {
  fadeIn: {
    from: { opacity: 0 },
    to: { opacity: 1 }
  },
  slideUp: {
    from: { transform: 'translateY(20px)', opacity: 0 },
    to: { transform: 'translateY(0)', opacity: 1 }
  },
  slideDown: {
    from: { transform: 'translateY(-20px)', opacity: 0 },
    to: { transform: 'translateY(0)', opacity: 1 }
  },
  slideLeft: {
    from: { transform: 'translateX(20px)', opacity: 0 },
    to: { transform: 'translateX(0)', opacity: 1 }
  },
  slideRight: {
    from: { transform: 'translateX(-20px)', opacity: 0 },
    to: { transform: 'translateX(0)', opacity: 1 }
  },
  scaleUp: {
    from: { transform: 'scale(0.9)', opacity: 0 },
    to: { transform: 'scale(1)', opacity: 1 }
  },
  rotateIn: {
    from: { transform: 'rotate(-10deg) scale(0.9)', opacity: 0 },
    to: { transform: 'rotate(0) scale(1)', opacity: 1 }
  }
};
```

#### 2. Exit Animations
```typescript
export const EXIT_ANIMATIONS = {
  fadeOut: {
    from: { opacity: 1 },
    to: { opacity: 0 }
  },
  slideUpOut: {
    from: { transform: 'translateY(0)', opacity: 1 },
    to: { transform: 'translateY(-20px)', opacity: 0 }
  },
  slideDownOut: {
    from: { transform: 'translateY(0)', opacity: 1 },
    to: { transform: 'translateY(20px)', opacity: 0 }
  },
  scaleDown: {
    from: { transform: 'scale(1)', opacity: 1 },
    to: { transform: 'scale(0.9)', opacity: 0 }
  },
  collapseHeight: {
    from: { height: 'auto', opacity: 1 },
    to: { height: '0', opacity: 0 }
  }
};
```

#### 3. Attention Animations
```typescript
export const ATTENTION_ANIMATIONS = {
  pulse: {
    keyframes: [
      { transform: 'scale(1)' },
      { transform: 'scale(1.05)' },
      { transform: 'scale(1)' }
    ],
    options: { duration: 1000, iterations: Infinity }
  },
  shake: {
    keyframes: [
      { transform: 'translateX(0)' },
      { transform: 'translateX(-10px)' },
      { transform: 'translateX(10px)' },
      { transform: 'translateX(-10px)' },
      { transform: 'translateX(10px)' },
      { transform: 'translateX(0)' }
    ],
    options: { duration: 500 }
  },
  bounce: {
    keyframes: [
      { transform: 'translateY(0)' },
      { transform: 'translateY(-20px)' },
      { transform: 'translateY(0)' }
    ],
    options: { duration: 600, easing: ANIMATION_EASING.bounce }
  }
};
```

#### 4. Loading Animations
```typescript
export const LOADING_ANIMATIONS = {
  spinner: {
    keyframes: [
      { transform: 'rotate(0deg)' },
      { transform: 'rotate(360deg)' }
    ],
    options: { duration: 1000, iterations: Infinity, easing: 'linear' }
  },
  skeleton: {
    keyframes: [
      { backgroundPosition: '-200% 0' },
      { backgroundPosition: '200% 0' }
    ],
    options: { duration: 1500, iterations: Infinity, easing: 'linear' }
  },
  dots: {
    keyframes: [
      { opacity: 0.3 },
      { opacity: 1 },
      { opacity: 0.3 }
    ],
    options: { duration: 1400, iterations: Infinity }
  }
};
```

## Implementation Standards

### Component Animation Wrapper

```typescript
// components/AnimationWrapper.tsx
import React, { useRef, useEffect } from 'react';
import { useMotionPreference } from '@/hooks/useMotionPreference';
import { optimizeAnimation } from '@/utils/animation-performance';

interface AnimationWrapperProps {
  children: React.ReactNode;
  animation: keyof typeof ENTRANCE_ANIMATIONS;
  duration?: number;
  delay?: number;
  easing?: keyof typeof ANIMATION_EASING;
  onComplete?: () => void;
  triggerOnScroll?: boolean;
  className?: string;
}

export const AnimationWrapper: React.FC<AnimationWrapperProps> = ({
  children,
  animation,
  duration = ANIMATION_DURATION.normal,
  delay = 0,
  easing = 'ease',
  onComplete,
  triggerOnScroll = false,
  className = ''
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const prefersReducedMotion = useMotionPreference();
  
  useEffect(() => {
    if (!ref.current || prefersReducedMotion) return;
    
    const element = ref.current;
    const animationConfig = ENTRANCE_ANIMATIONS[animation];
    
    // Set initial state
    Object.assign(element.style, animationConfig.from);
    
    const animate = () => {
      optimizeAnimation(element, 'transform, opacity');
      
      const animation = element.animate(
        [animationConfig.from, animationConfig.to],
        {
          duration: prefersReducedMotion ? 0 : duration,
          delay,
          easing: ANIMATION_EASING[easing],
          fill: 'forwards'
        }
      );
      
      animation.onfinish = () => {
        if (onComplete) onComplete();
      };
    };
    
    if (triggerOnScroll) {
      const observer = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting) {
            animate();
            observer.disconnect();
          }
        },
        { threshold: 0.1 }
      );
      
      observer.observe(element);
      return () => observer.disconnect();
    } else {
      animate();
    }
  }, [animation, duration, delay, easing, prefersReducedMotion, triggerOnScroll]);
  
  return (
    <div ref={ref} className={className}>
      {children}
    </div>
  );
};
```

### Animation Hooks

```typescript
// hooks/useAnimation.ts
import { useRef, useCallback } from 'react';
import { useMotionPreference } from './useMotionPreference';

export const useAnimation = () => {
  const prefersReducedMotion = useMotionPreference();
  const animationRef = useRef<Animation | null>(null);
  
  const animate = useCallback(
    (
      element: HTMLElement,
      keyframes: Keyframe[],
      options: KeyframeAnimationOptions = {}
    ) => {
      if (prefersReducedMotion && !options.essential) {
        // Skip non-essential animations
        return Promise.resolve();
      }
      
      // Cancel any existing animation
      if (animationRef.current) {
        animationRef.current.cancel();
      }
      
      // Apply performance optimizations
      optimizeAnimation(element, 'transform, opacity');
      
      // Create and start animation
      animationRef.current = element.animate(keyframes, {
        ...options,
        duration: prefersReducedMotion ? 0 : options.duration
      });
      
      return animationRef.current.finished;
    },
    [prefersReducedMotion]
  );
  
  const cancelAnimation = useCallback(() => {
    if (animationRef.current) {
      animationRef.current.cancel();
      animationRef.current = null;
    }
  }, []);
  
  return { animate, cancelAnimation };
};
```

### Orchestrated Animations

```typescript
// utils/animation-orchestrator.ts
export class AnimationOrchestrator {
  private queue: Array<() => Promise<void>> = [];
  private running = false;
  
  // Sequence animations one after another
  sequence(animations: Array<() => Promise<void>>) {
    this.queue.push(...animations);
    this.run();
  }
  
  // Run animations in parallel
  parallel(animations: Array<() => Promise<void>>) {
    const parallelAnimation = () => Promise.all(animations.map(fn => fn()));
    this.queue.push(parallelAnimation);
    this.run();
  }
  
  // Stagger animations with delay
  stagger(
    animations: Array<() => Promise<void>>,
    delay: number = 100
  ) {
    const staggeredAnimation = async () => {
      for (let i = 0; i < animations.length; i++) {
        setTimeout(() => animations[i](), i * delay);
      }
      // Wait for all to complete
      await new Promise(resolve => 
        setTimeout(resolve, animations.length * delay + 500)
      );
    };
    this.queue.push(staggeredAnimation);
    this.run();
  }
  
  private async run() {
    if (this.running || this.queue.length === 0) return;
    
    this.running = true;
    while (this.queue.length > 0) {
      const animation = this.queue.shift();
      if (animation) await animation();
    }
    this.running = false;
  }
}
```

### Page Transition System

```typescript
// components/PageTransition.tsx
import { AnimatePresence, motion } from 'framer-motion';
import { useRouter } from 'next/router';
import { ReactNode } from 'react';

const pageVariants = {
  initial: {
    opacity: 0,
    x: -20
  },
  in: {
    opacity: 1,
    x: 0
  },
  out: {
    opacity: 0,
    x: 20
  }
};

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.3
};

interface PageTransitionProps {
  children: ReactNode;
}

export const PageTransition: React.FC<PageTransitionProps> = ({ children }) => {
  const router = useRouter();
  
  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={router.pathname}
        initial="initial"
        animate="in"
        exit="out"
        variants={pageVariants}
        transition={pageTransition}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
};
```

## Testing Requirements

### Performance Testing

```typescript
// tests/animation-performance.test.ts
import { measureAnimationPerformance } from '@/utils/testing';

describe('Animation Performance', () => {
  it('should maintain 60fps for standard animations', async () => {
    const metrics = await measureAnimationPerformance(
      '.fade-in-animation',
      1000 // duration
    );
    
    expect(metrics.averageFPS).toBeGreaterThanOrEqual(59);
    expect(metrics.droppedFrames).toBeLessThan(2);
    expect(metrics.paintTime).toBeLessThan(10);
  });
  
  it('should not exceed animation budget', async () => {
    const concurrentAnimations = document.querySelectorAll('[data-animating]');
    expect(concurrentAnimations.length).toBeLessThanOrEqual(
      ANIMATION_BUDGET.MAX_CONCURRENT
    );
  });
});
```

### Accessibility Testing

```typescript
// tests/animation-accessibility.test.ts
describe('Animation Accessibility', () => {
  it('should respect prefers-reduced-motion', () => {
    // Mock reduced motion preference
    window.matchMedia = jest.fn().mockImplementation(query => ({
      matches: query === '(prefers-reduced-motion: reduce)',
      addEventListener: jest.fn(),
      removeEventListener: jest.fn()
    }));
    
    const { container } = render(<AnimatedComponent />);
    const element = container.querySelector('.animated-element');
    
    // Check that animation duration is minimal
    const styles = getComputedStyle(element!);
    expect(styles.animationDuration).toBe('0.01ms');
  });
  
  it('should maintain focus during animations', async () => {
    const { getByRole } = render(<AnimatedForm />);
    const input = getByRole('textbox');
    
    input.focus();
    expect(document.activeElement).toBe(input);
    
    // Trigger animation
    fireEvent.submit(getByRole('form'));
    
    // Wait for animation
    await waitFor(() => {
      expect(document.activeElement).toBe(input);
    });
  });
});
```

### Visual Regression Testing

```typescript
// tests/animation-visual.test.ts
import { test, expect } from '@playwright/test';

test.describe('Animation Visual Tests', () => {
  test('entrance animations should match snapshots', async ({ page }) => {
    await page.goto('/animation-showcase');
    
    // Disable animations for consistent snapshots
    await page.addStyleTag({
      content: `
        *, *::before, *::after {
          animation-duration: 0s !important;
          transition-duration: 0s !important;
        }
      `
    });
    
    // Take snapshots at key animation states
    await expect(page).toHaveScreenshot('animations-initial.png');
    
    // Trigger animations
    await page.click('[data-trigger="animate"]');
    
    await expect(page).toHaveScreenshot('animations-complete.png');
  });
});
```

## Migration Strategy

### Phase 1: Audit Existing Animations (Week 1)

```typescript
// scripts/audit-animations.ts
export async function auditAnimations() {
  const results = {
    tailwindAnimations: [],
    customAnimations: [],
    performanceIssues: [],
    accessibilityIssues: []
  };
  
  // Scan for Tailwind animation classes
  const tailwindClasses = [
    'animate-fade-in',
    'animate-fade-out',
    'animate-slide-in',
    'animate-slide-out',
    'animate-accordion-down',
    'animate-accordion-up'
  ];
  
  // Find all usage
  for (const className of tailwindClasses) {
    const elements = document.querySelectorAll(`.${className}`);
    results.tailwindAnimations.push({
      className,
      count: elements.length,
      locations: Array.from(elements).map(el => getElementPath(el))
    });
  }
  
  // Check for performance issues
  const animations = document.getAnimations();
  for (const animation of animations) {
    const timing = animation.effect?.getTiming();
    if (timing && timing.duration > ANIMATION_BUDGET.MAX_DURATION) {
      results.performanceIssues.push({
        element: animation.effect?.target,
        duration: timing.duration,
        recommendation: 'Reduce duration to under 1000ms'
      });
    }
  }
  
  return results;
}
```

### Phase 2: Create Migration Components (Week 2)

```typescript
// components/legacy/AnimationAdapter.tsx
export const AnimationAdapter: React.FC<{
  legacyClass?: string;
  animation?: string;
  children: ReactNode;
}> = ({ legacyClass, animation, children }) => {
  // Map legacy Tailwind classes to new animation system
  const animationMap: Record<string, string> = {
    'animate-fade-in': 'fadeIn',
    'animate-fade-out': 'fadeOut',
    'animate-slide-in': 'slideUp',
    'animate-slide-out': 'slideDown',
    'animate-accordion-down': 'expandHeight',
    'animate-accordion-up': 'collapseHeight'
  };
  
  const mappedAnimation = legacyClass 
    ? animationMap[legacyClass] 
    : animation;
  
  if (!mappedAnimation) {
    console.warn(`Unknown animation: ${legacyClass || animation}`);
    return <>{children}</>;
  }
  
  return (
    <AnimationWrapper animation={mappedAnimation as any}>
      {children}
    </AnimationWrapper>
  );
};
```

### Phase 3: Progressive Enhancement (Week 3-4)

```typescript
// config/feature-flags.ts
export const ANIMATION_FEATURES = {
  USE_NEW_ANIMATION_SYSTEM: process.env.NEXT_PUBLIC_NEW_ANIMATIONS === 'true',
  ENABLE_PAGE_TRANSITIONS: process.env.NEXT_PUBLIC_PAGE_TRANSITIONS === 'true',
  ENABLE_SCROLL_ANIMATIONS: process.env.NEXT_PUBLIC_SCROLL_ANIMATIONS === 'true',
  ENABLE_FRAMER_MOTION: process.env.NEXT_PUBLIC_FRAMER_MOTION === 'true'
};

// components/SmartAnimation.tsx
export const SmartAnimation: React.FC<AnimationProps> = (props) => {
  if (ANIMATION_FEATURES.USE_NEW_ANIMATION_SYSTEM) {
    return <AnimationWrapper {...props} />;
  }
  
  // Fallback to legacy Tailwind animations
  return <div className={props.legacyClass}>{props.children}</div>;
};
```

### Phase 4: Performance Monitoring (Ongoing)

```typescript
// utils/animation-monitoring.ts
export class AnimationMonitor {
  private metrics: Map<string, PerformanceMetrics> = new Map();
  
  startMonitoring() {
    // Use Performance Observer API
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'measure' && entry.name.startsWith('animation:')) {
          this.recordMetric(entry);
        }
      }
    });
    
    observer.observe({ entryTypes: ['measure'] });
  }
  
  measureAnimation(name: string, fn: () => void) {
    performance.mark(`animation:${name}:start`);
    fn();
    performance.mark(`animation:${name}:end`);
    performance.measure(
      `animation:${name}`,
      `animation:${name}:start`,
      `animation:${name}:end`
    );
  }
  
  private recordMetric(entry: PerformanceEntry) {
    const name = entry.name.replace('animation:', '');
    const metrics = this.metrics.get(name) || {
      count: 0,
      totalDuration: 0,
      averageDuration: 0,
      maxDuration: 0
    };
    
    metrics.count++;
    metrics.totalDuration += entry.duration;
    metrics.averageDuration = metrics.totalDuration / metrics.count;
    metrics.maxDuration = Math.max(metrics.maxDuration, entry.duration);
    
    this.metrics.set(name, metrics);
  }
  
  getReport() {
    return Array.from(this.metrics.entries()).map(([name, metrics]) => ({
      name,
      ...metrics
    }));
  }
}
```

## Code Examples

### Button with Micro-interactions

```typescript
// components/AnimatedButton.tsx
import { useAnimation } from '@/hooks/useAnimation';
import { useRef } from 'react';

export const AnimatedButton: React.FC<ButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  ...props
}) => {
  const buttonRef = useRef<HTMLButtonElement>(null);
  const { animate } = useAnimation();
  
  const handleClick = async (e: React.MouseEvent) => {
    if (!buttonRef.current) return;
    
    // Ripple effect
    const ripple = createRipple(e, buttonRef.current);
    buttonRef.current.appendChild(ripple);
    
    // Button press animation
    await animate(
      buttonRef.current,
      [
        { transform: 'scale(1)' },
        { transform: 'scale(0.95)' },
        { transform: 'scale(1)' }
      ],
      { duration: 150, easing: ANIMATION_EASING.easeOut }
    );
    
    // Clean up ripple
    setTimeout(() => ripple.remove(), 600);
    
    if (onClick) onClick(e);
  };
  
  return (
    <button
      ref={buttonRef}
      className={cn(
        'relative overflow-hidden transition-all',
        'hover:shadow-md active:shadow-sm',
        variantStyles[variant]
      )}
      onClick={handleClick}
      {...props}
    >
      {children}
    </button>
  );
};
```

### Loading State with Skeleton

```typescript
// components/ContentSkeleton.tsx
export const ContentSkeleton: React.FC<{ lines?: number }> = ({ 
  lines = 3 
}) => {
  return (
    <div className="space-y-3">
      {Array.from({ length: lines }).map((_, i) => (
        <div
          key={i}
          className="h-4 bg-gray-200 rounded animate-pulse"
          style={{
            width: `${100 - (i * 10)}%`,
            animationDelay: `${i * 100}ms`,
            background: `linear-gradient(
              90deg,
              #f0f0f0 25%,
              #e0e0e0 50%,
              #f0f0f0 75%
            )`,
            backgroundSize: '200% 100%',
            animation: 'skeleton 1.5s ease-in-out infinite'
          }}
        />
      ))}
    </div>
  );
};
```

### Scroll-triggered Animations

```typescript
// components/ScrollReveal.tsx
import { useInView } from '@/hooks/useInView';
import { AnimationWrapper } from './AnimationWrapper';

export const ScrollReveal: React.FC<{
  children: ReactNode;
  animation?: AnimationType;
  threshold?: number;
  stagger?: boolean;
  staggerDelay?: number;
}> = ({ 
  children, 
  animation = 'fadeIn',
  threshold = 0.1,
  stagger = false,
  staggerDelay = 100
}) => {
  const { ref, isInView } = useInView({ threshold });
  
  if (stagger && React.Children.count(children) > 1) {
    return (
      <div ref={ref}>
        {React.Children.map(children, (child, index) => (
          <AnimationWrapper
            animation={animation}
            delay={isInView ? index * staggerDelay : 0}
            triggerOnScroll={false}
          >
            {child}
          </AnimationWrapper>
        ))}
      </div>
    );
  }
  
  return (
    <div ref={ref}>
      {isInView && (
        <AnimationWrapper animation={animation}>
          {children}
        </AnimationWrapper>
      )}
    </div>
  );
};
```

## Best Practices

### Do's
- ✅ Use transform and opacity for smooth animations
- ✅ Test on low-end devices
- ✅ Provide loading states for async operations
- ✅ Use will-change sparingly
- ✅ Respect user motion preferences
- ✅ Keep animations under 400ms for responsiveness
- ✅ Use GPU-accelerated properties
- ✅ Test with screen readers
- ✅ Implement proper easing functions
- ✅ Monitor performance metrics

### Don'ts
- ❌ Animate width, height, or position properties
- ❌ Use animations longer than 1 second
- ❌ Animate more than 3 elements simultaneously
- ❌ Ignore accessibility settings
- ❌ Use animations without purpose
- ❌ Create flashing or strobing effects
- ❌ Block user interaction during animations
- ❌ Forget to test on mobile devices
- ❌ Use infinite animations without pause controls
- ❌ Animate during critical rendering paths

## Troubleshooting

### Common Issues and Solutions

#### 1. Janky Animations
**Problem**: Animations stuttering or dropping frames
**Solution**:
```typescript
// Check for layout thrashing
const diagnosePerformance = () => {
  const observer = new PerformanceObserver((list) => {
    const entries = list.getEntries();
    const layoutThrashing = entries.filter(
      entry => entry.entryType === 'layout'
    );
    
    if (layoutThrashing.length > 10) {
      console.warn('Layout thrashing detected:', layoutThrashing);
    }
  });
  
  observer.observe({ entryTypes: ['layout'] });
};
```

#### 2. Animations Not Respecting Motion Preferences
**Problem**: Animations playing despite reduced motion setting
**Solution**:
```css
/* Ensure this is at the end of your CSS */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
```

#### 3. Memory Leaks from Animations
**Problem**: Animations causing memory leaks
**Solution**:
```typescript
// Always clean up animations
useEffect(() => {
  const animation = element.animate(keyframes, options);
  
  return () => {
    animation.cancel();
  };
}, []);
```

#### 4. Conflicting Animations
**Problem**: Multiple animations interfering with each other
**Solution**:
```typescript
// Use animation queue system
const animationQueue = new AnimationQueue();

animationQueue.add(() => animate(element1, animation1));
animationQueue.add(() => animate(element2, animation2));
animationQueue.run(); // Ensures sequential execution
```

## Conclusion

These enterprise animation guidelines provide a comprehensive framework for implementing performant, accessible, and maintainable animations in the SEO SAAS platform. By following these standards, we ensure a consistent, delightful user experience while maintaining the highest standards of web performance and accessibility.

Remember: Great animations are invisible - they enhance the experience without drawing attention to themselves.