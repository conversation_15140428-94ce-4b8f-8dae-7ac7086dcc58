/**
 * AnimationWrapper Component
 * Core component for applying animations to children elements
 */

import React, { useRef, useEffect } from 'react';
import { AnimationType, ANIMATION_DURATION, ANIMATION_EASING } from '@/lib/animations/constants';
import { useAnimation, useMotionPreference } from '@/lib/animations/hooks';
import { cn } from '@/lib/utils';

interface AnimationWrapperProps {
  children: React.ReactNode;
  animation: AnimationType;
  duration?: number;
  delay?: number;
  easing?: keyof typeof ANIMATION_EASING;
  onComplete?: () => void;
  triggerOnScroll?: boolean;
  threshold?: number;
  className?: string;
  essential?: boolean;
  iterationCount?: number;
  direction?: PlaybackDirection;
}

export const AnimationWrapper: React.FC<AnimationWrapperProps> = ({
  children,
  animation,
  duration = ANIMATION_DURATION.normal,
  delay = 0,
  easing = 'ease',
  onComplete,
  triggerOnScroll = false,
  threshold = 0.1,
  className = '',
  essential = false,
  iterationCount = 1,
  direction = 'normal'
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const { animate } = useAnimation();
  const prefersReducedMotion = useMotionPreference();
  const hasAnimated = useRef(false);

  useEffect(() => {
    if (!ref.current) return;
    if (triggerOnScroll && !hasAnimated.current) return;

    const element = ref.current;

    const runAnimation = async () => {
      try {
        await animate(element, animation, {
          duration: prefersReducedMotion && !essential ? 0 : duration,
          delay,
          easing: ANIMATION_EASING[easing],
          iterations: iterationCount,
          direction,
          essential,
          fill: 'forwards'
        });

        if (onComplete) {
          onComplete();
        }
      } catch (error) {
        console.error('Animation failed:', error);
      }
    };

    if (!triggerOnScroll) {
      runAnimation();
    }
  }, [
    animation,
    duration,
    delay,
    easing,
    animate,
    prefersReducedMotion,
    essential,
    iterationCount,
    direction,
    onComplete,
    triggerOnScroll
  ]);

  useEffect(() => {
    if (!triggerOnScroll || !ref.current || hasAnimated.current) return;

    const element = ref.current;
    const observer = new IntersectionObserver(
      async (entries) => {
        if (entries[0].isIntersecting && !hasAnimated.current) {
          hasAnimated.current = true;
          observer.disconnect();

          await animate(element, animation, {
            duration: prefersReducedMotion && !essential ? 0 : duration,
            delay,
            easing: ANIMATION_EASING[easing],
            iterations: iterationCount,
            direction,
            essential,
            fill: 'forwards'
          });

          if (onComplete) {
            onComplete();
          }
        }
      },
      { threshold }
    );

    observer.observe(element);

    return () => observer.disconnect();
  }, [
    triggerOnScroll,
    threshold,
    animation,
    duration,
    delay,
    easing,
    animate,
    prefersReducedMotion,
    essential,
    iterationCount,
    direction,
    onComplete
  ]);

  return (
    <div ref={ref} className={cn('will-change-auto', className)}>
      {children}
    </div>
  );
};