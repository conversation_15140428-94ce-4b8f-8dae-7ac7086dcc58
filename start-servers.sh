#!/bin/bash

# Start SEO SAAS Application Servers
echo "🚀 Starting SEO SAAS Application..."

# Function to check if port is in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        echo "Port $1 is already in use"
        return 1
    else
        return 0
    fi
}

# Kill any existing processes on ports 3000 and 5000
echo "🧹 Cleaning up existing processes..."
pkill -f "node.*3000" 2>/dev/null || true
pkill -f "node.*5000" 2>/dev/null || true
pkill -f "next.*dev" 2>/dev/null || true
pkill -f "nodemon" 2>/dev/null || true

# Wait a moment for processes to close
sleep 2

# Start backend server
echo "🔧 Starting backend server on port 5000..."
cd backend
npm run dev > ../backend.log 2>&1 &
BACKEND_PID=$!
cd ..

# Wait for backend to start
echo "⏳ Waiting for backend to start..."
sleep 5

# Check if backend is running
if curl -s http://localhost:5000/health >/dev/null 2>&1; then
    echo "✅ Backend server is running on http://localhost:5000"
else
    echo "❌ Backend server failed to start. Check backend.log for errors."
    echo "Backend log:"
    tail -n 20 backend.log
fi

# Start frontend server
echo "🎨 Starting frontend server on port 3000..."
npm run dev:frontend > frontend.log 2>&1 &
FRONTEND_PID=$!

# Wait for frontend to start
echo "⏳ Waiting for frontend to start..."
sleep 10

# Check if frontend is running
if curl -s http://localhost:3000 >/dev/null 2>&1; then
    echo "✅ Frontend server is running on http://localhost:3000"
else
    echo "❌ Frontend server failed to start. Check frontend.log for errors."
    echo "Frontend log:"
    tail -n 20 frontend.log
fi

echo ""
echo "🎉 SEO SAAS Application Setup Complete!"
echo ""
echo "📋 Server Status:"
echo "   Frontend: http://localhost:3000"
echo "   Backend:  http://localhost:5000"
echo ""
echo "📖 To access the application:"
echo "   1. Open http://localhost:3000 in your browser"
echo "   2. Register a new account or login"
echo "   3. Navigate to Content Generation"
echo "   4. Create a project and start generating content!"
echo ""
echo "🔧 To stop the servers:"
echo "   kill $BACKEND_PID $FRONTEND_PID"
echo ""
echo "📄 Logs:"
echo "   Backend: tail -f backend.log"
echo "   Frontend: tail -f frontend.log"