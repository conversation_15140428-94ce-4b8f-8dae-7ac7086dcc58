/**
 * NicheAnalyzer Component
 * Enterprise SEO SAAS - Universal niche analysis and adaptation interface
 */

import { useState, useEffect } from 'react'
import { UniversalNicheAdapter, NicheAnalysis } from '@/utils/universalNicheAdapter'
import {
  MagnifyingGlassIcon,
  SparklesIcon,
  ChartBarIcon,
  LightBulbIcon,
  TargetIcon,
  GlobeAltIcon,
  TagIcon,
  UserGroupIcon,
  TrendingUpIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  BrainIcon,
  EyeIcon,
  DocumentTextIcon,
  ArrowRightIcon,
  StarIcon
} from '@heroicons/react/24/outline'

interface NicheAnalyzerProps {
  keyword: string
  onAnalysisComplete?: (analysis: NicheAnalysis) => void
  autoAnalyze?: boolean
}

export default function NicheAnalyzer({ keyword, onAnalysisComplete, autoAnalyze = false }: NicheAnalyzerProps) {
  const [analysis, setAnalysis] = useState<NicheAnalysis | null>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [error, setError] = useState<string>('')
  const [adapter] = useState(() => new UniversalNicheAdapter())

  useEffect(() => {
    if (autoAnalyze && keyword) {
      handleAnalyze()
    }
  }, [keyword, autoAnalyze])

  const handleAnalyze = async () => {
    if (!keyword.trim()) {
      setError('Please provide a keyword to analyze')
      return
    }

    setIsAnalyzing(true)
    setError('')
    
    try {
      const result = await adapter.analyzeKeyword(keyword)
      setAnalysis(result)
      onAnalysisComplete?.(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Analysis failed')
    } finally {
      setIsAnalyzing(false)
    }
  }

  const getIndustryIcon = (industry: string) => {
    const icons: Record<string, any> = {
      technology: '💻',
      healthcare: '🏥',
      finance: '💰',
      education: '🎓',
      ecommerce: '🛒',
      real_estate: '🏠',
      automotive: '🚗',
      food_beverage: '🍽️',
      fashion: '👗',
      travel: '✈️',
      fitness: '💪',
      marketing: '📈',
      consulting: '💼',
      legal: '⚖️',
      entertainment: '🎬',
      other: '📂'
    }
    return icons[industry] || icons.other
  }

  const getIntentColor = (intent: string) => {
    switch (intent) {
      case 'informational': return 'text-blue-600 bg-blue-100'
      case 'commercial': return 'text-green-600 bg-green-100'
      case 'transactional': return 'text-purple-600 bg-purple-100'
      case 'navigational': return 'text-gray-600 bg-gray-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getCompetitivenessColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-green-600 bg-green-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'high': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getCommercialValueColor = (value: string) => {
    switch (value) {
      case 'low': return 'text-gray-600 bg-gray-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'high': return 'text-green-600 bg-green-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-600 bg-green-100'
    if (confidence >= 60) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-start gap-3">
          <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
          <div>
            <h3 className="text-sm font-medium text-red-800 mb-1">Analysis Error</h3>
            <p className="text-sm text-red-700">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  if (isAnalyzing) {
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h3 className="text-lg font-medium text-blue-900 mb-2">Analyzing "{keyword}"</h3>
          <p className="text-sm text-blue-700">
            Running universal niche adaptation across all industries...
          </p>
        </div>
      </div>
    )
  }

  if (!analysis) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-8">
        <div className="text-center">
          <BrainIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Universal Niche Analysis</h3>
          <p className="text-gray-600 mb-6">
            Analyze any keyword across any industry for optimal content strategy
          </p>
          <button
            onClick={handleAnalyze}
            disabled={!keyword.trim()}
            className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white font-medium rounded-lg transition-colors"
          >
            <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
            Analyze Keyword
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Analysis Header */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Universal Analysis: "{analysis.keyword}"
            </h2>
            <p className="text-gray-600">
              AI-powered analysis across all industries and niches
            </p>
          </div>
          
          <div className="text-right">
            <div className="text-sm text-gray-500 mb-1">Analysis Quality</div>
            <div className="text-2xl font-bold text-blue-600">
              {Math.round((analysis.industryConfidence + analysis.intentConfidence) / 2)}%
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-2xl">{getIndustryIcon(analysis.detectedIndustry)}</span>
              <span className="font-medium text-gray-900">Industry</span>
            </div>
            <div className="text-sm text-gray-600 capitalize mb-1">
              {analysis.detectedIndustry.replace('_', ' ')}
            </div>
            <span className={`
              inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
              ${getConfidenceColor(analysis.industryConfidence)}
            `}>
              {analysis.industryConfidence}% confidence
            </span>
          </div>

          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="flex items-center gap-2 mb-2">
              <TargetIcon className="h-5 w-5 text-gray-600" />
              <span className="font-medium text-gray-900">Search Intent</span>
            </div>
            <div className="text-sm text-gray-600 capitalize mb-1">
              {analysis.searchIntent}
            </div>
            <span className={`
              inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
              ${getIntentColor(analysis.searchIntent)}
            `}>
              {analysis.intentConfidence}% confidence
            </span>
          </div>

          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="flex items-center gap-2 mb-2">
              <ChartBarIcon className="h-5 w-5 text-gray-600" />
              <span className="font-medium text-gray-900">Competition</span>
            </div>
            <div className="text-sm text-gray-600 capitalize mb-1">
              {analysis.competitiveness}
            </div>
            <span className={`
              inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
              ${getCompetitivenessColor(analysis.competitiveness)}
            `}>
              {analysis.competitiveness} level
            </span>
          </div>

          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUpIcon className="h-5 w-5 text-gray-600" />
              <span className="font-medium text-gray-900">Commercial Value</span>
            </div>
            <div className="text-sm text-gray-600 capitalize mb-1">
              {analysis.commercialValue}
            </div>
            <span className={`
              inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
              ${getCommercialValueColor(analysis.commercialValue)}
            `}>
              {analysis.commercialValue} value
            </span>
          </div>
        </div>
      </div>

      {/* Detailed Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Keyword Intelligence */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
            <SparklesIcon className="h-5 w-5 text-blue-500" />
            Keyword Intelligence
          </h3>
          
          <div className="space-y-4">
            <div>
              <div className="text-sm font-medium text-gray-700 mb-1">Keyword Type</div>
              <span className={`
                inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                ${analysis.keywordType === 'long-tail' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}
              `}>
                {analysis.keywordType.replace('-', ' ')}
              </span>
            </div>

            <div>
              <div className="text-sm font-medium text-gray-700 mb-1">Suggested Content Type</div>
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                {analysis.suggestedContentType.replace('_', ' ')}
              </span>
            </div>

            <div>
              <div className="text-sm font-medium text-gray-700 mb-2">Target Audience</div>
              <p className="text-sm text-gray-600">{analysis.targetAudience}</p>
            </div>
          </div>
        </div>

        {/* Semantic Keywords */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
            <TagIcon className="h-5 w-5 text-green-500" />
            Semantic Keywords
          </h3>
          
          <div className="space-y-3">
            <div className="text-sm text-gray-600 mb-3">
              AI-generated semantic variations for comprehensive coverage
            </div>
            
            <div className="flex flex-wrap gap-2">
              {analysis.semanticKeywords.slice(0, 10).map((keyword, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800"
                >
                  {keyword}
                </span>
              ))}
            </div>
            
            {analysis.semanticKeywords.length > 10 && (
              <div className="text-xs text-gray-500">
                +{analysis.semanticKeywords.length - 10} more keywords available
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Content Strategy Recommendations */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
          <LightBulbIcon className="h-5 w-5 text-yellow-500" />
          AI-Generated Content Strategy
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-800 mb-3">Content Recommendations</h4>
            <ul className="space-y-2">
              {analysis.contentSuggestions.map((suggestion, index) => (
                <li key={index} className="flex items-start gap-2 text-sm text-gray-600">
                  <CheckCircleIcon className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  {suggestion}
                </li>
              ))}
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-800 mb-3">Optimization Focus</h4>
            <div className="space-y-3">
              {analysis.searchIntent === 'commercial' && (
                <div className="flex items-center gap-2 text-sm text-green-700 bg-green-50 rounded-lg p-3">
                  <TrendingUpIcon className="h-4 w-4" />
                  High commercial potential - focus on conversion optimization
                </div>
              )}
              
              {analysis.competitiveness === 'high' && (
                <div className="flex items-center gap-2 text-sm text-red-700 bg-red-50 rounded-lg p-3">
                  <ExclamationTriangleIcon className="h-4 w-4" />
                  High competition - differentiate with unique angles
                </div>
              )}
              
              {analysis.keywordType === 'long-tail' && (
                <div className="flex items-center gap-2 text-sm text-blue-700 bg-blue-50 rounded-lg p-3">
                  <StarIcon className="h-4 w-4" />
                  Long-tail opportunity - target specific user intent
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Validation Results */}
      {analysis.validationResult && !analysis.validationResult.isValid && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <InformationCircleIcon className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="text-sm font-medium text-yellow-800 mb-2">Validation Warnings</h4>
              <ul className="text-sm text-yellow-700 space-y-1">
                {analysis.validationResult.warnings?.map((warning: string, index: number) => (
                  <li key={index}>• {warning}</li>
                ))}
              </ul>
              
              {analysis.validationResult.suggestions?.length > 0 && (
                <div className="mt-2">
                  <div className="text-sm font-medium text-yellow-800 mb-1">Suggestions:</div>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    {analysis.validationResult.suggestions.map((suggestion: string, index: number) => (
                      <li key={index}>• {suggestion}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Success Footer */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center gap-3">
          <CheckCircleIcon className="h-5 w-5 text-green-600" />
          <div>
            <h4 className="text-sm font-medium text-green-800">Universal Analysis Complete</h4>
            <p className="text-sm text-green-700">
              Ready to generate high-quality, niche-adapted content for "{analysis.keyword}"
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}