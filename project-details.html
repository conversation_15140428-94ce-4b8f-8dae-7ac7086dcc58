<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Detailed project analysis with SEO metrics, competitor insights, and E-E-A-T scoring for professional content optimization.">
    <title>Project Details - SEO SAAS</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/data-visualization.css">
    <link rel="stylesheet" href="css/animations.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
</head>
<body class="dashboard-layout">
    <!-- Dashboard App Container -->
    <div class="dashboard-app">
        <!-- Global Top Navigation -->
        <header class="top-navigation">
            <div class="nav-left">
                <button class="sidebar-toggle" aria-label="Toggle sidebar" onclick="toggleSidebar()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
                <div class="brand-section">
                    <svg class="brand-logo w-8 h-8 text-primary-600" viewBox="0 0 40 40" fill="currentColor">
                        <path d="M20 5L5 15v10l15 10 15-10V15L20 5z"/>
                        <path d="M20 15l-10 6v8l10 6 10-6v-8l-10-6z" fill="white" opacity="0.5"/>
                    </svg>
                    <span class="brand-name">SEO SAAS</span>
                    <span class="environment-badge">Pro</span>
                </div>
            </div>
            
            <div class="nav-center">
                <div class="global-search">
                    <svg class="search-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <input type="search" placeholder="Search projects, keywords, content..." class="search-input">
                </div>
            </div>
            
            <div class="nav-right">
                <button class="quick-action-btn" title="Regenerate Content" onclick="regenerateContent()">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    <span>Regenerate</span>
                </button>
                
                <div class="notifications-dropdown">
                    <button class="notification-btn" title="Notifications">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5-5V9.5a6.5 6.5 0 10-13 0V12l-5 5h5"></path>
                        </svg>
                        <span class="notification-badge">3</span>
                    </button>
                </div>
                
                <div class="help-dropdown">
                    <button class="help-btn" title="Help & Support">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </button>
                </div>
                
                <div class="user-menu-dropdown">
                    <button class="user-menu-btn">
                        <div class="user-avatar">
                            <span>JD</span>
                        </div>
                        <div class="user-info">
                            <span class="user-name">John Doe</span>
                            <span class="user-plan">Professional</span>
                        </div>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <aside class="sidebar-navigation" id="sidebarNav">
            <nav class="nav-menu">
                <!-- Dashboard Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Overview</h3>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="dashboard.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z"></path>
                                </svg>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="analytics.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                <span>Analytics</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Content Creation Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Content Creation</h3>
                        <button class="section-toggle" aria-label="Toggle section" onclick="toggleSection(this)">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="content-creator.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                <span>Create Content</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="content-optimizer.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                <span>Optimize Content</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="bulk-generator.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                <span>Bulk Generator</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="content-editor.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                                </svg>
                                <span>Content Editor</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Projects Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Projects</h3>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item active">
                            <a href="projects.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                <span>All Projects</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="templates.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <span>Templates</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Settings Section -->
                <div class="nav-section">
                    <div class="nav-section-header">
                        <h3 class="nav-section-title">Settings</h3>
                    </div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="account-settings.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <span>Account</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="billing.html" class="nav-link">
                                <svg class="nav-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                </svg>
                                <span>Billing</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Sidebar Footer -->
            <div class="sidebar-footer">
                <div class="usage-indicator">
                    <div class="usage-label">API Usage</div>
                    <div class="usage-bar">
                        <div class="usage-fill" style="width: 65%"></div>
                    </div>
                    <div class="usage-text">325 / 500 requests</div>
                </div>
                
                <button class="upgrade-btn" onclick="window.location.href='pricing.html'">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    <span>Upgrade Plan</span>
                </button>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Project Header -->
            <div class="project-header">
                <div class="header-content">
                    <div class="header-left">
                        <div class="breadcrumb">
                            <a href="projects.html" class="breadcrumb-link">Projects</a>
                            <svg class="breadcrumb-separator w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                            <span class="breadcrumb-current">Digital Marketing Agency</span>
                        </div>
                        <h1 class="project-title">Digital Marketing Agency</h1>
                        <div class="project-meta">
                            <span class="project-keyword">digital marketing services</span>
                            <span class="project-separator">•</span>
                            <span class="project-industry">Marketing</span>
                            <span class="project-separator">•</span>
                            <span class="project-location">United States</span>
                        </div>
                    </div>
                    <div class="header-right">
                        <div class="project-status">
                            <span class="status-badge status-active">Active</span>
                            <div class="score-badge score-excellent">96</div>
                        </div>
                        <div class="project-actions">
                            <button class="btn btn-secondary" onclick="editProject()">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                Edit
                            </button>
                            <button class="btn btn-primary" onclick="generateContent()">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                Generate Content
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Project Content Tabs -->
            <div class="project-tabs">
                <div class="tabs-nav">
                    <button class="tab-btn active" onclick="switchTab('overview')" data-tab="overview">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        Overview
                    </button>
                    <button class="tab-btn" onclick="switchTab('analysis')" data-tab="analysis">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Analysis
                    </button>
                    <button class="tab-btn" onclick="switchTab('competitors')" data-tab="competitors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        Competitors
                    </button>
                    <button class="tab-btn" onclick="switchTab('content')" data-tab="content">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Content
                    </button>
                    <button class="tab-btn" onclick="switchTab('eeat')" data-tab="eeat">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                        </svg>
                        E-E-A-T
                    </button>
                </div>
            </div>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Overview Tab -->
                <div class="tab-panel active" id="overview">
                    <div class="overview-grid">
                        <!-- SEO Metrics Card -->
                        <div class="metrics-card">
                            <div class="card-header">
                                <h3>SEO Performance Metrics</h3>
                                <span class="card-subtitle">Real-time analysis results</span>
                            </div>
                            <div class="metrics-grid">
                                <div class="metric-item">
                                    <div class="metric-value">2,847</div>
                                    <div class="metric-label">Words</div>
                                    <div class="metric-change positive">+12% vs competitors</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">1.8%</div>
                                    <div class="metric-label">Keyword Density</div>
                                    <div class="metric-change positive">Optimal range</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">73</div>
                                    <div class="metric-label">Readability</div>
                                    <div class="metric-change positive">Easy to read</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">18</div>
                                    <div class="metric-label">H2 Headings</div>
                                    <div class="metric-change positive">Well structured</div>
                                </div>
                            </div>
                        </div>

                        <!-- Authority Links Card -->
                        <div class="authority-links-card">
                            <div class="card-header">
                                <h3>Authority Links</h3>
                                <span class="card-subtitle">High-quality external references</span>
                            </div>
                            <div class="authority-links-list">
                                <div class="authority-link-item">
                                    <div class="link-info">
                                        <div class="link-domain">wikipedia.org</div>
                                        <div class="link-title">Digital Marketing - Wikipedia</div>
                                    </div>
                                    <div class="link-score">
                                        <div class="score-badge score-excellent">98</div>
                                    </div>
                                </div>
                                <div class="authority-link-item">
                                    <div class="link-info">
                                        <div class="link-domain">hubspot.com</div>
                                        <div class="link-title">The Ultimate Guide to Digital Marketing</div>
                                    </div>
                                    <div class="link-score">
                                        <div class="score-badge score-excellent">95</div>
                                    </div>
                                </div>
                                <div class="authority-link-item">
                                    <div class="link-info">
                                        <div class="link-domain">google.com</div>
                                        <div class="link-title">Think with Google - Marketing Insights</div>
                                    </div>
                                    <div class="link-score">
                                        <div class="score-badge score-excellent">99</div>
                                    </div>
                                </div>
                            </div>
                            <button class="btn btn-ghost btn-sm w-full mt-4">View All Authority Links</button>
                        </div>

                        <!-- Project Timeline -->
                        <div class="timeline-card">
                            <div class="card-header">
                                <h3>Project Timeline</h3>
                                <span class="card-subtitle">Recent activity and updates</span>
                            </div>
                            <div class="timeline-list">
                                <div class="timeline-item">
                                    <div class="timeline-icon">
                                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                        </svg>
                                    </div>
                                    <div class="timeline-content">
                                        <div class="timeline-title">Content Generated</div>
                                        <div class="timeline-description">High-quality SEO content created with 96 score</div>
                                        <div class="timeline-time">2 hours ago</div>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-icon">
                                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="timeline-content">
                                        <div class="timeline-title">Competitor Analysis</div>
                                        <div class="timeline-description">Analyzed 15 top competitors for insights</div>
                                        <div class="timeline-time">4 hours ago</div>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-icon">
                                        <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                    </div>
                                    <div class="timeline-content">
                                        <div class="timeline-title">Project Created</div>
                                        <div class="timeline-description">Digital Marketing Agency project initialized</div>
                                        <div class="timeline-time">1 day ago</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analysis Tab (Hidden by default) -->
                <div class="tab-panel hidden" id="analysis">
                    <div class="analysis-content">
                        <h3>Content Analysis Details</h3>
                        <p>Detailed analysis results will be displayed here.</p>
                    </div>
                </div>

                <!-- Competitors Tab (Hidden by default) -->
                <div class="tab-panel hidden" id="competitors">
                    <div class="competitors-content">
                        <h3>Competitor Analysis</h3>
                        <p>Competitor comparison data will be displayed here.</p>
                    </div>
                </div>

                <!-- Content Tab (Hidden by default) -->
                <div class="tab-panel hidden" id="content">
                    <div class="content-preview">
                        <h3>Generated Content</h3>
                        <p>Content preview and editing interface will be displayed here.</p>
                    </div>
                </div>

                <!-- E-E-A-T Tab (Hidden by default) -->
                <div class="tab-panel hidden" id="eeat">
                    <div class="eeat-assessment">
                        <h3>E-E-A-T Assessment</h3>
                        <p>E-E-A-T scoring and recommendations will be displayed here.</p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script>
        // Project Details JavaScript functionality
        document.addEventListener('DOMContentLoaded', function() {
            initializeProjectDetails();
            loadProjectData();
        });

        function initializeProjectDetails() {
            // Get project ID from URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const projectId = urlParams.get('id');
            const activeTab = urlParams.get('tab') || 'overview';
            
            if (activeTab !== 'overview') {
                switchTab(activeTab);
            }
            
            // Initialize sidebar state
            const sidebar = document.getElementById('sidebarNav');
            const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (isCollapsed) {
                sidebar.classList.add('collapsed');
            }
        }

        function loadProjectData() {
            // Sample project data - would come from API in real implementation
            const projectData = {
                id: 1,
                name: "Digital Marketing Agency",
                keyword: "digital marketing services",
                industry: "Marketing",
                location: "United States",
                status: "active",
                score: 96,
                metrics: {
                    wordCount: 2847,
                    keywordDensity: 1.8,
                    readability: 73,
                    headings: 18
                },
                authorityLinks: [
                    {
                        domain: "wikipedia.org",
                        title: "Digital Marketing - Wikipedia",
                        score: 98
                    },
                    {
                        domain: "hubspot.com", 
                        title: "The Ultimate Guide to Digital Marketing",
                        score: 95
                    },
                    {
                        domain: "google.com",
                        title: "Think with Google - Marketing Insights", 
                        score: 99
                    }
                ]
            };
            
            // Update page with project data
            updateProjectDisplay(projectData);
        }

        function updateProjectDisplay(data) {
            // Update project title and meta information
            document.querySelector('.project-title').textContent = data.name;
            document.querySelector('.project-keyword').textContent = data.keyword;
            document.querySelector('.project-industry').textContent = data.industry;
            document.querySelector('.project-location').textContent = data.location;
            
            // Update SEO metrics
            const metricItems = document.querySelectorAll('.metric-item');
            if (metricItems.length >= 4) {
                metricItems[0].querySelector('.metric-value').textContent = data.metrics.wordCount.toLocaleString();
                metricItems[1].querySelector('.metric-value').textContent = data.metrics.keywordDensity + '%';
                metricItems[2].querySelector('.metric-value').textContent = data.metrics.readability;
                metricItems[3].querySelector('.metric-value').textContent = data.metrics.headings;
            }
        }

        function switchTab(tabName) {
            // Remove active class from all tabs and panels
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-panel').forEach(panel => {
                panel.classList.remove('active');
                panel.classList.add('hidden');
            });
            
            // Activate selected tab and panel
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
            const panel = document.getElementById(tabName);
            panel.classList.remove('hidden');
            panel.classList.add('active');
            
            // Update URL without page reload
            const url = new URL(window.location);
            url.searchParams.set('tab', tabName);
            window.history.replaceState({}, '', url);
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebarNav');
            sidebar.classList.toggle('collapsed');
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }

        function toggleSection(button) {
            const section = button.closest('.nav-section');
            const items = section.querySelector('.nav-items');
            const icon = button.querySelector('svg');
            
            section.classList.toggle('collapsed');
            
            if (section.classList.contains('collapsed')) {
                items.style.display = 'none';
                icon.style.transform = 'rotate(-90deg)';
            } else {
                items.style.display = 'block';
                icon.style.transform = 'rotate(0deg)';
            }
        }

        function editProject() {
            // Navigate to project edit mode
            window.location.href = 'content-creator.html?edit=true&id=' + getCurrentProjectId();
        }

        function generateContent() {
            // Navigate to content generation
            window.location.href = 'content-creator.html?id=' + getCurrentProjectId();
        }

        function regenerateContent() {
            // Trigger content regeneration
            if (confirm('Are you sure you want to regenerate the content? This will replace the current content.')) {
                console.log('Regenerating content...');
                // Implementation would call API to regenerate content
            }
        }

        function getCurrentProjectId() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('id') || '1';
        }
    </script>
</body>
</html>