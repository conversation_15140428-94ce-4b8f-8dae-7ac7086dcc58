/**
 * Project Management System
 * Comprehensive project management with team collaboration, tasks, and workflow automation
 */

'use client'

import React, { useState, useEffect } from 'react'
import { useNotifications } from '@/components/Notifications'
import Card from '@/components/UI/Card'
import Button from '@/components/UI/Button'
import Input from '@/components/UI/Input'
import Badge from '@/components/UI/Badge'
import LoadingSpinner from '@/components/UI/LoadingSpinner'
import {
  FolderIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  CalendarIcon,
  UserIcon,
  ChartBarIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  FlagIcon,
  ChatBubbleLeftIcon,
  DocumentTextIcon,
  PaperClipIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  UserGroupIcon,
  BellIcon,
  ArrowPathIcon,
  Bars3Icon,
  TableCellsIcon,
  ListBulletIcon,
  KanbanIcon,
  CalendarDaysIcon,
  ChartPieIcon,
  CogIcon,
  ShareIcon,
  ArrowUpTrayIcon,
  ArrowDownTrayIcon,
  PlayIcon,
  PauseIcon,
  StopIcon,
  FireIcon,
  LightBulbIcon,
  StarIcon,
  BookmarkIcon,
  TagIcon,
  LinkIcon,
  PhoneIcon,
  VideoCameraIcon,
  InboxIcon,
  ArchiveBoxIcon,
  HandRaisedIcon,
  TrophyIcon,
  RocketLaunchIcon,
  BuildingOfficeIcon,
  GlobeAltIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  ClipboardDocumentListIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'

interface ProjectManagementSystemProps {
  onProjectSelect?: (project: ProjectData) => void
  onTaskSelect?: (task: TaskData) => void
}

interface ProjectData {
  id: string
  name: string
  description: string
  status: 'planning' | 'active' | 'on_hold' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  type: 'seo_campaign' | 'content_creation' | 'link_building' | 'technical_audit' | 'competitor_analysis' | 'website_launch'
  client: {
    name: string
    company: string
    avatar?: string
    email: string
  }
  team: TeamMember[]
  manager: TeamMember
  startDate: string
  endDate: string
  budget: number
  spent: number
  progress: number
  tasks: {
    total: number
    completed: number
    inProgress: number
    pending: number
    overdue: number
  }
  milestones: Milestone[]
  tags: string[]
  attachments: Attachment[]
  lastActivity: string
  createdAt: string
  metrics: {
    efficiency: number
    quality: number
    clientSatisfaction: number
    profitability: number
  }
  workflow: {
    currentStage: string
    stages: WorkflowStage[]
  }
  risks: ProjectRisk[]
  communications: Communication[]
}

interface TaskData {
  id: string
  title: string
  description: string
  projectId: string
  projectName: string
  status: 'todo' | 'in_progress' | 'review' | 'completed' | 'blocked'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  type: 'research' | 'content' | 'technical' | 'outreach' | 'analysis' | 'review' | 'admin'
  assignee: TeamMember
  reporter: TeamMember
  startDate: string
  dueDate: string
  estimatedHours: number
  actualHours: number
  progress: number
  tags: string[]
  attachments: Attachment[]
  comments: Comment[]
  dependencies: string[]
  subtasks: SubTask[]
  checklist: ChecklistItem[]
  timeTracking: TimeEntry[]
  labels: string[]
  customFields: Record<string, any>
  createdAt: string
  updatedAt: string
}

interface TeamMember {
  id: string
  name: string
  email: string
  avatar?: string
  role: 'manager' | 'lead' | 'senior' | 'junior' | 'intern' | 'contractor'
  department: 'seo' | 'content' | 'technical' | 'design' | 'pm' | 'qa'
  skills: string[]
  workload: number
  availability: 'available' | 'busy' | 'offline'
  timezone: string
  hourlyRate: number
  performance: {
    tasksCompleted: number
    averageRating: number
    onTimeDelivery: number
  }
}

interface Milestone {
  id: string
  title: string
  description: string
  dueDate: string
  status: 'pending' | 'completed' | 'overdue'
  tasks: string[]
  deliverables: string[]
  payment?: number
}

interface WorkflowStage {
  id: string
  name: string
  description: string
  order: number
  status: 'pending' | 'active' | 'completed' | 'skipped'
  tasks: string[]
  requirements: string[]
  approvals: string[]
  duration: number
}

interface ProjectRisk {
  id: string
  title: string
  description: string
  probability: 'low' | 'medium' | 'high'
  impact: 'low' | 'medium' | 'high'
  status: 'identified' | 'mitigating' | 'resolved' | 'accepted'
  owner: string
  mitigation: string
  createdAt: string
}

interface Communication {
  id: string
  type: 'email' | 'call' | 'meeting' | 'message' | 'note'
  subject: string
  content: string
  participants: string[]
  timestamp: string
  attachments: Attachment[]
  tags: string[]
}

interface Attachment {
  id: string
  name: string
  size: number
  type: string
  url: string
  uploadedBy: string
  uploadedAt: string
}

interface Comment {
  id: string
  content: string
  author: TeamMember
  timestamp: string
  attachments: Attachment[]
  mentions: string[]
  reactions: Record<string, string[]>
}

interface SubTask {
  id: string
  title: string
  status: 'todo' | 'completed'
  assignee?: TeamMember
  dueDate?: string
}

interface ChecklistItem {
  id: string
  title: string
  completed: boolean
  assignee?: TeamMember
}

interface TimeEntry {
  id: string
  date: string
  hours: number
  description: string
  billable: boolean
  user: TeamMember
}

export default function ProjectManagementSystem({
  onProjectSelect,
  onTaskSelect
}: ProjectManagementSystemProps) {
  const [projects, setProjects] = useState<ProjectData[]>([])
  const [tasks, setTasks] = useState<TaskData[]>([])
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([])
  const [activeTab, setActiveTab] = useState<'projects' | 'tasks' | 'team' | 'timeline' | 'reports' | 'settings'>('projects')
  const [viewMode, setViewMode] = useState<'grid' | 'table' | 'kanban' | 'calendar'>('grid')
  const [searchQuery, setSearchQuery] = useState('')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [filterPriority, setFilterPriority] = useState<string>('all')
  const [filterAssignee, setFilterAssignee] = useState<string>('all')
  const [selectedProject, setSelectedProject] = useState<ProjectData | null>(null)
  const [selectedTask, setSelectedTask] = useState<TaskData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [showCreateProject, setShowCreateProject] = useState(false)
  const [showCreateTask, setShowCreateTask] = useState(false)
  const [showProjectDetails, setShowProjectDetails] = useState(false)
  const [showTaskDetails, setShowTaskDetails] = useState(false)
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [notifications, setNotifications] = useState<any[]>([])
  
  const { success: notifySuccess, error: notifyError } = useNotifications()

  // Mock data - in real app this would come from API
  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    setIsLoading(true)
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock team members
      const mockTeamMembers: TeamMember[] = [
        {
          id: 'tm1',
          name: 'Sarah Wilson',
          email: '<EMAIL>',
          role: 'manager',
          department: 'seo',
          skills: ['SEO Strategy', 'Content Planning', 'Team Leadership'],
          workload: 85,
          availability: 'available',
          timezone: 'EST',
          hourlyRate: 85,
          performance: {
            tasksCompleted: 156,
            averageRating: 4.8,
            onTimeDelivery: 92
          }
        },
        {
          id: 'tm2',
          name: 'Mike Chen',
          email: '<EMAIL>',
          role: 'lead',
          department: 'technical',
          skills: ['Technical SEO', 'Website Optimization', 'Analytics'],
          workload: 75,
          availability: 'available',
          timezone: 'PST',
          hourlyRate: 75,
          performance: {
            tasksCompleted: 134,
            averageRating: 4.7,
            onTimeDelivery: 88
          }
        },
        {
          id: 'tm3',
          name: 'Emily Rodriguez',
          email: '<EMAIL>',
          role: 'senior',
          department: 'content',
          skills: ['Content Writing', 'SEO Writing', 'Research'],
          workload: 70,
          availability: 'busy',
          timezone: 'EST',
          hourlyRate: 65,
          performance: {
            tasksCompleted: 98,
            averageRating: 4.9,
            onTimeDelivery: 95
          }
        },
        {
          id: 'tm4',
          name: 'David Park',
          email: '<EMAIL>',
          role: 'junior',
          department: 'seo',
          skills: ['Link Building', 'Research', 'Outreach'],
          workload: 60,
          availability: 'available',
          timezone: 'CST',
          hourlyRate: 45,
          performance: {
            tasksCompleted: 67,
            averageRating: 4.5,
            onTimeDelivery: 85
          }
        }
      ]
      
      setTeamMembers(mockTeamMembers)
      
      // Mock projects
      setProjects([
        {
          id: 'proj1',
          name: 'E-commerce SEO Overhaul',
          description: 'Complete SEO audit and optimization for major e-commerce platform',
          status: 'active',
          priority: 'high',
          type: 'seo_campaign',
          client: {
            name: 'John Smith',
            company: 'TechCorp Inc.',
            email: '<EMAIL>'
          },
          team: [mockTeamMembers[0], mockTeamMembers[1], mockTeamMembers[2]],
          manager: mockTeamMembers[0],
          startDate: '2024-01-15T00:00:00Z',
          endDate: '2024-04-15T00:00:00Z',
          budget: 50000,
          spent: 28500,
          progress: 65,
          tasks: {
            total: 24,
            completed: 12,
            inProgress: 6,
            pending: 4,
            overdue: 2
          },
          milestones: [
            {
              id: 'ms1',
              title: 'Technical Audit Complete',
              description: 'Complete technical SEO audit and report',
              dueDate: '2024-02-15T00:00:00Z',
              status: 'completed',
              tasks: ['task1', 'task2'],
              deliverables: ['Technical Audit Report', 'Recommendation Document']
            },
            {
              id: 'ms2',
              title: 'Content Strategy',
              description: 'Develop comprehensive content strategy',
              dueDate: '2024-03-15T00:00:00Z',
              status: 'pending',
              tasks: ['task3', 'task4'],
              deliverables: ['Content Strategy Document', 'Keyword Research'],
              payment: 15000
            }
          ],
          tags: ['e-commerce', 'technical-seo', 'content-strategy'],
          attachments: [
            {
              id: 'att1',
              name: 'Project-Brief.pdf',
              size: 2048000,
              type: 'pdf',
              url: '/attachments/project-brief.pdf',
              uploadedBy: 'tm1',
              uploadedAt: '2024-01-15T10:00:00Z'
            }
          ],
          lastActivity: '2024-01-16T14:30:00Z',
          createdAt: '2024-01-15T09:00:00Z',
          metrics: {
            efficiency: 88,
            quality: 92,
            clientSatisfaction: 95,
            profitability: 78
          },
          workflow: {
            currentStage: 'content_optimization',
            stages: [
              {
                id: 'stage1',
                name: 'Technical Audit',
                description: 'Complete technical SEO audit',
                order: 1,
                status: 'completed',
                tasks: ['task1', 'task2'],
                requirements: ['Site access', 'Analytics access'],
                approvals: ['client_approval'],
                duration: 14
              },
              {
                id: 'stage2',
                name: 'Content Optimization',
                description: 'Optimize existing content and create new content',
                order: 2,
                status: 'active',
                tasks: ['task3', 'task4'],
                requirements: ['Content audit', 'Keyword research'],
                approvals: ['content_approval'],
                duration: 21
              }
            ]
          },
          risks: [
            {
              id: 'risk1',
              title: 'Technical Implementation Delays',
              description: 'Client development team may cause delays in implementing technical changes',
              probability: 'medium',
              impact: 'high',
              status: 'mitigating',
              owner: 'tm1',
              mitigation: 'Regular check-ins with client dev team and backup implementation plan',
              createdAt: '2024-01-16T10:00:00Z'
            }
          ],
          communications: [
            {
              id: 'comm1',
              type: 'meeting',
              subject: 'Project Kickoff Meeting',
              content: 'Initial project discussion and requirements gathering',
              participants: ['tm1', '<EMAIL>'],
              timestamp: '2024-01-15T14:00:00Z',
              attachments: [],
              tags: ['kickoff', 'requirements']
            }
          ]
        },
        {
          id: 'proj2',
          name: 'Content Marketing Campaign',
          description: 'Develop and execute comprehensive content marketing strategy',
          status: 'planning',
          priority: 'medium',
          type: 'content_creation',
          client: {
            name: 'Lisa Johnson',
            company: 'StartupCo',
            email: '<EMAIL>'
          },
          team: [mockTeamMembers[2], mockTeamMembers[3]],
          manager: mockTeamMembers[2],
          startDate: '2024-02-01T00:00:00Z',
          endDate: '2024-05-01T00:00:00Z',
          budget: 35000,
          spent: 5000,
          progress: 25,
          tasks: {
            total: 18,
            completed: 3,
            inProgress: 2,
            pending: 12,
            overdue: 1
          },
          milestones: [
            {
              id: 'ms3',
              title: 'Content Strategy Development',
              description: 'Create comprehensive content strategy',
              dueDate: '2024-02-15T00:00:00Z',
              status: 'pending',
              tasks: ['task5', 'task6'],
              deliverables: ['Content Strategy', 'Editorial Calendar'],
              payment: 10000
            }
          ],
          tags: ['content-marketing', 'startup', 'brand-awareness'],
          attachments: [],
          lastActivity: '2024-01-16T09:15:00Z',
          createdAt: '2024-01-14T11:00:00Z',
          metrics: {
            efficiency: 75,
            quality: 85,
            clientSatisfaction: 90,
            profitability: 65
          },
          workflow: {
            currentStage: 'strategy_development',
            stages: [
              {
                id: 'stage3',
                name: 'Strategy Development',
                description: 'Develop content marketing strategy',
                order: 1,
                status: 'active',
                tasks: ['task5', 'task6'],
                requirements: ['Brand guidelines', 'Target audience research'],
                approvals: ['strategy_approval'],
                duration: 14
              }
            ]
          },
          risks: [],
          communications: []
        },
        {
          id: 'proj3',
          name: 'Technical SEO Audit',
          description: 'Comprehensive technical SEO audit for enterprise website',
          status: 'completed',
          priority: 'high',
          type: 'technical_audit',
          client: {
            name: 'Robert Chen',
            company: 'Enterprise Solutions',
            email: '<EMAIL>'
          },
          team: [mockTeamMembers[1]],
          manager: mockTeamMembers[1],
          startDate: '2024-01-01T00:00:00Z',
          endDate: '2024-01-14T00:00:00Z',
          budget: 15000,
          spent: 14500,
          progress: 100,
          tasks: {
            total: 12,
            completed: 12,
            inProgress: 0,
            pending: 0,
            overdue: 0
          },
          milestones: [
            {
              id: 'ms4',
              title: 'Audit Complete',
              description: 'Complete technical SEO audit and report',
              dueDate: '2024-01-14T00:00:00Z',
              status: 'completed',
              tasks: ['task7', 'task8'],
              deliverables: ['Technical Audit Report', 'Implementation Roadmap'],
              payment: 15000
            }
          ],
          tags: ['technical-seo', 'enterprise', 'audit'],
          attachments: [],
          lastActivity: '2024-01-14T16:00:00Z',
          createdAt: '2024-01-01T09:00:00Z',
          metrics: {
            efficiency: 95,
            quality: 98,
            clientSatisfaction: 100,
            profitability: 85
          },
          workflow: {
            currentStage: 'completed',
            stages: [
              {
                id: 'stage4',
                name: 'Technical Audit',
                description: 'Complete technical SEO audit',
                order: 1,
                status: 'completed',
                tasks: ['task7', 'task8'],
                requirements: ['Site access', 'Analytics access'],
                approvals: ['final_approval'],
                duration: 14
              }
            ]
          },
          risks: [],
          communications: []
        }
      ])
      
      // Mock tasks
      setTasks([
        {
          id: 'task1',
          title: 'Site Speed Optimization Analysis',
          description: 'Analyze current site speed and identify optimization opportunities',
          projectId: 'proj1',
          projectName: 'E-commerce SEO Overhaul',
          status: 'completed',
          priority: 'high',
          type: 'technical',
          assignee: mockTeamMembers[1],
          reporter: mockTeamMembers[0],
          startDate: '2024-01-15T00:00:00Z',
          dueDate: '2024-01-20T00:00:00Z',
          estimatedHours: 16,
          actualHours: 14,
          progress: 100,
          tags: ['performance', 'technical'],
          attachments: [],
          comments: [
            {
              id: 'comment1',
              content: 'Analysis complete, found significant optimization opportunities',
              author: mockTeamMembers[1],
              timestamp: '2024-01-20T15:30:00Z',
              attachments: [],
              mentions: ['tm1'],
              reactions: { '👍': ['tm1'], '✅': ['tm1'] }
            }
          ],
          dependencies: [],
          subtasks: [
            {
              id: 'subtask1',
              title: 'Run PageSpeed Insights analysis',
              status: 'completed',
              assignee: mockTeamMembers[1]
            },
            {
              id: 'subtask2',
              title: 'Analyze Core Web Vitals',
              status: 'completed',
              assignee: mockTeamMembers[1]
            }
          ],
          checklist: [
            {
              id: 'check1',
              title: 'Run speed tests on all key pages',
              completed: true,
              assignee: mockTeamMembers[1]
            },
            {
              id: 'check2',
              title: 'Document recommendations',
              completed: true,
              assignee: mockTeamMembers[1]
            }
          ],
          timeTracking: [
            {
              id: 'time1',
              date: '2024-01-15',
              hours: 6,
              description: 'Initial analysis and tool setup',
              billable: true,
              user: mockTeamMembers[1]
            },
            {
              id: 'time2',
              date: '2024-01-16',
              hours: 8,
              description: 'Detailed analysis and report writing',
              billable: true,
              user: mockTeamMembers[1]
            }
          ],
          labels: ['optimization', 'performance'],
          customFields: {
            complexity: 'medium',
            client_priority: 'high'
          },
          createdAt: '2024-01-15T09:00:00Z',
          updatedAt: '2024-01-20T16:00:00Z'
        },
        {
          id: 'task2',
          title: 'Content Gap Analysis',
          description: 'Identify content gaps and opportunities for new content creation',
          projectId: 'proj1',
          projectName: 'E-commerce SEO Overhaul',
          status: 'in_progress',
          priority: 'medium',
          type: 'analysis',
          assignee: mockTeamMembers[2],
          reporter: mockTeamMembers[0],
          startDate: '2024-01-16T00:00:00Z',
          dueDate: '2024-01-25T00:00:00Z',
          estimatedHours: 20,
          actualHours: 12,
          progress: 60,
          tags: ['content', 'research'],
          attachments: [],
          comments: [],
          dependencies: ['task1'],
          subtasks: [
            {
              id: 'subtask3',
              title: 'Analyze competitor content',
              status: 'completed',
              assignee: mockTeamMembers[2]
            },
            {
              id: 'subtask4',
              title: 'Identify content gaps',
              status: 'todo',
              assignee: mockTeamMembers[2]
            }
          ],
          checklist: [
            {
              id: 'check3',
              title: 'Review current content inventory',
              completed: true,
              assignee: mockTeamMembers[2]
            },
            {
              id: 'check4',
              title: 'Competitor content analysis',
              completed: true,
              assignee: mockTeamMembers[2]
            },
            {
              id: 'check5',
              title: 'Document content opportunities',
              completed: false,
              assignee: mockTeamMembers[2]
            }
          ],
          timeTracking: [
            {
              id: 'time3',
              date: '2024-01-16',
              hours: 4,
              description: 'Initial research and planning',
              billable: true,
              user: mockTeamMembers[2]
            },
            {
              id: 'time4',
              date: '2024-01-17',
              hours: 8,
              description: 'Content inventory and competitor analysis',
              billable: true,
              user: mockTeamMembers[2]
            }
          ],
          labels: ['research', 'content-strategy'],
          customFields: {
            complexity: 'high',
            client_priority: 'medium'
          },
          createdAt: '2024-01-16T09:00:00Z',
          updatedAt: '2024-01-17T17:00:00Z'
        },
        {
          id: 'task3',
          title: 'Link Building Strategy',
          description: 'Develop comprehensive link building strategy and identify opportunities',
          projectId: 'proj2',
          projectName: 'Content Marketing Campaign',
          status: 'todo',
          priority: 'high',
          type: 'outreach',
          assignee: mockTeamMembers[3],
          reporter: mockTeamMembers[2],
          startDate: '2024-02-01T00:00:00Z',
          dueDate: '2024-02-10T00:00:00Z',
          estimatedHours: 24,
          actualHours: 0,
          progress: 0,
          tags: ['link-building', 'outreach'],
          attachments: [],
          comments: [],
          dependencies: [],
          subtasks: [],
          checklist: [
            {
              id: 'check6',
              title: 'Research link building opportunities',
              completed: false,
              assignee: mockTeamMembers[3]
            },
            {
              id: 'check7',
              title: 'Create outreach templates',
              completed: false,
              assignee: mockTeamMembers[3]
            }
          ],
          timeTracking: [],
          labels: ['strategy', 'outreach'],
          customFields: {
            complexity: 'high',
            client_priority: 'high'
          },
          createdAt: '2024-01-14T11:00:00Z',
          updatedAt: '2024-01-14T11:00:00Z'
        }
      ])
      
      // Mock notifications
      setNotifications([
        {
          id: 'notif1',
          type: 'task_overdue',
          message: 'Task "Content Gap Analysis" is overdue',
          timestamp: '2024-01-16T14:00:00Z',
          read: false,
          action: 'View Task'
        },
        {
          id: 'notif2',
          type: 'project_milestone',
          message: 'Milestone "Technical Audit Complete" was completed',
          timestamp: '2024-01-16T10:00:00Z',
          read: false,
          action: 'View Project'
        },
        {
          id: 'notif3',
          type: 'team_mention',
          message: 'Sarah Wilson mentioned you in a comment',
          timestamp: '2024-01-16T09:30:00Z',
          read: true,
          action: 'View Comment'
        }
      ])
      
    } catch (error) {
      notifyError('Failed to load project data')
    } finally {
      setIsLoading(false)
    }
  }

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         project.client.company.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = filterStatus === 'all' || project.status === filterStatus
    const matchesPriority = filterPriority === 'all' || project.priority === filterPriority
    return matchesSearch && matchesStatus && matchesPriority
  })

  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         task.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         task.projectName.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = filterStatus === 'all' || task.status === filterStatus
    const matchesPriority = filterPriority === 'all' || task.priority === filterPriority
    const matchesAssignee = filterAssignee === 'all' || task.assignee.id === filterAssignee
    return matchesSearch && matchesStatus && matchesPriority && matchesAssignee
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'completed':
        return 'success'
      case 'planning':
      case 'todo':
        return 'info'
      case 'in_progress':
        return 'warning'
      case 'on_hold':
      case 'blocked':
        return 'secondary'
      case 'cancelled':
        return 'error'
      case 'review':
        return 'warning'
      default:
        return 'secondary'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'error'
      case 'high':
        return 'warning'
      case 'medium':
        return 'info'
      case 'low':
        return 'secondary'
      default:
        return 'secondary'
    }
  }

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'text-green-600 dark:text-green-400'
    if (progress >= 50) return 'text-yellow-600 dark:text-yellow-400'
    return 'text-blue-600 dark:text-blue-400'
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const formatTimeAgo = (dateString: string) => {
    const diff = Date.now() - new Date(dateString).getTime()
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    
    if (days > 0) return `${days}d ago`
    if (hours > 0) return `${hours}h ago`
    return 'Just now'
  }

  const ProjectCard = ({ project }: { project: ProjectData }) => (
    <Card 
      className={`p-6 cursor-pointer transition-all hover:shadow-lg ${
        selectedProject?.id === project.id ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : ''
      }`}
      onClick={() => {
        setSelectedProject(project)
        onProjectSelect?.(project)
      }}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
            <FolderIcon className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-gray-100">
              {project.name}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {project.client.company}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant={getStatusColor(project.status)}>
            {project.status.replace('_', ' ')}
          </Badge>
          <Badge variant={getPriorityColor(project.priority)}>
            {project.priority}
          </Badge>
        </div>
      </div>
      
      <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
        {project.description}
      </p>
      
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-500">Progress</span>
          <span className={`text-sm font-medium ${getProgressColor(project.progress)}`}>
            {project.progress}%
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all"
            style={{ width: `${project.progress}%` }}
          />
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="text-xs text-gray-500">Budget</div>
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {formatCurrency(project.spent)} / {formatCurrency(project.budget)}
            </div>
          </div>
          <div>
            <div className="text-xs text-gray-500">Due Date</div>
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {formatDate(project.endDate)}
            </div>
          </div>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="flex -space-x-2">
              {project.team.slice(0, 3).map((member, index) => (
                <div
                  key={member.id}
                  className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center border-2 border-white dark:border-gray-800"
                  title={member.name}
                >
                  <span className="text-xs text-gray-700 dark:text-gray-300">
                    {member.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
              ))}
              {project.team.length > 3 && (
                <div className="w-6 h-6 bg-gray-400 dark:bg-gray-500 rounded-full flex items-center justify-center border-2 border-white dark:border-gray-800">
                  <span className="text-xs text-white">+{project.team.length - 3}</span>
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-4 text-xs text-gray-500">
            <span>{project.tasks.completed}/{project.tasks.total} tasks</span>
            <span>{formatTimeAgo(project.lastActivity)}</span>
          </div>
        </div>
      </div>
    </Card>
  )

  const TaskCard = ({ task }: { task: TaskData }) => (
    <Card 
      className={`p-4 cursor-pointer transition-all hover:shadow-md ${
        selectedTask?.id === task.id ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : ''
      }`}
      onClick={() => {
        setSelectedTask(task)
        onTaskSelect?.(task)
      }}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Badge variant={getStatusColor(task.status)} size="sm">
            {task.status.replace('_', ' ')}
          </Badge>
          <Badge variant={getPriorityColor(task.priority)} size="sm">
            {task.priority}
          </Badge>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
            <span className="text-xs text-gray-700 dark:text-gray-300">
              {task.assignee.name.split(' ').map(n => n[0]).join('')}
            </span>
          </div>
        </div>
      </div>
      
      <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
        {task.title}
      </h4>
      
      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
        {task.description}
      </p>
      
      <div className="space-y-2">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-500">Progress</span>
          <span className={`font-medium ${getProgressColor(task.progress)}`}>
            {task.progress}%
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
          <div
            className="bg-blue-500 h-1 rounded-full transition-all"
            style={{ width: `${task.progress}%` }}
          />
        </div>
        
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>{task.projectName}</span>
          <span>Due: {formatDate(task.dueDate)}</span>
        </div>
      </div>
    </Card>
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading project data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Project Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage projects, tasks, and team collaboration
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <div className="relative">
            <Button
              variant="outline"
              size="sm"
              className="relative"
            >
              <BellIcon className="h-4 w-4 mr-2" />
              Notifications
              {notifications.filter(n => !n.read).length > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {notifications.filter(n => !n.read).length}
                </span>
              )}
            </Button>
          </div>
          <Button
            variant="outline"
            onClick={() => setShowCreateTask(true)}
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            New Task
          </Button>
          <Button
            onClick={() => setShowCreateProject(true)}
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            New Project
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center">
            <FolderIcon className="h-8 w-8 text-blue-600 dark:text-blue-400 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Active Projects</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {projects.filter(p => p.status === 'active').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center">
            <CheckCircleIcon className="h-8 w-8 text-green-600 dark:text-green-400 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Completed Tasks</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {tasks.filter(t => t.status === 'completed').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center">
            <UserGroupIcon className="h-8 w-8 text-purple-600 dark:text-purple-400 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Team Members</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {teamMembers.length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center">
            <ExclamationTriangleIcon className="h-8 w-8 text-orange-600 dark:text-orange-400 mr-3" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Overdue Tasks</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {tasks.filter(t => new Date(t.dueDate) < new Date() && t.status !== 'completed').length}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <div className="flex space-x-8">
          <button
            onClick={() => setActiveTab('projects')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'projects'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <FolderIcon className="h-4 w-4 inline mr-2" />
            Projects ({projects.length})
          </button>
          <button
            onClick={() => setActiveTab('tasks')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'tasks'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <CheckCircleIcon className="h-4 w-4 inline mr-2" />
            Tasks ({tasks.length})
          </button>
          <button
            onClick={() => setActiveTab('team')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'team'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <UserGroupIcon className="h-4 w-4 inline mr-2" />
            Team ({teamMembers.length})
          </button>
          <button
            onClick={() => setActiveTab('timeline')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'timeline'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <CalendarIcon className="h-4 w-4 inline mr-2" />
            Timeline
          </button>
          <button
            onClick={() => setActiveTab('reports')}
            className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'reports'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <ChartBarIcon className="h-4 w-4 inline mr-2" />
            Reports
          </button>
        </div>
      </div>

      {/* Projects Tab */}
      {activeTab === 'projects' && (
        <div className="space-y-4">
          {/* Filters */}
          <Card className="p-4">
            <div className="flex items-center space-x-4">
              <div className="flex-1 relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search projects..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="planning">Planning</option>
                <option value="on_hold">On Hold</option>
                <option value="completed">Completed</option>
              </select>
              <select
                value={filterPriority}
                onChange={(e) => setFilterPriority(e.target.value)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
              >
                <option value="all">All Priority</option>
                <option value="urgent">Urgent</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
              <div className="flex items-center border border-gray-300 dark:border-gray-600 rounded-lg">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 ${viewMode === 'grid' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'text-gray-500'}`}
                >
                  <Bars3Icon className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('table')}
                  className={`p-2 ${viewMode === 'table' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'text-gray-500'}`}
                >
                  <TableCellsIcon className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('kanban')}
                  className={`p-2 ${viewMode === 'kanban' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'text-gray-500'}`}
                >
                  <KanbanIcon className="h-4 w-4" />
                </button>
              </div>
            </div>
          </Card>

          {/* Projects Grid */}
          {viewMode === 'grid' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredProjects.map((project) => (
                <ProjectCard key={project.id} project={project} />
              ))}
            </div>
          )}

          {/* Projects Table */}
          {viewMode === 'table' && (
            <Card className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">Project</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">Client</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">Status</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">Priority</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">Progress</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">Budget</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">Due Date</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                    {filteredProjects.map((project) => (
                      <tr key={project.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="py-3 px-4">
                          <div className="font-medium text-gray-900 dark:text-gray-100">
                            {project.name}
                          </div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {project.description}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="text-sm text-gray-900 dark:text-gray-100">
                            {project.client.company}
                          </div>
                          <div className="text-sm text-gray-500">
                            {project.client.name}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <Badge variant={getStatusColor(project.status)}>
                            {project.status.replace('_', ' ')}
                          </Badge>
                        </td>
                        <td className="py-3 px-4">
                          <Badge variant={getPriorityColor(project.priority)}>
                            {project.priority}
                          </Badge>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-2">
                            <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                              <div
                                className="bg-blue-500 h-2 rounded-full"
                                style={{ width: `${project.progress}%` }}
                              />
                            </div>
                            <span className="text-sm text-gray-600 dark:text-gray-400">
                              {project.progress}%
                            </span>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="text-sm text-gray-900 dark:text-gray-100">
                            {formatCurrency(project.spent)}
                          </div>
                          <div className="text-sm text-gray-500">
                            / {formatCurrency(project.budget)}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="text-sm text-gray-900 dark:text-gray-100">
                            {formatDate(project.endDate)}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedProject(project)
                                setShowProjectDetails(true)
                              }}
                            >
                              <EyeIcon className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                            >
                              <PencilIcon className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Card>
          )}

          {/* Kanban Board */}
          {viewMode === 'kanban' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {['planning', 'active', 'on_hold', 'completed'].map((status) => (
                <Card key={status} className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-medium text-gray-900 dark:text-gray-100 capitalize">
                      {status.replace('_', ' ')}
                    </h3>
                    <Badge variant="secondary">
                      {filteredProjects.filter(p => p.status === status).length}
                    </Badge>
                  </div>
                  <div className="space-y-3">
                    {filteredProjects
                      .filter(project => project.status === status)
                      .map((project) => (
                        <div
                          key={project.id}
                          className="p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 cursor-pointer hover:shadow-md transition-shadow"
                          onClick={() => {
                            setSelectedProject(project)
                            onProjectSelect?.(project)
                          }}
                        >
                          <div className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                            {project.name}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                            {project.client.company}
                          </div>
                          <div className="flex items-center justify-between">
                            <Badge variant={getPriorityColor(project.priority)} size="sm">
                              {project.priority}
                            </Badge>
                            <div className="flex -space-x-1">
                              {project.team.slice(0, 2).map((member) => (
                                <div
                                  key={member.id}
                                  className="w-5 h-5 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center border border-white dark:border-gray-800"
                                >
                                  <span className="text-xs text-gray-700 dark:text-gray-300">
                                    {member.name.split(' ').map(n => n[0]).join('')}
                                  </span>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Tasks Tab */}
      {activeTab === 'tasks' && (
        <div className="space-y-4">
          {/* Filters */}
          <Card className="p-4">
            <div className="flex items-center space-x-4">
              <div className="flex-1 relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search tasks..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
              >
                <option value="all">All Status</option>
                <option value="todo">To Do</option>
                <option value="in_progress">In Progress</option>
                <option value="review">Review</option>
                <option value="completed">Completed</option>
                <option value="blocked">Blocked</option>
              </select>
              <select
                value={filterPriority}
                onChange={(e) => setFilterPriority(e.target.value)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
              >
                <option value="all">All Priority</option>
                <option value="urgent">Urgent</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
              <select
                value={filterAssignee}
                onChange={(e) => setFilterAssignee(e.target.value)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
              >
                <option value="all">All Assignees</option>
                {teamMembers.map((member) => (
                  <option key={member.id} value={member.id}>
                    {member.name}
                  </option>
                ))}
              </select>
            </div>
          </Card>

          {/* Tasks Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTasks.map((task) => (
              <TaskCard key={task.id} task={task} />
            ))}
          </div>
        </div>
      )}

      {/* Team Tab */}
      {activeTab === 'team' && (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {teamMembers.map((member) => (
              <Card key={member.id} className="p-4">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                    <span className="text-white font-medium">
                      {member.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <div className="font-medium text-gray-900 dark:text-gray-100">
                      {member.name}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {member.role} • {member.department}
                    </div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Workload</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {member.workload}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        member.workload > 80 ? 'bg-red-500' : 
                        member.workload > 60 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${member.workload}%` }}
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="text-gray-500">Tasks:</span>
                      <span className="ml-1 font-medium text-gray-900 dark:text-gray-100">
                        {member.performance.tasksCompleted}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500">Rating:</span>
                      <span className="ml-1 font-medium text-gray-900 dark:text-gray-100">
                        {member.performance.averageRating}/5
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Badge variant={
                      member.availability === 'available' ? 'success' :
                      member.availability === 'busy' ? 'warning' : 'secondary'
                    } size="sm">
                      {member.availability}
                    </Badge>
                    <span className="text-sm text-gray-500">
                      ${member.hourlyRate}/hr
                    </span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Timeline Tab */}
      {activeTab === 'timeline' && (
        <Card className="p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">
            Project Timeline
          </h3>
          <div className="space-y-4">
            {projects.map((project) => (
              <div key={project.id} className="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="w-4 h-4 bg-blue-500 rounded-full flex-shrink-0"></div>
                <div className="flex-1">
                  <div className="font-medium text-gray-900 dark:text-gray-100">
                    {project.name}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {formatDate(project.startDate)} - {formatDate(project.endDate)}
                  </div>
                </div>
                <div className="text-right">
                  <Badge variant={getStatusColor(project.status)}>
                    {project.status.replace('_', ' ')}
                  </Badge>
                  <div className="text-sm text-gray-500 mt-1">
                    {project.progress}% complete
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Reports Tab */}
      {activeTab === 'reports' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Project Status Distribution
            </h3>
            <div className="space-y-3">
              {['active', 'planning', 'completed', 'on_hold'].map((status) => (
                <div key={status} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                    {status.replace('_', ' ')}
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full">
                      <div
                        className="h-2 bg-blue-500 rounded-full"
                        style={{
                          width: `${(projects.filter(p => p.status === status).length / projects.length) * 100}%`
                        }}
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {projects.filter(p => p.status === status).length}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </Card>
          
          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Team Performance
            </h3>
            <div className="space-y-3">
              {teamMembers.map((member) => (
                <div key={member.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                      <span className="text-xs text-gray-700 dark:text-gray-300">
                        {member.name.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {member.name}
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {member.performance.averageRating}/5
                    </div>
                    <div className="text-xs text-gray-500">
                      {member.performance.tasksCompleted} tasks
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
          
          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Budget Overview
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Total Budget</span>
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {formatCurrency(projects.reduce((acc, p) => acc + p.budget, 0))}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Total Spent</span>
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {formatCurrency(projects.reduce((acc, p) => acc + p.spent, 0))}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Remaining</span>
                <span className="text-sm font-medium text-green-600 dark:text-green-400">
                  {formatCurrency(projects.reduce((acc, p) => acc + (p.budget - p.spent), 0))}
                </span>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  )
}