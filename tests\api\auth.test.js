/**
 * Authentication API Tests
 * 
 * Tests for authentication endpoints including login, registration,
 * password reset, and JWT token management.
 */

const request = require('supertest');
const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const jwt = require('jsonwebtoken');

// Initialize Next.js app for testing
const app = next({ dev: false, quiet: true });
const handle = app.getRequestHandler();

let server;

beforeAll(async () => {
  await app.prepare();
  server = createServer((req, res) => {
    const parsedUrl = parse(req.url, true);
    handle(req, res, parsedUrl);
  });
});

afterAll(async () => {
  if (server) {
    server.close();
  }
  await app.close();
});

describe('Authentication API Endpoints', () => {
  const { testUsers, getAuthHeaders, expectValidationError, expectAuthError } = global.testHelpers;

  describe('POST /api/auth/register', () => {
    const registerData = {
      email: '<EMAIL>',
      password: 'SecurePassword123!',
      name: 'New User'
    };

    test('should register a new user successfully', async () => {
      const response = await request(server)
        .post('/api/auth/register')
        .send(registerData)
        .expect(201);

      expect(response.body).toHaveProperty('user');
      expect(response.body).toHaveProperty('token');
      expect(response.body.user.email).toBe(registerData.email);
      expect(response.body.user.name).toBe(registerData.name);
      expect(response.body.user).not.toHaveProperty('password');
    });

    test('should validate required fields', async () => {
      const response = await request(server)
        .post('/api/auth/register')
        .send({})
        .expect(400);

      expectValidationError(response, 'email');
      expectValidationError(response, 'password');
      expectValidationError(response, 'name');
    });

    test('should validate email format', async () => {
      const response = await request(server)
        .post('/api/auth/register')
        .send({
          ...registerData,
          email: 'invalid-email'
        })
        .expect(400);

      expectValidationError(response, 'email');
    });

    test('should validate password strength', async () => {
      const weakPasswords = ['123', 'password', 'abc123'];

      for (const password of weakPasswords) {
        const response = await request(server)
          .post('/api/auth/register')
          .send({
            ...registerData,
            password
          })
          .expect(400);

        expectValidationError(response, 'password');
      }
    });

    test('should prevent duplicate email registration', async () => {
      // Register first user
      await request(server)
        .post('/api/auth/register')
        .send(registerData)
        .expect(201);

      // Try to register with same email
      const response = await request(server)
        .post('/api/auth/register')
        .send(registerData)
        .expect(409);

      expect(response.body.error).toContain('already exists');
    });

    test('should sanitize input data', async () => {
      const maliciousData = {
        email: '<EMAIL>',
        password: 'SecurePassword123!',
        name: '<script>alert("XSS")</script>Malicious User'
      };

      const response = await request(server)
        .post('/api/auth/register')
        .send(maliciousData)
        .expect(201);

      expect(response.body.user.name).not.toContain('<script>');
      expect(response.body.user.name).toBe('Malicious User');
    });

    test('should rate limit registration attempts', async () => {
      const requests = [];
      
      // Make multiple registration attempts
      for (let i = 0; i < 10; i++) {
        requests.push(
          request(server)
            .post('/api/auth/register')
            .send({
              email: `test${i}@example.com`,
              password: 'SecurePassword123!',
              name: `Test User ${i}`
            })
        );
      }

      const responses = await Promise.all(requests);
      
      // Some requests should be rate limited
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('POST /api/auth/login', () => {
    const loginData = {
      email: testUsers.user1.email,
      password: testUsers.user1.password
    };

    test('should login with valid credentials', async () => {
      const response = await request(server)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body).toHaveProperty('user');
      expect(response.body).toHaveProperty('token');
      expect(response.body.user.email).toBe(loginData.email);
      expect(response.body.user).not.toHaveProperty('password');

      // Verify JWT token
      const decoded = jwt.verify(response.body.token, process.env.JWT_SECRET);
      expect(decoded.email).toBe(loginData.email);
    });

    test('should reject invalid credentials', async () => {
      const response = await request(server)
        .post('/api/auth/login')
        .send({
          email: testUsers.user1.email,
          password: 'wrongpassword'
        })
        .expect(401);

      expect(response.body.error).toContain('Invalid credentials');
    });

    test('should reject non-existent user', async () => {
      const response = await request(server)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'anypassword'
        })
        .expect(401);

      expect(response.body.error).toContain('Invalid credentials');
    });

    test('should validate required fields', async () => {
      const response = await request(server)
        .post('/api/auth/login')
        .send({})
        .expect(400);

      expectValidationError(response, 'email');
      expectValidationError(response, 'password');
    });

    test('should rate limit login attempts', async () => {
      const requests = [];
      
      // Make multiple failed login attempts
      for (let i = 0; i < 10; i++) {
        requests.push(
          request(server)
            .post('/api/auth/login')
            .send({
              email: testUsers.user1.email,
              password: 'wrongpassword'
            })
        );
      }

      const responses = await Promise.all(requests);
      
      // Some requests should be rate limited
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });

    test('should handle remember me functionality', async () => {
      const response = await request(server)
        .post('/api/auth/login')
        .send({
          ...loginData,
          rememberMe: true
        })
        .expect(200);

      // Should have longer token expiry for remember me
      const decoded = jwt.verify(response.body.token, process.env.JWT_SECRET);
      const expirationTime = decoded.exp - decoded.iat;
      expect(expirationTime).toBeGreaterThan(3600); // More than 1 hour
    });
  });

  describe('POST /api/auth/logout', () => {
    test('should logout successfully with valid token', async () => {
      const response = await request(server)
        .post('/api/auth/logout')
        .set(getAuthHeaders(testUsers.user1))
        .expect(200);

      expect(response.body.message).toContain('Logged out successfully');
    });

    test('should require authentication', async () => {
      const response = await request(server)
        .post('/api/auth/logout')
        .expect(401);

      expectAuthError(response);
    });

    test('should handle invalid token', async () => {
      const response = await request(server)
        .post('/api/auth/logout')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expectAuthError(response);
    });
  });

  describe('POST /api/auth/refresh', () => {
    test('should refresh valid token', async () => {
      const response = await request(server)
        .post('/api/auth/refresh')
        .set(getAuthHeaders(testUsers.user1))
        .expect(200);

      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('user');

      // Verify new token
      const decoded = jwt.verify(response.body.token, process.env.JWT_SECRET);
      expect(decoded.email).toBe(testUsers.user1.email);
    });

    test('should require authentication', async () => {
      const response = await request(server)
        .post('/api/auth/refresh')
        .expect(401);

      expectAuthError(response);
    });

    test('should reject expired token', async () => {
      // Create expired token
      const expiredToken = jwt.sign(
        {
          sub: testUsers.user1.id,
          email: testUsers.user1.email,
          exp: Math.floor(Date.now() / 1000) - 3600 // 1 hour ago
        },
        process.env.JWT_SECRET
      );

      const response = await request(server)
        .post('/api/auth/refresh')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(401);

      expectAuthError(response);
    });
  });

  describe('POST /api/auth/forgot-password', () => {
    test('should initiate password reset', async () => {
      const response = await request(server)
        .post('/api/auth/forgot-password')
        .send({
          email: testUsers.user1.email
        })
        .expect(200);

      expect(response.body.message).toContain('Password reset email sent');
    });

    test('should validate email format', async () => {
      const response = await request(server)
        .post('/api/auth/forgot-password')
        .send({
          email: 'invalid-email'
        })
        .expect(400);

      expectValidationError(response, 'email');
    });

    test('should handle non-existent email gracefully', async () => {
      // Should not reveal whether email exists
      const response = await request(server)
        .post('/api/auth/forgot-password')
        .send({
          email: '<EMAIL>'
        })
        .expect(200);

      expect(response.body.message).toContain('Password reset email sent');
    });

    test('should rate limit password reset requests', async () => {
      const requests = [];
      
      // Make multiple password reset requests
      for (let i = 0; i < 10; i++) {
        requests.push(
          request(server)
            .post('/api/auth/forgot-password')
            .send({
              email: testUsers.user1.email
            })
        );
      }

      const responses = await Promise.all(requests);
      
      // Some requests should be rate limited
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('POST /api/auth/reset-password', () => {
    let resetToken;

    beforeEach(() => {
      // Generate reset token
      resetToken = jwt.sign(
        {
          sub: testUsers.user1.id,
          email: testUsers.user1.email,
          type: 'password-reset',
          exp: Math.floor(Date.now() / 1000) + 3600 // 1 hour
        },
        process.env.JWT_SECRET
      );
    });

    test('should reset password with valid token', async () => {
      const newPassword = 'NewSecurePassword123!';

      const response = await request(server)
        .post('/api/auth/reset-password')
        .send({
          token: resetToken,
          password: newPassword,
          confirmPassword: newPassword
        })
        .expect(200);

      expect(response.body.message).toContain('Password reset successful');
    });

    test('should validate password strength', async () => {
      const response = await request(server)
        .post('/api/auth/reset-password')
        .send({
          token: resetToken,
          password: 'weak',
          confirmPassword: 'weak'
        })
        .expect(400);

      expectValidationError(response, 'password');
    });

    test('should validate password confirmation', async () => {
      const response = await request(server)
        .post('/api/auth/reset-password')
        .send({
          token: resetToken,
          password: 'NewSecurePassword123!',
          confirmPassword: 'DifferentPassword123!'
        })
        .expect(400);

      expectValidationError(response, 'password');
    });

    test('should reject invalid token', async () => {
      const response = await request(server)
        .post('/api/auth/reset-password')
        .send({
          token: 'invalid-token',
          password: 'NewSecurePassword123!',
          confirmPassword: 'NewSecurePassword123!'
        })
        .expect(400);

      expect(response.body.error).toContain('Invalid or expired token');
    });

    test('should reject expired token', async () => {
      const expiredToken = jwt.sign(
        {
          sub: testUsers.user1.id,
          email: testUsers.user1.email,
          type: 'password-reset',
          exp: Math.floor(Date.now() / 1000) - 3600 // 1 hour ago
        },
        process.env.JWT_SECRET
      );

      const response = await request(server)
        .post('/api/auth/reset-password')
        .send({
          token: expiredToken,
          password: 'NewSecurePassword123!',
          confirmPassword: 'NewSecurePassword123!'
        })
        .expect(400);

      expect(response.body.error).toContain('Invalid or expired token');
    });

    test('should reject wrong token type', async () => {
      const wrongToken = jwt.sign(
        {
          sub: testUsers.user1.id,
          email: testUsers.user1.email,
          type: 'access-token',
          exp: Math.floor(Date.now() / 1000) + 3600
        },
        process.env.JWT_SECRET
      );

      const response = await request(server)
        .post('/api/auth/reset-password')
        .send({
          token: wrongToken,
          password: 'NewSecurePassword123!',
          confirmPassword: 'NewSecurePassword123!'
        })
        .expect(400);

      expect(response.body.error).toContain('Invalid or expired token');
    });
  });

  describe('GET /api/auth/me', () => {
    test('should return current user with valid token', async () => {
      const response = await request(server)
        .get('/api/auth/me')
        .set(getAuthHeaders(testUsers.user1))
        .expect(200);

      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(testUsers.user1.email);
      expect(response.body.user).not.toHaveProperty('password');
    });

    test('should require authentication', async () => {
      const response = await request(server)
        .get('/api/auth/me')
        .expect(401);

      expectAuthError(response);
    });

    test('should handle invalid token', async () => {
      const response = await request(server)
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expectAuthError(response);
    });
  });

  describe('PUT /api/auth/me', () => {
    test('should update user profile', async () => {
      const updateData = {
        name: 'Updated Name',
        email: '<EMAIL>'
      };

      const response = await request(server)
        .put('/api/auth/me')
        .set(getAuthHeaders(testUsers.user1))
        .send(updateData)
        .expect(200);

      expect(response.body.user.name).toBe(updateData.name);
      expect(response.body.user.email).toBe(updateData.email);
    });

    test('should require authentication', async () => {
      const response = await request(server)
        .put('/api/auth/me')
        .send({
          name: 'Updated Name'
        })
        .expect(401);

      expectAuthError(response);
    });

    test('should validate email format', async () => {
      const response = await request(server)
        .put('/api/auth/me')
        .set(getAuthHeaders(testUsers.user1))
        .send({
          email: 'invalid-email'
        })
        .expect(400);

      expectValidationError(response, 'email');
    });

    test('should prevent duplicate email', async () => {
      const response = await request(server)
        .put('/api/auth/me')
        .set(getAuthHeaders(testUsers.user1))
        .send({
          email: testUsers.admin.email
        })
        .expect(409);

      expect(response.body.error).toContain('already exists');
    });

    test('should sanitize input data', async () => {
      const maliciousData = {
        name: '<script>alert("XSS")</script>Updated Name'
      };

      const response = await request(server)
        .put('/api/auth/me')
        .set(getAuthHeaders(testUsers.user1))
        .send(maliciousData)
        .expect(200);

      expect(response.body.user.name).not.toContain('<script>');
      expect(response.body.user.name).toBe('Updated Name');
    });
  });

  describe('POST /api/auth/change-password', () => {
    test('should change password with valid current password', async () => {
      const passwordData = {
        currentPassword: testUsers.user1.password,
        newPassword: 'NewSecurePassword123!',
        confirmPassword: 'NewSecurePassword123!'
      };

      const response = await request(server)
        .post('/api/auth/change-password')
        .set(getAuthHeaders(testUsers.user1))
        .send(passwordData)
        .expect(200);

      expect(response.body.message).toContain('Password changed successfully');
    });

    test('should require authentication', async () => {
      const response = await request(server)
        .post('/api/auth/change-password')
        .send({
          currentPassword: 'current',
          newPassword: 'new',
          confirmPassword: 'new'
        })
        .expect(401);

      expectAuthError(response);
    });

    test('should validate current password', async () => {
      const response = await request(server)
        .post('/api/auth/change-password')
        .set(getAuthHeaders(testUsers.user1))
        .send({
          currentPassword: 'wrongpassword',
          newPassword: 'NewSecurePassword123!',
          confirmPassword: 'NewSecurePassword123!'
        })
        .expect(400);

      expect(response.body.error).toContain('Current password is incorrect');
    });

    test('should validate new password strength', async () => {
      const response = await request(server)
        .post('/api/auth/change-password')
        .set(getAuthHeaders(testUsers.user1))
        .send({
          currentPassword: testUsers.user1.password,
          newPassword: 'weak',
          confirmPassword: 'weak'
        })
        .expect(400);

      expectValidationError(response, 'password');
    });

    test('should validate password confirmation', async () => {
      const response = await request(server)
        .post('/api/auth/change-password')
        .set(getAuthHeaders(testUsers.user1))
        .send({
          currentPassword: testUsers.user1.password,
          newPassword: 'NewSecurePassword123!',
          confirmPassword: 'DifferentPassword123!'
        })
        .expect(400);

      expectValidationError(response, 'password');
    });

    test('should prevent same password reuse', async () => {
      const response = await request(server)
        .post('/api/auth/change-password')
        .set(getAuthHeaders(testUsers.user1))
        .send({
          currentPassword: testUsers.user1.password,
          newPassword: testUsers.user1.password,
          confirmPassword: testUsers.user1.password
        })
        .expect(400);

      expect(response.body.error).toContain('New password must be different');
    });
  });

  describe('DELETE /api/auth/me', () => {
    test('should delete user account', async () => {
      const response = await request(server)
        .delete('/api/auth/me')
        .set(getAuthHeaders(testUsers.user1))
        .send({
          password: testUsers.user1.password
        })
        .expect(200);

      expect(response.body.message).toContain('Account deleted successfully');
    });

    test('should require authentication', async () => {
      const response = await request(server)
        .delete('/api/auth/me')
        .send({
          password: 'password'
        })
        .expect(401);

      expectAuthError(response);
    });

    test('should validate password', async () => {
      const response = await request(server)
        .delete('/api/auth/me')
        .set(getAuthHeaders(testUsers.user1))
        .send({
          password: 'wrongpassword'
        })
        .expect(400);

      expect(response.body.error).toContain('Password is incorrect');
    });

    test('should require password confirmation', async () => {
      const response = await request(server)
        .delete('/api/auth/me')
        .set(getAuthHeaders(testUsers.user1))
        .send({})
        .expect(400);

      expectValidationError(response, 'password');
    });
  });
});

// Helper function to verify JWT token structure
function verifyTokenStructure(token) {
  const decoded = jwt.decode(token, { complete: true });
  
  expect(decoded).toHaveProperty('header');
  expect(decoded).toHaveProperty('payload');
  expect(decoded.header.alg).toBe('HS256');
  expect(decoded.payload).toHaveProperty('sub');
  expect(decoded.payload).toHaveProperty('email');
  expect(decoded.payload).toHaveProperty('iat');
  expect(decoded.payload).toHaveProperty('exp');
  
  return decoded;
}

module.exports = {
  verifyTokenStructure
};