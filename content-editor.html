<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Advanced content editor with live SEO optimization, real-time suggestions, and authority link integration.">
    <title>Content Editor - SEO SAAS</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/forms.css">
    <link rel="stylesheet" href="css/data-visualization.css">
    <link rel="stylesheet" href="css/animations.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
</head>
<body class="dashboard-layout editor-layout">
    <!-- Dashboard App Container -->
    <div class="dashboard-app">
        <!-- Global Top Navigation -->
        <header class="top-navigation">
            <div class="nav-left">
                <button class="sidebar-toggle" aria-label="Toggle sidebar" onclick="toggleSidebar()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
                <div class="brand-section">
                    <svg class="brand-logo w-8 h-8 text-primary-600" viewBox="0 0 40 40" fill="currentColor">
                        <path d="M20 5L5 15v10l15 10 15-10V15L20 5z"/>
                        <path d="M20 15l-10 6v8l10 6 10-6v-8l-10-6z" fill="white" opacity="0.5"/>
                    </svg>
                    <span class="brand-name">SEO SAAS</span>
                    <span class="environment-badge">Pro</span>
                </div>
            </div>
            
            <div class="nav-center">
                <div class="editor-controls">
                    <button class="editor-btn" onclick="togglePreview()" id="previewBtn">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        Preview
                    </button>
                    <button class="editor-btn" onclick="toggleSidePanel()" id="sidePanelBtn">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        SEO Panel
                    </button>
                    <div class="word-count-display">
                        <span id="currentWordCount">0</span> / <span id="targetWordCount">2,500</span> words
                    </div>
                </div>
            </div>
            
            <div class="nav-right">
                <button class="quick-action-btn" onclick="saveContent()" id="saveBtn">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <span>Save</span>
                </button>
                
                <button class="quick-action-btn btn-primary" onclick="publishContent()">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                    <span>Publish</span>
                </button>
                
                <div class="user-menu-dropdown">
                    <button class="user-menu-btn">
                        <div class="user-avatar">
                            <span>JD</span>
                        </div>
                        <div class="user-info">
                            <span class="user-name">John Doe</span>
                            <span class="user-plan">Professional</span>
                        </div>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Editor Layout -->
        <div class="editor-main">
            <!-- Content Editor Area -->
            <div class="editor-container">
                <!-- Editor Header -->
                <div class="editor-header">
                    <div class="content-meta">
                        <h1 class="content-title" contenteditable="true" placeholder="Enter your title here..." onkeyup="updateSEOAnalysis()">
                            Complete Guide to Digital Marketing Services: Boost Your Business Growth
                        </h1>
                        <div class="content-info">
                            <span class="content-keyword">Target: digital marketing services</span>
                            <span class="content-separator">•</span>
                            <span class="content-type">Service Page</span>
                            <span class="content-separator">•</span>
                            <span class="last-saved">Saved 2 minutes ago</span>
                        </div>
                    </div>
                    
                    <div class="seo-score-display">
                        <div class="score-circle" id="seoScoreCircle">
                            <div class="score-number" id="seoScore">96</div>
                            <div class="score-label">SEO Score</div>
                        </div>
                    </div>
                </div>
                
                <!-- Content Editor -->
                <div class="content-editor" id="contentEditor">
                    <div class="editor-toolbar">
                        <div class="toolbar-group">
                            <button class="toolbar-btn" onclick="formatText('bold')" title="Bold">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 4h8a4 4 0 014 4 4 4 0 01-4 4H6z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 12h9a4 4 0 014 4 4 4 0 01-4 4H6z"></path>
                                </svg>
                            </button>
                            <button class="toolbar-btn" onclick="formatText('italic')" title="Italic">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 4l4 16"></path>
                                </svg>
                            </button>
                            <button class="toolbar-btn" onclick="formatText('underline')" title="Underline">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18h12"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 4v8a6 6 0 0012 0V4"></path>
                                </svg>
                            </button>
                        </div>
                        
                        <div class="toolbar-separator"></div>
                        
                        <div class="toolbar-group">
                            <button class="toolbar-btn" onclick="formatHeading('h2')" title="H2 Heading">
                                <span class="text-sm font-semibold">H2</span>
                            </button>
                            <button class="toolbar-btn" onclick="formatHeading('h3')" title="H3 Heading">
                                <span class="text-sm font-semibold">H3</span>
                            </button>
                            <button class="toolbar-btn" onclick="formatHeading('h4')" title="H4 Heading">
                                <span class="text-sm font-semibold">H4</span>
                            </button>
                        </div>
                        
                        <div class="toolbar-separator"></div>
                        
                        <div class="toolbar-group">
                            <button class="toolbar-btn" onclick="insertList('ul')" title="Bullet List">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                </svg>
                            </button>
                            <button class="toolbar-btn" onclick="insertList('ol')" title="Numbered List">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </button>
                        </div>
                        
                        <div class="toolbar-separator"></div>
                        
                        <div class="toolbar-group">
                            <button class="toolbar-btn" onclick="insertLink()" title="Insert Link">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                </svg>
                            </button>
                            <button class="toolbar-btn" onclick="insertAuthorityLink()" title="Authority Link">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                                </svg>
                            </button>
                        </div>
                        
                        <div class="toolbar-separator"></div>
                        
                        <div class="toolbar-group">
                            <button class="toolbar-btn" onclick="optimizeContent()" title="AI Optimize">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <div class="editor-content" contenteditable="true" id="editorContent" onkeyup="updateSEOAnalysis()" onpaste="handlePaste(event)">
                        <h2>What Are Digital Marketing Services?</h2>
                        <p>Digital marketing services encompass a comprehensive range of online marketing strategies designed to help businesses reach, engage, and convert their target audience through digital channels. These services have become essential for companies looking to establish a strong online presence and drive sustainable growth in today's competitive marketplace.</p>
                        
                        <h3>Core Components of Digital Marketing</h3>
                        <p>Professional digital marketing services typically include search engine optimization (SEO), pay-per-click advertising (PPC), social media marketing, content marketing, email marketing, and conversion rate optimization. Each component works synergistically to create a cohesive marketing strategy that maximizes your return on investment.</p>
                        
                        <h2>Benefits of Professional Digital Marketing Services</h2>
                        <p>Investing in professional digital marketing services provides numerous advantages for businesses of all sizes. These services offer expertise, scalability, and measurable results that can significantly impact your bottom line.</p>
                        
                        <h3>Increased Online Visibility</h3>
                        <p>Digital marketing services help improve your online visibility across multiple channels, ensuring your brand appears where your customers are searching. This increased visibility translates to more qualified leads and higher conversion rates.</p>
                        
                        <h3>Targeted Audience Reach</h3>
                        <p>Advanced targeting capabilities allow digital marketing services to reach specific demographics, interests, and behaviors, ensuring your marketing budget is spent efficiently on prospects most likely to convert.</p>
                        
                        <h2>Choosing the Right Digital Marketing Service Provider</h2>
                        <p>Selecting the appropriate digital marketing service provider is crucial for achieving your business objectives. Consider factors such as experience, industry expertise, transparent reporting, and proven track record of success.</p>
                        
                        <p>For comprehensive insights into digital marketing strategies, refer to authoritative sources like <a href="https://en.wikipedia.org/wiki/Digital_marketing" target="_blank">Wikipedia's Digital Marketing guide</a> which provides detailed information about various digital marketing methodologies.</p>
                    </div>
                </div>
            </div>

            <!-- SEO Sidebar Panel -->
            <div class="seo-sidebar" id="seoSidebar">
                <div class="sidebar-header">
                    <h3>SEO Optimization</h3>
                    <button class="sidebar-close" onclick="toggleSidePanel()">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <div class="sidebar-content">
                    <!-- Real-time Metrics -->
                    <div class="metrics-section">
                        <h4>Live Metrics</h4>
                        <div class="metrics-grid">
                            <div class="metric-item">
                                <div class="metric-value" id="wordCountMetric">847</div>
                                <div class="metric-label">Words</div>
                                <div class="metric-status good">Good length</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value" id="keywordDensityMetric">1.8%</div>
                                <div class="metric-label">Keyword Density</div>
                                <div class="metric-status excellent">Optimal</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value" id="readabilityMetric">73</div>
                                <div class="metric-label">Readability</div>
                                <div class="metric-status good">Easy to read</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value" id="headingsMetric">6</div>
                                <div class="metric-label">Headings</div>
                                <div class="metric-status good">Well structured</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- SEO Suggestions -->
                    <div class="suggestions-section">
                        <h4>SEO Suggestions</h4>
                        <div class="suggestions-list" id="suggestionsList">
                            <div class="suggestion-item priority-high">
                                <div class="suggestion-icon">
                                    <svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.872-.833-2.464 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                </div>
                                <div class="suggestion-content">
                                    <div class="suggestion-title">Add more internal links</div>
                                    <div class="suggestion-description">Include 2-3 more internal links to related pages</div>
                                </div>
                                <button class="suggestion-action" onclick="applySuggestion('internal-links')">Apply</button>
                            </div>
                            
                            <div class="suggestion-item priority-medium">
                                <div class="suggestion-icon">
                                    <svg class="w-4 h-4 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="suggestion-content">
                                    <div class="suggestion-title">Optimize meta description</div>
                                    <div class="suggestion-description">Add a compelling meta description</div>
                                </div>
                                <button class="suggestion-action" onclick="applySuggestion('meta-description')">Apply</button>
                            </div>
                            
                            <div class="suggestion-item priority-low">
                                <div class="suggestion-icon">
                                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                                    </svg>
                                </div>
                                <div class="suggestion-content">
                                    <div class="suggestion-title">Add FAQ section</div>
                                    <div class="suggestion-description">Include FAQ to improve user experience</div>
                                </div>
                                <button class="suggestion-action" onclick="applySuggestion('faq-section')">Apply</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Keyword Analysis -->
                    <div class="keywords-section">
                        <h4>Keyword Analysis</h4>
                        <div class="keywords-list">
                            <div class="keyword-item">
                                <div class="keyword-term">digital marketing services</div>
                                <div class="keyword-stats">
                                    <span class="keyword-count">15 occurrences</span>
                                    <span class="keyword-density">1.8%</span>
                                </div>
                            </div>
                            <div class="keyword-item">
                                <div class="keyword-term">digital marketing</div>
                                <div class="keyword-stats">
                                    <span class="keyword-count">8 occurrences</span>
                                    <span class="keyword-density">0.9%</span>
                                </div>
                            </div>
                            <div class="keyword-item">
                                <div class="keyword-term">online marketing</div>
                                <div class="keyword-stats">
                                    <span class="keyword-count">3 occurrences</span>
                                    <span class="keyword-density">0.4%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Authority Links -->
                    <div class="authority-links-section">
                        <h4>Authority Links</h4>
                        <div class="authority-links-list">
                            <div class="authority-link-item">
                                <div class="link-info">
                                    <div class="link-title">Digital Marketing - Wikipedia</div>
                                    <div class="link-url">wikipedia.org</div>
                                </div>
                                <div class="link-score">98</div>
                                <button class="link-action" onclick="insertAuthorityLinkContent('wikipedia')">Insert</button>
                            </div>
                            <div class="authority-link-item">
                                <div class="link-info">
                                    <div class="link-title">Marketing Insights - Google</div>
                                    <div class="link-url">thinkwithgoogle.com</div>
                                </div>
                                <div class="link-score">99</div>
                                <button class="link-action" onclick="insertAuthorityLinkContent('google')">Insert</button>
                            </div>
                        </div>
                        <button class="btn btn-secondary btn-sm w-full mt-4" onclick="findMoreAuthorityLinks()">Find More Links</button>
                    </div>
                    
                    <!-- E-E-A-T Score -->
                    <div class="eeat-section">
                        <h4>E-E-A-T Assessment</h4>
                        <div class="eeat-scores">
                            <div class="eeat-item">
                                <div class="eeat-label">Experience</div>
                                <div class="eeat-score">85</div>
                                <div class="eeat-bar">
                                    <div class="eeat-fill" style="width: 85%"></div>
                                </div>
                            </div>
                            <div class="eeat-item">
                                <div class="eeat-label">Expertise</div>
                                <div class="eeat-score">92</div>
                                <div class="eeat-bar">
                                    <div class="eeat-fill" style="width: 92%"></div>
                                </div>
                            </div>
                            <div class="eeat-item">
                                <div class="eeat-label">Authoritativeness</div>
                                <div class="eeat-score">88</div>
                                <div class="eeat-bar">
                                    <div class="eeat-fill" style="width: 88%"></div>
                                </div>
                            </div>
                            <div class="eeat-item">
                                <div class="eeat-label">Trustworthiness</div>
                                <div class="eeat-score">94</div>
                                <div class="eeat-bar">
                                    <div class="eeat-fill" style="width: 94%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Authority Link Modal -->
    <div class="modal hidden" id="authorityLinkModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Insert Authority Link</h3>
                <button class="modal-close" onclick="closeModal('authorityLinkModal')">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">Link Text</label>
                    <input type="text" id="linkText" class="form-input" placeholder="Enter link text">
                </div>
                <div class="form-group">
                    <label class="form-label">Authority URL</label>
                    <select id="authorityUrl" class="form-select">
                        <option value="">Select authority source</option>
                        <option value="https://en.wikipedia.org/wiki/Digital_marketing">Wikipedia - Digital Marketing</option>
                        <option value="https://www.thinkwithgoogle.com">Google Marketing Insights</option>
                        <option value="https://moz.com/beginners-guide-to-seo">Moz SEO Guide</option>
                    </select>
                </div>
            </div>
            <div class="modal-actions">
                <button class="btn btn-secondary" onclick="closeModal('authorityLinkModal')">Cancel</button>
                <button class="btn btn-primary" onclick="insertSelectedAuthorityLink()">Insert Link</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Content Editor JavaScript functionality
        let currentContent = '';
        let autosaveInterval;
        let seoAnalysisTimeout;
        
        document.addEventListener('DOMContentLoaded', function() {
            initializeEditor();
            setupAutosave();
            updateSEOAnalysis();
            checkGeneratedContent();
        });

        function initializeEditor() {
            // Initialize editor state
            const editor = document.getElementById('editorContent');
            currentContent = editor.innerHTML;
            
            // Set up real-time word count
            updateWordCount();
            
            // Initialize SEO sidebar visibility
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('seo') === 'true') {
                document.getElementById('seoSidebar').classList.add('visible');
            }
        }

        function setupAutosave() {
            autosaveInterval = setInterval(() => {
                saveContent(true); // Auto-save silently
            }, 30000); // Auto-save every 30 seconds
        }

        function updateSEOAnalysis() {
            clearTimeout(seoAnalysisTimeout);
            seoAnalysisTimeout = setTimeout(() => {
                performSEOAnalysis();
            }, 1000); // Debounce analysis by 1 second
        }

        function performSEOAnalysis() {
            const content = document.getElementById('editorContent').innerText;
            const title = document.querySelector('.content-title').innerText;
            
            // Update word count
            updateWordCount();
            
            // Calculate keyword density
            const keywordDensity = calculateKeywordDensity(content, 'digital marketing services');
            document.getElementById('keywordDensityMetric').textContent = keywordDensity + '%';
            
            // Calculate readability
            const readability = calculateReadability(content);
            document.getElementById('readabilityMetric').textContent = readability;
            
            // Count headings
            const headingCount = countHeadings();
            document.getElementById('headingsMetric').textContent = headingCount;
            
            // Update overall SEO score
            const seoScore = calculateSEOScore(content, title, keywordDensity, readability, headingCount);
            updateSEOScore(seoScore);
            
            // Update suggestions
            updateSuggestions(content, title);
        }

        function updateWordCount() {
            const content = document.getElementById('editorContent').innerText;
            const wordCount = content.trim().split(/\s+/).filter(word => word.length > 0).length;
            
            document.getElementById('currentWordCount').textContent = wordCount;
            document.getElementById('wordCountMetric').textContent = wordCount;
            
            // Update status
            const targetWords = parseInt(document.getElementById('targetWordCount').textContent);
            const wordCountMetric = document.getElementById('wordCountMetric');
            const statusElement = wordCountMetric.parentNode.querySelector('.metric-status');
            
            if (wordCount < targetWords * 0.8) {
                statusElement.textContent = 'Too short';
                statusElement.className = 'metric-status warning';
            } else if (wordCount > targetWords * 1.2) {
                statusElement.textContent = 'Too long';
                statusElement.className = 'metric-status warning';
            } else {
                statusElement.textContent = 'Good length';
                statusElement.className = 'metric-status good';
            }
        }

        function calculateKeywordDensity(content, keyword) {
            const words = content.toLowerCase().split(/\s+/);
            const keywordOccurrences = content.toLowerCase().split(keyword.toLowerCase()).length - 1;
            const density = (keywordOccurrences / words.length) * 100;
            return Math.round(density * 10) / 10;
        }

        function calculateReadability(content) {
            // Simplified readability calculation
            const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
            const words = content.split(/\s+/).filter(w => w.length > 0);
            const avgWordsPerSentence = words.length / sentences.length;
            
            // Flesch Reading Ease approximation
            let score = 206.835 - (1.015 * avgWordsPerSentence);
            return Math.max(0, Math.min(100, Math.round(score)));
        }

        function countHeadings() {
            const editor = document.getElementById('editorContent');
            const headings = editor.querySelectorAll('h1, h2, h3, h4, h5, h6');
            return headings.length;
        }

        function calculateSEOScore(content, title, keywordDensity, readability, headingCount) {
            let score = 0;
            
            // Title optimization (20 points)
            if (title.toLowerCase().includes('digital marketing services')) score += 20;
            
            // Content length (20 points)
            const wordCount = content.split(/\s+/).length;
            if (wordCount >= 800 && wordCount <= 3000) score += 20;
            
            // Keyword density (20 points)
            if (keywordDensity >= 1.0 && keywordDensity <= 3.0) score += 20;
            
            // Readability (15 points)
            if (readability >= 60) score += 15;
            
            // Heading structure (15 points)
            if (headingCount >= 3) score += 15;
            
            // Authority links (10 points)
            const authorityLinks = content.match(/wikipedia\.org|\.gov|\.edu/g);
            if (authorityLinks && authorityLinks.length > 0) score += 10;
            
            return Math.min(100, score);
        }

        function updateSEOScore(score) {
            const scoreElement = document.getElementById('seoScore');
            const circleElement = document.getElementById('seoScoreCircle');
            
            scoreElement.textContent = score;
            
            // Update circle color based on score
            circleElement.className = 'score-circle';
            if (score >= 90) {
                circleElement.classList.add('score-excellent');
            } else if (score >= 80) {
                circleElement.classList.add('score-good');
            } else if (score >= 70) {
                circleElement.classList.add('score-fair');
            } else {
                circleElement.classList.add('score-poor');
            }
        }

        function updateSuggestions(content, title) {
            const suggestions = [];
            
            // Check for internal links
            const internalLinks = content.match(/href="[^"]*"/g) || [];
            if (internalLinks.length < 3) {
                suggestions.push({
                    type: 'internal-links',
                    priority: 'high',
                    title: 'Add more internal links',
                    description: `Include ${3 - internalLinks.length} more internal links to related pages`
                });
            }
            
            // Check for meta description
            if (!document.querySelector('meta[name="description"]')) {
                suggestions.push({
                    type: 'meta-description',
                    priority: 'medium',
                    title: 'Add meta description',
                    description: 'Create a compelling meta description for better SERP visibility'
                });
            }
            
            // Check for FAQ section
            if (!content.toLowerCase().includes('faq') && !content.toLowerCase().includes('frequently asked')) {
                suggestions.push({
                    type: 'faq-section',
                    priority: 'low',
                    title: 'Add FAQ section',
                    description: 'Include FAQ to improve user experience and SEO'
                });
            }
            
            // Update suggestions display
            displaySuggestions(suggestions);
        }

        function displaySuggestions(suggestions) {
            const container = document.getElementById('suggestionsList');
            
            if (suggestions.length === 0) {
                container.innerHTML = '<div class="no-suggestions">All SEO requirements met!</div>';
                return;
            }
            
            container.innerHTML = suggestions.map(suggestion => `
                <div class="suggestion-item priority-${suggestion.priority}">
                    <div class="suggestion-icon">
                        <svg class="w-4 h-4 ${getPriorityColor(suggestion.priority)}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${getPriorityIcon(suggestion.priority)}"></path>
                        </svg>
                    </div>
                    <div class="suggestion-content">
                        <div class="suggestion-title">${suggestion.title}</div>
                        <div class="suggestion-description">${suggestion.description}</div>
                    </div>
                    <button class="suggestion-action" onclick="applySuggestion('${suggestion.type}')">Apply</button>
                </div>
            `).join('');
        }

        function getPriorityColor(priority) {
            switch (priority) {
                case 'high': return 'text-red-500';
                case 'medium': return 'text-yellow-500';
                case 'low': return 'text-green-500';
                default: return 'text-gray-500';
            }
        }

        function getPriorityIcon(priority) {
            switch (priority) {
                case 'high': return 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.872-.833-2.464 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z';
                case 'medium': return 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
                case 'low': return 'M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z';
            }
        }

        function applySuggestion(type) {
            switch (type) {
                case 'internal-links':
                    insertInternalLink();
                    break;
                case 'meta-description':
                    addMetaDescription();
                    break;
                case 'faq-section':
                    insertFAQSection();
                    break;
            }
        }

        function insertInternalLink() {
            const link = prompt('Enter internal link URL:');
            if (link) {
                const linkText = prompt('Enter link text:');
                if (linkText) {
                    insertHTMLAtCursor(`<a href="${link}">${linkText}</a>`);
                    updateSEOAnalysis();
                }
            }
        }

        function addMetaDescription() {
            const description = prompt('Enter meta description (150-160 characters):');
            if (description) {
                // In a real implementation, this would update the page's meta tags
                console.log('Meta description added:', description);
                showNotification('Meta description added successfully', 'success');
            }
        }

        function insertFAQSection() {
            const faqHTML = `
                <h2>Frequently Asked Questions</h2>
                <h3>What are digital marketing services?</h3>
                <p>Digital marketing services are professional strategies and tactics used to promote businesses online through various digital channels.</p>
                
                <h3>How much do digital marketing services cost?</h3>
                <p>The cost varies depending on the scope of services, business size, and specific goals. Most agencies offer customized packages.</p>
                
                <h3>How long does it take to see results?</h3>
                <p>Results typically begin showing within 3-6 months, though some strategies like PPC can show immediate results.</p>
            `;
            
            const editor = document.getElementById('editorContent');
            editor.innerHTML += faqHTML;
            updateSEOAnalysis();
            showNotification('FAQ section added successfully', 'success');
        }

        // Editor toolbar functions
        function formatText(command) {
            document.execCommand(command, false, null);
            updateSEOAnalysis();
        }

        function formatHeading(tag) {
            document.execCommand('formatBlock', false, tag);
            updateSEOAnalysis();
        }

        function insertList(type) {
            if (type === 'ul') {
                document.execCommand('insertUnorderedList', false, null);
            } else {
                document.execCommand('insertOrderedList', false, null);
            }
            updateSEOAnalysis();
        }

        function insertLink() {
            const url = prompt('Enter URL:');
            if (url) {
                const text = prompt('Enter link text:') || url;
                insertHTMLAtCursor(`<a href="${url}" target="_blank">${text}</a>`);
                updateSEOAnalysis();
            }
        }

        function insertAuthorityLink() {
            document.getElementById('authorityLinkModal').classList.remove('hidden');
        }

        function insertSelectedAuthorityLink() {
            const linkText = document.getElementById('linkText').value;
            const linkUrl = document.getElementById('authorityUrl').value;
            
            if (linkText && linkUrl) {
                insertHTMLAtCursor(`<a href="${linkUrl}" target="_blank">${linkText}</a>`);
                closeModal('authorityLinkModal');
                updateSEOAnalysis();
            }
        }

        function insertHTMLAtCursor(html) {
            const sel = window.getSelection();
            if (sel.getRangeAt && sel.rangeCount) {
                const range = sel.getRangeAt(0);
                range.deleteContents();
                
                const el = document.createElement('div');
                el.innerHTML = html;
                const frag = document.createDocumentFragment();
                let node, lastNode;
                while ((node = el.firstChild)) {
                    lastNode = frag.appendChild(node);
                }
                range.insertNode(frag);
                
                if (lastNode) {
                    range = range.cloneRange();
                    range.setStartAfter(lastNode);
                    range.collapse(true);
                    sel.removeAllRanges();
                    sel.addRange(range);
                }
            }
        }

        function optimizeContent() {
            showNotification('Optimizing content with AI...', 'info');
            
            // Simulate AI optimization
            setTimeout(() => {
                showNotification('Content optimized successfully', 'success');
                updateSEOAnalysis();
            }, 2000);
        }

        function insertAuthorityLinkContent(source) {
            let linkHTML = '';
            
            switch (source) {
                case 'wikipedia':
                    linkHTML = '<a href="https://en.wikipedia.org/wiki/Digital_marketing" target="_blank">comprehensive digital marketing strategies</a>';
                    break;
                case 'google':
                    linkHTML = '<a href="https://www.thinkwithgoogle.com" target="_blank">latest marketing insights</a>';
                    break;
            }
            
            if (linkHTML) {
                insertHTMLAtCursor(linkHTML);
                updateSEOAnalysis();
            }
        }

        function findMoreAuthorityLinks() {
            showNotification('Searching for relevant authority links...', 'info');
            
            // Simulate finding more links
            setTimeout(() => {
                showNotification('Found 3 new authority links', 'success');
            }, 1500);
        }

        // UI Controls
        function togglePreview() {
            // Toggle between edit and preview mode
            const editor = document.getElementById('editorContent');
            const isEditable = editor.contentEditable === 'true';
            
            if (isEditable) {
                editor.contentEditable = 'false';
                editor.classList.add('preview-mode');
                document.getElementById('previewBtn').textContent = 'Edit';
            } else {
                editor.contentEditable = 'true';
                editor.classList.remove('preview-mode');
                document.getElementById('previewBtn').textContent = 'Preview';
            }
        }

        function toggleSidePanel() {
            const sidebar = document.getElementById('seoSidebar');
            sidebar.classList.toggle('visible');
        }

        function toggleSidebar() {
            // This function would toggle the main sidebar
            console.log('Toggle main sidebar');
        }

        function saveContent(silent = false) {
            const content = document.getElementById('editorContent').innerHTML;
            const title = document.querySelector('.content-title').innerText;
            
            // Simulate saving
            localStorage.setItem('editorContent', content);
            localStorage.setItem('editorTitle', title);
            
            if (!silent) {
                showNotification('Content saved successfully', 'success');
            }
            
            // Update last saved time
            document.querySelector('.last-saved').textContent = 'Saved just now';
        }

        function publishContent() {
            if (confirm('Are you sure you want to publish this content?')) {
                showNotification('Publishing content...', 'info');
                
                setTimeout(() => {
                    showNotification('Content published successfully', 'success');
                }, 2000);
            }
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        function handlePaste(event) {
            // Clean pasted content
            event.preventDefault();
            const paste = (event.clipboardData || window.clipboardData).getData('text');
            const cleanText = paste.replace(/<[^>]*>/g, ''); // Remove HTML tags
            insertHTMLAtCursor(cleanText);
            updateSEOAnalysis();
        }

        function checkGeneratedContent() {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('generated') === 'true') {
                showNotification('Content generated successfully! Ready for editing.', 'success');
                // Auto-open SEO panel
                setTimeout(() => {
                    toggleSidePanel();
                }, 1000);
            }
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            clearInterval(autosaveInterval);
            saveContent(true); // Final auto-save
        });
    </script>
</body>
</html>