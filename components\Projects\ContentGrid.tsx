/**
 * ContentGrid Component
 * Enterprise SEO SAAS - Content management interface with search, filtering, and actions
 */

import { useState } from 'react'
import { Project } from '@/types/project'
import {
  MagnifyingGlassIcon,
  PlusIcon,
  DocumentTextIcon,
  ChartBarIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  DocumentDuplicateIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon
} from '@heroicons/react/24/outline'

interface ContentGridProps {
  project: Project
  limit?: number
  showActions?: boolean
  onProjectUpdate?: (project: Project) => void
}

// Mock content data structure
interface ContentItem {
  id: string
  title: string
  type: 'blog_post' | 'product_description' | 'landing_page' | 'meta_description' | 'social_media' | 'email_marketing'
  status: 'draft' | 'published' | 'scheduled' | 'archived'
  wordCount: number
  seoScore: number
  keywords: string[]
  createdAt: string
  updatedAt: string
  publishedAt?: string
  views?: number
  clicks?: number
}

export default function ContentGrid({ project, limit, showActions = true, onProjectUpdate }: ContentGridProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showFilters, setShowFilters] = useState(false)

  // Mock content data (in real app, this would come from API)
  const mockContent: ContentItem[] = [
    // Since we enforce no demo data, we start with empty array
    // Real implementation would fetch user's actual content
  ]

  const contentTypes = [
    { value: 'all', label: 'All Types' },
    { value: 'blog_post', label: 'Blog Posts' },
    { value: 'product_description', label: 'Product Descriptions' },
    { value: 'landing_page', label: 'Landing Pages' },
    { value: 'meta_description', label: 'Meta Descriptions' },
    { value: 'social_media', label: 'Social Media' },
    { value: 'email_marketing', label: 'Email Marketing' }
  ]

  const statusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'draft', label: 'Draft' },
    { value: 'published', label: 'Published' },
    { value: 'scheduled', label: 'Scheduled' },
    { value: 'archived', label: 'Archived' }
  ]

  const filteredContent = mockContent.filter(content => {
    const matchesSearch = content.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         content.keywords.some(k => k.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesType = selectedType === 'all' || content.type === selectedType
    const matchesStatus = selectedStatus === 'all' || content.status === selectedStatus
    
    return matchesSearch && matchesType && matchesStatus
  })

  const displayedContent = limit ? filteredContent.slice(0, limit) : filteredContent

  const handleCreateContent = () => {
    // Navigate to content creation or open modal
  }

  const handleEditContent = (contentId: string) => {
    // Navigate to content editor
  }

  const handleDeleteContent = (contentId: string) => {
    if (confirm('Are you sure you want to delete this content?')) {
      // Delete content
    }
  }

  const handleDuplicateContent = (contentId: string) => {
    // Duplicate content
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800'
      case 'draft': return 'bg-gray-100 text-gray-800'
      case 'scheduled': return 'bg-blue-100 text-blue-800'
      case 'archived': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'blog_post': return '📝'
      case 'product_description': return '🛍️'
      case 'landing_page': return '🚀'
      case 'meta_description': return '🏷️'
      case 'social_media': return '📱'
      case 'email_marketing': return '📧'
      default: return '📄'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  if (!showActions && displayedContent.length === 0) {
    return (
      <div className="text-center py-8">
        <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
        <p className="text-gray-500">No content yet</p>
        <p className="text-sm text-gray-400">Create your first piece of content to get started</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Search and Actions */}
      {showActions && (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          {/* Search and Filters */}
          <div className="flex items-center gap-3 flex-1">
            <div className="relative flex-1 max-w-md">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search content..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-white text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <button
              onClick={() => setShowFilters(!showFilters)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              <FunnelIcon className="h-4 w-4 mr-2" />
              Filters
            </button>
          </div>

          {/* View Toggle and Create Button */}
          <div className="flex items-center gap-3">
            <div className="flex rounded-lg border border-gray-300 bg-white p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-1.5 rounded ${
                  viewMode === 'grid'
                    ? 'bg-blue-500 text-white'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <Squares2X2Icon className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-1.5 rounded ${
                  viewMode === 'list'
                    ? 'bg-blue-500 text-white'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <ListBulletIcon className="h-4 w-4" />
              </button>
            </div>

            <button
              onClick={handleCreateContent}
              className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Content
            </button>
          </div>
        </div>
      )}

      {/* Filters Panel */}
      {showFilters && showActions && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Content Type</label>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {contentTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {statusOptions.map((status) => (
                  <option key={status.value} value={status.value}>
                    {status.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Content Display */}
      {displayedContent.length > 0 ? (
        <div className={`
          ${viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' 
            : 'space-y-4'
          }
        `}>
          {displayedContent.map((content) => (
            <div
              key={content.id}
              className={`
                bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow
                ${viewMode === 'list' ? 'p-4' : 'p-6'}
              `}
            >
              {viewMode === 'grid' ? (
                // Grid view
                <>
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <span className="text-lg">{getTypeIcon(content.type)}</span>
                      <span className={`
                        inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                        ${getStatusColor(content.status)}
                      `}>
                        {content.status}
                      </span>
                    </div>
                    
                    {showActions && (
                      <div className="flex items-center gap-1">
                        <button
                          onClick={() => handleEditContent(content.id)}
                          className="p-1 text-gray-400 hover:text-gray-600 rounded"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDuplicateContent(content.id)}
                          className="p-1 text-gray-400 hover:text-gray-600 rounded"
                        >
                          <DocumentDuplicateIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteContent(content.id)}
                          className="p-1 text-gray-400 hover:text-red-600 rounded"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    )}
                  </div>

                  <h3 className="font-medium text-gray-900 mb-2 line-clamp-2">
                    {content.title}
                  </h3>

                  <div className="space-y-3 mb-4">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-500">SEO Score</span>
                      <span className="font-medium text-gray-900">{content.seoScore}/10</span>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-500">Word Count</span>
                      <span className="font-medium text-gray-900">{content.wordCount.toLocaleString()}</span>
                    </div>

                    {content.views && (
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-500">Views</span>
                        <span className="font-medium text-gray-900">{content.views.toLocaleString()}</span>
                      </div>
                    )}
                  </div>

                  <div className="text-xs text-gray-500">
                    Updated {formatDate(content.updatedAt)}
                  </div>
                </>
              ) : (
                // List view
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4 flex-1 min-w-0">
                    <span className="text-lg flex-shrink-0">{getTypeIcon(content.type)}</span>
                    
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-gray-900 truncate">{content.title}</h3>
                      <div className="flex items-center gap-4 text-sm text-gray-500 mt-1">
                        <span className={`
                          inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                          ${getStatusColor(content.status)}
                        `}>
                          {content.status}
                        </span>
                        <span>{content.wordCount.toLocaleString()} words</span>
                        <span>SEO: {content.seoScore}/10</span>
                        <span>Updated {formatDate(content.updatedAt)}</span>
                      </div>
                    </div>
                  </div>

                  {showActions && (
                    <div className="flex items-center gap-2 flex-shrink-0">
                      <button
                        onClick={() => handleEditContent(content.id)}
                        className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-50"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDuplicateContent(content.id)}
                        className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-50"
                      >
                        <DocumentDuplicateIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteContent(content.id)}
                        className="p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-gray-50"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        // Empty state
        <div className="text-center py-12">
          <DocumentTextIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No content found</h3>
          <p className="text-gray-600 mb-6">
            {searchTerm || selectedType !== 'all' || selectedStatus !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Create your first piece of SEO-optimized content to get started'
            }
          </p>
          {showActions && (
            <button
              onClick={handleCreateContent}
              className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Content
            </button>
          )}
        </div>
      )}

      {/* Load More Button (if limited) */}
      {limit && filteredContent.length > limit && (
        <div className="text-center">
          <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
            View All Content ({filteredContent.length})
          </button>
        </div>
      )}
    </div>
  )
}