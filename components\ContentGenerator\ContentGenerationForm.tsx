'use client';

import React, { useState, useEffect } from 'react';
import { 
  DocumentTextIcon, 
  MagnifyingGlassIcon, 
  SparklesIcon,
  ClockIcon,
  CogIcon,
  ChevronDownIcon,
  PlusIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface ContentGenerationFormProps {
  onGenerate: (config: ContentGenerationConfig) => void;
  loading?: boolean;
  projectId?: string;
}

interface ContentGenerationConfig {
  contentType: 'blog_post' | 'product_description' | 'landing_page' | 'social_media' | 'email' | 'meta_description';
  primaryKeyword: string;
  targetKeywords: string[];
  industry: string;
  targetAudience: string;
  tone: 'professional' | 'casual' | 'friendly' | 'authoritative' | 'conversational';
  contentLength: 'short' | 'medium' | 'long' | 'custom';
  customWordCount?: number;
  includeCompetitorAnalysis: boolean;
  competitorUrls: string[];
  seoRequirements: {
    includeMetaTags: boolean;
    includeHeaders: boolean;
    includeInternalLinks: boolean;
    targetReadingLevel: 'elementary' | 'middle' | 'high' | 'college';
  };
  additionalInstructions?: string;
}

const contentTypes = [
  { value: 'blog_post', label: 'Blog Post', icon: DocumentTextIcon },
  { value: 'product_description', label: 'Product Description', icon: SparklesIcon },
  { value: 'landing_page', label: 'Landing Page', icon: DocumentTextIcon },
  { value: 'social_media', label: 'Social Media', icon: SparklesIcon },
  { value: 'email', label: 'Email', icon: DocumentTextIcon },
  { value: 'meta_description', label: 'Meta Description', icon: DocumentTextIcon },
];

const tones = [
  { value: 'professional', label: 'Professional' },
  { value: 'casual', label: 'Casual' },
  { value: 'friendly', label: 'Friendly' },
  { value: 'authoritative', label: 'Authoritative' },
  { value: 'conversational', label: 'Conversational' },
];

const contentLengths = [
  { value: 'short', label: 'Short (300-500 words)' },
  { value: 'medium', label: 'Medium (500-1000 words)' },
  { value: 'long', label: 'Long (1000-2000 words)' },
  { value: 'custom', label: 'Custom' },
];

const readingLevels = [
  { value: 'elementary', label: 'Elementary (Grade 1-5)' },
  { value: 'middle', label: 'Middle School (Grade 6-8)' },
  { value: 'high', label: 'High School (Grade 9-12)' },
  { value: 'college', label: 'College Level' },
];

export default function ContentGenerationForm({ 
  onGenerate, 
  loading = false, 
  projectId 
}: ContentGenerationFormProps) {
  const [config, setConfig] = useState<ContentGenerationConfig>({
    contentType: 'blog_post',
    primaryKeyword: '',
    targetKeywords: [],
    industry: '',
    targetAudience: '',
    tone: 'professional',
    contentLength: 'medium',
    includeCompetitorAnalysis: false,
    competitorUrls: [],
    seoRequirements: {
      includeMetaTags: true,
      includeHeaders: true,
      includeInternalLinks: false,
      targetReadingLevel: 'high',
    },
  });

  const [newKeyword, setNewKeyword] = useState('');
  const [newCompetitorUrl, setNewCompetitorUrl] = useState('');
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!config.primaryKeyword.trim()) {
      alert('Please enter a primary keyword');
      return;
    }
    onGenerate(config);
  };

  const addKeyword = () => {
    if (newKeyword.trim() && !config.targetKeywords.includes(newKeyword.trim())) {
      setConfig(prev => ({
        ...prev,
        targetKeywords: [...prev.targetKeywords, newKeyword.trim()]
      }));
      setNewKeyword('');
    }
  };

  const removeKeyword = (keyword: string) => {
    setConfig(prev => ({
      ...prev,
      targetKeywords: prev.targetKeywords.filter(k => k !== keyword)
    }));
  };

  const addCompetitorUrl = () => {
    if (newCompetitorUrl.trim() && !config.competitorUrls.includes(newCompetitorUrl.trim())) {
      setConfig(prev => ({
        ...prev,
        competitorUrls: [...prev.competitorUrls, newCompetitorUrl.trim()]
      }));
      setNewCompetitorUrl('');
    }
  };

  const removeCompetitorUrl = (url: string) => {
    setConfig(prev => ({
      ...prev,
      competitorUrls: prev.competitorUrls.filter(u => u !== url)
    }));
  };

  return (
    <div className="bg-white rounded-xl border border-gray-200 p-6">
      <div className="flex items-center mb-6">
        <SparklesIcon className="h-6 w-6 text-blue-600 mr-2" />
        <h2 className="text-xl font-semibold text-gray-900">Generate Content</h2>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Content Type Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Content Type
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {contentTypes.map((type) => (
              <button
                key={type.value}
                type="button"
                onClick={() => setConfig(prev => ({ ...prev, contentType: type.value as any }))}
                className={`p-3 rounded-lg border-2 transition-all ${
                  config.contentType === type.value
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <type.icon className="h-5 w-5 mx-auto mb-1" />
                <span className="text-sm font-medium">{type.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Primary Keyword */}
        <div>
          <label htmlFor="primaryKeyword" className="block text-sm font-medium text-gray-700 mb-2">
            Primary Keyword *
          </label>
          <input
            type="text"
            id="primaryKeyword"
            value={config.primaryKeyword}
            onChange={(e) => setConfig(prev => ({ ...prev, primaryKeyword: e.target.value }))}
            placeholder="Enter your main keyword"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            required
          />
        </div>

        {/* Target Keywords */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Target Keywords
          </label>
          <div className="flex gap-2 mb-2">
            <input
              type="text"
              value={newKeyword}
              onChange={(e) => setNewKeyword(e.target.value)}
              placeholder="Add related keywords"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addKeyword())}
            />
            <button
              type="button"
              onClick={addKeyword}
              className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <PlusIcon className="h-5 w-5" />
            </button>
          </div>
          {config.targetKeywords.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {config.targetKeywords.map((keyword) => (
                <span
                  key={keyword}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {keyword}
                  <button
                    type="button"
                    onClick={() => removeKeyword(keyword)}
                    className="ml-1 text-blue-600 hover:text-blue-800"
                  >
                    <XMarkIcon className="h-3 w-3" />
                  </button>
                </span>
              ))}
            </div>
          )}
        </div>

        {/* Industry and Target Audience */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="industry" className="block text-sm font-medium text-gray-700 mb-2">
              Industry
            </label>
            <input
              type="text"
              id="industry"
              value={config.industry}
              onChange={(e) => setConfig(prev => ({ ...prev, industry: e.target.value }))}
              placeholder="e.g., Technology, Healthcare, E-commerce"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div>
            <label htmlFor="targetAudience" className="block text-sm font-medium text-gray-700 mb-2">
              Target Audience
            </label>
            <input
              type="text"
              id="targetAudience"
              value={config.targetAudience}
              onChange={(e) => setConfig(prev => ({ ...prev, targetAudience: e.target.value }))}
              placeholder="e.g., Small business owners, Marketing professionals"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* Tone and Content Length */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="tone" className="block text-sm font-medium text-gray-700 mb-2">
              Tone
            </label>
            <select
              id="tone"
              value={config.tone}
              onChange={(e) => setConfig(prev => ({ ...prev, tone: e.target.value as any }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {tones.map(tone => (
                <option key={tone.value} value={tone.value}>{tone.label}</option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="contentLength" className="block text-sm font-medium text-gray-700 mb-2">
              Content Length
            </label>
            <select
              id="contentLength"
              value={config.contentLength}
              onChange={(e) => setConfig(prev => ({ ...prev, contentLength: e.target.value as any }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {contentLengths.map(length => (
                <option key={length.value} value={length.value}>{length.label}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Custom Word Count */}
        {config.contentLength === 'custom' && (
          <div>
            <label htmlFor="customWordCount" className="block text-sm font-medium text-gray-700 mb-2">
              Custom Word Count
            </label>
            <input
              type="number"
              id="customWordCount"
              value={config.customWordCount || ''}
              onChange={(e) => setConfig(prev => ({ ...prev, customWordCount: parseInt(e.target.value) || undefined }))}
              placeholder="Enter word count"
              min="100"
              max="10000"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        )}

        {/* Competitor Analysis */}
        <div>
          <div className="flex items-center mb-3">
            <input
              type="checkbox"
              id="includeCompetitorAnalysis"
              checked={config.includeCompetitorAnalysis}
              onChange={(e) => setConfig(prev => ({ ...prev, includeCompetitorAnalysis: e.target.checked }))}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="includeCompetitorAnalysis" className="ml-2 block text-sm font-medium text-gray-700">
              Include Competitor Analysis
            </label>
          </div>
          
          {config.includeCompetitorAnalysis && (
            <div>
              <div className="flex gap-2 mb-2">
                <input
                  type="url"
                  value={newCompetitorUrl}
                  onChange={(e) => setNewCompetitorUrl(e.target.value)}
                  placeholder="Enter competitor URL"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addCompetitorUrl())}
                />
                <button
                  type="button"
                  onClick={addCompetitorUrl}
                  className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <PlusIcon className="h-5 w-5" />
                </button>
              </div>
              {config.competitorUrls.length > 0 && (
                <div className="space-y-2">
                  {config.competitorUrls.map((url) => (
                    <div
                      key={url}
                      className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                    >
                      <span className="text-sm text-gray-700 truncate">{url}</span>
                      <button
                        type="button"
                        onClick={() => removeCompetitorUrl(url)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Advanced Options */}
        <div>
          <button
            type="button"
            onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
            className="flex items-center text-sm font-medium text-gray-700 hover:text-gray-900"
          >
            <CogIcon className="h-4 w-4 mr-1" />
            Advanced Options
            <ChevronDownIcon className={`h-4 w-4 ml-1 transition-transform ${showAdvancedOptions ? 'rotate-180' : ''}`} />
          </button>
          
          {showAdvancedOptions && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    SEO Requirements
                  </label>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={config.seoRequirements.includeMetaTags}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          seoRequirements: { ...prev.seoRequirements, includeMetaTags: e.target.checked }
                        }))}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">Include Meta Tags</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={config.seoRequirements.includeHeaders}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          seoRequirements: { ...prev.seoRequirements, includeHeaders: e.target.checked }
                        }))}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">Include Headers (H1, H2, H3)</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={config.seoRequirements.includeInternalLinks}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          seoRequirements: { ...prev.seoRequirements, includeInternalLinks: e.target.checked }
                        }))}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">Include Internal Links</span>
                    </label>
                  </div>
                </div>
                
                <div>
                  <label htmlFor="targetReadingLevel" className="block text-sm font-medium text-gray-700 mb-2">
                    Target Reading Level
                  </label>
                  <select
                    id="targetReadingLevel"
                    value={config.seoRequirements.targetReadingLevel}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      seoRequirements: { ...prev.seoRequirements, targetReadingLevel: e.target.value as any }
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    {readingLevels.map(level => (
                      <option key={level.value} value={level.value}>{level.label}</option>
                    ))}
                  </select>
                </div>
              </div>
              
              <div>
                <label htmlFor="additionalInstructions" className="block text-sm font-medium text-gray-700 mb-2">
                  Additional Instructions
                </label>
                <textarea
                  id="additionalInstructions"
                  value={config.additionalInstructions || ''}
                  onChange={(e) => setConfig(prev => ({ ...prev, additionalInstructions: e.target.value }))}
                  placeholder="Any specific requirements or instructions..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          )}
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading || !config.primaryKeyword.trim()}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
          >
            {loading ? (
              <>
                <ClockIcon className="h-5 w-5 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <SparklesIcon className="h-5 w-5 mr-2" />
                Generate Content
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}