/* Global Search Component Styles */

.global-search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    z-index: 9999;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding-top: 10vh;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease-in-out;
}

.global-search-overlay.search-open {
    opacity: 1;
    visibility: visible;
}

.global-search-overlay.hidden {
    display: none;
}

/* Search Modal */
.search-modal {
    background: white;
    border-radius: 12px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    width: 100%;
    max-width: 640px;
    max-height: 80vh;
    overflow: hidden;
    transform: translateY(-20px) scale(0.95);
    transition: all 0.2s ease-in-out;
}

.search-open .search-modal {
    transform: translateY(0) scale(1);
}

/* Search Header */
.search-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
}

.search-input-container {
    position: relative;
    display: flex;
    align-items: center;
    gap: 12px;
}

.search-icon {
    color: #6b7280;
    flex-shrink: 0;
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 18px;
    font-weight: 500;
    color: #111827;
    background: transparent;
    padding: 0;
}

.search-input::placeholder {
    color: #9ca3af;
}

.search-shortcuts {
    display: flex;
    gap: 4px;
}

.shortcut-key {
    background: #f3f4f6;
    color: #6b7280;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid #e5e7eb;
}

/* Search Content */
.search-content {
    max-height: 500px;
    overflow-y: auto;
}

/* Search Suggestions */
.search-suggestions {
    padding: 16px 0;
}

.suggestions-section {
    margin-bottom: 24px;
}

.suggestions-section h4 {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin: 0 0 12px 24px;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.recent-searches {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.recent-search-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 24px;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    color: #6b7280;
    font-size: 14px;
    transition: all 0.15s ease;
    width: 100%;
}

.recent-search-item:hover {
    background: #f9fafb;
    color: #111827;
}

.recent-search-item svg {
    flex-shrink: 0;
    width: 16px;
    height: 16px;
}

.no-recent {
    padding: 12px 24px;
    color: #9ca3af;
    font-size: 14px;
    font-style: italic;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.quick-action {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 24px;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    color: #374151;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.15s ease;
    width: 100%;
}

.quick-action:hover {
    background: #f3f4f6;
    color: #111827;
}

.quick-action svg {
    flex-shrink: 0;
    color: #6b7280;
}

/* Search Results */
.search-results {
    padding: 16px 0;
}

.results-section {
    margin-bottom: 24px;
}

.results-section h4 {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin: 0 0 12px 24px;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.results-list {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.result-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 24px;
    cursor: pointer;
    transition: all 0.15s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.result-item:hover {
    background: #f3f4f6;
}

.result-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: #f3f4f6;
    border-radius: 6px;
    color: #6b7280;
    flex-shrink: 0;
}

.result-content {
    flex: 1;
    min-width: 0;
}

.result-title {
    font-size: 14px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.result-description {
    font-size: 13px;
    color: #6b7280;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.result-meta {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.score-badge {
    background: #10b981;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
}

.score-badge.score-excellent {
    background: #10b981;
}

.score-badge.score-good {
    background: #3b82f6;
}

.score-badge.score-average {
    background: #f59e0b;
}

.score-badge.score-poor {
    background: #ef4444;
}

.content-type {
    background: #e5e7eb;
    color: #374151;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
}

.industry-tag {
    background: #ddd6fe;
    color: #7c3aed;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
}

.no-results {
    padding: 12px 24px;
    color: #9ca3af;
    font-size: 14px;
    font-style: italic;
}

/* Search Footer */
.search-footer {
    padding: 12px 24px;
    border-top: 1px solid #e5e7eb;
    background: #f9fafb;
}

.search-tips {
    display: flex;
    gap: 16px;
    font-size: 12px;
    color: #6b7280;
}

.tip {
    display: flex;
    align-items: center;
    gap: 4px;
}

.tip kbd {
    background: #e5e7eb;
    color: #374151;
    padding: 1px 4px;
    border-radius: 2px;
    font-size: 10px;
    font-weight: 600;
    border: 1px solid #d1d5db;
    box-shadow: 0 1px 0 #d1d5db;
}

/* Responsive Design */
@media (max-width: 768px) {
    .global-search-overlay {
        padding: 20px;
        padding-top: 5vh;
    }
    
    .search-modal {
        max-height: 90vh;
    }
    
    .search-input {
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    .search-tips {
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .result-item {
        padding: 16px 20px;
    }
    
    .result-title,
    .result-description {
        white-space: normal;
        overflow: visible;
        text-overflow: initial;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .search-modal {
        background: #1f2937;
        color: #f9fafb;
    }
    
    .search-header {
        border-bottom-color: #374151;
    }
    
    .search-input {
        color: #f9fafb;
    }
    
    .search-input::placeholder {
        color: #9ca3af;
    }
    
    .shortcut-key {
        background: #374151;
        color: #d1d5db;
        border-color: #4b5563;
    }
    
    .recent-search-item:hover,
    .quick-action:hover,
    .result-item:hover {
        background: #374151;
    }
    
    .result-icon {
        background: #374151;
        color: #9ca3af;
    }
    
    .result-title {
        color: #f9fafb;
    }
    
    .result-description {
        color: #d1d5db;
    }
    
    .search-footer {
        background: #111827;
        border-top-color: #374151;
    }
    
    .tip kbd {
        background: #374151;
        color: #d1d5db;
        border-color: #4b5563;
        box-shadow: 0 1px 0 #4b5563;
    }
}