/* 
 * SEO SAAS Data Visualization Components
 * Professional chart and data display components
 * Enterprise-grade metrics and analytics styling
 */

/* ===========================
   CHART CONTAINERS & FOUNDATIONS
   =========================== */

.chart-container {
  background: white;
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
  padding: var(--space-6);
  position: relative;
  overflow: hidden;
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--gray-200);
}

.chart-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.chart-subtitle {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: var(--space-1) 0 0 0;
}

.chart-controls {
  display: flex;
  gap: var(--space-2);
  align-items: center;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-4);
  margin-top: var(--space-4);
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: var(--radius-sm);
  flex-shrink: 0;
}

/* ===========================
   PROGRESS INDICATORS
   =========================== */

.progress-container {
  margin-bottom: var(--space-4);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
}

.progress-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

.progress-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-500) 0%, var(--primary-600) 100%);
  border-radius: var(--radius-full);
  transition: width 0.8s ease-out;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

/* Progress variants */
.progress-fill.success {
  background: linear-gradient(90deg, var(--success-500) 0%, var(--success-600) 100%);
}

.progress-fill.warning {
  background: linear-gradient(90deg, var(--warning-500) 0%, var(--warning-600) 100%);
}

.progress-fill.error {
  background: linear-gradient(90deg, var(--error-500) 0%, var(--error-600) 100%);
}

/* Segmented progress */
.progress-segmented {
  display: flex;
  gap: 2px;
  height: 8px;
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-segment {
  flex: 1;
  background-color: var(--gray-200);
  transition: background-color 0.3s ease;
}

.progress-segment.filled {
  background-color: var(--primary-600);
}

/* ===========================
   CIRCULAR PROGRESS & GAUGES
   =========================== */

.circular-progress {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.circular-progress svg {
  transform: rotate(-90deg);
}

.circular-progress-bg {
  fill: none;
  stroke: var(--gray-200);
  stroke-width: 8;
}

.circular-progress-fill {
  fill: none;
  stroke: var(--primary-600);
  stroke-width: 8;
  stroke-linecap: round;
  transition: stroke-dasharray 0.8s ease-out;
}

.circular-progress-text {
  position: absolute;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
}

/* SEO Score Gauge */
.seo-score-gauge {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 0 auto;
}

.gauge-arc {
  fill: none;
  stroke-width: 12;
}

.gauge-bg {
  stroke: var(--gray-200);
}

.gauge-fill {
  stroke-linecap: round;
  transition: stroke-dasharray 1s ease-out;
}

.gauge-fill.excellent {
  stroke: var(--success-600);
}

.gauge-fill.good {
  stroke: var(--primary-600);
}

.gauge-fill.fair {
  stroke: var(--warning-600);
}

.gauge-fill.poor {
  stroke: var(--error-600);
}

.gauge-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.gauge-score {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.gauge-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
  margin-top: var(--space-1);
}

/* ===========================
   SEO SCORE BREAKDOWN
   =========================== */

.seo-score-card {
  background: white;
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
  padding: var(--space-6);
  position: relative;
  overflow: hidden;
}

.score-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-6);
}

.score-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.score-value {
  font-size: 3rem;
  font-weight: 700;
  color: var(--primary-600);
  line-height: 1;
}

.score-breakdown {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.score-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.score-item-label {
  flex: 1;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  min-width: 120px;
}

.score-bar {
  flex: 2;
  height: 8px;
  background-color: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

.score-fill {
  height: 100%;
  border-radius: var(--radius-full);
  transition: width 0.8s ease-out;
  position: relative;
}

.score-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s infinite;
}

.score-number {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  min-width: 40px;
  text-align: right;
}

/* Score color variations */
.score-fill.excellent { background: linear-gradient(90deg, var(--success-500) 0%, var(--success-600) 100%); }
.score-fill.good { background: linear-gradient(90deg, var(--primary-500) 0%, var(--primary-600) 100%); }
.score-fill.fair { background: linear-gradient(90deg, var(--warning-500) 0%, var(--warning-600) 100%); }
.score-fill.poor { background: linear-gradient(90deg, var(--error-500) 0%, var(--error-600) 100%); }

/* ===========================
   TREND INDICATORS
   =========================== */

.trend-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  font-size: 0.75rem;
  font-weight: 500;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
}

.trend-indicator.up {
  color: var(--success-700);
  background-color: var(--success-100);
}

.trend-indicator.down {
  color: var(--error-700);
  background-color: var(--error-100);
}

.trend-indicator.neutral {
  color: var(--gray-700);
  background-color: var(--gray-100);
}

.trend-icon {
  width: 12px;
  height: 12px;
}

.trend-value {
  font-weight: 600;
}

/* ===========================
   COMPARISON CHARTS
   =========================== */

.comparison-chart {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.comparison-item {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-3);
  background-color: var(--gray-50);
  border-radius: var(--radius-lg);
  transition: all 0.2s ease-in-out;
}

.comparison-item:hover {
  background-color: var(--gray-100);
  transform: translateX(4px);
}

.comparison-label {
  flex: 1;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  min-width: 100px;
}

.comparison-bar {
  flex: 2;
  height: 24px;
  background-color: var(--gray-200);
  border-radius: var(--radius-lg);
  overflow: hidden;
  position: relative;
}

.comparison-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-500) 0%, var(--primary-600) 100%);
  border-radius: var(--radius-lg);
  transition: width 0.8s ease-out;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: var(--space-2);
  position: relative;
}

.comparison-value {
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.comparison-number {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  min-width: 50px;
  text-align: right;
}

/* ===========================
   METRIC DISPLAYS
   =========================== */

.metric-display {
  text-align: center;
  padding: var(--space-4);
}

.metric-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: var(--space-2);
}

.metric-number.large {
  font-size: 3.5rem;
}

.metric-number.small {
  font-size: 1.5rem;
}

.metric-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-description {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  margin-top: var(--space-1);
}

/* Metric with trend */
.metric-with-trend {
  position: relative;
}

.metric-trend {
  position: absolute;
  top: var(--space-2);
  right: var(--space-2);
}

/* ===========================
   DATA TABLES WITH VISUALIZATIONS
   =========================== */

.data-table-visual {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: var(--radius-xl);
  overflow: hidden;
  border: 1px solid var(--gray-200);
}

.data-table-visual th {
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
  color: var(--gray-700);
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: var(--space-4);
  text-align: left;
  border-bottom: 1px solid var(--gray-200);
}

.data-table-visual td {
  padding: var(--space-4);
  border-bottom: 1px solid var(--gray-100);
  vertical-align: middle;
}

.data-table-visual tr:hover {
  background-color: var(--gray-50);
}

.data-table-visual tr:last-child td {
  border-bottom: none;
}

/* Table cell with progress */
.table-cell-progress {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.table-progress-bar {
  flex: 1;
  height: 6px;
  background-color: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.table-progress-fill {
  height: 100%;
  border-radius: var(--radius-full);
  transition: width 0.5s ease-out;
}

.table-score {
  font-weight: 600;
  font-size: 0.875rem;
  min-width: 40px;
  text-align: right;
}

/* ===========================
   SPARKLINE CHARTS
   =========================== */

.sparkline-container {
  display: inline-block;
  width: 80px;
  height: 24px;
  position: relative;
}

.sparkline {
  width: 100%;
  height: 100%;
}

.sparkline-line {
  fill: none;
  stroke: var(--primary-600);
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.sparkline-area {
  fill: var(--primary-100);
  opacity: 0.5;
}

.sparkline-dot {
  fill: var(--primary-600);
  stroke: white;
  stroke-width: 2;
}

/* Sparkline variants */
.sparkline-positive .sparkline-line {
  stroke: var(--success-600);
}

.sparkline-positive .sparkline-area {
  fill: var(--success-100);
}

.sparkline-negative .sparkline-line {
  stroke: var(--error-600);
}

.sparkline-negative .sparkline-area {
  fill: var(--error-100);
}

/* ===========================
   STATUS INDICATORS
   =========================== */

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-indicator.online {
  background-color: var(--success-100);
  color: var(--success-800);
}

.status-indicator.online .status-dot {
  background-color: var(--success-600);
  animation: pulse 2s infinite;
}

.status-indicator.offline {
  background-color: var(--gray-100);
  color: var(--gray-800);
}

.status-indicator.offline .status-dot {
  background-color: var(--gray-500);
}

.status-indicator.warning {
  background-color: var(--warning-100);
  color: var(--warning-800);
}

.status-indicator.warning .status-dot {
  background-color: var(--warning-600);
}

.status-indicator.error {
  background-color: var(--error-100);
  color: var(--error-800);
}

.status-indicator.error .status-dot {
  background-color: var(--error-600);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* ===========================
   HEATMAP VISUALIZATION
   =========================== */

.heatmap {
  display: grid;
  gap: 2px;
  padding: var(--space-4);
  background: white;
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-200);
}

.heatmap-cell {
  aspect-ratio: 1;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease-in-out;
  position: relative;
  cursor: pointer;
}

.heatmap-cell:hover {
  transform: scale(1.1);
  z-index: 10;
}

.heatmap-cell.intensity-0 { background-color: var(--gray-100); }
.heatmap-cell.intensity-1 { background-color: var(--primary-100); }
.heatmap-cell.intensity-2 { background-color: var(--primary-200); }
.heatmap-cell.intensity-3 { background-color: var(--primary-300); }
.heatmap-cell.intensity-4 { background-color: var(--primary-400); }
.heatmap-cell.intensity-5 { background-color: var(--primary-500); }

/* ===========================
   RESPONSIVE DATA VISUALIZATION
   =========================== */

@media (max-width: 768px) {
  .chart-container {
    padding: var(--space-4);
  }
  
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }
  
  .chart-legend {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
  
  .seo-score-gauge {
    width: 150px;
    height: 150px;
  }
  
  .gauge-score {
    font-size: 2rem;
  }
  
  .score-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
  
  .score-item-label {
    min-width: auto;
  }
  
  .score-bar {
    width: 100%;
  }
  
  .comparison-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-2);
  }
  
  .comparison-label {
    min-width: auto;
  }
  
  .data-table-visual {
    font-size: 0.75rem;
  }
  
  .data-table-visual th,
  .data-table-visual td {
    padding: var(--space-2);
  }
  
  .metric-number {
    font-size: 2rem;
  }
  
  .metric-number.large {
    font-size: 2.5rem;
  }
  
  .table-cell-progress {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-1);
  }
  
  .table-score {
    text-align: left;
    min-width: auto;
  }
}